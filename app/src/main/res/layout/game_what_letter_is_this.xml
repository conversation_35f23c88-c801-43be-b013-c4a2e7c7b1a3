<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#849BFE">

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/content_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:horizontalInsetRatio="0.1"
        app:itemRatio="1"
        app:layout_constraintHeight_percent="0.4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:spacingRatio="0.1"
        app:verticalInsetRatio="0.1" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/answer_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:horizontalInsetRatio="0.1"
        app:itemRatio="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:spacingRatio="0.1"
        app:verticalInsetRatio="0.1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/line_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_sound">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/coin_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.3"></androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_sound"
        android:layout_width="0dp"
        android:layout_height="55dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_sound_bg_purple"
        android:visibility="gone"
        app:layout_constraintDimensionRatio="1017:235"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginRight="10dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="upper &amp; lower case"
            android:textColor="#FFF"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3"
            app:layout_constraintWidth_percent="0.75" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>