<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/banhxe_8"
    android:layout_width="0dp"
    android:layout_height="0dp"
    app:layout_constraintBottom_toBottomOf="parent"
    app:layout_constraintDimensionRatio="1"
    app:layout_constraintLeft_toLeftOf="parent"
    app:layout_constraintRight_toRightOf="parent"
    app:layout_constraintTop_toTopOf="parent"
    app:pcl_layout_marginPercent="0.1"
    tools:showIn="@layout/game_toannangcao_banhxebian">

    <com.kidsup.giaoducsom.view.SVGAutosizeView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/toan_banhxe8" />


    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_1"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="1"
        android:text="1"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.95"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_4"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="4"
        android:text="4"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.33"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.93"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="2"
        android:text="2"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.95"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.7"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_3"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="3"
        android:text="3"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.67"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.93"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_0"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="0"
        android:text="0"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.67"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.07"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_7"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="7"
        android:text="7"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.33"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.07"
        app:layout_constraintWidth_percent="0.25" />


    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_5"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="5"
        android:text="5"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.7"
        app:layout_constraintWidth_percent="0.25" />

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_6"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:tag="6"
        android:text="6"
        android:textColor="#fff"
        app:heightRatio="0.7"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHorizontal_bias="0.05"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3"
        app:layout_constraintWidth_percent="0.25" />
</androidx.constraintlayout.widget.ConstraintLayout>