//
//  tuduy_list_thekhoetlo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_thekhoetlo: NhanBietGameFragment {
    // MARK: - Properties
    private var dots: [Int] = Array(repeating: 0, count: 9)
    private var stars: [Int] = Array(repeating: 0, count: 9)
    private var answer: [Int] = Array(repeating: 0, count: 9)
    private var dotSize: Int = 0
    private var starSize: Int = 0
    private var answerSize: Int = 0
    private var dotIndexs: [Int] = []
    private var starIndexs: [Int] = []
    private var gridLayout: MyGridView!
    private var leftGridLayout: MyGridView!
    private var coinView: UIView!
    private var wrongAnswers: [[Int]] = []
    private var answers: [[Int]] = []
    let viewBgRight = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        
        viewBgRight.alpha = 0
        viewBgRight.backgroundColor = UIColor(red: 86/255, green: 169/255, blue: 241/255, alpha: 1) // #56A9F1
        view.addSubview(viewBgRight)
        viewBgRight.snp.makeConstraints { make in
            make.left.equalTo(gridLayout)
            make.top.right.bottom.equalTo(self)
        }
        view.bringSubviewToFront(gridLayout)
        
        leftGridLayout = MyGridView()
        leftGridLayout.alpha = 0.01
        view.addSubview(leftGridLayout)
        leftGridLayout.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        coinView = UIView() // Giả lập từ item_coin_view.xml
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.3)
            make.height.equalTo(view).multipliedBy(0.3)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        dotIndexs = Utils.generatePermutation(random(4, 5, 6), size: 9)
        starIndexs = Utils.generatePermutation(random(6, 7, 8), size: 9)
        answerSize = 0
        dotSize = 0
        starSize = 0
        
        for i in 0..<9 {
            dots[i] = dotIndexs.contains(i) ? 1 : 0
            stars[i] = starIndexs.contains(i) ? 1 : 0
            answer[i] = (dots[i] + stars[i] == 2) ? 1 : 0
            answerSize += answer[i]
            dotSize += dots[i]
            starSize += stars[i]
        }
        
        var tries = 0
        while tries <= 100 {
            if let wrongAnswer = createWrongAnswerArray(), !containArray(list: wrongAnswers, array: wrongAnswer) {
                wrongAnswers.append(wrongAnswer)
                tries = 0
            } else {
                tries += 1
            }
            if wrongAnswers.count >= 3 { break }
        }
        
        wrongAnswers.sort { (o1, o2) in
            let count1 = o1.enumerated().filter { $1 == answer[$0] }.count
            let count2 = o2.enumerated().filter { $1 == answer[$0] }.count
            return count2 > count1
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_the khoet lo")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        gridLayout.alpha = 0
        leftGridLayout.moveToCenter(of: self, duration: 0)
        leftGridLayout.alpha = 1
        let files = ["tuduy_khoetthelo_xanh", "tuduy_khoetthelo_do"]
        let svgList = files.map{Utilities.GetSVGKImage(named: $0)}
        let view1 = UIImageView()
        self.loadImage(svgView: view1, svg: Utilities.GetSVGKImage(named: files[0]), dots: self.dots, color: UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1)) // #74B6FF
        let view2 = UIImageView()
        self.loadImage(svgView: view2, svg: Utilities.GetSVGKImage(named: files[1]), dots: self.stars, color: UIColor(red: 255/255, green: 110/255, blue: 106/255, alpha: 1)) // #FF6E6A
        
        self.leftGridLayout.columns = 2
        self.leftGridLayout.itemRatio = 1
        self.leftGridLayout.itemSpacingRatio = 0.1
        self.leftGridLayout.insetRatio = 0.1
        self.leftGridLayout.reloadItemViews(views: [view1, view2])
        
        answers = wrongAnswers.prefix(3).shuffled()
        let answerIndex = Int.random(in: 0...(answers.count))
        answers.insert(self.answer, at: answerIndex)
        
        var views: [UIView] = []
        for i in 0..<answers.count {
            let view = UIImageView()
            self.loadImage(svgView: view, svg: Utilities.GetSVGKImage(named: files[0]), dots: answers[i], color: UIColor(red: 255/255, green: 110/255, blue: 106/255, alpha: 1)) // #FF6E6A
            //AnimationUtils.setTouchEffect(view: view)
            view.tag = i
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
            views.append(view)
        }
        
        self.gridLayout.columns = 2
        self.gridLayout.itemRatio = 1
        self.gridLayout.itemSpacingRatio = 0.1
        self.gridLayout.insetRatio = 0.1
        self.gridLayout.reloadItemViews(views: views)
        
        var delay = self.playSound(self.openGameSound(), "tuduy/tuduy_the khoet lo")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.leftGridLayout.transform = .identity
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
            self.viewBgRight.alpha = 1
        }
        delay += self.gridLayout.showItems(startDelay: delay + 0.5)
        self.scheduler.schedule(after: delay + 1.5) { [weak self] in
            self?.startGame()
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        pauseGame()
        let tag = view.tag
        if sameArray(array1: answers[tag], array2: answer) {
            coinView.frame = view.frame
            animateCoinIfCorrect(view: coinView)
            playSound("effect/answer_end")
            scheduler.schedule(delay: 2.0) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong")
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createWrongAnswerArray() -> [Int]? {
        var wrongAnswer = Array(repeating: 0, count: 9)
        let chooseIndex = Utils.generatePermutation(Int.random(in: 0...dotSize), size: dotSize)
        for index in chooseIndex {
            wrongAnswer[dotIndexs[index]] = 1
        }
        if !sameArray(array1: wrongAnswer, array2: answer) {
            return wrongAnswer
        }
        return nil
    }
    
    private func containArray(list: [[Int]], array: [Int]) -> Bool {
        return list.contains { sameArray(array1: $0, array2: array) }
    }
    
    private func sameArray(array1: [Int], array2: [Int]) -> Bool {
        guard array1.count == array2.count else { return false }
        return array1.enumerated().allSatisfy { $1 == array2[$0] }
    }
    
    private func loadImage(svgView: UIImageView, svg: SVGKImage, dots: [Int], color: UIColor) {
        let clone = svg
        for i in 0..<9 {
            clone.caLayerTree.sublayers![i + 1].setFillColor(color: (dots[i] == 1) ? color : .clear)
        }
        svgView.image = clone.uiImage
        svgView.contentMode = .scaleAspectFit
    }
}


class SVGKPath {
    var fillColor: UIColor
    
    init(fillColor: UIColor) {
        self.fillColor = fillColor
    }
}
