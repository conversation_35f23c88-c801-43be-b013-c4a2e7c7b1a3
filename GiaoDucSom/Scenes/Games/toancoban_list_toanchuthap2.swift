//
//  toancoban_list_toanchuthap2.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_toanchuthap2: toannangcao_list_toanchuthappv10  {
    // MARK: - Validation
    private func isValid(_ number: Int) -> Bool {
        return number > 0 && number <= MAX_VALUE
    }
    
    // MARK: - Game Logic
    override func updateData() {
        MAX_VALUE = 9
        SIZE_COL = 7
        SIZE_ROW = 5
        super.updateData()
    }
    
    override func createLines() {
        grid = Array(repeating: Array(repeating: 0, count: SIZE_COL), count: SIZE_ROW)
        while true {
            grid[0][2] = Int.random(in: 1...MAX_VALUE)
            grid[0][4] = Int.random(in: 1...MAX_VALUE)
            grid[2][0] = Int.random(in: 1...MAX_VALUE)
            grid[2][2] = Int.random(in: 1...MAX_VALUE)
            grid[2][4] = Int.random(in: 1...MAX_VALUE)
            grid[1][2] = [PLUS, MINUS].randomElement()!
            grid[1][4] = [PLUS, MINUS].randomElement()!
            grid[2][1] = [PLUS, MINUS].randomElement()!
            grid[2][3] = [PLUS, MINUS].randomElement()!
            grid[2][5] = EQUAL
            grid[3][2] = EQUAL
            grid[3][4] = EQUAL
            grid[4][2] = grid[0][2] + (grid[1][2] == PLUS ? 1 : -1) * grid[2][2]
            grid[4][4] = grid[0][4] + (grid[1][4] == PLUS ? 1 : -1) * grid[2][4]
            grid[2][6] = grid[2][0] + (grid[2][1] == PLUS ? 1 : -1) * grid[2][2] + (grid[2][3] == PLUS ? 1 : -1) * grid[2][4]
            
            if isValid(grid[2][6]) && isValid(grid[4][2]) && isValid(grid[4][4]) && isValid(grid[2][0] + (grid[2][1] == PLUS ? 1 : -1) * grid[2][2]) {
                break
            }
        }
    }
    
    override func removeNumbers() {
        let row2 = [0, 2, 4].randomElement()!
        let row4 = [0, 2, 4].randomElement()!
        grid[row2][2] = 0
        grid[row4][4] = 0
        
        while true {
            let r = [0, 2, 4].randomElement()!
            let c = [0, 2, 4, 6].randomElement()!
            if (c == 2 && r + row2 == 4) || (c == 4 && r + row4 == 4) {
                continue
            }
            if grid[r][c] != 0 {
                grid[r][c] = 0
                break
            }
        }
    }
}
