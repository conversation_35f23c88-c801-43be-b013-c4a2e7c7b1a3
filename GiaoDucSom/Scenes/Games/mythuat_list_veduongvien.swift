//
//  mythuat_list_veduongvienhinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/5/25.
//

class mythuat_list_veduongvien :  nhanbiet_list_veduongvien {
    override func updateData() {
        var items: [Item] = []
        var folders: [String] = []
        
        FlashcardsManager.shared.getPacks().forEach { pack in
            pack.items.forEach { item in
                if item.tracing ?? false {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
        }
        
        // Select a random item and folder
        while true {
            let randomIndex = (0..<items.count).randomElement()!
            if folders[randomIndex] == "2D Shapes" {
                continue
            }
            setItem(items[randomIndex])
            setFolder(folders[randomIndex])
            break
        }
        super.updateData()
    }
}
