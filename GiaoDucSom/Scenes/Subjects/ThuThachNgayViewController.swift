//
//  ThuThachNgayViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/5/25.
//


import UIKit
import SnapKit

class ThuThachNgayViewController: TwoClickExitActivity {
    // MARK: - Constants
    private let dimMax: CGFloat = 0.8
    private let openDuration: TimeInterval = 0.5
    private static let gameCount = 20
    
    // MARK: - Properties
    private var viewDim: UIView!
    private var gameContainer: UIView!
    private var gameIndex: Int = -1
    private var games: [String] = []
    private var scores: [Float] = []
    private var items: [Item] = []
    private let allGamesOneItem: [String] = [
        nhanbiet_list_kinhlup.className,
        nhanbiet_list_xepthanhdoc.className,
        nhanbiet_list_xepthanhngang.className,
        nhanbiet_list_xephinh4mieng.className,
        nhanbiet_list_remngang.className,
        nhanbiet_list_remdoc.className,
        nhanbiet_list_ongnhom.className,
        nhanbiet_list_xoayhinh.className,
        nhanbiet_list_veduongvien.className,
        nhanbiet_list_upcoc.className,
        nhanbiet_list_ghephinhngang.className,
        nhanbiet_list_canhcua.className,
        nhanbiet_list_phoido.className,
        nhanbiet_list_tudo.className,
        nhanbiet_list_ruottraicay.className
    ]
    private let allGamesThreeItems: [String] = [
        nhanbiet_list_bongbay.className,
        nhanbiet_list_bongnuoc.className,
        nhanbiet_list_hinhvabong.className,
        //nhanbiet_list_latthehinh.className,
        nhanbiet_list_ghephinhten.className,
        nhanbiet_list_phanbiet.className
    ]
    private let allGamesByTopic: [String] = [
        nhanbiet_list_canhcua.className,
        nhanbiet_list_phoido.className,
        nhanbiet_list_tudo.className,
        nhanbiet_list_ruottraicay.className,
        nhanbiet_list_bongnuoc.className
        //VocabDoorGameFragment.className,
        //VocabDryGameFragment.className,
        //VocabStorageGameFragment.className,
        //VocabInsideGameFragment.className,
        //VocabBubbleGameFragment.className
    ]
    private var gameFinished: Bool = false
    private var gameName: UILabel!
    private var btnNext: UIButton!
    private var gameItems: [String: Item] = [:]
    private var oldCoin: Int = 0
    private var gameClosed: Bool = true
    private var btnPlay: KUButton!
    private var challengeCompleted: Bool = false
    private var textCountdown: HeightRatioTextView!
    private var pack: Folder?
    private var selectedItems: [Item] = []
    private var timer: Timer?
    
    override var backgroundMusicFile: String? {
        "bg_other"
    }
    
    // MARK: - View Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        //backgroundMusicName = "bg_other"
        view.backgroundColor = UIColor(hex: "#F9F1B7")
        //hideStatusBar()
        initViews()
        oldCoin = CoinManager.shared.getCoin()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        startUpdateThuThachNgay()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopUpdateThuThachNgay()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        ChallengeManager.shared.setGames(games: games)
        ChallengeManager.shared.setScores(scores: scores)
        ChallengeManager.shared.setGameIndex(gameIndex: gameIndex)
        ChallengeManager.shared.saveChallenge()
    }
    
    // MARK: - Configure Layout
    private func configureLayout(_ view: UIView) {
        /*
        let btnClose = KUButton()
        btnClose.stringTag = "btn_close"
        btnClose.setImage(Utilities.SVGImage(named: "btn_back3"), for: .normal)
        btnClose.imageView?.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        //AnimationUtils.setTouchEffect(btnClose)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.width.equalTo(btnClose.snp.height)
            make.height.equalToSuperview().multipliedBy(0.1)
            make.left.equalToSuperview().multipliedBy(0.05)
            make.top.equalToSuperview().multipliedBy(0.1)
            make.left.equalToSuperview().offset(0.05 * view.frame.height)
        }
        */
        let viewTopLeft2 = UIView()
        viewTopLeft2.stringTag = "view_topleft_2"
        view.addSubview(viewTopLeft2)
        viewTopLeft2.snp.makeConstraints { make in
            make.width.equalTo(viewTopLeft2.snp.height)
            make.height.equalToSuperview().multipliedBy(0.05)
            make.left.equalTo(btnClose!.snp.right)
            make.top.equalToSuperview()
        }
        
        let btnGallery = KUButton()
        btnGallery.stringTag = "btn_gallery"
        btnGallery.setImage(Utilities.SVGImage(named: "home_btn_sticker_book"), for: .normal)
        btnGallery.imageView?.contentMode = .scaleAspectFit
        btnGallery.isHidden = true
        view.addSubview(btnGallery)
        btnGallery.snp.makeConstraints { make in
            make.width.equalTo(btnGallery.snp.height).multipliedBy(514.0/575.0)
            make.top.bottom.equalTo(btnClose!)
        }
        btnGallery.snpRightTop(ratio: 1)
        
        let txtTitle = AutosizeLabel()
        txtTitle.overrideTextAlignment = false
        txtTitle.textAlignment = .left
        txtTitle.stringTag = "txt_title"
        txtTitle.text = "Thử thách ngày"
        txtTitle.textColor = UIColor(hex: "#FF5F07")
        txtTitle.textAlignment = .left
        txtTitle.font = .Freude(size: 20)
        view.addSubview(txtTitle)
        txtTitle.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.06)
            make.left.equalTo(viewTopLeft2.snp.right)
            make.right.equalTo(btnGallery.snp.left)
            make.centerY.equalTo(btnClose!)
        }
        
        let viewCenter = UIView()
        viewCenter.stringTag = "view_center"
        view.addSubview(viewCenter)
        viewCenter.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.35)
            make.width.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        //viewCenter.makeViewCenterAndKeep(widthPercent: 0.9, heightPercent: 0.35, verticalBias: 0.5)
        
        let centerInnerContainer = UIView()
        viewCenter.addSubview(centerInnerContainer)
        centerInnerContainer.makeViewCenterAndKeep(ratio: 4.7)
        
        btnPlay = KUButton()
        btnPlay.stringTag = "btn_play"
        btnPlay.setImage(Utilities.SVGImage(named: "btn_subject0"), for: .normal)
        //AnimationUtils.setTouchEffect(btnPlay)
        btnPlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(playTapped)))
        centerInnerContainer.addSubview(btnPlay)
        btnPlay.makeViewCenterAndKeep(ratio: 494.0/444.0)
        
        let bonusContainer = UIView()
        bonusContainer.stringTag = "bonus_container"
        //AnimationUtils.setTouchEffect(bonusContainer)
        bonusContainer.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(bonusTapped)))
        centerInnerContainer.addSubview(bonusContainer)
        bonusContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(bonusContainer.snp.height).multipliedBy(400.0/350.0)
        }
        //bonusContainer.makeViewCenterAndKeep(ratio: 400.0/350.0, horizontalBias: 0.0, verticalBias: 1)
        
        let imageBonus = UIImageView()
        imageBonus.stringTag = "image_bonus"
        imageBonus.image = Utilities.SVGImage(named: "icon_bonus")
        imageBonus.contentMode = .scaleAspectFit
        bonusContainer.addSubview(imageBonus)
        imageBonus.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBonusLevel = HeightRatioTextView()
        textBonusLevel.stringTag = "text_bonus_level"
        textBonusLevel.setHeightRatio(0.8)
        textBonusLevel.text = "x2"
        textBonusLevel.textColor = UIColor.white
        textBonusLevel.textAlignment = .center
        textBonusLevel.font = UIFont(name: "SVN-Freude", size: 1500)
        textBonusLevel.adjustsFontSizeToFitWidth = true
        textBonusLevel.minimumScaleFactor = 0.1
        bonusContainer.addSubview(textBonusLevel)
        textBonusLevel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalToSuperview().multipliedBy(0.3)
            //make.right.equalToSuperview().multipliedBy(0.8)
            //make.top.equalToSuperview().multipliedBy(0.2)
        }
        textBonusLevel.waitForLayout {
            textBonusLevel.snapToHorizontalBias(horizontalBias: 0.8)
            textBonusLevel.snapToVerticalBias(verticalBias: 0.2)
        }
        
        let viewTop = UIView()
        viewTop.stringTag = "view_top"
        //viewTop.backgroundColor = UIColor(hex: "#0F00")
        view.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.2)
            make.centerX.equalToSuperview()
        }
        viewTop.waitForLayout {
            viewTop.snapToVerticalBias(verticalBias: 0.15)
        }
        //viewTop.makeViewCenterAndKeep(widthPercent: 0.8, heightPercent: 0.2, verticalBias: 0.15)
        
        let viewDate = UIStackView()
        viewDate.stringTag = "view_date"
        viewDate.axis = .horizontal
        viewDate.distribution = .equalSpacing
        viewDate.alignment = .center
        viewTop.addSubview(viewDate)
        viewDate.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        let colors = [UIColor(hex: "#A8DB91"), UIColor(hex: "#F0CE7A"), UIColor(hex: "#F8A7C0")]
        let texts = ["Today", "is", getWeekDayName()]
        let textColors = [UIColor(hex: "#6AB144"), UIColor(hex: "#E1A14D"), UIColor(hex: "#DF5F86")]
        for i in 0..<3 {
            let container = UIView()
            container.transform = CGAffineTransform(rotationAngle: i % 2 == 0 ? -5 * .pi / 180 : 5 * .pi / 180)
            viewDate.addArrangedSubview(container)
            
            let textView = UILabel()
            textView.text = texts[i]
            textView.textColor = textColors[i]
            textView.textAlignment = .center
            textView.font = .Freude(size: 20)
            textView.backgroundColor = colors[i]
            textView.layer.cornerRadius = 10
            textView.layer.masksToBounds = true
            textView.isHidden = true
            container.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.width.height.equalToSuperview().multipliedBy(0.8)
            }
            
            scheduler.schedule(after: 0.1) { [weak self] in
                textView.font = UIFont(name: "SVN-Freude", size: viewTop.frame.height * (i == 2 ? 1.2 : 1) / 3.5)
                textView.isHidden = false
            }
        }
        
        let guidelineRight = UIView()
        guidelineRight.stringTag = "line_right"
        view.addSubview(guidelineRight)
        guidelineRight.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.8)
        }
        
        let boyContainer = UIView()
        boyContainer.stringTag = "boy_container"
        view.addSubview(boyContainer)
        boyContainer.snp.makeConstraints { make in
            make.centerX.equalTo(guidelineRight)
            make.height.equalToSuperview().multipliedBy(0.65)
            make.width.equalTo(boyContainer.snp.height)
            make.centerY.equalToSuperview()
        }
        //boyContainer.makeViewCenterAndKeep(ratio: 1, heightPercent: 0.65, anchor: .left, relativeTo: guidelineRight)
        
        let imageBoy = UIImageView()
        imageBoy.stringTag = "image_boy"
        imageBoy.image = Utilities.SVGImage(named: "thuthach_bg_boy")
        imageBoy.contentMode = .scaleAspectFill
        boyContainer.addSubview(imageBoy)
        imageBoy.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let viewBottom = UIView()
        viewBottom.stringTag = "view_bottom"
        view.addSubview(viewBottom)
        viewBottom.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.85)
            make.height.equalTo(viewBottom.snp.width).dividedBy(10)
            make.centerX.equalToSuperview()
        }
        viewBottom.waitForLayout {
            viewBottom.snapToVerticalBias(verticalBias: 0.865)
        }
        //viewBottom.makeViewCenterAndKeep(ratio: 10, widthPercent: 0.85, verticalBias: 0.865)
        
        let progressContainer = UIView()
        progressContainer.stringTag = "progress_container"
        viewBottom.addSubview(progressContainer)
        progressContainer.makeViewCenterAndKeep(ratio: 1979.7/142.3)
        
        let progressBg = UIImageView()
        progressBg.stringTag = "progress_bg"
        progressBg.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar1")
        progressContainer.addSubview(progressBg)
        progressBg.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let progressCoin = UIImageView()
        progressCoin.stringTag = "progress_coin"
        progressCoin.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar6")
        progressContainer.addSubview(progressCoin)
        progressCoin.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.width.equalTo(progressCoin.snp.height).multipliedBy(110.0/142.3)
        }
        //progressCoin.makeViewCenterAndKeep(ratio: 110.0/142.3, anchor: .right)
        
        let progressBgOrange = UIView()
        progressBgOrange.stringTag = "progress_bg_orange"
        progressContainer.addSubview(progressBgOrange)
        progressBgOrange.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.0)
        }
        
        let orangeInnerImage = UIImageView()
        orangeInnerImage.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar4")
        progressBgOrange.addSubview(orangeInnerImage)
        orangeInnerImage.makeViewCenterAndKeep(ratio: 1979.7/142.3)
        
        let orangeCircle = UIView()
        orangeCircle.backgroundColor = UIColor(hex: "#C0E1FF")
        progressBgOrange.addSubview(orangeCircle)
        orangeCircle.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.39)
            make.width.equalTo(orangeCircle.snp.height)
        }
        orangeCircle.waitForLayout {
            orangeCircle.snapToVerticalBias(verticalBias: 0.35)
        }
        //orangeCircle.makeViewCenterAndKeep(ratio: 1, heightPercent: 0.39, verticalBias: 0.35, anchor: .right)
        
        let progress1 = UIImageView()
        progress1.stringTag = "progress1"
        progress1.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar3")
        progressBgOrange.addSubview(progress1)
        progress1.snp.makeConstraints { make in
            make.bottom.right.top.equalToSuperview()
            make.width.equalTo(progress1.snp.height).multipliedBy(98.9/142.3)
        }
        //progress1.makeViewCenterAndKeep(ratio: 98.9/142.3, anchor: .right)
        
        let txtProgress = HeightRatioTextView()
        txtProgress.stringTag = "txt_progress"
        txtProgress.setHeightRatio(0.7)
        txtProgress.text = "2"
        txtProgress.textColor = UIColor.white
        txtProgress.textAlignment = .center
        txtProgress.font = UIFont(name: "SVN-Freude", size: 1500)
        txtProgress.adjustsFontSizeToFitWidth = true
        txtProgress.minimumScaleFactor = 0.1
        progressBgOrange.addSubview(txtProgress)
        txtProgress.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.47)
            make.width.equalTo(txtProgress.snp.height)
        }
        txtProgress.waitForLayout {
            txtProgress.snapToVerticalBias(verticalBias: 0.2)
        }
        //txtProgress.makeViewCenterAndKeep(ratio: 1, heightPercent: 0.47, verticalBias: 0.2, anchor: .right, margin: 0.1 * view.frame.height)
        
        let progressBarDone = UIImageView()
        progressBarDone.stringTag = "progress_bar_done"
        progressBarDone.image = Utilities.SVGImage(named: "progress_bar_done")
        progressBarDone.contentMode = .scaleAspectFit
        progressBarDone.isHidden = true
        viewBottom.addSubview(progressBarDone)
        progressBarDone.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        textCountdown = HeightRatioTextView()
        textCountdown.stringTag = "text_countdown"
        textCountdown.setHeightRatio(0.6)
        textCountdown.textColor = UIColor(hex: "#FF7761")
        textCountdown.textAlignment = .center
        textCountdown.font = UIFont(name: "SVN-Freude", size: 1500)
        textCountdown.adjustsFontSizeToFitWidth = true
        textCountdown.minimumScaleFactor = 0.1
        view.addSubview(textCountdown)
        textCountdown.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalTo(viewBottom.snp.top)
            make.top.equalTo(viewCenter.snp.bottom)
        }
        
        viewDim = UIView()
        viewDim.stringTag = "view_dim"
        viewDim.backgroundColor = UIColor.black
        viewDim.alpha = 0
        viewDim.isUserInteractionEnabled = false
        view.addSubview(viewDim)
        viewDim.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gameContainer = UIView()
        gameContainer.stringTag = "game_container"
        gameContainer.isHidden = true
        gameContainer.alpha = 0
        view.addSubview(gameContainer)
        gameContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        if let gameView = gameView {
            gameView.removeFromSuperview()
            gameView.isUserInteractionEnabled = true
            gameContainer.addSubview(gameView)
            gameView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        let viewTopLeftGame = UIView()
        viewTopLeftGame.stringTag = "view_topleft"
        gameContainer.addSubview(viewTopLeftGame)
        viewTopLeftGame.snp.makeConstraints { make in
            make.width.equalTo(viewTopLeftGame.snp.height)
            make.height.equalToSuperview().multipliedBy(0.05)
            make.left.top.equalToSuperview()
        }
        /*
        let btnCloseGame = KUButton()
        btnCloseGame.stringTag = "btn_close_game"
        btnCloseGame.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal)
        btnCloseGame.imageView?.contentMode = .scaleAspectFit
        gameContainer.addSubview(btnCloseGame)
        btnCloseGame.snp.makeConstraints { make in
            make.width.equalTo(btnCloseGame.snp.height)
            make.height.equalToSuperview().multipliedBy(0.1)
            make.left.equalTo(viewTopLeftGame.snp.right).multipliedBy(0.05)
            make.top.equalTo(viewTopLeftGame.snp.bottom).multipliedBy(0.1)
        }
        
        let btnHint = KUButton()
        btnHint.stringTag = "btn_hint"
        btnHint.setImage(Utilities.SVGImage(named: "btn_intro"), for: .normal)
        btnHint.imageView?.contentMode = .scaleAspectFit
        btnHint.alpha = 0
        gameContainer.addSubview(btnHint)
        btnHint.snp.makeConstraints { make in
            make.width.equalTo(btnHint.snp.height)
            make.height.equalToSuperview().multipliedBy(0.1)
            make.left.equalToSuperview().multipliedBy(0.05)
            make.top.equalTo(btnCloseGame.snp.bottom).multipliedBy(0.1)
            make.left.equalToSuperview().offset(0.05 * view.frame.height)
        }
        
        gameName = UILabel()
        gameName.stringTag = "game_name"
        gameName.text = "123"
        gameName.textColor = UIColor.red
        gameName.font = UIFont.systemFont(ofSize: 30)
        view.addSubview(gameName)
        gameName.snp.makeConstraints { make in
            make.left.bottom.equalToSuperview()
        }
        
        btnNext = UIButton(type: .system)
        btnNext.stringTag = "btn_next"
        btnNext.setTitle("next", for: .normal)
        btnNext.addTarget(self, action: #selector(nextTapped), for: .touchUpInside)
        view.addSubview(btnNext)
        btnNext.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview()
        }
        */
        view.bringSubviewToFront(btnClose!)
        view.bringSubviewToFront(btnHint!)
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
    }
    
    private func startUpdateThuThachNgay() {
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            if self.textCountdown.isHidden == false {
                self.textCountdown.text = getTimeRemainingUntilMidnight()
            }
        }
    }
    
    private func stopUpdateThuThachNgay() {
        timer?.invalidate()
        timer = nil
    }
    
    
    @objc private func nextTapped() {
        gameFragment?.finishGame()
    }
    
    @objc private func playTapped() {
        let inTrialTime = inTrialTime()
        runPremiumFeatures ({
            if self.gameIndex >= 0 && self.gameIndex < self.games.count {
                self.playGame(game: self.games[self.gameIndex])
            } else {
                let startTime = Date()
                self.chooseGame()
                let duration = Date().timeIntervalSince(startTime) * 1000
                print("ThuThachNgayViewController: chooseGame: \(duration)ms")
            }
        } ,inTrialTime)
    }
    
    private func getWeekDayName() -> String {
        let calendar = Calendar.current
        let day = calendar.component(.weekday, from: Date())
        switch day {
        case 1: return "Sunday"
        case 2: return "Monday"
        case 3: return "Tuesday"
        case 4: return "Wednesday"
        case 5: return "Thursday"
        case 6: return "Friday"
        case 7: return "Saturday"
        default: return ""
        }
    }
    
    @objc private func bonusTapped() {
        //if isBusy(duration: 5.0) { return }
        playSound("vi/btn_bonus")
    }
    
    private func initViews() {
        configureLayout(view)
        
        pack = FlashcardsManager.shared.getPacks().filter { $0.recognize ?? false && $0.name.en != nil && $0.name.vi != nil }.shuffled().first
        let unlockedStickers = GalleryManager.shared.getStickers().map { "\($0.folder)/\($0.sticker)" }
        for item in pack?.items ?? [] {
            if !unlockedStickers.contains("\(pack?.folder ?? "")/\(item.name.en ?? "")") {
                selectedItems.append(item)
            }
            if selectedItems.count >= 3 { break }
        }
        while selectedItems.count < 3 {
            if let item = pack?.items.randomElement(), !selectedItems.contains(where: { $0.name.en == item.name.en }) {
                selectedItems.append(item)
            }
        }
        
        challengeCompleted = ChallengeManager.shared.isChallengeCompleted()
        btnPlay.isUserInteractionEnabled = !challengeCompleted
        btnPlay.alpha = challengeCompleted ? 0.3 : 1
        if !challengeCompleted {
            let ok = ChallengeManager.shared.loadChallenge()
            if ok {
                gameIndex = ChallengeManager.shared.getGameIndex()
                games = ChallengeManager.shared.getGames()
                scores = ChallengeManager.shared.getScores()
                setPosition(position: gameIndex - 1)
            }
            textCountdown.isHidden = true
        } else {
            playSound("vi/challenge_done")
            setPosition(position: Self.gameCount)
            textCountdown.isHidden = false
        }
        
        if let textBonusLevel = view.viewWithStringTag("text_bonus_level") as? HeightRatioTextView {
            textBonusLevel.text = "x\(min(5, ChallengeManager.shared.countSeriesOfChallenge() + 1))"
        }
         
    }
    
    private func playGame(game: String) {
        
        // Đo thời gian chạy
        let start = Date()
        let abcClasses = findClassesStartingWith(prefix: "toancoban_list_")
        let duration = Date().timeIntervalSince(start) * 1000 // Chuyển sang mili giây

        print("Thời gian chạy: \(duration) ms")
        for cls in abcClasses {
            print(cls)
        }
        let moduleName = Utils.getAppModuleName()
        guard let gameClass = NSClassFromString("\(moduleName!).\(game)") as? NhanBietGameFragment.Type else {
            print("Game class not found: \(game)")
            return
        }
        guard let gameFragment = try? gameClass.init() else {
            print("Failed to initialize game: \(game)")
            return
        }
        /*
        if gameFragment is WritingOlyGameFragment {
            let abc = ["a", "ă", "â", "b", "c", "d", "đ", "e", "ê", "g", "h", "i", "k", "l", "m", "n", "o", "ô", "ơ", "p", "q", "r", "s", "t", "u", "ư", "v", "x", "y"]
            let letter = abc.randomElement()! + "\(Int.random(in: 1...(DataManager.shared.profileAge >= 7 ? 2 : 1)))"
            (gameFragment as? WritingOlyGameFragment)?.setLetter(letter: letter)
            (gameFragment as? WritingOlyGameFragment)?.setQuestionCount(count: 1)
        }
        
        
        stopBackgroundMusic()
         */
        scheduler.clearAll()
        if gameClosed {
            gameContainer.transform = CGAffineTransform(translationX: 0, y: gameContainer.frame.height)
            viewDim.alpha = 0
        }
        gameContainer.isHidden = false
        UIView.animate(withDuration: openDuration) {
            self.viewDim.alpha = self.dimMax
            self.gameContainer.transform = .identity
            self.gameContainer.alpha = 1
        }
        
        self.gameFragment = gameFragment
        
        gameFragment.setLanguage(game.description.contains(".vocab.") || game.description.contains(".phonics.") ? "en" : "vi")
        if gameFragment.getListItems() == nil {
            gameFragment.setListItems(selectedItems)
            gameFragment.setItem(gameItems[game] ?? selectedItems.shuffled().first!)
            gameFragment.setFolder(pack?.folder ?? "")
        }
        gameFragment.onGameFragmentListener = self
        gameView?.removeAllSubviews()
        gameView?.addSubview(gameFragment)
        gameFragment.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        gameClosed = false
    }
    
    private func setPosition(position: Int) {
        guard let progressContainer = view.viewWithStringTag("progress_container") as? UIView,
              let progressBarDone = view.viewWithStringTag("progress_bar_done") as? UIImageView,
              let progressBgOrange = view.viewWithStringTag("progress_bg_orange") as? UIView,
              let progressCoin = view.viewWithStringTag("progress_coin") as? UIImageView,
              let txtProgress = view.viewWithStringTag("txt_progress") as? UILabel else { return }
        
        if position >= Self.gameCount {
            progressContainer.isHidden = true
            progressBarDone.isHidden = false
            return
        }
        if position < 0 { return }
        
        progressBgOrange.isHidden = false
        let multiple = position == 20 ? 1.0 : 0.1 + (0.99 - 0.1) / 19.0 * Double(min(position, 19))
        progressBgOrange.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(multiple)
        }
        progressCoin.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar6")
        txtProgress.text = "\(position + 1)"
    }
    
    private func getGameOneByTopic(pack: Folder?) -> [String] {
        guard let folder = pack?.folder else { return [] }
        if ["Bath Room", "Bed Room", "Living Room"].contains(where: { $0.lowercased() == folder.lowercased() }) {
            return [nhanbiet_list_canhcua.className, vocab_list_canhcua.className]
        }
        if folder.lowercased() == "Clothes".lowercased() {
            return [nhanbiet_list_phoido.className, vocab_list_phoido.className]
        }
        if folder.lowercased() == "Sport Equipment".lowercased() {
            return [nhanbiet_list_tudo.className, vocab_list_tudo.className]
        }
        if folder.lowercased() == "Fruits".lowercased() {
            return [nhanbiet_list_ruottraicay.className, vocab_list_ruottraicay.className]
        }
        return []
    }
    
    
    
    private func getSupportGameOne(pack: Folder?) -> [String] {
        let gameOneByTopic = getGameOneByTopic(pack: pack)
        return allGamesOneItem.filter { game in
            if allGamesByTopic.contains(where: { $0 == game }) {
                return gameOneByTopic.contains(where: { $0 == game })
            }
            return true
        }
    }
    
    private func isSupportGame(game: String, pack: Folder?) -> Bool {
        if allGamesByTopic.contains(where: { $0 == game }) {
            return getGameOneByTopic(pack: pack).contains(where: { $0 == game })
        }
        return true
    }
    
    private func chooseGame() {
        let yearOfBirth = DataManager.shared.currentProfile!.yearOfBirth
        let currentYear = Calendar.current.component(.year, from: Date())
        let age = yearOfBirth == nil ? 0 : currentYear - yearOfBirth
        let level = age <= 3 ? 1 : age <= 5 ? 2 : 3
        
        var subclassesNhanbiet = findClassesStartingWith(prefix: "nhanbiet_list_")
        subclassesNhanbiet = subclassesNhanbiet.filter { !Utils.isAbstract($0) && isSupportGame(game: $0, pack: pack) }.shuffled()
        var subclassesVocab = findClassesStartingWith(prefix: "vocab_list_")
        subclassesVocab = subclassesVocab.filter { !Utils.isAbstract($0) && isSupportGame(game: $0, pack: pack) }.shuffled()
        
        var tries = 0
        while true {
            tries += 1
            print("ThuThachNgayViewController: chooseGame: \(tries)")
            var games: [String] = []
            
            var gamesNhanbiet = Array(subclassesNhanbiet.shuffled().prefix(3))
            items = []
            items.append(contentsOf: selectedItems)
            items.append(contentsOf: selectedItems)
            while items.count < 20 {
                items.append(selectedItems[Int(arc4random_uniform(3))])
            }
            var wrongData = false
            for (i, game) in gamesNhanbiet.enumerated() {
                let item = items[i]
                if game == nhanbiet_list_xepthanhngang.className && item.noHorizontal ?? false {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_xepthanhdoc.className && item.noVertical ?? false {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_veduongvien.className && !(item.tracing ?? false) {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_ruottraicay.className && ["blueberry", "cherry", "grapes", "green grapes"].contains(where: { $0.lowercased() == item.name.en?.lowercased() }) {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_tudo.className && pack?.folder.lowercased() != "Sport Equipment".lowercased() {
                    wrongData = true
                    break
                }
                gameItems[game] = item
            }
            
            if wrongData { continue }
            
            var gamesVocab = Array(subclassesVocab.shuffled().prefix(3))
            items = []
            items.append(contentsOf: selectedItems)
            items.append(contentsOf: selectedItems)
            while items.count < gamesVocab.count {
                items.append(selectedItems[Int(arc4random_uniform(3))])
            }
            for (i, game) in gamesVocab.enumerated() {
                let item = items[i]
                if game == nhanbiet_list_xepthanhngang.className && item.noHorizontal ?? false {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_xepthanhdoc.className && item.noVertical ?? false {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_veduongvien.className && !(item.tracing ?? false) {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_ruottraicay.className && ["blueberry", "cherry", "grapes", "green grapes"].contains(where: { $0.lowercased() == item.name.en?.lowercased() }) {
                    wrongData = true
                    break
                }
                if game == nhanbiet_list_tudo.className && pack?.folder.lowercased() != "Sport Equipment".lowercased() {
                    wrongData = true
                    break
                }
                gameItems[game] = item
            }
            
            if wrongData { continue }
            
            games.append(contentsOf: gamesNhanbiet)
            games.append(contentsOf: gamesVocab)
            //games.append(LanguageFillGameFragment.self)
            //games.append(WritingOlyGameFragment.self)
            games.append(contentsOf: chooseGamesByLevel(packageName: "toancoban", count: 3, level: level))
            games.append(contentsOf: chooseGamesByLevel(packageName: "taptrung", count: 2, level: level))
            games.append(contentsOf: chooseGamesByLevel(packageName: "tuduy", count: 2, level: level))
            games.append(contentsOf: chooseGamesByLevel(packageName: "amnhac", count: 2, level: level))
            games.append(contentsOf: chooseGamesByLevel(packageName: "mythuat", count: 2, level: level))
            games.append(contentsOf: chooseGamesByLevel(packageName: "ngonngu", count: 1, level: level))
            
            if checkIfSameShortNames(games: games) { continue }
            
            games = games.shuffled()
            games.sort { g1, g2 in
                let l1 = GameLevelHelper.getGameLevelNumber(gameName: g1) ?? (g1.description.contains("nhanbiet") ? 1 : g1.description.contains("vocab") ? 2 : 1)
                let l2 = GameLevelHelper.getGameLevelNumber(gameName: g2) ?? (g2.description.contains("nhanbiet") ? 1 : g2.description.contains("vocab") ? 2 : 1)
                return l1 < l2
            }
            
            playGames(games: games)
            return
        }
        
        playGames(games: [toancoban_list_8xuctu.className])
    }
    
    private func checkIfSameShortNames(games: [String]) -> Bool {
        var shortNames: Set<String> = []
        
        for game in games {
            // Tách chuỗi sau "_list_"
            let components = game.components(separatedBy: "_list_")
            // Lấy phần sau "_list_" nếu tồn tại, nếu không thì bỏ qua
            guard components.count > 1, let shortName = components.last else {
                continue
            }
            
            // Kiểm tra trùng lặp trong Set
            if shortNames.contains(shortName) {
                return true
            }
            shortNames.insert(shortName)
        }
        
        return false
    }
    
    private func chooseGamesByLevel(packageName: String, count: Int, level: Int) -> [String] {
        let isLanguage = packageName == "ngonngu"
        let subclasses = findClassesStartingWith(prefix: "\(packageName)_list_")
        let supportGames = subclasses.filter { game in
            if isLanguage {
                return [
                    ngonngu_list_tutrainghia.className,
                    ngonngu_list_daiduong.className,
                    ngonngu_list_rungram.className,
                    ngonngu_list_dovattheocap.className,
                    ngonngu_list_timchu.className,
                    ngonngu_list_timdau.className,
                    ngonngu_list_chonchu.className,
                    ngonngu_list_hinhcungdauthanh.className,
                    ngonngu_list_noitughep.className,
                    ngonngu_list_sapxepcau.className
                ].contains(where: { $0 == game })
            }
            if let gameLevel = GameLevelHelper.getGameLevelNumber(gameName: game) {
                return gameLevel >= level - 1 && gameLevel <= level + 1
            }
            return false
        }
        
        var higherSize = 1
        var tries = 0
        while true {
            tries += 1
            if tries > 1000 {
                higherSize += 1
                if higherSize > 4 { higherSize = 0 }
                tries = 0
            }
            let chooseGames = Array(supportGames.shuffled().prefix(count))
            let level1 = chooseGames.filter { GameLevelHelper.getGameLevelNumber(gameName: $0) == level }.count
            let level2 = chooseGames.filter { GameLevelHelper.getGameLevelNumber(gameName: $0) == level + higherSize }.count
            if higherSize > 1 || (level1 >= 1 && level2 <= 1) {
                return chooseGames
            }
        }
    }
    
    
    private func playGames(games: [String]) {
        guard !games.isEmpty else { return }
        self.games = games
        gameIndex = 0
        playGame(game: games[gameIndex])
    }
    
    override func onBackPressed() {
        if gameClosed {
            dismiss(animated: true, completion: nil)
            navigationController?.popViewController(animated: true)
        } else {
            super.onBackPressed()
        }
    }
    
    override func onConfirmExit() {
        #if DEBUG
        //super.onConfirmExit()
        //return
        #endif
        closeGame()
    }
    
    private func closeGame() {
        gameClosed = true
        UIView.animate(withDuration: openDuration, animations: {
            self.viewDim.alpha = 0
            self.gameContainer.transform = CGAffineTransform(translationX: 0, y: self.gameContainer.bounds.height)
            self.gameContainer.alpha = 0
        }) { _ in
            self.gameContainer.isHidden = true
            self.gameFragment?.removeFromSuperview()
            self.gameFragment = nil
        }
    }
}

extension ThuThachNgayViewController : OnGameFragmentListener {
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        self.scores.append(gameFragment.score)
        if self.gameIndex < self.games.count - 1 {
            self.setPosition(position: self.gameIndex)
            self.gameIndex += 1
            self.playGame(game: self.games[self.gameIndex])
        } else {
            self.gameIndex += 1
            if self.gameIndex > Self.gameCount {
                self.gameIndex = Self.gameCount
            }
            self.gameFinished = true
            let dialog = LessonCompleteDialog()
            dialog.onClosed = {
                ///ReviewHelper.shared.onChallengeCompleted()
                self.dismiss(animated: true, completion: nil)
                self.navigationController?.popViewController(animated: true)
            }
            let aveScore = self.scores.isEmpty ? 0 : self.scores.reduce(0, +) / Float(self.scores.count)
            dialog.setScore(aveScore)
            var coinReward = CoinManager.shared.getCoin() - self.oldCoin
            if coinReward < 0 { coinReward = 0 }
            let oldBonusLevel = min(5, ChallengeManager.shared.countSeriesOfChallenge() + 1)
            let newBonusLevel = min(5, ChallengeManager.shared.countSeriesOfChallenge() + 2)
            dialog.setCoinReward(coinReward)
            dialog.setCoinBonus(10 * oldBonusLevel)
            dialog.setOldBonusLevel(oldBonusLevel)
            dialog.setNewBonusLevel(newBonusLevel)
            dialog.setLanguage("vi")
            dialog.setupPopupPresentation()
            self.present(dialog, animated: false) {
                dialog.presentPopup()
            }
        }
    }
    
    func onUpdateGameProgress(_ progress: Float) {
        
    }
}


import Foundation

func findClassesStartingWith(prefix: String) -> [String] {
    var classes: [String] = []
    let moduleName = Bundle.main.infoDictionary?["CFBundleName"] as? String ?? ""
    let moduleLength = moduleName.count + 1
    // Get the number of registered classes
    var classCount: Int32 = objc_getClassList(nil, 0)
    
    // Allocate buffer for class list
    let classList = UnsafeMutablePointer<AnyClass?>.allocate(capacity: Int(classCount))
    defer { classList.deallocate() }
    
    // Populate the class list
    classCount = objc_getClassList(AutoreleasingUnsafeMutablePointer(classList), classCount)
    
    // Iterate through all classes
    for i in 0..<Int(classCount) {
        if let cls = classList[i] {
            let className = NSStringFromClass(cls)
            // Kiểm tra module và prefix
            if className.hasPrefix("\(moduleName).\(prefix)") && cls.isSubclass(of: NhanBietGameFragment.self) {
                classes.append(className.dropFirst(moduleLength).string)
            }
        }
    }
    
    return classes
}
