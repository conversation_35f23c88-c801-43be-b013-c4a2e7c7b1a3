//
//  taptrung_list_duongchamdiem.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//


import UIKit
import SnapKit
import AVFAudio

class taptrung_list_duongchamdiem: NhanBietGameFragment {
    // MARK: - Properties
    private var values: [Int] = []
    private var gridView: GridView!
    private var itemContainer: UIView!
    private var gridLayout: MyGridView!
    private var topContainer: UIView!
    private var bottomContainer: UIView!
    private var selectedPoints: [(x: Int, y: Int)] = []
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#E9FDFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        let paddingLeft = UIView()
        leftContainer.addSubviewWithPercentInset(subview: paddingLeft, percentInset: 5)
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        //itemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        paddingLeft.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1.3)
        
        topContainer = UIView()
        topContainer.clipsToBounds = false
        topContainer.backgroundColor = UIColor.cyan.withAlphaComponent(0.06) // #20f0
        itemContainer.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.9)
            make.height.equalTo(topContainer.snp.width).dividedBy(3) // Ratio 3
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.topContainer.snapToVerticalBias(verticalBias: 0.06)
        }
        
        bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        bottomContainer.backgroundColor = UIColor.cyan.withAlphaComponent(0.06) // #20f0
        itemContainer.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.9)
            make.height.equalTo(bottomContainer.snp.width).dividedBy(3) // Ratio 3
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.bottomContainer.snapToVerticalBias(verticalBias: 0.85)
        }
        
        gridView = GridView()
        topContainer.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bottomGridView = GridView()
        bottomContainer.addSubview(bottomGridView)
        bottomGridView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.color(hex: "#D6FAFF")
        gridLayout.clipsToBounds = false
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.4)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        values = []
        while values.count < 9 {
            let value = [0, 1, 1, 2, 2, 3].randomElement()!
            if !values.isEmpty && value == values.last! {
                continue
            }
            let length = [1, 1, 2, 2, 3].randomElement()!
            for _ in 0..<length {
                if values.count >= 9 { break }
                values.append(value)
            }
        }
        
        gridView.updateLines(values: values)
        
        var points: [(x: Int, y: Int)] = []
        for i in 0..<values.count {
            let value = values[i]
            let lastValue = i == 0 ? 3 : values[i - 1]
            if value != lastValue {
                points.append(contentsOf: findPointsOnLine(x1: i, y1: lastValue, x2: i, y2: value))
            }
            points.append(contentsOf: findPointsOnLine(x1: i, y1: value, x2: i + 1, y2: value))
        }
        
        let p = points.randomElement()!
        selectedPoints = [p]
        for _ in 0..<3 {
            while true {
                let point = (x: Int.random(in: 0..<9), y: Int.random(in: 0..<3))
                if !selectedPoints.contains(where: { $0.x == point.x && $0.y == point.y }) &&
                   !points.contains(where: { $0.x == point.x && $0.y == point.y }) {
                    selectedPoints.append(point)
                    break
                }
            }
        }
        selectedPoints = selectedPoints.shuffled()
        meIndex = selectedPoints.firstIndex(where: { $0.x == p.x && $0.y == p.y })!
    }
    
    override func createGame() {
        super.createGame()
        let texts = ["A", "B", "C", "D"]
        for i in 0..<selectedPoints.count {
            let point = selectedPoints[i]
            let view = KUButton()
            view.backgroundColor = .clear
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "option_bg_white_shadow"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
                make.width.equalTo(bgContainer.snp.height).multipliedBy(34.0 / 37.0) // Ratio 34:37
            }
            
            let textView = HeightRatioTextView()
            textView.text = texts[i]
            textView.textColor = UIColor.color(hex: "#2DCEC9")
            textView.font = UIFont(name: "SVN-Freude", size: 24)
            textView.textAlignment = .center
            textView.setHeightRatio(0.5)
            bgContainer.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let cellWidth = bottomContainer.frame.width / 9
            let cellHeight = bottomContainer.frame.height / 3
            bottomContainer.addSubview(view)
            view.snp.makeConstraints { make in
                make.width.equalTo(cellWidth)
                make.height.equalTo(cellHeight)
                make.left.equalToSuperview().offset(CGFloat(Double(point.x) - 0.5) * cellWidth)
                make.top.equalToSuperview().offset(CGFloat(Double(point.y) - 0.5) * cellHeight)
            }
        }
        
        var views: [UIView] = []
        for i in 0..<texts.count {
            let view = KUButton()
            view.backgroundColor = .clear
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "option_bg_white_shadow"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
                make.width.equalTo(bgContainer.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
            }
            
            let textView = AutosizeLabel()
            textView.text = texts[i]
            textView.textColor = UIColor.color(hex: "#74B6FF")
            textView.font = .Freude(size: 20)
            textView.textAlignment = .center
            textView.adjustsFontSizeToFitWidth = true
            textView.minimumScaleFactor = 0.1
            bgContainer.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.width.equalTo(bgContainer).multipliedBy(0.8)
                make.height.equalTo(bgContainer).multipliedBy(0.7)
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(0.95) // verticalBias=0.45
            }
            
            view.stringTag = "\(i)"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        itemContainer.transform = .identity
        gridLayout.alpha = 0
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_duong cham diem"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.itemContainer.transform = .identity
            }
            UIView.animate(withDuration: 0.2, delay: 0.5) {
                self.gridLayout.alpha = 1
            }
        }
        
        delay += 0.7
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_duong cham diem")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0"),
              let textView = sender.findSubviews(ofType: UILabel.self).first else { return }
        pauseGame(stopMusic: false)
        if index == meIndex {
            animateCoinIfCorrect(view: textView)
            textView.textColor = UIColor.color(hex: "#87D657")
            let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            textView.textColor = UIColor.color(hex: "#FF7760")
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
                textView.textColor = UIColor.color(hex: "#74B6FF")
            }
        }
    }
    
    // MARK: - Helper Methods
    private func findPointsOnLine(x1: Int, y1: Int, x2: Int, y2: Int) -> [(x: Int, y: Int)] {
        var points: [(x: Int, y: Int)] = []
        
        if x1 == x2 {
            let startY = min(y1, y2)
            let endY = max(y1, y2)
            for y in startY...endY {
                points.append((x: x1, y: y))
            }
        } else if y1 == y2 {
            let startX = min(x1, x2)
            let endX = max(x1, x2)
            for x in startX...endX {
                points.append((x: x, y: y1))
            }
        }
        
        return points
    }
    
    // MARK: - GridView
    class GridView: UIView {
        private var values: [Int] = []
        private let gridPaint = Paint()
        private let strokePaint = Paint()
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            initPaints()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initPaints()
        }
        
        private func initPaints() {
            backgroundColor = UIColor.color(hex: "#D6FAFF")
            gridPaint.color = UIColor.color(hex: "#2DCEC9")
            gridPaint.strokeWidth = 5
            gridPaint.strokeCap = .round
            gridPaint.strokeJoin = .round
            
            strokePaint.color = UIColor.color(hex: "#FF7760")
            //strokePaint.style = .stroke
            strokePaint.strokeWidth = 10
            strokePaint.strokeCap = .round
            strokePaint.strokeJoin = .round
        }
        
        func updateLines(values: [Int]) {
            self.values = values
            setNeedsDisplay()
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            gridPaint.strokeWidth = frame.width / 250
            strokePaint.strokeWidth = frame.width / 125
        }
        
        override func draw(_ rect: CGRect) {
            super.draw(rect)
                
            guard let context = UIGraphicsGetCurrentContext() else { return }
                
            let width = frame.width
            let height = frame.height
            let rows = 3
            let cols = 9
            
            // Thụt vào strokeWidth/2 ở cả 4 phía
            let gridOffset = gridPaint.strokeWidth / 2
            let strokeOffset = strokePaint.strokeWidth / 2
            
            // Điều chỉnh cellWidth và cellHeight để trừ đi phần thụt vào cả 2 phía
            let cellWidth = (width - strokePaint.strokeWidth) / CGFloat(cols)
            let cellHeight = (height - strokePaint.strokeWidth) / CGFloat(rows)
            
            context.setStrokeColor(gridPaint.color.cgColor)
            context.setLineWidth(gridPaint.strokeWidth)
            context.setLineCap(gridPaint.strokeCap)
            context.setLineJoin(gridPaint.strokeJoin)
            
            // Vẽ lưới với offset ở cả 4 phía
            for i in 0...rows {
                context.move(to: CGPoint(x: gridOffset, y: CGFloat(i) * cellHeight + gridOffset))
                context.addLine(to: CGPoint(x: width - gridOffset, y: CGFloat(i) * cellHeight + gridOffset))
                context.strokePath()
            }
            
            for i in 0...cols {
                context.move(to: CGPoint(x: CGFloat(i) * cellWidth + gridOffset, y: gridOffset))
                context.addLine(to: CGPoint(x: CGFloat(i) * cellWidth + gridOffset, y: height - gridOffset))
                context.strokePath()
            }
            
            context.setStrokeColor(strokePaint.color.cgColor)
            context.setLineWidth(strokePaint.strokeWidth)
            context.setLineCap(strokePaint.strokeCap)
            context.setLineJoin(strokePaint.strokeJoin)
            
            // Vẽ đường chấm điểm với offset ở cả 4 phía
            for i in 0..<values.count {
                let value = values[i]
                let lastValue = i == 0 ? 3 : values[i - 1]
                if value != lastValue {
                    context.move(to: CGPoint(x: CGFloat(i) * cellWidth + strokeOffset, y: CGFloat(lastValue) * cellHeight + strokeOffset))
                    context.addLine(to: CGPoint(x: CGFloat(i) * cellWidth + strokeOffset, y: CGFloat(value) * cellHeight + strokeOffset))
                    context.strokePath()
                }
                context.move(to: CGPoint(x: CGFloat(i) * cellWidth + strokeOffset, y: CGFloat(value) * cellHeight + strokeOffset))
                context.addLine(to: CGPoint(x: CGFloat(i + 1) * cellWidth + strokeOffset, y: CGFloat(value) * cellHeight + strokeOffset))
                context.strokePath()
            }
        }
    }
    
    // MARK: - Paint
    class Paint {
        var color: UIColor = .black
        var strokeWidth: CGFloat = 1
        var strokeCap: CGLineCap = .butt
        var strokeJoin: CGLineJoin = .miter
        //var style: CGContext.DrawingStyle = .fill
    }
}
