//
//  toancoban_list_xemgiophut.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_chinhgiotheomau
class toancoban_list_xemgiophut: NhanBietGameFragment {
    // MARK: - Properties
    private var hour = 0
    private var minute = 0
    private var clockDigital: ClockDigital!
    private var textHour: AutosizeLabel!
    private var textMinute: AutosizeLabel!

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")

        let clockContainer = UIView()
        clockContainer.stringTag = "clock_container"
        view.addSubview(clockContainer)
        clockContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(clockContainer.snp.width).dividedBy(2)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }

        clockDigital = ClockDigital()
        clockDigital.stringTag = "clock_digital"
        clockContainer.addSubview(clockDigital)
        clockDigital.makeViewCenterAndKeep(ratio: 1)
        clockDigital.setCanEdit(true)
        clockDigital.setSnapTo5Minutes(true)
        clockDigital.setTimeChangeListener { [weak self] hour, minute in
            guard let self = self else { return }
            var adjustedHour = hour
            var adjustedMinute = minute
            while adjustedMinute < 0 {
                adjustedMinute += 60
                adjustedHour -= 1
            }
            while adjustedMinute >= 60 {
                adjustedMinute -= 60
                adjustedHour += 1
            }
            while adjustedHour < 0 { adjustedHour += 12 }
            while adjustedHour > 12 { adjustedHour -= 12 }
            if adjustedHour == self.hour && adjustedMinute == self.minute {
                self.pauseGame()
                self.animateCoinIfCorrect(view: self.clockDigital)
                let delay = self.playSound(self.finishCorrect1Sounds())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            }
        }

        let textContainer = UIImageView()
        textContainer.stringTag = "text_container"
        textContainer.image = Utilities.SVGImage(named: "math_clock2")
        view.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalTo(textContainer.snp.width).multipliedBy(25.0/40.0)
        }
        textContainer.snpRightTop(ratio: 1)

        textHour = AutosizeLabel()
        textHour.stringTag = "text_hour"
        textHour.text = "23:"
        textHour.textColor = UIColor(hex: "#006EB8")
        textHour.font = .UTMAvo(size: 20)
        textHour.overrideFont = false
        textHour.textAlignment = .right
        textHour.overrideTextAlignment = false
        textContainer.addSubview(textHour)
        textHour.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.53)
            make.left.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
        }

        textMinute = AutosizeLabel()
        textMinute.stringTag = "text_minute"
        textMinute.text = "58"
        textMinute.textColor = UIColor(hex: "#E73C2B")
        textMinute.font = .UTMAvo(size: 20)
        textMinute.overrideFont = false
        textMinute.textAlignment = .left
        textMinute.overrideTextAlignment = false
        textContainer.addSubview(textMinute)
        textMinute.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.47)
            make.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        hour = Int.random(in: 1...12)
        minute = Int.random(in: 0..<12) * 5
        clockDigital.setTime(hour: Int.random(in: 1...12), minute: Int.random(in: 0..<12) * 5)
        textHour.text = "\(hour):"
        textMinute.text = String(format: "%02d", minute)
    }

    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_xem gio1", getNumberSound(hour), "toan/giờ", minute == 0 ? "" : getNumberSound(minute), "toan/toan_xem gio2")
        scheduler.schedule(delay: Double(delay)) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_xem gio1", getNumberSound(hour), "toan/giờ", getNumberSound(minute), "toan/toan_xem gio2")
            scheduler.schedule(delay: Double(delay)) {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
}
