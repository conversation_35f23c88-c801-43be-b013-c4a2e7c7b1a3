//
//  toancoban_list_demsao.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_demsao: NhanBietGameFragment {
    // MARK: - Properties
    private let ids = ["toan_demsao4", "toan_demsao5", "toan_demsao6", "toan_demsao7", "toan_demsao8"]
    private var gridLayout: MyGridView!
    private var itemContainer: UIView!
    private var count: Int = 0
    private var edges: Int = 0
    private let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_space"))
        bgImage.contentMode = .scaleAspectFill
        bgImage.clipsToBounds = true
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 162/255, green: 133/255, blue: 210/255, alpha: 1) // #A285D2
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 162/255, green: 133/255, blue: 210/255, alpha: 1) // #A285D2
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        itemContainer = UIView()
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    override func createGame() {
        super.createGame()
        let star = 10 + Int.random(in: 0..<10) // 10 to 19
        edges = [4, 5, 6, 7, 8].randomElement()! // random(4, 5, 6, 7, 8)
        var starList: [Int] = []
        count = 0
        for _ in 0..<star {
            let newStar = 4 + Int.random(in: 0..<5) // 4 to 8
            starList.append(newStar)
            if newStar == edges { count += 1 }
        }
        
        let points = PointsHelper.getPoints(size: star, container: itemContainer)
        let width = itemContainer.frame.width / 10
        let height = itemContainer.frame.height / 10
        
        for i in 0..<points.count {
            let id = starList[i] - 4
            let point = points[i]
            let view = UIImageView(image: Utilities.SVGImage(named: ids[id]))
            view.contentMode = .scaleAspectFit
            view.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0..<360) * .pi / 180)
            itemContainer.addSubview(view)
            view.snp.makeConstraints { make in
                make.width.equalTo(width)
                make.height.equalTo(height)
                make.center.equalToSuperview()
            }
            view.transform = CGAffineTransformMakeTranslation(
                CGFloat(point.x) - itemContainer.frame.width / 2,
                CGFloat(point.y) - itemContainer.frame.height / 2
            ).rotated(by: CGFloat.random(in: 0..<360) * .pi / 180)
        }
        
        buildGrid(grid: gridLayout, count: count)
        
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        var delay = playSound(openGameSound(), "toan/toan_dem canh sao", "toan/toan_dem canh sao\(edges)")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 1
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem canh sao", "toan/toan_dem canh sao\(edge)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(0, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == count
        var delay: TimeInterval = 0
        
        delay += playSound(correct ? "effect/answer_correct" : "effect/answer_wrong", "topics/Numbers/\(value)")
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
