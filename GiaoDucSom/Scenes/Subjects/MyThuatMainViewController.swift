//
//  MyThuatMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class MyThuatMainViewController: SubjectMainViewController {
    override var backgroundMusicFile: String? {
        "bg_list7"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Mỹ Thuật")
        titleColor = UIColor(hex: "#E43333")
        backgroundColor = UIColor(hex: "#FFE0E0")
        // Reset playedColors (tương tự TronMauGameFragment.playedColors)
        TronMauGameFragment.playedColors = []
    }
    
    override func setupData() {
        // Section 1: Hình dạng
        let hinhDangSection = SectionData(
            header: "Hình dạng",
            items: [
                TroChoiNgonNguItem(data: "mythuat_list_vehinhcoban", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_phanbiethinhcoban", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_phanbiethinhnangcao", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_timcaphinhgiongnhau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_veduongvien", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_latthehinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_hinhbanhquy", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_vedoixung", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_vehaitay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_ghepmatna", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_vetungnet", isTapDocGame: false)
            ]
        )
        
        // Section 2: Màu sắc
        let mauSacSection = SectionData(
            header: "Màu sắc",
            items: [
                TroChoiNgonNguItem(data: "mythuat_list_7saccauvong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_maubongbay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_noihinhvoimau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_phanbietmaucoban", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_phanbietmaunangcao", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_latthemau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_mauoto", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_hoatronmaucoban", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_hoatronmaunangcao", isTapDocGame: false)
            ]
        )
        
        // Section 3: Bộ sưu tập
        let boSuuTapSection = SectionData(
            header: "Bộ sưu tập",
            items: [
                TroChoiNgonNguItem(data: "mythuat_list_tranhtomau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_vetudo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "mythuat_list_hopmau", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            hinhDangSection,
            mauSacSection,
            boSuuTapSection
        ]
        collectionView.reloadData()
    }
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = checkFreeGameByThumbnail(troChoiItem.data)
            let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
            
            if playable {
                switch troChoiItem.data {
                case "mythuat_list_vetungnet":
                    // Mở MyThuatVeTungNetViewController
                    let veTungNetVC = MyThuatVeTungNetViewController()
                    navigationController?.pushViewController(veTungNetVC, animated: true)
                case "mythuat_list_vetudo":
                    // Mở MyThuatVeTuDoViewController
                    let veTuDoVC = MyThuatVeTuDoViewController()
                    navigationController?.pushViewController(veTuDoVC, animated: true)
                    
                case "mythuat_list_ghepmatna":
                    // Mở MyThuatGhepMatNaViewController
                    let ghepMatNaVC = MyThuatGhepMatNaViewController()
                    navigationController?.pushViewController(ghepMatNaVC, animated: true)
                case "mythuat_list_tranhtomau":
                    // Mở MyThuatTranhToMauViewController
                    let tranhToMauVC = MyThuatTranhToMauViewController()
                    navigationController?.pushViewController(tranhToMauVC, animated: true)
                case "mythuat_list_vedoixung":
                    // Mở MyThuatVeDoiXungViewController
                    let veDoiXungVC = MyThuatVeDoiXungViewController()
                    navigationController?.pushViewController(veDoiXungVC, animated: true)
                default:
                    super.collectionView(collectionView, didSelectItemAt: indexPath)
                    /*
                    // Mở SingleGameViewController với game tương ứng
                    let vc = SingleGameActivity()
                    vc.game = troChoiItem.data
                    self.navigationController?.pushViewController(vc, animated: true)
                     */
                }
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this game")
            }
        }
    }    
    
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự MyThuatGameNameHelper.getGameByThumb
        return thumbnail
    }
}

// MARK: - TronMauGameFragment (Giả lập)
struct TronMauGameFragment {
    static var playedColors: [String] = []
}

