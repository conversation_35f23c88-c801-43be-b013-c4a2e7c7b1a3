//
//  nhanbiet_list_upcoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_upcoc: NhanBietGameFragment {
    // MARK: - Properties
    private var ivXos: [UIButton] = []
    private var svgViews: [SVGImageView] = []
    private var viewItems: [UIView] = []
    private var firstIndex: Int = 0
    private var svg: SVGKImage?
    private var orderCount: Int = 3
    private var coinView: UIView!
    private let swapDuration: TimeInterval = 1.3
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.alpha = 0.001
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let bgImage = SVGImageView(frame: .zero)
        bgImage.SVGName = "nhanbiet_bg_shellgame"
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let lineCenter = UIView()
        view.addSubview(lineCenter)
        lineCenter.snp.makeConstraints { make in
            make.height.equalTo(0)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.6)
        }
        
        coinView = UIView()
        coinView.clipsToBounds = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        let image3 = Utilities.SVGImage(named: "nhanbiet_bg_shellgame3")
        let image2 = Utilities.SVGImage(named: "nhanbiet_bg_shellgame2")
        for i in 0..<3 {
            let viewItem = UIImageView()
            viewItem.image = image3
            viewItem.clipsToBounds = false
            viewItem.transform = CGAffineTransformMakeScale(0.7, 0.7)
            //viewItem.isHidden = true
            viewItem.alpha = 0.01
            view.addSubview(viewItem)
            viewItem.snp.makeConstraints { make in
                make.width.equalTo(viewItem.snp.height).multipliedBy(491.0 / 375.0) // Ratio 491:375
            }
            
            let svgView = SVGImageView(frame: .zero)
            svgView.transform = CGAffineTransform(scaleX: 1, y: 0.8)
            viewItem.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let ivXo = UIButton()
            ivXo.setImage(image2, for: .normal)
            ivXo.stringTag = "\(i)"
            ivXo.addTarget(self, action: #selector(xoTapped(_:)), for: .touchUpInside)
            view.addSubview(ivXo)
            ivXo.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.20)
                make.height.equalTo(ivXo.snp.width).multipliedBy(870.0 / 582.0) // Ratio 582:870
                make.bottom.equalTo(lineCenter)
            }
            
            // Căn chỉnh horizontalBias
            let biases: [CGFloat] = [0.1, 0.5, 0.9]
            let I = i
            scheduler.schedule(delay: 0.2) {
                [weak self] in
                guard let self = self else { return }
                ivXo.snapToHorizontalBias(horizontalBias: biases[I])
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    originXs[I] = ivXo.frame.minX
                    originYs[I] = ivXo.frame.minY
                    view.alpha = 1
                }
            }
            
            // Căn chỉnh viewItem theo ivXo
            viewItem.snp.makeConstraints { make in
                make.bottom.equalTo(ivXo)
                make.left.right.equalTo(ivXo)
            }
            
            ivXos.append(ivXo)
            svgViews.append(svgView)
            viewItems.append(viewItem)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()/*
        if let yearOfBirth = DataManager.shared.currentProfile?.yearOfBirth {
            let currentYear = Calendar.current.component(.year, from: Date())
            let age = currentYear - yearOfBirth
            orderCount = age + 1
        }*/
        let age = 4
        orderCount = age + 1
        
        firstIndex = Int.random(in: 0..<3)
        
        if getFolder() == nil || getItem() == nil || getItem()?.path == nil {
            let pack = FlashcardsManager.shared.getPacks().first { $0.recognize ?? false } ?? FlashcardsManager.shared.getPacks().randomElement()!
            setFolder(pack.folder)
            setItem(pack.items.randomElement()!)
        }
        
        if let folder = getFolder(), let itemPath = getItem()?.path {
            svg = Utilities.GetSVGKImage(named: "topics/\(folder)/\(itemPath)")
            for svgView in self.svgViews {
                svgView.image = svg!.uiImage
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        var moves: [Int] = []
        while true {
            moves = []
            for _ in 0..<orderCount {
                let index1 = Int.random(in: 0..<2)
                moves.append(index1)
            }
            if moves.contains(firstIndex) || moves.contains(firstIndex - 1) {
                break
            }
        }
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_shellgame"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            self.open(index: self.firstIndex)
            self.scheduler.schedule(delay: 1.0) { [weak self] in
                guard let self = self else { return }
                self.close(index: self.firstIndex)
                for (i, index1) in moves.enumerated() {
                    self.scheduler.schedule(delay: self.swapDuration * 1.2 * Double(i) + self.swapDuration) { [weak self] in
                        guard let self = self else { return }
                        self.swap(index1: index1, index2: index1 + 1)
                    }
                }
                self.scheduler.schedule(delay: self.swapDuration * 1.2 * Double(self.orderCount) + self.swapDuration) { [weak self] in
                    self?.startGame()
                }
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("\(getLanguage())/nhanbiet/nhanbiet_shellgame")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func xoTapped(_ sender: KUButton) {
        guard gameState == .playing, let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        playSound("effect/slide1")
        open(index: index)
        if index == firstIndex {
            pauseGame(stopMusic: false)
            coinView.moveToCenter(of: svgViews[index], duration: 0)
            scheduler.schedule(delay: 0.1) {
                [weak self] in
                guard let self = self else { return }
                self.animateCoinIfCorrect(view: coinView)
            }
            let delay: TimeInterval = playSound(delay: 0, names: [answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            scheduler.schedule(delay: 1.0) { [weak self] in
                guard let self = self else { return }
                self.close(index: index)
                self.scheduler.schedule(delay: 0.5) { [weak self] in
                    self?.resumeGame(startMusic: false)
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    var originXs : [CGFloat] = [0.0,0.0,0.0]
    var originYs : [CGFloat] = [0.0,0.0,0.0]
    var zPosition = 10.0
    private func open(index: Int) {
        for i in 0..<viewItems.count {
            //viewItems[i].isHidden = i != firstIndex
            viewItems[i].alpha = i != firstIndex ? 0.001 : 1
        }
        let ivXo = ivXos[index]
        originXs[index] = ivXo.frame.minX
        originYs[index] = ivXo.frame.minY
        ivXo.layer.anchorPoint = CGPoint(x: 0.5, y: 1.0)
        ivXo.frame.origin.x = originXs[index]
        ivXo.frame.origin.y = originYs[index]
        ivXo.layer.zPosition = zPosition
        zPosition += 1
        UIView.animate(withDuration: 0.3) {
            ivXo.transform = CGAffineTransform(rotationAngle: 45 * .pi / 180)
                .translatedBy(x: ivXo.frame.width / 3 / 10, y: -ivXo.frame.width * 0.7)
        }
    }
    
    private func close(index: Int) {
        playSound("effect/cup_pull")
        let ivXo = ivXos[index]
        UIView.animate(withDuration: 0.3) {
            [weak self] in
            guard let self = self else { return }
            ivXo.transform = .identity
            ivXo.frame.origin = CGPointMake(self.originXs[index], self.originYs[index])
        } completion: {
            [weak self] _ in
            guard let self = self else { return }
            //self.originXs[index] = ivXo.frame.minX
            //self.originYs[index] = ivXo.frame.minY
            ivXo.layer.anchorPoint = CGPoint(x: 0.5, y: 0.5)
            ivXo.frame.origin.x = self.originXs[index]
            ivXo.frame.origin.y = self.originYs[index]
            for viewItem in self.viewItems {
                //viewItem.isHidden = true
                viewItem.alpha = 0.001
            }
        }
    }
    
    private func swap(index1: Int, index2: Int) {
        // Cập nhật firstIndex
        if firstIndex == index1 {
            firstIndex = index2
        } else if firstIndex == index2 {
            firstIndex = index1
        }
        
        // Lấy hai view cần hoán đổi
        let view1 = ivXos[index1]
        let view2 = ivXos[index2]
        
        // Đặt zPosition để kiểm soát thứ tự xếp chồng
        view1.layer.zPosition = 10
        view2.layer.zPosition = 12
        ivXos[3 - index1 - index2].layer.zPosition = 11
        
        // Reset transform và anchor point
        view1.transform = .identity
        view1.layer.anchorPoint = CGPoint(x: 0.5, y: 0.5)
        view1.frame = CGRect(x: originXs[index1], y: originYs[index1], width: view1.frame.width, height: view1.frame.height)
        view2.transform = .identity
        view2.layer.anchorPoint = CGPoint(x: 0.5, y: 0.5)
        view2.frame = CGRect(x: originXs[index2], y: originYs[index2], width: view2.frame.width, height: view2.frame.height)
        
        // Tính toán vị trí ban đầu và đường path
        let x1 = view1.center.x
        let x2 = view2.center.x
        let y = view1.center.y  // Giả sử cả hai view có cùng y
        
        let x = (x1 + x2) / 2
        let yTop = y - (x2 - x1) / 3
        let yBottom = y + (x2 - x1) / 3
        
        // Tạo path cho view1 (từ x1 đến x2 qua yTop)
        let pathTop = UIBezierPath()
        pathTop.move(to: CGPoint(x: x1, y: y))
        pathTop.addQuadCurve(to: CGPoint(x: x2, y: y), controlPoint: CGPoint(x: x, y: yTop))
        
        // Tạo path cho view2 (từ x2 đến x1 qua yBottom)
        let pathBottom = UIBezierPath()
        pathBottom.move(to: CGPoint(x: x2, y: y))
        pathBottom.addQuadCurve(to: CGPoint(x: x1, y: y), controlPoint: CGPoint(x: x, y: yBottom))
        
        // Bắt đầu giao dịch animation với completion block
        CATransaction.begin()
        CATransaction.setCompletionBlock {
            // Đặt vị trí cuối cùng sau khi animation hoàn tất
            view1.center = CGPoint(x: x1, y: y)
            view2.center = CGPoint(x: x2, y: y)
            // Xóa animation để tránh ảnh hưởng sau này
            view1.layer.removeAnimation(forKey: "swapAnimation")
            view2.layer.removeAnimation(forKey: "swapAnimation")
        }
        
        // Animation cho view1
        let keyframe1 = CAKeyframeAnimation(keyPath: "position")
        keyframe1.path = pathTop.cgPath
        keyframe1.duration = swapDuration
        view1.layer.add(keyframe1, forKey: "swapAnimation")
        
        // Animation cho view2
        let keyframe2 = CAKeyframeAnimation(keyPath: "position")
        keyframe2.path = pathBottom.cgPath
        keyframe2.duration = swapDuration
        view2.layer.add(keyframe2, forKey: "swapAnimation")
        
        CATransaction.commit()
        
        // Phát âm thanh
        playSound("effect/slide1")
    }
}
