//
//  mythuat_list_phanbiethinhnangcao.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/5/25.
//

import Foundation

class mythuat_list_phanbiethinhnangcao : nhanbiet_list_phanbiet {
    private let colors: [String] = ["circle", "heart", "rectangle", "triangle", "square", "star", "hexagon", "pentagon"]
    
    required init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupData()
    }
    
    func setupData() {
        for pack in FlashcardsManager.shared.getPacks() {
            if pack.folder == "2D Shapes" {
                self.setFolder(pack.folder)
                let selectedItems = pack.items
                    .filter { !colors.contains($0.name.en!.lowercased()) }
                    .shuffled()
                    .prefix(3)
                setListItems(Array(selectedItems))
            }
        }
    }
}
