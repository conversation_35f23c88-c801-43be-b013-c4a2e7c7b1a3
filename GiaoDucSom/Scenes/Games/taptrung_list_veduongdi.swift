//
//  taptrung_list_veduongdi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFoundation

class taptrung_list_veduongdi: NhanBietGameFragment {
    // MARK: - Properties
    private var player: AVAudioPlayer?
    private var svgImageView: SVGImageView!
    private var itemContainer: UIView!
    private var indexes: [Int] = []
    private var pathIndex: Int = -1
    private var pack: Folder?
    private var item: Item?
    private var like: String?
    private var unlike: String?
    private var isFood: Bool = false
    private var paths: [[CGPoint]] = []
    private var pathView: PathView!
    private var coinView: UIView!
    private var path = UIBezierPath()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        playWritingSound()
        backgroundColor = UIColor.color(hex: "#58B146")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let bgImageLeft = UIImageView(image: Utilities.SVGImage(named: "taptrung_timduong_bg1"))
        bgImageLeft.contentMode = .scaleAspectFit
        view.addSubview(bgImageLeft)
        bgImageLeft.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(bgImageLeft.snp.height).multipliedBy(bgImageLeft.image!.size.width / bgImageLeft.image!.size.height)
        }
        
        let bgImageRight = UIImageView(image: Utilities.SVGImage(named: "taptrung_timduong_bg2"))
        bgImageRight.contentMode = .scaleAspectFit
        view.addSubview(bgImageRight)
        bgImageRight.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(bgImageRight.snp.height).multipliedBy(bgImageRight.image!.size.width / bgImageRight.image!.size.height)
        }
        
        let mazeContainer = UIView()
        mazeContainer.clipsToBounds = false
        view.addSubview(mazeContainer)
        mazeContainer.makeViewCenterAndKeep(ratio: 510.0/300.0)
                
        
        svgImageView = SVGImageView(frame: .zero)
        svgImageView.tintColor = .white
        mazeContainer.addSubview(svgImageView)
        svgImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        mazeContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.clipsToBounds = false
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        pathView = PathView()
        pathView.Color = UIColor.color(hex: "#FBD200")
        itemContainer.addSubview(pathView)
        pathView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        var tries = 0
        while true {
            let packs = FlashcardsManager.shared.getPacks()
            pack = packs.randomElement()
            if let pack = pack, !pack.items.isEmpty {
                item = pack.items.randomElement()
                if let item = item, (item.foodLike != nil || item.homeLike != nil) {
                    let likes = [item.foodLike, item.homeLike].compactMap { $0 }
                    let unlikes = [item.foodUnlike, item.homeDislike].compactMap { $0 }
                    let indexs = (0..<2).shuffled()
                    for i in indexs {
                        if likes.indices.contains(i) {
                            like = likes[i]
                            unlike = unlikes[i]
                            isFood = i == 0
                            self.item = item
                            break
                        }
                    }
                    break
                }
            }
            tries += 1
            if tries > 100 { break } // Prevent infinite loop
        }
        
        let delay: TimeInterval = playSound(delay: 0, names: [
            openGameSound(),
            "taptrung/tim duong1",
            "topics/\(pack?.folder ?? "")/\(item?.path!.replacingOccurrences(of: ".svg", with: "") ?? "")",
            isFood ? "taptrung/tim duong3" : "taptrung/tim duong4"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    var scale : CGFloat = 1.0
    override func createGame() {
        super.createGame()
        
        var filename = "simple string maze/\(Int.random(in: 1...24)).svg"
        #if DEBUG
        //filename = "simple string maze/1.svg"
        #endif
        let svg = Utilities.GetSVGKImage(named: filename)
        self.svgImageView.image = svg.uiImage.withRenderingMode(.alwaysTemplate)
        
        
        let groups = svg.caLayerTree.sublayers ?? []
        let size = CGSize(width: self.itemContainer.frame.width, height: self.itemContainer.frame.height)
        let bounds = groups.first?.sublayers?[0].shapeContentBounds ?? CGRect.zero
        scale = itemContainer.frame.width / svg.size.width
        let scale2 = itemContainer.frame.height / svg.size.height
        let sourceView = SVGImageView(frame: .zero)
        sourceView.SVGName = "topics/\(self.pack?.folder ?? "")/\(self.item?.path ?? "")"
        self.itemContainer.addSubview(sourceView)
        sourceView.snp.makeConstraints { make in
            make.width.equalTo(bounds.width * scale)
            make.height.equalTo(bounds.height * scale)
            make.left.equalToSuperview().offset(bounds.minX * scale)
            make.top.equalToSuperview().offset(bounds.minY * scale)
        }
        
        let destIndexes = (0..<(groups.count - 1)).shuffled()
        let paths = [self.like, self.unlike].compactMap { $0 }
        for i in 0..<(groups.count - 1) {
            let destView = SVGImageView(frame: .zero)
            destView.SVGName = paths[i]
            self.itemContainer.addSubview(destView)
            let destBounds = groups[destIndexes[i] + 1].sublayers![0].shapeContentBounds ?? CGRect.zero
            destView.snp.makeConstraints { make in
                make.width.equalTo(destBounds.width * scale)
                make.height.equalTo(destBounds.height * scale)
                make.left.equalToSuperview().offset(destBounds.minX * scale)
                make.top.equalToSuperview().offset(destBounds.minY * scale)
            }
            destView.stringTag = "\(i)"
            let path = (groups[destIndexes[i] + 1].sublayers?[1] as! CAShapeLayer as CAShapeLayer).path
            let point = groups[destIndexes[i] + 1].sublayers![1].convert(CGRect.zero, to: svg.caLayerTree)
            let points = self.getPoints(path: UIBezierPath(cgPath: path!))
            self.paths.append(points.map{CGPoint(x: (point.minX+$0.x) * self.scale, y: (point.minY+$0.y) * self.scale)})
            self.indexes.append(0)
        }
               
        for i in 1..<groups[0].sublayers!.count {
            let path = (groups[0].sublayers?[i] as! CAShapeLayer).path
            let point = groups[0].sublayers![i].convert(CGRect.zero, to: svg.caLayerTree)
            let bpath = UIBezierPath(cgPath: path!)
            let points = self.getPoints(path: bpath)
            self.paths.append(points.map{CGPoint(x:  (point.minX + $0.x) * self.scale, y: (point.minY + $0.y) * self.scale)})
            let reversedPoints = points.reversed()
            self.paths.append(reversedPoints.map{CGPoint(x:  (point.minX + $0.x) * self.scale, y: (point.minY + $0.y) * self.scale)})
            self.indexes.append(0)
            self.indexes.append(0)
        }
        
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound(delay: 0, names: [
                "taptrung/tim duong1",
                "topics/\(pack?.folder ?? "")/\(item?.path!.replacingOccurrences(of: ".svg", with: "") ?? "")",
                isFood ? "taptrung/tim duong3" : "taptrung/tim duong4"
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    override func removeFromSuperview() {
        super.removeFromSuperview()
        player?.stop()
        player = nil
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        switch gesture.state {
        case .began:
            // TODO: Implement vibrate if needed
            Utils.vibrate()
            pathView.Color = UIColor.color(hex: "#FBD200")
            var index = -1
            var minDistance = CGFloat.greatestFiniteMagnitude
            for i in 0..<paths.count {
                let points = paths[i]
                let pointIndex = indexes[i]
                let point = points[pointIndex]
                let distance = sqrt(pow(point.x - location.x, 2) + pow(point.y - location.y , 2))
                if distance < minDistance {
                    minDistance = distance
                    index = i
                }
            }
            if minDistance < itemContainer.frame.height / 10 {
                if pathIndex == -1 || pathIndex != index {
                    path = UIBezierPath()
                    path.move(to: location)
                    pathView.Path = path
                }
                pathIndex = index
                for i in 0..<indexes.count {
                    if i != pathIndex {
                        indexes[i] = 0
                    }
                }
            }
            // TODO: Implement vibrate if needed
            Utils.vibrate()
            player?.currentTime = 0
            player?.play()
            if pathIndex != -1 {
                return
            }
            
        case .changed:
            if pathIndex != -1 {
                if !(player?.isPlaying ?? false) {
                    player?.play()
                }
                var index = indexes[pathIndex]
                let points = paths[pathIndex]
                var point = points[index]
                var distance = sqrt(pow(point.x - location.x, 2) + pow(point.y - location.y, 2))
                while distance < itemContainer.frame.height / 10 && index < points.count - 1 {
                    index += 1
                    let nextPoint = points[index]
                    let nextDistance = sqrt(pow(nextPoint.x - location.x, 2) + pow(nextPoint.y - location.y , 2))
                    if nextDistance > distance {
                        break
                    }
                    distance = nextDistance
                    indexes[pathIndex] = index
                    path.addLine(to: nextPoint)
                    pathView.setNeedsDisplay()
                    point = nextPoint
                }
            }
            
        case .ended, .cancelled:
            // TODO: Implement vibrate if needed
            player?.pause()
            Utils.vibrate()
            if pathIndex != -1 {
                let index = indexes[pathIndex]
                let points = paths[pathIndex]
                if index > points.count * 95 / 100 {
                    pathView.Color = pathIndex == 0 ? UIColor.color(hex: "#07FF7C") : UIColor.color(hex: "#A46E34")
                    if pathIndex == 0 {
                        pauseGame(stopMusic: false)
                        animateCoinIfCorrect(view: coinView)
                        let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
                        scheduler.schedule(after: delay) { [weak self] in
                            self?.finishGame()
                        }
                    } else {
                        pauseGame(stopMusic: false)
                        setGameWrong()
                        let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
                        scheduler.schedule(after: delay) { [weak self] in
                            self?.resumeGame(startMusic: false)
                        }
                    }
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func playWritingSound() {
        if let url = Bundle.main.url(forResource: "writing", withExtension: "mp3", subdirectory: "Sounds/effect") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.numberOfLoops = -1
                player?.prepareToPlay()
            } catch {
                
            }
        }
    }
    
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        var points: [CGPoint] = []
        let count = 500
        return path.evenlySpacedPointsUsingDash(count: count)
    }
    
    // MARK: - PathView
    class PathView: UIView {
        private var path: UIBezierPath?
        private var color: UIColor = .black
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            initPaints()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initPaints()
        }
        
        private func initPaints() {
            backgroundColor = .clear
        }
        
        var Path: UIBezierPath? {
            get { self.path }
            set {
                path = newValue
                setNeedsDisplay()
            }
        }
        
        var Color: UIColor {
            get { self.color }
            set {
                color = newValue
                setNeedsDisplay()
            }
        }
        
        override func draw(_ rect: CGRect) {
            super.draw(rect)
            
            guard let context = UIGraphicsGetCurrentContext(), let path = path else { return }            
            context.setStrokeColor(color.cgColor)
            path.lineWidth = self.frame.height * 0.025
            path.lineCapStyle = .round
            path.lineJoinStyle = .round
            path.stroke()
        }
    }
}

extension UIBezierPath {
    
    // Lấy 500 điểm cách đều nhau bằng cách dùng nét đứt (đơn giản hóa cho single path)
    func evenlySpacedPointsUsingDash(count: Int) -> [CGPoint] {
        guard count > 0 else { return [] }
        // Bước 1: Ước lượng độ dài path để chia đều
        var approximateLength: CGFloat = self.length
        //var lastPoint: CGPoint = .zero
        guard approximateLength > 0 else { return [] }

        // Bước 2: Tạo nét đứt với 500 đoạn
        let dashLength = approximateLength / CGFloat(count)
        let points = cgPath.copy(dashingWithPhase: 0, lengths: [dashLength, dashLength]).points
        return points
    }
    func evenlySpacedPointsUsingDash(dashLength: CGFloat) -> [CGPoint] {
        guard dashLength > 0 else { return [] }
        let points = cgPath.copy(dashingWithPhase: 0, lengths: [dashLength, dashLength]).points
        return points
    }
}
