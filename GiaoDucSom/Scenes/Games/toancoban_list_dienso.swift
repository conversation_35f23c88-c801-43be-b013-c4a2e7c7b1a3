//
//  toancoban_list_dienso.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_dienso: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var numbers: [Int] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var dragView: UIView!
    private var textA: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textC: AutosizeLabel!
    private var textD: AutosizeLabel!
    private var numberA: UIImageView!
    private var numberB: UIImageView!
    private var numberC: UIImageView!
    private var numberD: UIImageView!
    private var fillValues: [Int] = [0, 0, 0, 0]
    private var viewSnapIndexMap: [UIView: Int] = [:]
    private var textCSign: AutosizeLabel!
    private var coinView: UIView!
    private var zPos: CGFloat = 100.0
    private var origins: [UIView: CGPoint] = [:]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        let bgTop = UIView()
        bgTop.backgroundColor = UIColor(hex: "#C5F7FF")
        view.addSubview(bgTop)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(hex: "#C5F7FF")
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        bgTop.snp.makeConstraints { make in
            make.bottom.equalTo(gridLayout)
            make.left.right.top.equalTo(self)
        }
        let mainContainer = UIView()
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let innerMainContainer = UIView()
        mainContainer.addSubview(innerMainContainer)
        innerMainContainer.makeViewCenterAndKeep(ratio: 8.0)
        
        numberA = UIImageView()
        numberA.image = Utilities.SVGImage(named: "math_diendau_selected")
        innerMainContainer.addSubview(numberA)
        numberA.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(numberA.snp.width)
            make.top.bottom.equalToSuperview()
        }
        
        textA = AutosizeLabel()
        textA.text = "2"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textA.alpha = 0.01
        numberA.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bSignContainer = UIView()
        innerMainContainer.addSubview(bSignContainer)
        bSignContainer.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(bSignContainer.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(numberA.snp.right)
        }
        
        let textBSign = AutosizeLabel()
        textBSign.text = "+"
        textBSign.textColor = UIColor(hex: "#87D657")
        textBSign.font = .Freude(size: 20)
        textBSign.textAlignment = .center
        textBSign.adjustsFontSizeToFitWidth = true
        textBSign.minimumScaleFactor = 0.1
        bSignContainer.addSubview(textBSign)
        textBSign.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        numberB = UIImageView()
        numberB.image = Utilities.SVGImage(named: "math_diendau_selected")
        innerMainContainer.addSubview(numberB)
        numberB.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(numberB.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(bSignContainer.snp.right)
        }
        
        textB = AutosizeLabel()
        textB.text = "4"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textB.alpha = 0.01
        numberB.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let cSignContainer = UIView()
        innerMainContainer.addSubview(cSignContainer)
        cSignContainer.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(cSignContainer.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(numberB.snp.right)
            make.centerX.equalToSuperview()
        }
        
        textCSign = AutosizeLabel()
        textCSign.text = "="
        textCSign.textColor = UIColor(hex: "#74B6FF")
        textCSign.font = .Freude(size: 20)
        textCSign.textAlignment = .center
        textCSign.adjustsFontSizeToFitWidth = true
        textCSign.minimumScaleFactor = 0.1
        cSignContainer.addSubview(textCSign)
        textCSign.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        numberC = UIImageView()
        numberC.image = Utilities.SVGImage(named: "math_diendau_selected")
        innerMainContainer.addSubview(numberC)
        numberC.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(numberC.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(cSignContainer.snp.right)
        }
        
        textC = AutosizeLabel()
        textC.text = "3"
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 20)
        textC.textAlignment = .center
        textC.adjustsFontSizeToFitWidth = true
        textC.minimumScaleFactor = 0.1
        textC.alpha = 0.01
        numberC.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let plusContainer = UIView()
        innerMainContainer.addSubview(plusContainer)
        plusContainer.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(plusContainer.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(numberC.snp.right)
        }
        
        let plusText = AutosizeLabel()
        plusText.text = "+"
        plusText.textColor = UIColor(hex: "#87D657")
        plusText.font = .Freude(size: 20)
        plusText.textAlignment = .center
        plusText.adjustsFontSizeToFitWidth = true
        plusText.minimumScaleFactor = 0.1
        plusContainer.addSubview(plusText)
        plusText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        numberD = UIImageView()
        numberD.image = Utilities.SVGImage(named: "math_diendau_selected")
        innerMainContainer.addSubview(numberD)
        numberD.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(numberD.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(plusContainer.snp.right)
        }
        
        textD = AutosizeLabel()
        textD.text = "3"
        textD.textColor = UIColor(hex: "#74B6FF")
        textD.font = .Freude(size: 20)
        textD.textAlignment = .center
        textD.adjustsFontSizeToFitWidth = true
        textD.minimumScaleFactor = 0.1
        textD.alpha = 0.01
        numberD.addSubview(textD)
        textD.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        self.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        
        dragView = UIView()
        view.addSubview(dragView)
        dragView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        dragView.addGestureRecognizer(UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:))))
        
        gridLayout.layer.zPosition = 1000
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: view)
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                let point = gesture.location(in: view.superview)
                dX = currentView.frame.origin.x - point.x
                dY = currentView.frame.origin.y - point.y
                if origins[currentView] == nil {
                    origins[currentView] = currentView.frame.origin
                }
                zPos += 1
                currentView.layer.zPosition = CGFloat(zPos)
                currentView.transform = .identity
                if let index = viewSnapIndexMap[currentView] {
                    let numbers = [numberA, numberB, numberC, numberD]
                    numbers[index]?.image = Utilities.SVGImage(named: "math_diendau_selected")
                    viewSnapIndexMap.removeValue(forKey: currentView)
                    fillValues[index] = 0
                }
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            }
        case .changed:
            if let currentView = currentView {
                let point = gesture.location(in: view.superview)
                currentView.frame.origin = CGPoint(x: point.x + dX, y: point.y + dY)
            }
        case .ended:
            if let currentView = currentView {
                var minDistance = CGFloat.greatestFiniteMagnitude
                var minView: UIView?
                let numbers = [numberA, numberB, numberC, numberD]
                for number in numbers {
                    if let number = number {
                        let point = currentView.distanceFromCenterToCenter(to: number)
                        let distance = hypot(point.x, point.y)
                        if distance < minDistance {
                            minDistance = distance
                            minView = number
                        }
                    }
                }
                if let minView = minView, minDistance < minView.frame.height / 2 {
                    playSound("effect/word puzzle drop")
                    guard let textNumber = currentView.viewWithTag(R6.id.text_number) as? AutosizeLabel,
                          let value = Int(textNumber.text ?? "0"),
                          let index = numbers.firstIndex(of: minView as? UIImageView) else {
                        self.currentView = nil
                        return
                    }
                    
                    if let existingView = viewSnapIndexMap.first(where: { $0.value == index })?.key {
                        viewSnapIndexMap.removeValue(forKey: existingView)
                        UIView.animate(withDuration: 0.5) {
                            existingView.transform = .identity
                            existingView.frame.origin = self.origins[existingView] ?? .zero
                        }
                    }
                    
                    currentView.moveCenter(to: minView, fitType: .inside, duration: 0.2) { _ in
                        self.viewSnapIndexMap[currentView] = index
                        minView.backgroundColor = nil
                        self.fillValues[index] = value
                        if self.fillValues.allSatisfy({ $0 != 0 }) {
                            if self.fillValues[0] + self.fillValues[1] == self.fillValues[2] + self.fillValues[3] {
                                self.pauseGame()
                                self.coinView.moveCenter(to: self.textCSign, fitType: .inside, duration: 0)
                                self.animateCoinIfCorrect(view: self.coinView)
                                let delay = self.playSound(
                                    self.answerCorrect1EffectSound(),
                                    self.getCorrectHumanSound(),
                                    "topics/Numbers/\(self.fillValues[0])",
                                    "toan/cộng",
                                    "topics/Numbers/\(self.fillValues[1])",
                                    "toan/bằng",
                                    "topics/Numbers/\(self.fillValues[2])",
                                    "toan/cộng",
                                    "topics/Numbers/\(self.fillValues[3])",
                                    self.endGameSound()
                                )
                                self.scheduler.schedule(after: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            } else {
                                self.setGameWrong()
                                self.pauseGame()
                                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                                self.scheduler.schedule(after: delay) { [weak self] in
                                    self?.resumeGame()
                                }
                            }
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.5) {
                        currentView.transform = .identity
                        currentView.frame.origin = self.origins[currentView]!
                    }
                    playSound("effect/slide2")
                }
                self.currentView = nil
            }
        default:
            break
        }
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for child in gridLayout.subviews.reversed() {
            if x >= child.frame.origin.x && x <= child.frame.origin.x + child.frame.width &&
               y >= child.frame.origin.y && y <= child.frame.origin.y + child.frame.height {
                return child
            }
        }
        return nil
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        var tenNumbers = (1...9).shuffled()
        while true {
            numbers = Array(tenNumbers.shuffled().prefix(3))
            let next = numbers[0] + numbers[1] - numbers[2]
            if next > 0 && next < 10 && !numbers.contains(next) {
                numbers.append(next)
                break
            }
        }
        while true {
            let next = tenNumbers.randomElement()!
            if !numbers.contains(next) {
                numbers.append(next)
                break
            }
        }
        numbers.shuffle()
        
        var views: [UIView] = []
        for value in numbers {
            let view = createItemNumber(value: value)
            views.append(view)
        }
        
        gridLayout.columns = 5
        gridLayout.itemRatio = 0.96
        gridLayout.itemSpacingRatio = 0.06
        gridLayout.insetRatio = 0.06
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "toan/toan_dien so")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    private func createItemNumber(value: Int) -> UIView {
        let view = UIView()
        
        let viewBackground = UIImageView()
        viewBackground.tag = R6.id.view_background
        viewBackground.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            //make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.tag = R6.id.text_number
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 24)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            textNumber.snapToVerticalBias(verticalBias: 0.45)
        }
        
        //view.transform = CGAffineTransform(scaleX: 0, y: 0)
        //view.alpha = 0
        return view
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dien so")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
}

