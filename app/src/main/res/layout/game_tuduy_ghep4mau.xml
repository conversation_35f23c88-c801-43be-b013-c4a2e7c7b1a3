<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#CEA89F"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:scaleX="1.6"
        android:scaleY="1.6"
        android:src="@drawable/tuduy_ghep4mau_bg2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="8.5:4.5"
        app:layout_constraintHorizontal_bias="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6" />

    <ImageView
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:src="@drawable/tuduy_ghep4mau_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="8.5:4.5"
        app:layout_constraintHorizontal_bias="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="8.5:4.5"
        app:layout_constraintHorizontal_bias="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.6" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"

        app:layout_constraintWidth_percent="0.4">

        <com.kidsup.giaoducsom.view.MyGridLayout
            app:layout_constraintHorizontal_bias="0.25"
            android:id="@+id/answer_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="7:5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.76470588235" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>