//
//  IdentifyViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 10/5/25.
//


import UIKit
import SnapKit
import SVGKit

class EnglishVocabViewController: TwoClickExitActivity, OnGameFragmentListener {
    // MARK: - Constants
    private let dimMax: CGFloat = 0.8
    private let openDuration: TimeInterval = 0.5
    
    // MARK: - Properties
    private var gameIndex: Int = -1
    private var games: [String] = []
    private var items: [Item] = []
    private var scores: [Float] = []
    private var gameClosed: Bool = true
    var pack: Folder?
    private var selectedItems: [Item] = []
    private var oldCoin: Int = 0
    
    private let allGamesOneItem = [
        "nhanbiet_list_kinhlup",
        "BarVerticalGameFragment",
        "BarHorizontalGameFragment",
        "nhanbiet_list_xephinh",
        "Curtain1GameFragment",
        "Curtain2GameFragment",
        "nhanbiet_list_ongnhom",
        "nhanbiet_list_xoayhinh",
        "TracingGameFragment",
        "nhanbiet_list_upcoc",
        "Scroll3GameFragment",
        "DoorGameFragment",
        "DryGameFragment",
        "StorageGameFragment",
        "InsideGameFragment"
    ]
    
    private let allGamesThreeItems = [
        "BalloonGameFragment",
        "BubbleGameFragment",
        "ShadowGameFragment",
        "CardGameFragment",
        "Puzzle2GameFragment",
        "PhanBietGameFragment"
    ]
    
    private let allGamesByTopic = [
        "DoorGameFragment",
        "DryGameFragment",
        "StorageGameFragment",
        "InsideGameFragment",
        "BubbleGameFragment"
    ]
    
    // MARK: - UI Elements
    private let viewDim = UIView()
    private let gameContainer = UIView()
    private let txtTitle = UILabel()
    private let btnGallery = KUButton()
    private let option1 = KUButton()
    private let option2 = KUButton()
    private let option3 = KUButton()
    private let svgImage1 = SVGImageView(frame: .zero)
    private let svgImage2 = SVGImageView(frame: .zero)
    private let svgImage3 = SVGImageView(frame: .zero)
    private let btnPlay = KUButton()
    private let progressBg = UIImageView()
    private let progressBgOrange = UIView()
    private let progressCoin = UIImageView()
    private let progressViews: [UIImageView] = (1...10).map { _ in UIImageView() }
    private let btnClosePage = KUButton()
    
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        setupUI()
        super.viewDidLoad()
        view.backgroundColor = .color(hex: "#D7FBFF")
        navigationController?.setNavigationBarHidden(true, animated: false)
        initActivity()
        oldCoin = CoinManager.shared.getCoin()
    }
    
    // MARK: - Setup UI
    private func setupUI() {
        // Background
        let background = SVGImageView(frame: .zero)
        background.SVGName = "nhanbiet_bg_cloud"
        background.contentMode = .scaleAspectFill
        view.addSubview(background)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // btnClose
        btnClosePage.setImage(Utilities.SVGImage(named: "btn_back"), for: .normal)
        btnClosePage.contentMode = .scaleAspectFit
        btnClosePage.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClosePage)
        
        btnClosePage.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.12)
            make.width.equalTo(btnClosePage.snp.height)
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
        }
        btnClosePage.snpLeftTop(ratio: 1)
        
        // txtTitle
        txtTitle.font = .Freude(size: 20)
        txtTitle.textColor = UIColor(named: "blue")
        txtTitle.textAlignment = .left
        txtTitle.adjustsFontSizeToFitWidth = true
        view.addSubview(txtTitle)
        /*
        txtTitle.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.06)
            make.left.equalTo(btnClose.snp.right).offset(10)
            make.centerY.equalTo(btnClose)
        }
        */
        // btnGallery
        btnGallery.setImage(Utilities.SVGImage(named: "home_btn_sticker_book"), for: .normal)
        btnGallery.contentMode = .scaleAspectFit
        //btnGallery.addTouchEffect()
        btnGallery.addTarget(self, action: #selector(galleryTapped), for: .touchUpInside)
        view.addSubview(btnGallery)
        btnGallery.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.12)
            make.width.equalTo(btnGallery.snp.height)
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
        }
        btnGallery.snpRightTop(ratio: 1)
        
        // Options container
        let optionsContainer = UIView()
        view.addSubview(optionsContainer)
        optionsContainer.snp.makeConstraints { make in
            make.width.equalTo(view.snp.width).multipliedBy(0.9)
            make.height.equalTo(view.snp.height).multipliedBy(0.3)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.6)
        }
        
        
        // Options inner container
        let optionsInner = UIView()
        optionsContainer.addSubview(optionsInner)
        optionsInner.makeViewCenterAndKeep(ratio: 4.7)
        
        let whiteBg = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        // Option 1
        option1.setImage(whiteBg, for: .normal)
        //option1.addTouchEffect()
        optionsInner.addSubview(option1)
        option1.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(option1.snp.height)
        }
        option1.addSubview(svgImage1)
        svgImage1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Option 2
        option2.setImage(whiteBg, for: .normal)
        //option2.addTouchEffect()
        optionsInner.addSubview(option2)
        option2.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(option1.snp.right).offset(10)
            make.width.equalTo(option2.snp.height)
        }
        option2.addSubview(svgImage2)
        svgImage2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Option 3
        option3.setImage(whiteBg, for: .normal)
        //option3.addTouchEffect()
        optionsInner.addSubview(option3)
        option3.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(option2.snp.right).offset(10)
            make.width.equalTo(option3.snp.height)
        }
        option3.addSubview(svgImage3)
        svgImage3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // btnPlay
        btnPlay.setImage(Utilities.SVGImage(named: "nhanbiet_btn_play_blue"), for: .normal)
        //btnPlay.addTouchEffect()
        btnPlay.addTarget(self, action: #selector(playTapped), for: .touchUpInside)
        optionsInner.addSubview(btnPlay)
        btnPlay.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(btnPlay.snp.height)
        }
        
        // Progress container
        let progressContainer = UIView()
        view.addSubview(progressContainer)
        progressContainer.snp.makeConstraints { make in
            make.width.equalTo(view.snp.width).multipliedBy(0.9)
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.8)
        }
        
        // Progress inner
        let progressInner = UIView()
        progressContainer.addSubview(progressInner)
        progressInner.makeViewCenterAndKeep(ratio: 1979.7 / 142.3)
        
        // progressBg
        progressBg.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar1")
        progressInner.addSubview(progressBg)
        progressBg.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // progressBgOrange
        progressInner.addSubview(progressBgOrange)
        progressBgOrange.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.0)
        }
        let orangeImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_progressbar4"))
        progressBgOrange.addSubview(orangeImage)
        orangeImage.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(orangeImage.snp.height).multipliedBy(1979.7 / 142.3)
        }
        
        // progressCoin
        progressCoin.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar6")
        progressInner.addSubview(progressCoin)
        progressCoin.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(progressCoin.snp.height).multipliedBy(110.0 / 142.3)
        }
        
        // Progress items
        let progressItemsContainer = UIView()
        progressInner.addSubview(progressItemsContainer)
        progressItemsContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.845)
            make.centerX.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        let nhanbiet_bg_progressbar2 = Utilities.SVGImage(named: "nhanbiet_bg_progressbar2")
        for (index, progressView) in progressViews.enumerated() {
            progressView.image = nhanbiet_bg_progressbar2
            progressItemsContainer.addSubview(progressView)
            progressView.snp.makeConstraints { make in
                make.width.equalTo(progressView.snp.height).multipliedBy(98.9 / 142.3)
                make.top.bottom.equalToSuperview()
                make.right.equalToSuperview().multipliedBy(0.1 * Double(index) + 0.1)
            }
            
            let numberLabel = UILabel()
            numberLabel.text = "\(index + 1)"
            numberLabel.font = .Freude(size: 12)
            numberLabel.textColor = .white
            numberLabel.textAlignment = .center
            progressItemsContainer.addSubview(numberLabel)
            numberLabel.snp.makeConstraints { make in
                make.width.equalTo(progressView).multipliedBy(0.9)
                make.height.equalTo(progressView).multipliedBy(index == 9 ? 0.35 : 0.4)
                make.center.equalTo(progressView)
            }
        }
        
        // viewDim
        viewDim.backgroundColor = .black
        viewDim.alpha = 0
        viewDim.isUserInteractionEnabled = false
        view.addSubview(viewDim)
        viewDim.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // gameContainer
        view.addSubview(gameContainer)
        gameContainer.isHidden = true
        gameContainer.alpha = 0
        gameContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // gameView
        
        // Game view (ConstraintLayout)
        gameView = UIControl()
        gameView?.isUserInteractionEnabled = true // clickable="true"
        gameContainer.addSubview(gameView!)
        gameView?.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // btn_close_game (AnimatedImageButton)
        btnClose = KUButton(type: .custom)
        btnClose?.alpha = 0.6
        btnClose?.backgroundColor = .clear // #0000
        btnClose?.accessibilityLabel = "button close"
        btnClose?.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal) // Giả sử btn_close là tên asset
        btnClose?.contentMode = .scaleAspectFit // fitXY
        gameContainer.addSubview(btnClose!)
        btnClose!.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.1)
            make.width.equalTo(btnClose!.snp.height)
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
        }
        btnClose!.snpLeftTop(ratio: 1)        

        // btn_hint (AnimatedImageButton)
        btnHint = KUButton(type: .custom)
        btnHint?.alpha = 0
        btnHint?.backgroundColor = .clear // #0000
        btnHint?.accessibilityLabel = "button replay intro"
        btnHint?.setImage(Utilities.SVGImage(named: "btn_intro"), for: .normal) // Giả sử btn_intro là tên asset
        btnHint?.contentMode = .scaleAspectFit // fitXY
        btnHint?.isEnabled = false
        gameContainer.addSubview(btnHint!)
        btnHint!.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.12)
            make.width.equalTo(btnHint!.snp.height)
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.22)
            make.left.equalTo(btnClose!)
        }
    }
    
    // MARK: - Initialization
    private func initActivity() {
        /*
        guard let packJson = navigationController?.viewControllers.last?.restorationIdentifier,
              let packData = packJson.data(using: .utf8),
              let pack = try? JSONDecoder().decode(Folder.self, from: packData) else {
            print("Failed to decode pack")
            return
        }
         
        self.pack = pack
        */
        guard let pack = pack else { return }
        txtTitle.text = pack.name.en
        
        let unlockedStickers = GalleryManager.shared.getStickers()
            //.filter { $0.vocab == nil || !$0.vocab! }
            .map { "\($0.folder)/\($0.sticker)" }
        
        for item in pack.items {
            if !unlockedStickers.contains("\(pack.folder)/\(item.name.en!)") {
                selectedItems.append(item)
            }
            if selectedItems.count >= 3 { break }
        }
        while selectedItems.count < 3 {
            let randomItem = pack.items.randomElement()!
            if !selectedItems.contains(where: { $0.name.en == randomItem.name.en }) {
                selectedItems.append(randomItem)
            }
        }
        
        let options = [option1, option2, option3]
        let svgImages = [svgImage1, svgImage2, svgImage3]
        for (index, item) in selectedItems.enumerated() {
            svgImages[index].SVGName = "topics/\(pack.folder)/\(item.path!)"
            options[index].addTarget(self, action: #selector(optionTapped(_:)), for: .touchUpInside)
        }
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func galleryTapped() {
        guard let pack = pack else { return }
        for (index, item) in pack.items.enumerated() {
            if item.name.en == selectedItems.first?.name.en {
                let mapIndex = convertItemIndexToMapIndex(index)
                if mapIndex >= 0 && mapIndex < pack.maps!.count {
                    /*
                    let intent = Intent(to: GalleryViewController.self)
                    intent.putExtra("map", pack.maps![mapIndex])
                    intent.putExtra("mapColor", pack.map_color)
                    startActivity(intent)
                     */
                }
                return
            }
        }
    }
    
    @objc private func playTapped() {
        checkTimeoutAndRun { [weak self] in
            self?.doPlayAutomatic()
        }
    }
    
    @objc private func optionTapped(_ sender: UIView) {
        guard let pack = pack else { return }
        let index = [option1, option2, option3].firstIndex(of: sender) ?? 0
        let item = selectedItems[index]
        playSound("en/topics/\(pack.folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))")
    }
    
    // MARK: - Game Logic
    private func getGameOneByTopic(_ pack: Folder) -> [String] {
        switch pack.folder.lowercased() {
        case "bath room", "bed room", "living room":
            return ["DoorGameFragment"]
        case "clothes":
            return ["DryGameFragment"]
        case "sport equipment":
            return ["StorageGameFragment"]
        case "fruits":
            return ["InsideGameFragment"]
        case "aquatic animals":
            return ["BubbleGameFragment"]
        default:
            return []
        }
    }
    
    private func getSupportGameOne(_ pack: Folder) -> [String] {
        let gameOneByTopic = getGameOneByTopic(pack)
        var games: [String] = []
        for game in allGamesOneItem {
            if allGamesByTopic.contains(game) {
                if gameOneByTopic.contains(game) {
                    games.append(game)
                }
            } else {
                games.append(game)
            }
        }
        return games
    }
    
    private func doPlayAutomatic() {
        if gameIndex >= 0 && gameIndex < games.count {
            playGame(games[gameIndex])
            return
        }
        chooseGame()
    }
    
    private func convertItemIndexToMapIndex(_ itemIndex: Int) -> Int {
        guard let pack = pack else { return 0 }
        var mapIndex = itemIndex / (pack.items.count / pack.maps!.count + 1)
        if mapIndex >= pack.maps!.count { mapIndex = pack.maps!.count - 1 }
        return mapIndex
    }
    
    private func chooseGame() {
        var supportGameOne = getSupportGameOne(pack!)
        var games = supportGameOne.shuffled().prefix(6).map { $0 }
        games.append(contentsOf: allGamesThreeItems.shuffled().prefix(4).map { $0 })
        
        items = selectedItems + selectedItems
        while items.count < games.count {
            items.append(selectedItems.randomElement()!)
        }
        
        // Validate game data
        var wrongData = false
        for (index, game) in games.enumerated() {
            let item = items[index]
            if game == "BarHorizontalGameFragment" && item.noHorizontal == true {
                wrongData = true
            } else if game == "BarVerticalGameFragment" && item.noVertical == true {
                wrongData = true
            } else if game == "TracingGameFragment" && (item.tracing == nil || item.tracing == false) {
                wrongData = true
            } else if game == "InsideGameFragment" && ["blueberry", "cherry", "grapes", "green grapes"].contains(item.name.en!.lowercased()) {
                wrongData = true
            } else if game == "StorageGameFragment" && pack!.folder.lowercased() != "sport equipment" {
                wrongData = true
            } else if game == "BubbleGameFragment" && pack!.folder.lowercased() != "aquatic animals" {
                wrongData = true
            } else if game == "BubbleGameFragment" && pack!.folder.lowercased() != "aquatic animals" {
                wrongData = true
            } else if game == "UnscrambleGameFragment" && item.name.en!.count > 7 {
                wrongData = true
            } else if game == "WordPuzzleGameFragment" && (item.name.en!.count >= 7 || item.name.en!.count <= 2) {
                wrongData = true
            } else if game == "SpellTheWordGameFragment" && (item.name.en!.count >= 7 || item.name.en!.count <= 2) {
                wrongData = true
            }
        }
        
        if !wrongData {
            playGames(games)
        } else {
            chooseGame() // Retry if data is wrong
        }
    }
    
    private func playGames(_ games: [String]) {
        guard !games.isEmpty else { return }
        self.games = games
        gameIndex = 0
        playGame(games[gameIndex])
    }
    
    private func playGame(_ gameClassName: String) {
        var game = gameClassName
        #if DEBUG
        game = "nhanbiet_list_kinhlup"
        #endif
        let moduleName = Utils.getAppModuleName()
        guard let gameClass = NSClassFromString("\(moduleName!).\(game)") as? NhanBietGameFragment.Type else {
            print("Game class not found: \(game)")
            return
        }
        
        scheduler.clearAll()
        if gameClosed {
            gameContainer.transform = CGAffineTransform(translationX: 0, y: gameContainer.bounds.height)
            viewDim.alpha = 0
        }
        gameContainer.isHidden = false
        gameView?.isHidden = false
        UIView.animate(withDuration: openDuration) {
            self.viewDim.alpha = self.dimMax
            self.gameContainer.alpha = 1
            self.gameContainer.transform = .identity
        }
        
        gameFragment = gameClass.init()
        gameFragment?.setLanguage("vi")
        gameFragment?.setListItems(selectedItems)
        gameFragment?.setItem(items.isEmpty ? selectedItems.randomElement()! : items[gameIndex])
        gameFragment?.setFolder(pack!.folder)
        gameFragment?.onGameFragmentListener = self
        
        if let fragment = gameFragment {
            gameView!.removeAllSubviews()
            gameView!.addSubview(fragment)
            fragment.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        gameClosed = false
    }
    
    // MARK: - OnGameFragmentListener
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        scores.append(gameFragment.score)
        if gameIndex < games.count - 1 {
            setPosition(gameIndex)
            gameIndex += 1
            playGame(games[gameIndex])
        } else {
            unlockStickers()
            
            let sumScore = scores.reduce(0, +)
            let aveScore = scores.isEmpty ? 0 : sumScore / Float(scores.count)
            var coinReward = CoinManager.shared.getCoin() - oldCoin
            if coinReward < 0 { coinReward = 0 }
            /*
            let completeDialog = LessonCompleteDialogViewController()
            completeDialog.baseDialogListener = { [weak self] in
                self?.navigationController?.popViewController(animated: true)
            }
            completeDialog.scores = scores
            completeDialog.setCoinReward(coinReward)
            completeDialog.setFolder(pack?.folder ?? "")
            completeDialog.setStickers(selectedItems)
            completeDialog.setLanguage("vi")
            completeDialog.show(self)
             */
        }
    }
    
    func onUpdateGameProgress(_ progress: Float) {
        // No implementation needed
    }
    
    // MARK: - Helper Methods
    private func unlockStickers() {
        guard let pack = pack, !pack.maps!.isEmpty else { return }
        DataManager.shared.runRealmTransaction {_ in 
            for selectedItem in self.selectedItems {
                if let itemIndex = pack.items.firstIndex(where: { $0.name.en == selectedItem.name.en }) {
                    var mapIndex = itemIndex / (pack.items.count / pack.maps!.count + 1)
                    if mapIndex >= pack.maps!.count { mapIndex = pack.maps!.count - 1 }
                    GalleryManager.shared.addStickerToMap(map: pack.maps![mapIndex], folder: pack.folder, sticker: selectedItem.name.en!, vocab: false)
                }
            }
        }
    }
    
    private func closeGame() {
        gameClosed = true
        UIView.animate(withDuration: openDuration, animations: {
            self.viewDim.alpha = 0
            self.gameContainer.transform = CGAffineTransform(translationX: 0, y: self.gameContainer.bounds.height)
            self.gameContainer.alpha = 0
        }) { _ in
            self.gameContainer.isHidden = true
            self.gameFragment?.removeFromSuperview()
            self.gameFragment = nil
        }
    }
    
    private func setPosition(_ position: Int) {
        guard position >= 0 && position <= 10 else { return }
        progressBgOrange.isHidden = false
        progressBgOrange.snp.updateConstraints { make in
            //make.width.equalToSuperview().multipliedBy(position == 10 ? 1.0 : 0.09 + (0.885 - 0.09) / 9 * min(position, 9))
        }
        
        for i in 0..<position {
            progressViews[i].image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar3")
        }
        if position < progressViews.count {
            progressViews[position].image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar5")
        }
        for i in position + 1..<progressViews.count {
            progressViews[i].image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar2")
        }
        progressCoin.image = Utilities.SVGImage(named: "nhanbiet_bg_progressbar6")
    }
        
    // MARK: - Override
    override func onBackPressed() {
        if gameClosed {
            navigationController?.popViewController(animated: true)
            return
        }
        super.onBackPressed()
    }
    override func onConfirmExit() {
        closeGame()
    }
}
