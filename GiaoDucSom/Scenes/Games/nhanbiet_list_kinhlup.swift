//
//  nhanbiet_list_kinhlup.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_kinhlup: NhanBietGameFragment {
    // MARK: - Properties
    private var viewAnimation: UIView!
    private var path: UIBezierPath?
    private var viewAnimationContainer: UIView!
    private var svgList: [SVGKImage] = []
    private var svg: SVGKImage?
    private var viewZoom: SVGImageView!
    private var viewLookup: SVGImageView!
    private var viewZoomContainer: UIImageView!
    private var viewLookupContainer: UIImageView!
    private var index: Int = 0
    private var rightX: CGFloat = 0
    private var rightY: CGFloat = 0
    private var gridLayout: MyGridView!
    private var meIndex: Int = 0
    private var viewLeft: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_magnify"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 2)
        
        
        viewLeft = UIView()
        viewLeft.clipsToBounds = false
        itemContainer.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(itemContainer).multipliedBy(0.5)
        }
        
        viewLookupContainer = UIImageView()
        viewLookupContainer.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        viewLeft.addSubview(viewLookupContainer)
        viewLookupContainer.snp.makeConstraints { make in
            make.width.equalTo(viewLookupContainer.snp.height) // Ratio 1:1
            make.height.equalTo(viewLeft).multipliedBy(0.4)
            make.center.equalToSuperview()
        }
        
        viewLookup = SVGImageView(frame: .zero)
        viewLookupContainer.addSubview(viewLookup)
        viewLookup.snp.makeConstraints { make in
            make.width.equalTo(viewLookupContainer).multipliedBy(0.8)
            make.height.equalTo(viewLookup.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        viewAnimationContainer = UIView()
        viewAnimationContainer.clipsToBounds = false
        viewLeft.addSubview(viewAnimationContainer)
        viewAnimationContainer.snp.makeConstraints { make in
            make.width.equalTo(viewAnimationContainer.snp.height) // Ratio 1:1
            make.height.equalTo(viewLeft).multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        viewAnimation = UIView()
        viewAnimation.clipsToBounds = false
        viewAnimation.backgroundColor = UIColor.red // #f00
        viewAnimationContainer.addSubview(viewAnimation)
        viewAnimation.snp.makeConstraints { make in
            make.width.equalTo(viewAnimation.snp.height) // Ratio 1:1
            make.height.equalTo(viewAnimationContainer).multipliedBy(0.1)
            make.left.top.equalToSuperview()
        }
        
        let magnifyContainer = UIView()
        magnifyContainer.clipsToBounds = false
        viewAnimation.addSubview(magnifyContainer)
        magnifyContainer.snp.makeConstraints { make in
            make.width.equalTo(magnifyContainer.snp.height) // Ratio 1:1
            make.height.equalTo(viewAnimation).multipliedBy(20)
            make.left.top.equalToSuperview()
        }
        
        let magnifyImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_btn_magnify"))
        magnifyContainer.addSubview(magnifyImage)
        magnifyImage.snp.makeConstraints { make in
            make.width.equalTo(magnifyContainer).multipliedBy(1.44)
            make.height.equalTo(magnifyImage.snp.width) // Ratio 1:1
            make.right.bottom.equalToSuperview()
        }
        
        let maskContainer = UIView()
        magnifyContainer.addSubview(maskContainer)
        maskContainer.snp.makeConstraints { make in
            make.edges.equalTo(magnifyImage)
        }
        
        let maskView = MaskableView()
        maskView.setMask(mask: Utilities.SVGImage(named: "bg_white_circle"))
        maskView.porterDuffMode = .destinationIn
        maskContainer.addSubview(maskView)
        maskView.snp.makeConstraints { make in
            make.width.equalTo(maskContainer).multipliedBy(0.565)
            make.height.equalTo(maskView.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            maskView.snapToVerticalBias(verticalBias: 0.11)
            maskView.snapToHorizontalBias(horizontalBias: 0.1)
        }
        
        let tempView = UIView()
        maskView.addSubview(tempView)
        tempView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewZoomContainer = UIImageView()
        viewZoomContainer.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        tempView.addSubview(viewZoomContainer)
        viewZoomContainer.snp.makeConstraints { make in
            make.width.equalTo(viewZoomContainer.snp.height) // Ratio 1:1
            make.height.equalTo(magnifyContainer).multipliedBy(3.3157)
            make.center.equalToSuperview()
        }
        
        viewZoom = SVGImageView(frame: .zero)
        viewZoomContainer.addSubview(viewZoom)
        viewZoom.snp.makeConstraints { make in
            make.width.equalTo(viewZoomContainer).multipliedBy(0.8)
            make.height.equalTo(viewZoom.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = .clear
        itemContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(itemContainer).multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        svgList = []
        if let folder = getFolder(), let items = getListItems() {
            for item in items {
                let svg = Utilities.GetSVGKImage(named:  "topics/\(folder)/\(item.path!)")
                self.svgList.append(svg)
                if item == self.getItem() {
                    self.svg = svg
                    self.meIndex = self.getListItems()?.firstIndex(of: item) ?? 0
                }
                self.viewZoom.image = self.svg?.uiImage
                self.viewLookup.image = self.svg?.uiImage
            }
        }
        
        var views: [UIView] = []
        for i in 0..<svgList.count {
            let view = KUButton()
            view.backgroundColor = .clear
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "nhanbiet_bg_option_white"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
                make.width.equalTo(bgContainer.snp.height) // Ratio 1:1
            }
            
            let svgThumbnail = SVGImageView(frame: .zero)
            svgThumbnail.image = svgList[i].uiImage
            bgContainer.addSubview(svgThumbnail)
            svgThumbnail.snp.makeConstraints { make in
                make.width.equalTo(bgContainer).multipliedBy(0.85)
                make.height.equalTo(svgThumbnail.snp.width) // Ratio 1:1
                make.center.equalToSuperview()
            }
            
            view.stringTag = "\(i)"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0.01
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        svgList.shuffle()
        viewZoom.image = svg?.uiImage
        viewLookup.image = svg?.uiImage
    }
    
    func animate(){
        let duration: TimeInterval = 4.0
        let zoomLevel: CGFloat = -4.0501394
        let width = viewAnimationContainer.frame.width
        let centerX = width / 2
        let centerY = width / 2
        let radius = width * 0.5
        
        path = UIBezierPath()
        path?.move(to: CGPoint(x: centerX, y: centerY + radius))
        path?.addLine(to: CGPoint(x: centerX, y: centerY))
        
        
        
        let animator = UIViewPropertyAnimator(duration: duration, curve: .linear) {
            self.viewAnimation.layer.position = CGPoint(x: centerX, y: centerY)
        }
        animator.addAnimations {
            let keyframe = CAKeyframeAnimation(keyPath: "position")
            keyframe.path = self.path?.cgPath
            keyframe.duration = duration
            keyframe.timingFunction = CAMediaTimingFunction(name: .linear)
            self.viewAnimation.layer.add(keyframe, forKey: "position")
        }
        self.viewZoomContainer.transform = CGAffineTransform(translationX:0, y: radius * 1.4 * zoomLevel)
        animator.addAnimations {
            self.viewZoomContainer.transform = CGAffineTransform(translationX:0, y: 0)
        }
        animator.addCompletion { _ in
            self.scheduler.schedule(delay: 1.0) { [weak self] in
                guard let self = self else { return }
                self.path = UIBezierPath()
                self.path?.move(to: CGPoint(x: centerX, y: centerY))
                self.path?.addLine(to: CGPoint(x: centerX, y: centerY + radius))
                
                let animator2 = UIViewPropertyAnimator(duration: duration, curve: .linear) {
                    self.viewAnimation.layer.position = CGPoint(x: centerX, y: centerY + radius)
                }
                animator2.addAnimations {
                    let keyframe = CAKeyframeAnimation(keyPath: "position")
                    keyframe.path = self.path?.cgPath
                    keyframe.duration = duration
                    keyframe.timingFunction = CAMediaTimingFunction(name: .linear)
                    self.viewAnimation.layer.add(keyframe, forKey: "position")
                }
                animator2.addAnimations {
                    self.viewZoomContainer.transform = CGAffineTransform(translationX:0, y: radius * 1.4 * zoomLevel)
                }
                animator2.startAnimation()
            }
        }
        animator.startAnimation()
    }
    
    override func createGame() {
        super.createGame()
        
        let duration: TimeInterval = 4.0
        
        animate()
        
        viewLeft.transform = .identity
        gridLayout.step = 1
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_magnify"])
        delay = max(delay, 2 * duration + 2.0)
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.viewLeft.transform = .identity
            }
        }
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            let delay = gridLayout.showItems(startDelay: 0)
            scheduler.schedule(after: delay) { [weak self] in
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay: TimeInterval = playSound("\(getLanguage())/nhanbiet/nhanbiet_magnify")
            delay = max(delay, 2 * 4.0 + 2)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
            animate()
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: KUButton) {
        guard let index = Int(sender.stringTag ?? "0"),
              let svgImageView = sender.findSubviews(ofType: SVGImageView.self).first else { return }
        pauseGame(stopMusic: false)
        if index == meIndex {
            animateCoinIfCorrect(view: svgImageView)
            let delay: TimeInterval = playSound(delay: 0, names: [
                "\(getLanguage())/topics/\(getFolder() ?? "")/\(getItem()!.path!.replacingOccurrences(of: ".svg", with: ""))",
                "effect/answer_correct1",
                getCorrectHumanSound(),
                endGameSound()
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
