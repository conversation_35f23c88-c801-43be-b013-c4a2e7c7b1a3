//
//  tuduy_list_dieukhienoto.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 11/4/25.
//


import UIKit
import SnapKit

class tuduy_list_dieukhienoto: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var gridLayoutMap: MyGridView!
    private let ROW = 5
    private let COL = 5
    private var data: [[Int]] = Array(repeating: Array(repeating: 0, count: 5), count: 5)
    private var buttonPlay: KUButton!
    private var moves: [[Int]] = []
    private var buttonLeft: UIButton!
    private var buttonRight: UIButton!
    private var buttonUp: UIButton!
    private var buttonDown: UIButton!
    private var viewCar: UIView!
    private var imageCar: UIImageView!
    private var playIndex: Int = -1
    private var xCar: Int = 0
    private var yCar: Int = 0
    private var TREES: [[Int]] = []
    private var CARS: [[Int]] = []
    private var ROAD: [[Int]] = []
    private var count: Int = 0
    private var controllerView: UIView!
    private var imageCarZoom: UIImageView!
    private var engineId: String?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 224/255, green: 224/255, blue: 228/255, alpha: 1) // #E0E0E4
        
        let bgWhiteLeft = UIView()
        bgWhiteLeft.backgroundColor = .white
        view.addSubview(bgWhiteLeft)
        bgWhiteLeft.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        let contentLayout = UIView()
        contentLayout.clipsToBounds = false
        view.addSubview(contentLayout)
        contentLayout.snp.makeConstraints { make in
            make.top.bottom.left.right.equalToSuperview()
        }
        
        controllerView = UIView()
        contentLayout.addSubview(controllerView)
        controllerView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        let tempView = UIView()
        controllerView.addSubviewWithPercentInset(subview: tempView, percentInset: 5)
        
        let padContainer = UIView()
        //padContainer.backgroundColor = .red
        tempView.addSubview(padContainer)
        padContainer.makeViewCenterAndKeep(ratio: 1)
        
        let padBackground = UIImageView()
        padBackground.isUserInteractionEnabled = true
        padBackground.image = Utilities.SVGImage(named: "tuduy_car_pad")
        
        padContainer.addSubview(padBackground)
        padBackground.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview{$0.snp.bottom}.multipliedBy(0.5)
            make.width.equalTo(padBackground.snp.height).multipliedBy(1234.4 / 853.2) // Ratio 1234.4:853.2
        }
        
        buttonPlay = KUButton()
        buttonPlay.setImage(Utilities.SVGImage(named: "music_phimdan_play"), for: .normal)
        padBackground.addSubview(buttonPlay)
        buttonPlay.snp.makeConstraints { make in
            make.width.equalTo(padBackground).multipliedBy(0.20)
            make.height.equalTo(buttonPlay.snp.width) // Ratio 1:1
            //make.centerY.equalToSuperview().multipliedBy(0.56) // Bias 0.28
        }
        addActionOnLayoutSubviews {
            self.buttonPlay.snapToHorizontalBias(horizontalBias: 0.8)
            self.buttonPlay.snapToVerticalBias(verticalBias: 0.28)
        }
        buttonPlay.addTarget(self, action: #selector(handlePlayTap(_:)), for: .touchUpInside)
        
        buttonRight = UIButton()
        buttonRight.setImage(Utilities.SVGImage(named: "tuduy_car_pad2"), for: .normal)
        padBackground.addSubview(buttonRight)
        buttonRight.snp.makeConstraints { make in
            make.width.equalTo(padBackground).multipliedBy(0.13)
            make.height.equalTo(buttonRight.snp.width).multipliedBy(124.8 / 165.1) // Ratio 165.1:124.8
            //make.centerY.equalToSuperview().multipliedBy(0.64) // Bias 0.32
        }
        addActionOnLayoutSubviews {
            self.buttonRight.snapToHorizontalBias(horizontalBias: 0.32)
            self.buttonRight.snapToVerticalBias(verticalBias: 0.32)
        }
        buttonRight.addTarget(self, action: #selector(handleRightTap(_:)), for: .touchUpInside)
        
        let leftContainer = UIView()
        leftContainer.transform = CGAffineTransform(scaleX: -1, y: 1)
        padBackground.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(padBackground).multipliedBy(0.13)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(124.8 / 165.1) // Ratio 165.1:124.8
            //make.centerY.equalToSuperview().multipliedBy(0.64) // Bias 0.32
        }
        addActionOnLayoutSubviews {
            leftContainer.snapToHorizontalBias(horizontalBias: 0.14)
            leftContainer.snapToVerticalBias(verticalBias: 0.32)
        }
        
        buttonLeft = UIButton()
        buttonLeft.setImage(Utilities.SVGImage(named: "tuduy_car_pad2"), for: .normal)
        leftContainer.addSubview(buttonLeft)
        buttonLeft.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        buttonLeft.addTarget(self, action: #selector(handleLeftTap(_:)), for: .touchUpInside)
        
        buttonDown = UIButton()
        buttonDown.setImage(Utilities.SVGImage(named: "tuduy_car_pad2"), for: .normal)
        buttonDown.transform = CGAffineTransform(rotationAngle: .pi / 2)
        padBackground.addSubview(buttonDown)
        buttonDown.snp.makeConstraints { make in
            make.width.equalTo(padBackground).multipliedBy(0.13)
            make.height.equalTo(buttonDown.snp.width).multipliedBy(124.8 / 165.1) // Ratio 165.1:124.8
            //make.centerY.equalToSuperview().multipliedBy(0.9) // Bias 0.45
        }
        addActionOnLayoutSubviews {
            self.buttonDown.snapToHorizontalBias(horizontalBias: 0.23)
            self.buttonDown.snapToVerticalBias(verticalBias: 0.45)
        }
        buttonDown.addTarget(self, action: #selector(handleDownTap(_:)), for: .touchUpInside)
        
        buttonUp = UIButton()
        buttonUp.setImage(Utilities.SVGImage(named: "tuduy_car_pad2"), for: .normal)
        buttonUp.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        padBackground.addSubview(buttonUp)
        buttonUp.snp.makeConstraints { make in
            make.width.equalTo(padBackground).multipliedBy(0.13)
            make.height.equalTo(buttonUp.snp.width).multipliedBy(124.8 / 165.1) // Ratio 165.1:124.8
            //make.centerY.equalToSuperview().multipliedBy(0.38) // Bias 0.19
        }
        addActionOnLayoutSubviews {
            self.buttonUp.snapToHorizontalBias(horizontalBias: 0.23)
            self.buttonUp.snapToVerticalBias(verticalBias: 0.19)
        }
        buttonUp.addTarget(self, action: #selector(handleUpTap(_:)), for: .touchUpInside)
        
        gridLayout = MyGridView()
        gridLayout.insetRatio = 1
        gridLayout.itemSpacingRatio = 0.01
        gridLayout.insetRatio = 0
        gridLayout.backgroundColor = UIColor(red: 225/255, green: 225/255, blue: 229/255, alpha: 1) // #E1E1E5
        padContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.6) // Bias 0.3
        }
        
        scheduler.schedule(delay: 0.2) {
            [weak self] in
            guard let self = self else { return }
            self.gridLayout.layer.cornerRadius = gridLayout.frame.height / 2
        }
        
        let rightContainer = UIView()
        contentLayout.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(contentLayout).multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        gridLayoutMap = MyGridView()
        gridLayoutMap.columns = COL
        gridLayoutMap.itemSpacingRatio = 0.01
        gridLayoutMap.insetRatio = 0.01
        rightContainer.addSubview(gridLayoutMap)
        gridLayoutMap.snp.makeConstraints { make in
            make.width.height.equalTo(rightContainer).multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        viewCar = UIView()
        //viewCar.backgroundColor = .red.withAlphaComponent(0.5)
        rightContainer.addSubview(viewCar)
        viewCar.snp.makeConstraints { make in
            make.width.equalTo(rightContainer).multipliedBy(0.2)
            make.height.equalTo(viewCar.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        imageCar = UIImageView(image: Utilities.SVGImage(named: "tuduy_car"))
        imageCar.contentMode = .scaleAspectFit
        viewCar.addSubview(imageCar)
        imageCar.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageCarZoom = UIImageView(image: Utilities.SVGImage(named: "tuduy_car_end2"))
        imageCarZoom.contentMode = .scaleAspectFit
        imageCarZoom.alpha = 0
        rightContainer.addSubview(imageCarZoom)
        imageCarZoom.snp.makeConstraints { make in
            make.width.height.equalTo(rightContainer).multipliedBy(0.9)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        generateData()
        buildMap()
        let delay = playSound(openGameSound(), "tuduy/o to")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/o to")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handlePlayTap(_ sender: UIButton) {
        engineId = QueueSoundPlayer.shared.play(sound: "effect/car engine", delay: 0)
        play()
    }
    
    @objc private func handleLeftTap(_ sender: UIButton) {
        playSound("effect/bubble1")
        move(x: -1, y: 0)
    }
    
    @objc private func handleRightTap(_ sender: UIButton) {
        playSound("effect/bubble3")
        move(x: 1, y: 0)
    }
    
    @objc private func handleUpTap(_ sender: UIButton) {
        playSound("effect/bubble2")
        move(x: 0, y: -1)
    }
    
    @objc private func handleDownTap(_ sender: UIButton) {
        playSound("effect/bubble4")
        move(x: 0, y: 1)
    }
    
    // MARK: - Game Logic
    private func play() {
        if playIndex == -1 {
            let car = CARS[0]
            xCar = car[0]
            yCar = car[1]
        }
        playIndex += 1
        if playIndex == moves.count {
            if xCar == CARS[1][0] && yCar == CARS[1][1] {
                pauseGame()
                animateCoinIfCorrect(view: viewCar)
                QueueSoundPlayer.shared.stop(id: engineId ?? "")
                let delay = playSound("effect/answer_correct1", getCorrectHumanSound())
                imageCarZoom.alpha = 1
                scheduler.schedule(after: delay) {
                    self.controllerView.isHidden = true
                    UIView.animate(withDuration: 1.0) {
                        self.imageCarZoom.transform = .identity
                        self.imageCarZoom.alpha = 1
                    }
                }
                let totalDelay = delay + 1.0 + playSound(delay: delay + 1.0, names: [self.endGameSound()])
                scheduler.schedule(delay: totalDelay) { [weak self] in
                    self?.finishGame()
                }
                viewCar.alpha = 0.1
            } else {
                if ROAD.count == moves.count {
                    let fuelView = gridLayout.subviews[ROAD.count]
                    UIView.animate(withDuration: 0.3, delay: 0, options: [.repeat], animations: {
                        fuelView.alpha = 0.5
                    }) { _ in
                        fuelView.alpha = 1
                    }
                }
                QueueSoundPlayer.shared.stop(id :engineId ?? "")
            }
            playIndex -= 1
            return
        }
        
        let move = moves[playIndex]
        let x = move[0]
        let y = move[1]
        xCar += x
        yCar += y
        
        let rotation = imageCar.transform.rotationAngle()
        let newRotation = y == 0 ? (x == 1 ? .pi / 2.0 : 3.0 * .pi / 2.0) : (y == 1 ? 0.0 : .pi)
        let delta = abs(newRotation - rotation)
        let delay = TimeInterval(delta < .pi ? delta : 2 * .pi - delta) * 2 * 180 / .pi / 1000
        
        if rotation != newRotation {
            UIView.animate(withDuration: delay) {
                self.imageCar.transform = CGAffineTransform(rotationAngle: newRotation)
            } completion: { _ in
                self.imageCar.transform = CGAffineTransform(rotationAngle: (self.imageCar.transform.rotationAngle() + 2 * .pi).truncatingRemainder(dividingBy: 2 * .pi))
            }
        }
        
        let nextView = gridLayoutMap.subviews[xCar * COL + yCar]
        if xCar < 0 || xCar >= ROW || yCar < 0 || yCar >= COL {
            setGameWrong()
            animateCoinIfCorrect(view: self.viewCar)
            QueueSoundPlayer.shared.stop(id: self.engineId ?? "")
            self.imageCar.image = Utilities.SVGImage(named: "tuduy_car2")
            let delay = self.playSound("effect/car hit", "effect/end game2")
            self.scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
            return
        }
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            viewCar.moveToCenter(of: nextView, duration: 0.5) {
                [weak self] _ in
                guard let self = self else { return }
                if self.xCar < 0 || self.xCar >= self.ROW || self.yCar < 0 || self.yCar >= self.COL || self.data[self.xCar][self.yCar] == 1 {
                    self.setGameWrong()
                    self.animateCoinIfCorrect(view: self.viewCar)
                    QueueSoundPlayer.shared.stop(id: self.engineId ?? "")
                    self.imageCar.image = Utilities.SVGImage(named: "tuduy_car2")
                    nextView.alpha = 0.5
                    let delay = self.playSound("effect/car hit", "effect/end game2")
                    self.scheduler.schedule(delay: delay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    self.play()
                }
            }
        }
    }
    
    private func move(x: Int, y: Int) {
        if moves.count == ROAD.count { return }
        moves.append([y, x])
        guard let itemView = gridLayout.subviews[moves.count - 1] as? UIImageView else { return }
        itemView.image = Utilities.SVGImage(named: "tuduy_car_arrow")
        itemView.transform = CGAffineTransform(rotationAngle: x == 0 ? (y == 1 ? .pi / 2 : 3 * .pi / 2) : (x == 1 ? 0 : .pi))
    }
    
    private func generateData() {
        while true {
            data = Array(repeating: Array(repeating: 0, count: COL), count: ROW)
            let TREE = 5 + Int.random(in: 0..<3)
            let CAR = 2
            TREES = []
            CARS = []
            
            for i in 0..<(TREE + CAR) {
                while true {
                    let x = Int.random(in: 0..<ROW)
                    let y = Int.random(in: 0..<COL)
                    if data[x][y] == 0 {
                        data[x][y] = i < TREE ? 1 : 2
                        if data[x][y] == 1 {
                            TREES.append([x, y])
                        } else {
                            CARS.append([x, y])
                        }
                        break
                    }
                }
            }
            
            count = 0
            if checkDirectConnect(a: CARS[0], b: CARS[1]) { continue }
            ROAD = findRoadFromCar0ToCar1() ?? []
            if ROAD.count < 5 { continue }
            
            for i in 1..<ROAD.count {
                let road = ROAD[i]
                data[road[0]][road[1]] = 3
            }
            return
        }
    }
    
    private func buildMap() {
        moves = []
        var views: [UIView] = []
        for i in 0..<ROW {
            for j in 0..<COL {
                let view = UIImageView()
                if data[i][j] == 1 {
                    let id = "tuduy_car_tree\(1 + Int.random(in: 0..<5))"
                    view.image = Utilities.SVGImage(named: id)
                }
                if data[i][j] == 0 || data[i][j] == 2 || data[i][j] == 3 {
                    view.image = Utilities.SVGImage(named: "tuduy_car_cell")
                }
                if data[i][j] == 2 {
                    if CARS[0][0] == i && CARS[0][1] == j {
                        scheduler.schedule(after: 0.1) {
                            self.viewCar.moveToCenter(of: view)
                            //self.viewCar.frame = view.frame
                        }
                    } else {
                        scheduler.schedule(after: 0.1) {
                            self.imageCarZoom.frame = view.frame
                        }
                        view.image = Utilities.SVGImage(named: "tuduy_car_end")
                    }
                }
                view.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
                views.append(view)
            }
        }
        gridLayoutMap.reloadItemViews(views: views)
        
        views = []
        for i in 0...(ROAD.count) {
            let view = UIImageView()
            view.image = Utilities.SVGImage(named: i == ROAD.count ? "tuduy_car_fuel" : "tuduy_car_arrow2")
            view.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            views.append(view)
        }
        gridLayout.insetRatio = 0.01
        gridLayout.columns = ROAD.count + 1
        gridLayout.reloadItemViews(views: views)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(gridLayout.snp.height).multipliedBy(CGFloat(ROAD.count + 1) * 9 / 10)
        }
    }
    
    private func checkDirectConnect(a: [Int], b: [Int]) -> Bool {
        count += 1
        if count > 100 { count += 1 } // Debug logic
        
        if a[0] == b[0] || a[1] == b[1] {
            if a[0] == b[0] {
                let minY = min(a[1], b[1])
                let maxY = max(a[1], b[1])
                for i in (minY + 1)..<maxY {
                    if data[a[0]][i] != 0 { return false }
                }
                return true
            } else {
                let minX = min(a[0], b[0])
                let maxX = max(a[0], b[0])
                for i in (minX + 1)..<maxX {
                    if data[i][a[1]] != 0 { return false }
                }
                return true
            }
        }
        
        if data[a[0]][b[1]] == 0 {
            if checkDirectConnect(a: [a[0], b[1]], b: a) && checkDirectConnect(a: [a[0], b[1]], b: b) {
                return true
            }
        }
        if data[b[0]][a[1]] == 0 {
            if checkDirectConnect(a: [b[0], a[1]], b: a) && checkDirectConnect(a: [b[0], a[1]], b: b) {
                return true
            }
        }
        return false
    }
    
    private func findRoadFromCar0ToCar1() -> [[Int]]? {
        var distance = Array(repeating: Array(repeating: -1, count: COL), count: ROW)
        distance[CARS[0][0]][CARS[0][1]] = 0
        var queue: [[Int]] = [CARS[0]]
        
        while !queue.isEmpty {
            let u = queue.removeFirst()
            let directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
            for d in directions {
                let x = u[0] + d[0]
                let y = u[1] + d[1]
                if x >= 0 && y >= 0 && x < ROW && y < COL && (data[x][y] == 0 || data[x][y] == 2) && distance[x][y] == -1 {
                    distance[x][y] = distance[u[0]][u[1]] + 1
                    queue.append([x, y])
                }
            }
        }
        
        if distance[CARS[1][0]][CARS[1][1]] == -1 { return nil }
        
        var result: [[Int]] = []
        var x = CARS[1][0]
        var y = CARS[1][1]
        while x != CARS[0][0] || y != CARS[0][1] {
            result.append([x, y])
            let directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
            for d in directions {
                let u = x - d[0]
                let v = y - d[1]
                if u >= 0 && v >= 0 && u < ROW && v < COL && (data[u][v] == 0 || data[u][v] == 2) && distance[u][v] == distance[x][y] - 1 {
                    x = u
                    y = v
                    break
                }
            }
        }
        return result
    }
}


struct R9 {
    struct drawable {
        static let tuduy_car = 1
        static let tuduy_car2 = 2
        static let tuduy_car_end = 3
        static let tuduy_car_end2 = 4
        static let tuduy_car_cell = 5
        static let tuduy_car_arrow = 6
        static let tuduy_car_arrow2 = 7
        static let tuduy_car_fuel = 8
        static let tuduy_car_pad = 9
        static let tuduy_car_pad2 = 10
        static let music_phimdan_play = 11
    }
}
