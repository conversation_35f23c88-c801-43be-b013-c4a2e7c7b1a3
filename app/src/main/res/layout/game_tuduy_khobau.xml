<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/tuduy_khobau_bg"
    android:clipChildren="false"
    android:clipToPadding="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2.2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/map_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_khobau_hint"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="146.8:176.6"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/hint_text"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/utm_avo_bold"
                android:gravity="center"
                android:text="B5"
                android:textColor="#DB833A"
                app:autoSizeTextType="uniform"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.4"
                app:layout_constraintWidth_percent="0.7" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_khobau_map"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1803.6:1236.2"
            app:layout_constraintHorizontal_bias="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="@id/grid_layout"
                app:layout_constraintLeft_toLeftOf="@id/grid_layout"
                app:layout_constraintRight_toRightOf="@id/grid_layout"
                app:layout_constraintTop_toTopOf="@id/grid_layout">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/boat_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintHeight_percent="0.2"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.1428571428571429">

                    <ImageView
                        android:id="@+id/boat_image"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:src="@drawable/tuduy_khobau_boat"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="1.4"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1"
                        app:layout_constraintWidth_percent="1.4" />

                    <com.kidsup.giaoducsom.view.SVGAutosizeView
                        android:id="@+id/svg_animation_view"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="2"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintWidth_percent="2" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.kidsup.giaoducsom.view.MyGridLayout
                android:id="@+id/grid_layout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.85"
                app:layout_constraintHorizontal_bias="0.66"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.8"
                app:layout_constraintWidth_percent="0.89" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include layout="@layout/item_coin_view" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>