//
//  NhanBietGameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/3/25.
//

import UIKit

class NhanBietGameFragment: BaseFragmentView {
    // MARK: - Properties
    private var folder: String?
    private var item: Item?
    private var listItems: [Item]?
    var topMenuContainer: UIView?
    private var topMenu: UIView?
    private var timeoutTimer: TimeoutTimer?
    private var show: Bool = false


    // MARK: - Getters
    
    open func getListItems() -> [Item]? {
        return listItems
    }

    open func getItem() -> Item? {
        return item
    }

    open func getFolder() -> String? {
        return folder
    }

    // MARK: - Setters (Fluent API)
    
    @discardableResult
    open func setListItems(_ listItems: [Item]) -> NhanBietGameFragment {
        self.listItems = listItems
        return self
    }

    @discardableResult
    open func setItem(_ item: Item) -> NhanBietGameFragment {
        self.item = item
        return self
    }

    @discardableResult
    open func setFolder(_ folder: String) -> NhanBietGameFragment {
        self.folder = folder
        return self
    }

    // MARK: - Methods
    open func singleItemGame() -> Bool {
        return false
    }

    open func buildTopPopupView(_ view: UIView) {
        topMenuContainer = view.viewWithTag(1001) // Thay R.id.top_menu_container
        if let container = topMenuContainer {
            topMenu = container.viewWithTag(1002) // Thay R.id.top_menu
            let ivTop1 = container.viewWithTag(1003) // Thay R.id.iv_top_1
            let ivTop2 = container.viewWithTag(1004) // Thay R.id.iv_top_2
            let ivTop3 = container.viewWithTag(1005) // Thay R.id.iv_top_3
            container.isHidden = true            
            timeoutTimer?.duration = 3
            timeoutTimer?.onActived = {[weak self] in
                guard let self = self else { return }
                self.hideTopPopup()
            }
        }
    }

    open func showTopPopup() {
        guard let topMenu = topMenu, let topMenuContainer = topMenuContainer else { return }
        topMenuContainer.isHidden = false
        if !show {
            topMenu.transform = CGAffineTransform(translationX: 0, y: -topMenuContainer.bounds.height * 0.4)
            UIView.animate(withDuration: 0.5) {
                topMenu.transform = .identity
            }
        }
        show = true
        timeoutTimer?.schedule()
    }

    private func hideTopPopup() {
        guard let topMenu = topMenu, let topMenuContainer = topMenuContainer else { return }
        show = false
        UIView.animate(withDuration: 0.5, animations: {
            topMenu.transform = CGAffineTransform(translationX: 0, y: -topMenuContainer.bounds.height * 0.4)
        }) { _ in
            topMenuContainer.isHidden = true
        }
    }

    override open func updateData() {
        super.updateData()
        scheduler.schedule(delay: 3.0) {
            [weak self] in
            guard let self = self else { return }
            // finishGame() - Comment trong Java, tạm bỏ qua
            #if DEBUG
            //self.startGame()
            #endif
        }
    }

    open func getCorrectHumanSound() -> String {
        let randomNum = random(1, 2, 3, 4, 5, 6)
        return getLanguage() == "en" ? "en/answer_correct\(randomNum)" : "vi/answer_correct\(randomNum)"
    }

    open func getWrongHumanSound() -> String {
        let randomNum = random(1, 2)
        return getLanguage() == "en" ? "en/answer_wrong\(randomNum)" : "vi/answer_wrong\(randomNum)"
    }

    open func answerCorrect1EffectSound() -> String {
        return "effect/answer_correct1"
    }

    open func finishCorrect1Sounds() -> [String] {
        return [answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()]
    }

    open func finishEndSounds() -> [String] {
        return [answerEndEffectSound(), getCorrectHumanSound(), endGameSound()]
    }

    open func chooseWrongSounds() -> [String] {
        return [answerWrongEffectSound(), getWrongHumanSound()]
    }

    open func answerEndEffectSound() -> String {
        return "effect/answer_end"
    }

    open func answerWrongEffectSound() -> String {
        return "effect/answer_wrong"
    }

    open func openGameSound() -> String {
        return "effect/open game"
    }

    open func endGameSound() -> String {
        return "effect/end game"
    }

    open func itemSound() -> String? {
        guard let folder = folder, let item = item else { return nil }
        return "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
    }

    open func getNumberSound(_ number: Int) -> String {
        return "topics/Numbers/\(number)"
    }
    private func animateCoinIfCorrect(_ view: UIView) {
        UIView.animate(withDuration: 0.5) {
            view.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        } completion: { _ in
            UIView.animate(withDuration: 0.5) {
                view.transform = .identity
            }
        }
    }
}

extension BaseFragmentView {
    func random(_ numbers: Int...) -> Int {
        let index = Int.random(in: 0..<numbers.count)
        return numbers[index]
    }
    func random(_ texts: String...) -> String {
        let index = Int.random(in: 0..<texts.count)
        return texts[index]
    }
}


