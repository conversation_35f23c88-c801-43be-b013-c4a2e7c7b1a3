//
//  MusicMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class MusicMainViewController: SubjectMainViewController {
    override var backgroundMusicFile: String? {
        "bg_list3"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Âm Nhạc")
        titleColor = UIColor(hex: "#222222")
        backgroundColor = UIColor(hex: "#B5B9C5")
    }
    
    override func setupData() {
        // Section 1: Âm sắc
        let amSacSection = SectionData(
            header: "Âm sắc",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_thiennhien", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_connguoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_dongvat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhantao", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhaccu", isTapDocGame: false)
            ]
        )
        
        // Section 2: Nhạc lý
        let nhacLySection = SectionData(
            header: "Nhạc lý",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_timnotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_noinotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_notnhactrenkhuong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_notnhactrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_kyhieutrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_keonotnhacvaokhuong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_sapxepnottangdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_xuongamtrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_phachnotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_timgiaidieu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_phachdaulang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_truongdonotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_sochinhip", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_choidantudo", isTapDocGame: false)
            ]
        )
        
        // Section 3: Giai điệu
        let giaiDieuSection = SectionData(
            header: "Giai điệu",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_baihatyeuthich", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            amSacSection,
            nhacLySection,
            giaiDieuSection
        ]    
    }
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = checkFreeGameByThumbnail(troChoiItem.data)
            let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
            
            if playable {
                if troChoiItem.data == "amnhac_list_baihatyeuthich" {
                    // Mở MusicBaiHatYeuThichViewController
                    let musicBaiHatVC = MusicBaiHatYeuThichViewController()
                    navigationController?.pushViewController(musicBaiHatVC, animated: true)
                    return
                } else {
                   
                }
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this game")
            }
            super.collectionView(collectionView, didSelectItemAt: indexPath)
        }
    }
    
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự MusicGameNameHelper.getGameByThumb
        return thumbnail
    }
}


