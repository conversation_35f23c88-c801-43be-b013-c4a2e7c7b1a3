//
//  toancoban_list_sosanhbutchi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sosanhbutchi: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!    
    private var isTwoItems: Bool = false
    private var isHigh: Bool = false
    private var values: [Int] = []
    private var value: Int = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .white // #FFFFFF
        
        let middleContainer = UIView()
        addSubview(middleContainer)
        middleContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        itemContainer = UIView()
        middleContainer.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1.4)
        
        let image1 = UIImageView(image: Utilities.SVGImage(named: "math_butdaingan_3"))
        image1.contentMode = .scaleAspectFit
        itemContainer.addSubview(image1)
        image1.snp.makeConstraints { make in
            make.width.equalTo(image1.snp.height).multipliedBy(1642.0 / 174.0) // Ratio 1642:174
            make.left.right.equalToSuperview()
        }
        
        let image2 = UIImageView(image: Utilities.SVGImage(named: "math_butdaingan_1"))
        image2.contentMode = .scaleAspectFit
        itemContainer.addSubview(image2)
        image2.snp.makeConstraints { make in
            make.width.equalTo(image2.snp.height).multipliedBy(1642.0 / 174.0) // Ratio 1642:174
            make.center.left.right.equalToSuperview()
        }
        
        let image3 = UIImageView(image: Utilities.SVGImage(named: "math_butdaingan_1"))
        image3.contentMode = .scaleAspectFit
        itemContainer.addSubview(image3)
        image3.snp.makeConstraints { make in
            make.width.equalTo(image3.snp.height).multipliedBy(1642.0 / 174.0) // Ratio 1642:174
            make.left.right.equalToSuperview()
        }
        
        addActionOnLayoutSubviews{
            image1.snapToVerticalBias(verticalBias: 0.25)
            image3.snapToVerticalBias(verticalBias: 0.75)
        }
       
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
        
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image1.addGestureRecognizer(tap1)
        image1.isUserInteractionEnabled = true
        
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image2.addGestureRecognizer(tap2)
        image2.isUserInteractionEnabled = true
        
        let tap3 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image3.addGestureRecognizer(tap3)
        image3.isUserInteractionEnabled = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        isTwoItems = Bool.random()
        isHigh = Bool.random()
        
        if isTwoItems {
            values = [0, 1, 2].shuffled().prefix(2).map { $0 }
        } else {
            values = [0, 1, 2].shuffled()
        }
        
        let indexes = isTwoItems ? [0,2] : [0,1,2]
        
        let meValue = isHigh ? values.max()! : values.min()!
        let meIndex = values.firstIndex(of: meValue)!
        
        var index = 0
        for i in 0..<itemContainer.subviews.count {
            guard let imageView = itemContainer.subviews[i] as? UIImageView else { continue }
            if !indexes.contains(i) {
                imageView.isHidden = true
            } else {
                imageView.isHidden = false
                imageView.tag = index
                let value = values[index]
                index += 1
                imageView.image = Utilities.SVGImage(named: "math_butdaingan_\(value + 1)") // 1, 2, 3
                imageView.alpha = 0
                scheduler.schedule(after: 0.2) {
                    [weak self] in
                    guard let self = self else { return }
                    imageView.alpha = 1
                }
            }
        }
        
        value = isHigh ? 1 : 2
        value += isTwoItems ? 0 : 2
        let delay = playSound(openGameSound(), "toan/toan_but dai ngan\(value)")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_but dai ngan\(value)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func onImageTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        pauseGame()
        let meValue = isHigh ? values.max()! : values.min()!
        let meIndex = values.firstIndex(of: meValue)!
        let correct = view.tag == meIndex
        
        if correct {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - Supporting Structures
extension Array {
    func max() -> Element? where Element: Comparable {
        return self.max(by: { $0 < $1 })
    }
    
    func min() -> Element? where Element: Comparable {
        return self.min(by: { $0 < $1 })
    }
}
