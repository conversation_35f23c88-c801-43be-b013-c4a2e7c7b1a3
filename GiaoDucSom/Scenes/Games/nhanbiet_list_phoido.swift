//
//  nhanbiet_list_phoido.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_phoido: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView1: SVGImageView!
    private var svgView2: SVGImageView!
    private var svgView3: SVGImageView!
    private var svgView: SVGImageView!
    private var svgViewDrag: SVGImageView!
    private var svgViewDragInside: SVGImageView!
    private var viewsAdded: Int = 0
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var items: [Item] = []
    private var pathsColor: [CALayer?] = [nil, nil, nil]
    private var shadowsColor: [CALayer?] = [nil, nil, nil]
    private var washingMachineSVG: SVGKImage?
    private var coinView: UIView!
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_drying"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.height).multipliedBy(613.2/769.7) // Ratio 613.2:769.7
            make.height.equalToSuperview().multipliedBy(0.4)
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        svgView.snpLeftBottom(ratio: 1)
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.left.equalTo(svgView.snp.right)
        }
        
        let doorContainer = UIView()
        doorContainer.clipsToBounds = false
        rightContainer.addSubview(doorContainer)
        doorContainer.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(2) // Ratio 2:1
            make.top.left.right.equalToSuperview()
        }
        
        let dryingBg = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_drying2"))
        doorContainer.addSubview(dryingBg)
        dryingBg.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.width.equalTo(dryingBg.snp.height).multipliedBy(1984.7/417.5)
        }
        
        svgView1 = SVGImageView(frame: .zero)
        svgView1.contentMode = .scaleAspectFit
        //svgView1.backgroundColor = .clear// UIColor.black.withAlphaComponent(0.06) // #0f00
        doorContainer.addSubview(svgView1)
        svgView1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalTo(svgView1.snp.width) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(0.12) // Horizontal bias 0.06
            //make.centerY.equalToSuperview().multipliedBy(0.9) // Vertical bias 0.45
        }
        scheduler.schedule(after: 1) {
            [weak self] in
            guard let self = self else { return }
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.svgView1.snapToHorizontalBias(horizontalBias: 0.06)
            self.svgView1.snapToVerticalBias(verticalBias: 0.45)
        }
        
        svgView2 = SVGImageView(frame: .zero)
        svgView2.contentMode = .scaleAspectFit
        //svgView2.backgroundColor = UIColor.black.withAlphaComponent(0.06)
        doorContainer.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalTo(svgView2.snp.width) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(0.96) // Horizontal bias 0.48
            make.centerY.equalToSuperview() // Vertical bias 0
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.svgView2.snapToHorizontalBias(horizontalBias: 0.48)
        }
        
        svgView3 = SVGImageView(frame: .zero)
        svgView3.contentMode = .scaleAspectFit
        //svgView3.backgroundColor = UIColor.black.withAlphaComponent(0.06)
        doorContainer.addSubview(svgView3)
        svgView3.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalTo(svgView3.snp.width) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(1.82) // Horizontal bias 0.91
            //make.centerY.equalToSuperview().multipliedBy(0.96) // Vertical bias 0.48
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.svgView3.snapToHorizontalBias(horizontalBias: 0.91)
            self.svgView3.snapToVerticalBias(verticalBias: 0.48)
        }
        
        svgViewDrag = SVGImageView(frame: .zero)
        svgViewDrag.contentMode = .scaleAspectFit
        svgViewDrag.isUserInteractionEnabled = true
        svgViewDrag.backgroundColor = .clear// UIColor.black.withAlphaComponent(0.6)
        view.addSubview(svgViewDrag)
        svgViewDrag.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.width)
            make.height.equalTo(svgView.snp.height)
            make.center.equalTo(svgView)
        }
        
        // Add pan gesture for drag
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        svgViewDrag.addGestureRecognizer(panGesture)
        
        svgViewDragInside = SVGImageView(frame: .zero)
        svgViewDragInside.contentMode = .scaleAspectFit
        svgViewDrag.addSubview(svgViewDragInside)
        svgViewDragInside.makeViewCenterAndKeep(ratio: 1)
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(), listItems.count >= 3 else { return }
        
        items = listItems.shuffled()
        
        let delay = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_drying"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
        
        let svg = Utilities.GetSVGKImage(named: "nhanbiet_washing_machine")
        washingMachineSVG = svg
        
        // Update SVG colors based on item theme_color
        guard let sublayers = svg.caLayerTree?.sublayers else { return }
        for (i, g) in sublayers.enumerated() {
            g.sublayers?.forEach { p in
                if let path = p as? CAShapeLayer {
                    if path.name == "change_x5F_color1" {
                        path.fillColor = UIColor(hex: items[0].themeColor ?? "#FFFFFF").cgColor
                        pathsColor[0] = path
                        shadowsColor[0] = g.sublayers!.count > i + 1 ? g.sublayers![i + 1] : nil
                    } else if path.name == "change_x5F_color2" {
                        path.fillColor = UIColor(hex: items[1].themeColor ?? "#FFFFFF").cgColor
                        pathsColor[1] = path
                        shadowsColor[1] = g.sublayers!.count > i + 1 ? g.sublayers![i + 1] : nil
                    } else if path.name == "change_x5F_color3" {
                        path.fillColor = UIColor(hex: items[2].themeColor ?? "#FFFFFF").cgColor
                        pathsColor[2] = path
                        shadowsColor[2] = g.sublayers!.count > i + 1 ? g.sublayers![i + 1] : nil
                    }
                }
            }
        }
        
        svgView.image = svg.uiImage
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_drying")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    var oX: CGFloat = 0, oY: CGFloat = 0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? SVGImageView, let folder = getFolder() else { return }
        
        switch gesture.state {
        case .began:
            guard viewsAdded < items.count else { return }
            dX = view.frame.minX - gesture.location(in: view.superview).x
            dY = view.frame.minY - gesture.location(in: view.superview).y
            oX = view.frame.minX
            oY = view.frame.minY
            svgViewDragInside.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(items[viewsAdded].path!)").uiImage
            playSound("\(getLanguage())/topics/\(folder)/\(items[viewsAdded].path!.replacingOccurrences(of: ".svg", with: ""))")
            if svgViewDragInside.transform == .identity {
                let scale = svgView1.frame.width / svgViewDragInside.frame.width
                svgViewDragInside.transform = CGAffineTransform(scaleX: scale, y: scale)
            }
            pathsColor[viewsAdded]?.opacity = 0.01
            shadowsColor[viewsAdded]?.opacity = 0.001
            svgView.image = washingMachineSVG?.uiImage
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            Utils.vibrate()
            
        case .changed:
            let translation = gesture.translation(in: view)
            view.center = CGPoint(
                x: view.center.x + translation.x,
                y: view.center.y + translation.y
            )
            gesture.setTranslation(.zero, in: view)
            
        case .ended:
            Utils.vibrate()
            let views = [svgView1, svgView2, svgView3]
            var minDistance = CGFloat.greatestFiniteMagnitude
            var minView: SVGImageView?
            
            for svgView in views {
                let vector = view.distanceFromCenterToCenter(to: svgView!)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    minView = svgView
                }
            }
            
            if let minView = minView, minDistance < minView.frame.width / 2, minView.image == nil {
                playSound("effect/word puzzle drop")
                viewsAdded += 1
                var delay: TimeInterval = playSound(viewsAdded == 3 ? "effect/answer_end" : "effect/answer_correct")
                if viewsAdded == 3 {
                    pauseGame(stopMusic: false)
                    scheduler.schedule(after: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.animateCoinIfCorrect(view: self.coinView)
                    }
                    delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
                    scheduler.schedule(after: delay) { [weak self] in
                        self?.finishGame()
                    }
                }
                view.moveToCenter(of: minView, duration: 0.2) {
                    [weak self] _ in
                    guard let self = self else { return }
                    minView.image = svgViewDragInside.image
                    svgViewDragInside.image = nil
                    view.frame.origin = CGPoint(x: self.oX, y: self.oY)
                    view.transform = .identity//CGAffineTransform(scaleX: scale, y: scale)
                    //view.alpha = 0.01
                }
            } else {
                playSound("effect/slide2")
                setGameWrong()
                let scale = svgView1.frame.width / view.frame.width
                UIView.animate(withDuration: 0.5, animations: {
                    view.frame.origin = CGPoint(x: self.oX, y: self.oY)
                    view.transform = .identity//CGAffineTransform(scaleX: scale, y: scale)
                    view.alpha = 0.01
                }, completion: { _ in
                    self.svgViewDragInside.image = nil
                    view.alpha = 1
                    self.pathsColor[self.viewsAdded]?.opacity = 1
                    self.shadowsColor[self.viewsAdded]?.opacity = 0.1
                    self.svgView.image = self.washingMachineSVG?.uiImage
                })
            }
            
        default:
            break
        }
    }
}
