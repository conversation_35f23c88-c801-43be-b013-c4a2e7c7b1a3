//
//  toancoban_list_pheptinhchanlechu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit

class toancoban_list_pheptinhchanlechu: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var a: Int = 0
    private var b: Int = 0
    private var textLeft: UILabel!
    private var textRight: UILabel!
    private var mainContainer: UIView!
    private let bottomBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let topContainer = UIView()
        view.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        let top2Container = UIView()
        topContainer.addSubview(top2Container)
        top2Container.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        
        mainContainer = UIView()
        top2Container.addSubview(mainContainer)
        mainContainer.makeViewCenterAndKeep(ratio: 4.3)
        
        let signContainer = UIView()
        mainContainer.addSubview(signContainer)
        signContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(mainContainer).multipliedBy(3.3 / 4.3) // Ratio 3.3:4.3
        }
        
        textLeft = AutosizeLabel()
        textLeft.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textLeft.font = .Freude(size: 20)
        textLeft.textAlignment = .center
        textLeft.adjustsFontSizeToFitWidth = true
        textLeft.minimumScaleFactor = 0.1
        signContainer.addSubview(textLeft)
        textLeft.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let numberContainer = UIImageView()
        numberContainer.image = Utilities.SVGImage(named: "math_diendau_selected")
        mainContainer.addSubview(numberContainer)
        numberContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(numberContainer.snp.height) // Ratio 1:1
        }
        
        textRight = AutosizeLabel()
        textRight.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textRight.font = .Freude(size: 20)
        textRight.textAlignment = .center
        textRight.adjustsFontSizeToFitWidth = true
        textRight.minimumScaleFactor = 0.1
        numberContainer.addSubview(textRight)
        textRight.snp.makeConstraints { make in
            make.height.width.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        
        bottomBg.alpha = 0
        bottomBg.backgroundColor = UIColor(red: 197/255, green: 247/255, blue: 255/255, alpha: 1) // #C5F7FF
        view.addSubview(bottomBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 197/255, green: 247/255, blue: 255/255, alpha: 1) // #C5F7FF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        bottomBg.snp.makeConstraints { make in
            make.left.bottom.right.equalTo(self)
            make.top.equalTo(gridLayout)
        }
        /*
        let dragView = UIView()
        view.addSubview(dragView)
        dragView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
         */
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 1 + Int.random(in: 0..<99) // 1 to 99
            b = 1 + Int.random(in: 0..<199) - 99 // -98 to 99
            if a + b > 0 && a + b <= 99 && b <= 99 { break }
        }
        textLeft.text = formatSum(a: a, b: b)
        let answer = (a + b) % 2 == 0 ? "chẵn" : "lẻ"
        
        let list = ["chẵn", "lẻ"]
        var views: [UIView] = []
        for s in list {
            let view = createNumberItem(value: s)
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
    }
    
    private func formatSum(a: Int, b: Int) -> String {
        return b >= 0 ? "\(a % 2 == 0 ? "chẵn" : "lẻ") + \(b) =" : "\(a % 2 == 0 ? "chẵn" : "lẻ") - \(-b) ="
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), a % 2 == 0 ? "toan/toan_phep tinh chan le chu_mot so chan" : "toan/toan_phep tinh chan le chu_mot so le", b >= 0 ? "toan/cộng" : "toan/trừ", "topics/Numbers/\(abs(b))", "toan/toan_phep tinh chan le chu_thi duoc")
        mainContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.mainContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.mainContainer.transform = .identity // translationY(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.bottomBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(a % 2 == 0 ? "toan/toan_phep tinh chan le chu_mot so chan" : "toan/toan_phep tinh chan le chu_mot so le", b >= 0 ? "toan/cộng" : "toan/trừ", "topics/Numbers/\(abs(b))", "toan/toan_phep tinh chan le chu_thi duoc")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid Item
    private func createNumberItem(value: String) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = value
        textNumber.textColor = UIColor(red: 104/255, green: 193/255, blue: 255/255, alpha: 1) // #68C1FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value == "chẵn" ? 0 : 1
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag == 0 ? "chẵn" : "lẻ"
        let answer = (a + b) % 2 == 0 ? "chẵn" : "lẻ"
        let correct = value == answer
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound("effect/answer_end")
            delay += playSound(delay: delay, names: [
                a % 2 == 0 ? "toan/so chan" : "toan/so le",
                b >= 0 ? "toan/cộng" : "toan/trừ",
                "topics/Numbers/\(abs(b))",
                (a + b) % 2 == 0 ? "toan/bang mot so chan" : "toan/bang mot so le"
            ])
            textRight.text = (a + b) % 2 == 0 ? "chẵn" : "lẻ"
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay + 1.0) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: ["effect/answer_wrong", getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 104/255, green: 193/255, blue: 255/255, alpha: 1) // #68C1FF
            }
        }
    }
}
