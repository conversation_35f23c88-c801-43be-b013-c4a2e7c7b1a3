//
//  vocab_list_spelltheword.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/5/25.
//

import UIKit
import SnapKit
import SVGKit
import AVFAudio
import AnyCodable

class vocab_list_spelltheword: phonics_list_spelltheword {
    // MARK: - Game Logic
    override func updateData() {
        data = Animation()
        data?.text = "spell the word#@"
        data?.values = []
        data?.sounds = []
        data?.paths = []
        
        for listItem in getListItems()! {
            data?.values?.append(AnyCodable(listItem.name.en))
            data?.sounds?.append("en/topics/\(getFolder() ?? "")/\(listItem.path!.replacingOccurrences(of: ".svg", with: ""))")
            data?.paths?.append("topics/\(getFolder() ?? "")/\(listItem.path!)")
        }
        
        super.updateData()
    }
}
