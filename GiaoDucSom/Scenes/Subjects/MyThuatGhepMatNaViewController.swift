//
//  MyThuatGhepMatNaViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/6/25.
//


import UIKit
import SnapKit
import SVGKit

class MyThuatGhepMatNaViewController: BaseViewController {
    // MARK: - Properties
    
    private var musics: [Lyric] = []
    private var selectedIndex: Int = -1
    private var column: Int = 0
    private var masks: Set<String> = []
    private var playedMasks: Set<String> = []
    
    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        return UICollectionView(frame: .zero, collectionViewLayout: layout)
    }()
    private let btnClose = KUButton()
    private let gradientView = FadeGradientView()
    private let txtTitle = AutosizeLabel()
    
    var files : [String] = []
    
    override var backgroundMusicFile: String? {
        "bg_other"
    }
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(red: 254/255, green: 234/255, blue: 202/255, alpha: 1) // #FEEACA
        files = StorageManager.manager.list(path: "Animal Heads").sorted { o1, o2 in
            let paddedO1 = padText(o1)
            let paddedO2 = padText(o2)
            return paddedO1 < paddedO2
        }.map { "Animal Heads/\($0)" }
        unlockMask(mask: files.first ?? "")
        masks = loadUnlockedMask()
        playedMasks = loadPlayedMask()
        
        setupUI()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if selectedIndex != -1 {
            playedMasks = loadPlayedMask()
            collectionView.reloadData()
        }
    }
    
    // MARK: - Initialization
    private func setupUI() {
        view.removeAllSubviews()
        view.backgroundColor = UIColor(hex: "#DD9FF8")
        // btnClose
        btnClose.setImage(Utilities.SVGImage(named: "btn_back_mythuat_vetungnet"), for: .normal)
        btnClose.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.width.equalTo(btnClose.snp.height)
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
        }
        btnClose.snpLeftTop(ratio: 1)
        // txtTitle
        txtTitle.text = "Ghép mặt nạ"
        txtTitle.font = .Freude(size: 20)
        txtTitle.textColor = .white
        txtTitle.textAlignment = .left
        txtTitle.overrideTextAlignment = false
        txtTitle.numberOfLines = 1
        view.addSubview(txtTitle)
        txtTitle.snp.makeConstraints { make in
            make.centerY.equalTo(btnClose)
            make.width.equalTo(view.snp.width).multipliedBy(0.45)
            make.height.equalTo(view.snp.height).multipliedBy(0.06)
        }
        txtTitle.snpLeftTop(ratio: 3.0)
        
        
                
        // collectionView
        collectionView.backgroundColor = .clear
        collectionView.clipsToBounds = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(GhepMatNaCell.self, forCellWithReuseIdentifier: "GhepMatNaCell")
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            let column = Utilities.isIPad ? 4 : 3
            let dpToPx = view.bounds.width / CGFloat(column) * 0.92
            layout.itemSize = CGSize(width: dpToPx, height: dpToPx * 616 / 560)
            layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        }
        view.addSubview(collectionView)
        
        
        // gradientView
        gradientView.setupGradient(color: .color(hex: "#DD9FF8"))
        view.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(gradientView.snp.width).dividedBy(30)
            make.top.equalTo(btnClose.snp.bottom)
        }
        
        let viewTop = UIView()
        viewTop.backgroundColor = .color(hex: "#DD9FF8")
        view.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(gradientView.snp.top)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(gradientView.snp.bottom)
            make.left.equalTo(btnClose)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let coinView = CoinView()
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.top.bottom.equalTo(btnClose)
        }
        coinView.snpRightTop(ratio: 1)
        view.bringSubviewToFront(btnClose)
        view.bringSubviewToFront(txtTitle)
    }
    
    private func padText(_ text: String) -> String {
        if text.count == 5 {
            return "0" + text
        }
        return text
    }
    
    // MARK: - Data Management
    private func loadUnlockedMask() -> Set<String> {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        return Set(defaults.stringArray(forKey: "mask_unlocked_\(profileId)") ?? [])
    }
    
    private func unlockMask(mask: String) {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        var masks = Set(defaults.stringArray(forKey: "mask_unlocked_\(profileId)") ?? [])
        masks.insert(mask)
        defaults.set(Array(masks), forKey: "mask_unlocked_\(profileId)")
        self.masks = masks
    }
    
    private func loadPlayedMask() -> Set<String> {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        return Set(defaults.stringArray(forKey: "mask_played_\(profileId)") ?? [])
    }
    
    private func playMask(mask: String) {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        var masks = Set(defaults.stringArray(forKey: "mask_played_\(profileId)") ?? [])
        masks.insert(mask)
        defaults.set(Array(masks), forKey: "mask_played_\(profileId)")
        self.playedMasks = masks
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        navigationController?.popViewController(animated: true)
    }
    
    // MARK: - Helper Methods
    private func getSpanCount() -> Int {
        let width = view.frame.width
        let oneDp = 1.0 // Giả lập 1dp, có thể cần điều chỉnh theo scale
        var spanCount = Int(round(width / oneDp / 360))
        if spanCount < 3 { spanCount = 3 }
        column = spanCount
        return spanCount
    }
}


extension MyThuatGhepMatNaViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return files.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GhepMatNaCell", for: indexPath) as! GhepMatNaCell
        let item = files[indexPath.item]
        cell.configure(with: item, isUnlocked: self.masks.contains(item), isPlayed: self.playedMasks.contains(item), price: CoinManager.PRICE_MASK)
        cell.onTap = { [weak self] in
            guard let self = self else { return }
            let unlocked = self.masks.contains(item)
            if !unlocked {
                return
            }
            self.selectedIndex = indexPath.item
            let gameVC = SingleGameActivity()
            gameVC.game = mythuat_list_ghepmatna.className
            gameVC.data = item//.replacingOccurrences(of: "Animal Heads/", with: "").replacingOccurrences(of: ".svg", with: "")
            self.navigationController?.pushViewController(gameVC, animated: true)
        }
        cell.onBuy = { [weak self] in
            guard let self = self else { return }
            
            self.playSound("vi/popup_becodongydung", "vi/topics/Numbers/\(CoinManager.PRICE_MASK)", "vi/popup_xu_matna")
            let dialog = MessageDialogView()
                .setTitle("Mua mặt nạ mới")
                .setMessage("Bé có đồng ý dùng \(CoinManager.PRICE_MASK) xu để mua mặt nạ này không?")
                .setImageResId("icon_buy")
                .setButtonLaterText("Để sau")
                .setButtonOkText("Mua")
                .setShowLaterButton(true)
                .setShowOkButton(true)
                .setListener { buttonIndex in
                    if buttonIndex == .ok {
                        self.scheduler.clearAll()
                        if CoinManager.shared.useCoin(CoinManager.PRICE_MASK) {
                            self.playSound("effect/unlock item")
                            self.unlockMask(mask: item)
                            self.collectionView.reloadData()
                            self.scheduler.schedule(delay: 1.0) { [weak self] in
                                guard self != nil else { return }
                                // Animate coin removal if needed
                            }
                        } else {
                            let noCoinDialog = MessageDialogView()
                                .setTitle("Không đủ xu")
                                .setMessage("Bé không đủ xu để mua hình này")
                                .setImageResId("icon_no_coin")
                            noCoinDialog.showIn(self.view)
                        }
                    }
                }
            dialog.showIn(self.view)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let spanCount = Utilities.isIPad ? 4 : 3
        let totalSpacing = CGFloat(spanCount - 1) * 10 + 20 // 10 là minimumInteritemSpacing, 20 là padding trái + phải từ sectionInset
        let width = (collectionView.bounds.width - totalSpacing) / CGFloat(spanCount)
        return CGSize(width: width, height: width / 0.8)
    }
}



// MARK: - GhepMatNa Cell
class GhepMatNaCell: UICollectionViewCell {
    private let viewBackground = UIImageView()
    private let textName = HeightRatioTextView()
    private let svgThumbnail = SVGKFastImageView(svgkImage: nil)!
    private let textQuestion = HeightRatioTextView()
    private let btnBuy = KUButton()
    private let textItemCoin = HeightRatioTextView()
    
    var onTap: (() -> Void)?
    var onBuy: (() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupView() {
        let ratioView = UIView()
        contentView.addSubview(ratioView)
        ratioView.makeViewCenterAndKeep(ratio: 0.8)
        let bgView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "mythuat_tranhtomau_list_bg"))!
        ratioView.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.width.equalTo(bgView.snp.height).multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.9)
        }
        
        textName.text = "Name"
        textName.textColor = UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1) // #f39a71
        textName.font = .Freude(size: 16)
        textName.textAlignment = .center
        textName.setHeightRatio(0.7)
        bgView.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.15)
            make.left.right.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.7) // Bias 0.85
        }
        
        svgThumbnail.contentMode = .scaleAspectFit
        bgView.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.5)
            make.width.equalTo(svgThumbnail.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        svgThumbnail.waitForLayout {
            self.svgThumbnail.snapToVerticalBias(verticalBias: 0.25)
        }
        
        textQuestion.text = "?"
        textQuestion.textColor = UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1) // #f39a71
        textQuestion.font = .Freude(size: 30)
        textQuestion.textAlignment = .center
        textQuestion.setHeightRatio(0.8)
        bgView.addSubview(textQuestion)
        textQuestion.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.5)
            make.width.equalTo(textQuestion.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        textQuestion.waitForLayout {
            self.textQuestion.snapToVerticalBias(verticalBias: 0.25)
        }
        
        btnBuy.setImage(Utilities.SVGImage(named: "mythuat_hopmau_btn_buy"), for: .normal)
        btnBuy.isUserInteractionEnabled = true
        bgView.addSubview(btnBuy)
        btnBuy.snp.makeConstraints { make in
            make.width.equalTo(bgView).multipliedBy(0.8)
            make.height.equalTo(btnBuy.snp.width).multipliedBy(18.0 / 40.0) // Ratio 40:18
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
        }
        btnBuy.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            btnBuy.snapToVerticalBias(verticalBias: 0.9)
        }
        
        textItemCoin.text = "0"
        textItemCoin.textColor = UIColor(red: 255/255, green: 247/255, blue: 0, alpha: 1) // #fff700
        textItemCoin.font = .Freude(size: 14)
        textItemCoin.textAlignment = .center
        textItemCoin.setHeightRatio(0.7)
        btnBuy.addSubview(textItemCoin)
        textItemCoin.snp.makeConstraints { make in
            make.width.equalTo(btnBuy).multipliedBy(0.5)
            make.height.equalTo(btnBuy).multipliedBy(0.8)
            //make.centerY.equalToSuperview().multipliedBy(0.6) // Bias 0.3
        }
        textItemCoin.waitForLayout {
            self.textItemCoin.snapToVerticalBias(verticalBias: 0.3)
            self.textItemCoin.snapToHorizontalBias(horizontalBias: 0.8)
        }
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cellTapped))
        contentView.addGestureRecognizer(tapGesture)
        
        let buyGesture = UITapGestureRecognizer(target: self, action: #selector(buyTapped))
        btnBuy.addGestureRecognizer(buyGesture)
    }
    
    func configure(with data: String, isUnlocked: Bool, isPlayed: Bool, price: Int) {
        let name = data.replacingOccurrences(of: "Animal Heads/", with: "").replacingOccurrences(of: ".svg", with: "")
        textName.text = name
        btnBuy.isHidden = isUnlocked
        textQuestion.isHidden = isUnlocked
        svgThumbnail.isHidden = !isUnlocked
        textItemCoin.text = "\(price)"
        
        if isUnlocked {
            let image = Utilities.GetSVGKImage(named: "\(data)")
            if !isPlayed {
                let color = UIColor.color(hex: "#f39a71")
                image.caLayerTree.sublayers?.forEach { group in
                    group.sublayers?.forEach { path in
                        path.setFillColor(color: color)
                    }
                }
            }
            svgThumbnail.image = image
            //svgThumbnail.setColor(isPlayed ? 0 : UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1).rgbValue)
        }
    }
    
    @objc private func cellTapped() {
        onTap?()
    }
    
    @objc private func buyTapped() {
        onBuy?()
    }
}

// Extension hỗ trợ UIColor
extension UIColor {
    var rgbValue: Int {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        return (Int(red * 255) << 16) | (Int(green * 255) << 8) | Int(blue * 255)
    }
    
    convenience init(rgb: Int) {
        self.init(
            red: CGFloat((rgb >> 16) & 0xFF) / 255,
            green: CGFloat((rgb >> 8) & 0xFF) / 255,
            blue: CGFloat(rgb & 0xFF) / 255,
            alpha: 1
        )
    }
}
