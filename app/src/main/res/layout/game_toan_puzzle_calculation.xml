<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FDD08B"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/nhanbiet_bg_puzzle" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/item_container_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/grid_layout"
        app:layout_constraintVertical_bias="0" />
    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/grid_layout"
        app:layout_constraintVertical_bias="0" />


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>