<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8F0B7"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:splitMotionEvents="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_boy"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_sosanhkeo_boy"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="978:734"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.4" />

        <ImageView
            android:id="@+id/image_girl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_sosanhkeo_girl"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="978:734"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.4" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/girl_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/image_girl"
            app:layout_constraintRight_toRightOf="@id/image_girl"
            app:layout_constraintTop_toBottomOf="@id/image_girl" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/boy_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="@id/image_boy"
            app:layout_constraintRight_toRightOf="@id/image_boy"
            app:layout_constraintTop_toBottomOf="@id/image_boy" />

        <TextView
            android:id="@+id/text_girl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="5"
            android:textColor="#B843DF"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.12" />

        <TextView
            android:id="@+id/text_boy"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="5"
            android:textColor="#0EAFE1"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.12" />

        <TextView
            android:id="@+id/text_compare"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text=">"
            android:textColor="#87D657"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.12" />

        <View
            android:id="@+id/body_girl"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3" />

        <View
            android:id="@+id/body_boy"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.3" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>