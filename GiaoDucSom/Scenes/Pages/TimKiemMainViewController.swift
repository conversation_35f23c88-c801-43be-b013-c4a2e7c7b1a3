//
//  TimKiemMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/5/25.
//


import UIKit
import SnapKit
import SVGKit
import MBProgressHUD

class TimKiemMainViewController: SubjectMainViewController {
    // MARK: - Properties
    private static let vietnameseMap: [Character: Character] = {
        let original = "áàảãạăắằẳẵặâấầẩẫậđéèẻẽẹêếềểễệíìỉĩịóòỏõọôốồổỗộơớờởỡợúùủũụưứừửữựýỳỷỹỵ"
        let replacement = "aaaaaaaaaaaaaaaaadeeeeeeeeeeeiiiiiooooooooooooooooouuuuuuuuuuuyyyyy"
        var map: [Character: Character] = [:]
        for (o, r) in zip(original, replacement) {
            map[o] = r
            map[o.uppercased().first!] = r.uppercased().first!
        }
        return map
    }()
    let timeoutTimer = TimeoutTimer()
    private let btnRegister = KUButton()
    private let searchBar = UIView()
    private let edtSearch = UITextField()
    private var games: [Game] = []
    private var counts: [Int] = []
    private var spans: [Int] = []
    private var column: Int = 3
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#8585FF")
        backgroundColor = UIColor(hex: "#8585FF")
        titleColor = UIColor(hex: "#4F4FD3")
        //hideStatusBar()
        initUI()
        initData()
        setupSearch()
        timeoutTimer.duration = 0.5
        timeoutTimer.onActived = {
            [weak self] in
            guard let self = self else { return }
            self.search(keyword: self.edtSearch.text?.lowercased())
        }
    }
    override func setupData() {
        // Section 1: Âm sắc
        let amSacSection = SectionData(
            header: "Âm sắc",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_thiennhien", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_connguoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_dongvat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhantao", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhaccu", isTapDocGame: false)
            ]
        )
        
        // Section 2: Nhạc lý
        let nhacLySection = SectionData(
            header: "Nhạc lý",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_timnotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_noinotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_notnhactrenkhuong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_notnhactrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_kyhieutrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_keonotnhacvaokhuong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_sapxepnottangdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_xuongamtrenphimdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_phachnotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_timgiaidieu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_phachdaulang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_truongdonotnhac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_nhipdieu44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_sochinhip", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachnhip44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip24", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip34", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_gophachdannhip44", isTapDocGame: false),
                TroChoiNgonNguItem(data: "amnhac_list_choidantudo", isTapDocGame: false)
            ]
        )
        
        // Section 3: Giai điệu
        let giaiDieuSection = SectionData(
            header: "Giai điệu",
            items: [
                TroChoiNgonNguItem(data: "amnhac_list_baihatyeuthich", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            amSacSection,
            nhacLySection,
            giaiDieuSection
        ]
    }
    // MARK: - Setup UI
    private func initUI() {
        //view.removeAllSubviews()
        
        // gradient_view
        let gradientView = FadeGradientView()
        gradientView.setupGradient(color: .color(hex: "#8585FF"))
        view.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(gradientView.snp.width).dividedBy(30)
        }
        
        // Background Top
        let topBackground = UIView()
        topBackground.backgroundColor = UIColor(hex: "#8585FF")
        view.addSubview(topBackground)
        topBackground.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalTo(gradientView.snp.top)
        }
                
        
        // btn_register
        btnRegister.setImage(Utilities.SVGImage(named: "btn_premium"), for: .normal)
        view.addSubview(btnRegister)
        btnRegister.snp.makeConstraints { make in
            make.width.equalTo(btnRegister.snp.height).multipliedBy(470.0/139.7)
            make.top.equalTo(backButton)
            make.bottom.equalTo(backButton)
        }
        btnRegister.snpRightTop(ratio: 1)
        
        // search_bar
        searchBar.backgroundColor = UIColor(hex: "#3737B7")
        searchBar.layer.cornerRadius = view.bounds.height * 0.05
        searchBar.layer.masksToBounds = true
        view.addSubview(searchBar)
        searchBar.snp.makeConstraints { make in
            make.right.equalTo(btnRegister.snp.left).offset(-20)
            make.top.bottom.equalTo(backButton)
        }
        searchBar.snpLeftTop(ratio: 3.5)
        //searchBar.snpRightTop(ratio: 1)
        
        // Search Icon
        let searchIcon = SVGImageView(frame: .zero)
        searchIcon.SVGName = "home_search_bar"
        searchIcon.contentMode = .scaleAspectFit
        searchBar.addSubview(searchIcon)
        searchIcon.snp.makeConstraints { make in
            make.height.equalTo(searchBar.snp.height)
            make.width.equalTo(searchIcon.snp.height)
            make.left.top.bottom.equalToSuperview()
        }
        
        // edt_search
        edtSearch.attributedPlaceholder = NSAttributedString(
            string: "Tìm kiếm môn học, trãi nghiệm, trò chơi...",
            attributes: [.foregroundColor: UIColor(hex: "#ACACFC")]
        )
        edtSearch.font = .Freude(size: 20)
        edtSearch.textColor = UIColor(hex: "#ACACFC")
        edtSearch.backgroundColor = .clear
        edtSearch.returnKeyType = .search
        edtSearch.autocorrectionType = .no
        edtSearch.spellCheckingType = .no
        //edtSearch.delegate = self
        searchBar.addSubview(edtSearch)
        edtSearch.snp.makeConstraints { make in
            make.left.equalTo(searchIcon.snp.right)
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(searchBar.snp.width).multipliedBy(0.1)
        }
                    
        // Hide keyboard
        setupMoveWhenShowHideKeyboard()
        hideKeyboardWhenTappedAround()
    }
    //var games: [Game] = []
    // MARK: - Setup Data
    private func initData() {
        guard let stickerSizeUrl = Bundle.main.url(forResource: "games", withExtension: "json"),
              let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
              var games = try? JSONDecoder().decode([Game].self, from: stickerSizeData) else {
            print("Error loading sticker_size.json")
            return
        }
        for i in 0..<games.count {
            var game = games[i]
            game.search_text = "\(game.search_text ?? "") \(game.name.en ?? "") \(game.name.vi ?? "") \(game.id) \(game.difficulty ?? "")".lowercased()
            game.search_text_nosign = Self.removeVietnameseAccents(game.search_text ?? "")
            self.games.append(game)
        }
        search(keyword: nil)
    }
    
    private func setupSearch() {
        NotificationCenter.default.addObserver(self, selector: #selector(textFieldDidChange), name: UITextField.textDidChangeNotification, object: edtSearch)
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        navigationController?.popViewController(animated: true)
        dismiss(animated: true)
    }
    
    @objc private func textFieldDidChange() {
        timeoutTimer.schedule()
    }
    
    // MARK: - Helper Methods
    private static func removeVietnameseAccents(_ input: String) -> String {
        return String(input.map { vietnameseMap[$0] ?? $0 })
    }
    
    private func search(keyword: String?) {
        var keyword = keyword?.lowercased() ?? ""
        let noSignKeyword = Self.removeVietnameseAccents(keyword)
        let findSign = noSignKeyword != keyword
        keyword = findSign ? keyword : noSignKeyword
        let isEmpty = keyword.trimmingCharacters(in: .whitespaces).isEmpty
        
        sections.removeAll()
        counts.removeAll()
        spans.removeAll()
        var items: [TroChoiNgonNguItem] = []
        var section = SectionData(header: isEmpty ? "Trò chơi mới" : "Tìm kiếm cho: \(keyword)", items: items)
        for game in games {
            if game.id.hasPrefix("toannangcao") { continue }
            if isEmpty && GameHelper.shared.gameCompletedByGameId(id: game.id) { continue }
            if isEmpty {
                items.append(TroChoiNgonNguItem(data: game.id))
                continue
            }
            if findSign && (game.search_text?.contains(keyword) ?? false) {
                items.append(TroChoiNgonNguItem(data: game.id))
            } else if !findSign && (game.search_text_nosign?.contains(keyword) ?? false) {
                items.append(TroChoiNgonNguItem(data: game.id))
            }
        }
        section.items = items
        sections.append(section)
        collectionView.reloadData()
    }
    
    private func getSpanCount() -> Int {
        let width = view.bounds.width
        let oneDp = 1.0 / UIScreen.main.scale
        var spanCount = Int(width / oneDp / 360)
        if spanCount < 3 { spanCount = 3 }
        column = spanCount
        return spanCount
    }
}
