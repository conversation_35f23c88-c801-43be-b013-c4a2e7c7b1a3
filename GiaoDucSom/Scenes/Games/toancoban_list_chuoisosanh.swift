//
//  toancoban_list_chuoisosanh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_chuoisosanh: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var itemContainer: UIView!
    private var lines: [[Int]] = []
    private var centerValues: [Int] = []
    private var containerLayout: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var text1: UILabel!
    private var text2: UILabel!
    private var text3: UILabel!
    private var text4: UILabel!
    private var snapViews: [UIView?] = [nil, nil, nil, nil]
    private var check: [Bool?] = [nil, nil, nil, nil]
    private var coinView: UIView!
    private let rightBg = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        
        containerLayout = UIView()
        containerLayout.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: containerLayout, percentInset: 0)
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        containerLayout.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        containerLayout.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(containerLayout).multipliedBy(0.5)
        }
        rightBg.snp.makeConstraints { make in
            make.right.top.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        let leftView = UIView()
        containerLayout.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        let left5View = UIView()
        leftView.addSubviewWithPercentInset(subview: left5View, percentInset: 5)
        
        itemContainer = UIView()
        //itemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        left5View.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 0.8)
        
        let line1 = createLineView()
        itemContainer.addSubview(line1)
        line1.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.23)
            make.left.right.equalToSuperview()
        }
        
        let line2 = createLineView()
        itemContainer.addSubview(line2)
        line2.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.23)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.5)
        }
        
        let line3 = createLineView()
        itemContainer.addSubview(line3)
        line3.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.23)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.75)
        }
        
        let line4 = createLineView()
        itemContainer.addSubview(line4)
        line4.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.23)
            make.left.right.bottom.equalToSuperview()
        }
        
        text1 = line1.subviews[1].subviews[0] as? UILabel
        text2 = line2.subviews[1].subviews[0] as? UILabel
        text3 = line3.subviews[1].subviews[0] as? UILabel
        text4 = line4.subviews[1].subviews[0] as? UILabel
        
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
                
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        containerLayout.addGestureRecognizer(panGesture)
        containerLayout.bringSubviewToFront(gridLayout)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        lines = generateData()
        let leftTexts = ["", "", "< "]
        let rightTexts = [" <", "", ""]
        let textViews = [text1!, text2!, text3!, text4!]
        
        for (i, lineView) in itemContainer.subviews.enumerated() {
            let line = lines[i]
            for j in 0..<3 {
                let textView = j == 1 ? lineView.subviews[j].subviews[0] as! UILabel : lineView.subviews[j] as! UILabel
                let value = line[j] + 1
                textView.text = (line[j] == 0 || line[j] == 8) && Bool.random() ? "" : "\(leftTexts[j])\(value)\(rightTexts[j])"
                textView.tag = value
                if j == 1 { textView.textColor = .white }
            }
        }
        
        var views: [UIView] = []
        for value in centerValues {
            let view = createItemNumber(value: value + 1)
            view.tag = value + 1
            views.append(view)
        }
        gridLayout.itemRatio = 346.0 / 373.0
        gridLayout.columns = 2
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views.shuffled())
    }
    
    override func createGame() {
        super.createGame()
        gridLayout.alpha = 0
        var delay = playSound(openGameSound(), "toan/toan_chuoi so sanh")
        //itemContainer.frame = containerLayout.frame
        itemContainer.moveToCenter(of: containerLayout)
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 1.1
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_chuoi so sanh")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: containerLayout)
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
                let scale = text1.frame.height / currentView.frame.height
                UIView.animate(withDuration: 0.2) {
                    currentView.subviews[0].transform = CGAffineTransform(scaleX: scale, y: scale)
                }
                for i in 0..<snapViews.count {
                    if snapViews[i] == currentView {
                        snapViews[i] = nil
                        check[i] = nil
                    }
                }
                gridLayout.bringSubviewToFront(currentView)
                playSound("effect/cungchoi_pick\(random(1,2))")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = location.x + dX
                let newY = location.y + dY
                //currentView.frame = CGRect(x: newX, y: newY, width: currentView.frame.width, height: currentView.frame.height)
                let tran = gesture.translation(in: self)
                currentView.transform.tx += tran.x
                currentView.transform.ty += tran.y
                gesture.setTranslation(.zero, in: self)
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                var minIndex = -1
                let textViews = [text1!, text2!, text3!, text4!]
                for (i, textView) in textViews.enumerated() {
                    let distance = currentView.distanceFromCenterToCenter(to: textView)
                    let d = hypot(distance.x, distance.y)
                    if d < minDistance {
                        minDistance = d
                        targetView = textView
                        minIndex = i
                    }
                }
                if let target = targetView, minDistance < target.frame.height / 2 {
                    playSound("effect/word puzzle drop")
                    if let snapView = snapViews[minIndex] {
                        UIView.animate(withDuration: 0.8) {
                            snapView.transform = .identity
                            snapView.subviews[0].transform = .identity
                        }
                        if let textNumber = snapView.viewWithTag(R6.id.text_number) as? UILabel {
                            textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
                        }
                    }
                    currentView.moveToCenter(of: target, duration: 0.2)
                    snapViews[minIndex] = currentView
                    guard let textNumber = currentView.viewWithTag(R6.id.text_number) as? UILabel else { return }
                    let value = currentView.tag
                    let correct = lines[minIndex][0] + 1 < value && value < lines[minIndex][2] + 1
                    textNumber.textColor = correct ? UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) : UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #74B6FF or #FF7760
                    check[minIndex] = correct
                    if check.allSatisfy({ $0 != nil }) {
                        let correctAll = check.allSatisfy { $0 == true }
                        if correctAll {
                            pauseGame()
                            animateCoinIfCorrect(view: coinView)
                            let delay = playSound(finishEndSounds())
                            scheduler.schedule(delay: delay) { [weak self] in
                                self?.finishGame()
                            }
                        } else {
                            setGameWrong()
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.8) {
                        currentView.transform = .identity
                        currentView.subviews[0].transform = .identity
                    }
                    playSound("effect/slide2")
                }
                self.currentView = nil
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        let adjustedX = x - gridLayout.frame.minX
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            if adjustedX >= child.frame.minX && adjustedX <= child.frame.maxX && y >= child.frame.minY && y <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func createLineView() -> UIView {
        let container = UIView()
        
        let leftText = HeightRatioTextView()
        leftText.setHeightRatio(0.7)
        leftText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        leftText.font = .Freude(size: 20)
        leftText.textAlignment = .center
        leftText.adjustsFontSizeToFitWidth = true
        leftText.minimumScaleFactor = 0.1
        container.addSubview(leftText)
        leftText.snp.makeConstraints { make in
            make.width.equalTo(leftText.snp.height) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.centerY.left.equalToSuperview()
        }
        
        let centerView = UIImageView()
        centerView.image = Utilities.SVGImage(named: "math_result_bg")
        container.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.width.equalTo(centerView.snp.height) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.center.equalToSuperview()
        }
        let centerText = HeightRatioTextView()
        centerText.setHeightRatio(0.7)
        centerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        centerText.font = .Freude(size: 20)
        centerText.textAlignment = .center
        centerText.adjustsFontSizeToFitWidth = true
        centerText.minimumScaleFactor = 0.1
        centerView.addSubviewWithInset(subview: centerText, inset: 0)
        
        let rightText = HeightRatioTextView()
        rightText.setHeightRatio(0.7)
        rightText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        rightText.font = .Freude(size: 20)
        rightText.textAlignment = .center
        rightText.adjustsFontSizeToFitWidth = true
        rightText.minimumScaleFactor = 0.1
        container.addSubview(rightText)
        rightText.snp.makeConstraints { make in
            make.width.equalTo(rightText.snp.height) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.centerY.right.equalToSuperview()
        }
        
        return container
    }
    
    private func createItemNumber(value: Int) -> UIView {
        let view = UIView()
        
        let viewBackground = UIImageView()
        viewBackground.tag = R6.id.view_background
        viewBackground.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            //make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.tag = R6.id.text_number
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 24)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            textNumber.snapToVerticalBias(verticalBias: 0.45)
        }
        
        //view.transform = CGAffineTransform(scaleX: 0, y: 0)
        //view.alpha = 0
        return view
    }
    
    private func generatePermutations(nums: [Int]) -> [[Int]] {
        var result: [[Int]] = []
        var current: [Int] = []
        var used = Array(repeating: false, count: nums.count)
        
        func backtrack() {
            if current.count == nums.count {
                result.append(current)
                return
            }
            for i in 0..<nums.count {
                if used[i] { continue }
                used[i] = true
                current.append(nums[i])
                backtrack()
                used[i] = false
                current.removeLast()
            }
        }
        
        backtrack()
        return result
    }
    
    private func generateData() -> [[Int]] {
        var lines: [[Int]] = []
        while true {
            lines = []
            for _ in 0..<4 {
                var line = (0..<9).shuffled().prefix(3).map { $0 }
                line.sort()
                lines.append(line)
            }
            var valid = true
            centerValues = []
            var values: [Int] = []
            for line in lines {
                for v in line {
                    if !values.contains(v) {
                        values.append(v)
                    }
                }
                if !centerValues.contains(line[1]) {
                    centerValues.append(line[1])
                }
            }
            if centerValues.count < 4 {
                valid = false
            }
            if valid {
                let permutations = generatePermutations(nums: centerValues)
                var count = 0
                for perm in permutations {
                    var validPermutation = true
                    for j in 0..<4 {
                        let line = lines[j]
                        if !(line[0] < perm[j] && perm[j] < line[2]) {
                            validPermutation = false
                            break
                        }
                    }
                    if validPermutation {
                        count += 1
                    }
                }
                if count > 1 {
                    valid = false
                }
            }
            if valid {
                var easyCount = 0
                for line in lines {
                    if line[2] - line[0] == 2 || (line[0] == 0 && line[2] == 8) {
                        easyCount += 1
                    }
                }
                if easyCount > 0 {
                    valid = false
                }
            }
            if valid {
                break
            }
        }
        return lines
    }
}

// MARK: - Supporting Structures

struct R6 {
    struct id {
        static let view_background = -1
        static let text_number = -2
    }
}
