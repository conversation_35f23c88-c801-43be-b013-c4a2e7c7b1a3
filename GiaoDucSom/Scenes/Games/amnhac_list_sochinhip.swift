//
//  amnhac_list_sochinhip.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 20/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class amnhac_list_sochinhip: MusicGameFragment {
    // MARK: - Properties
    private var duration: Int = 0
    private var gridLayout: MyGridView!
    private var notes: [UIImageView] = []
    private var notes1: [Int] = []
    private var notes2: [Int] = []
    private var meIndex: Int = 0
    private var imageBeat: UIImageView!
    private var music: [String] = []
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    var loadingSounds = false
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 237/255, green: 251/255, blue: 255/255, alpha: 1) // #EDFBFF
        
        let topContainer = UIView()
        topContainer.clipsToBounds = false
        view.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let leftContainer = UIView()
        topContainer.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        let leftPaddingView = UIView()
        leftContainer.addSubviewWithPercentInset(subview: leftPaddingView, percentInset: 5)
        
        let musicContainer = UIView()
        leftPaddingView.addSubview(musicContainer)
        musicContainer.makeViewCenterAndKeep(ratio: 5.3)
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        musicContainer.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(musicContainer).multipliedBy(0.98)
            make.height.equalTo(musicContainer)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg").withRenderingMode(.alwaysTemplate))
        barline.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(musicContainer)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg").withRenderingMode(.alwaysTemplate))
        endline.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(musicContainer)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg").withRenderingMode(.alwaysTemplate))
        clef.contentMode = .scaleAspectFill
        clef.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(musicContainer)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            clef.snapToHorizontalBias(horizontalBias: 0.05)
        }
        
        let nhip = UIImageView(image: Utilities.SVGImage(named: "music_giaidieu_soannhac"))
        musicContainer.addSubview(nhip)
        nhip.snp.makeConstraints { make in
            make.width.equalTo(nhip.snp.height) // Ratio 1:1
            make.height.equalTo(musicContainer).multipliedBy(0.7)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            nhip.snapToHorizontalBias(horizontalBias: 0.15)
        }
        
        imageBeat = UIImageView()
        imageBeat.contentMode = .scaleAspectFit
        imageBeat.isHidden = true
        musicContainer.addSubview(imageBeat)
        imageBeat.snp.makeConstraints { make in
            make.width.equalTo(imageBeat.snp.height).multipliedBy(0.4) // Ratio 0.4:1
            make.height.equalTo(musicContainer)
            make.centerY.equalToSuperview()
            make.centerX.equalTo(nhip)
        }
        
        
        let noteTags = ["note1", "note2", "note3", "note4", "note5", "note6", "note7"]
        for (index, tag) in noteTags.enumerated() {
            let note = UIImageView()
            note.contentMode = .scaleAspectFit
            note.stringTag = tag
            note.tintColor = UIColor.color(hex: "#74B6FF")
            musicContainer.addSubview(note)
            note.snp.makeConstraints { make in
                make.width.equalTo(note.snp.height).multipliedBy(0.4) // Ratio 0.4:1
                make.height.equalTo(musicContainer)
                make.centerY.equalToSuperview()
                make.right.equalToSuperview().multipliedBy(0.35 + CGFloat(index) * 0.1)
            }
            notes.append(note)
        }
        
        let bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        view.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let bottomBg = UIView()
        bottomBg.backgroundColor = UIColor.color(hex: "#D6FAFF")
        bottomContainer.addSubview(bottomBg)
        bottomBg.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.bottom.right.equalTo(self)
        }
        
        let bottomPaddingView = UIView()
        bottomContainer.addSubviewWithPercentInset(subview: bottomPaddingView, percentInset: 5)
        
        gridLayout = MyGridView()
        gridLayout.clipsToBounds = false
        bottomPaddingView.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 4)
    }
    
    func loadPiano() {
        loadingSounds = true
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in music {
                if noteToMusicId.keys.contains(note) { continue }
                if let url = Utilities.url(soundPath: note.hasSuffix("s") ? "effect/music/\(note)" : "effect/music/piano_\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
            loadingSounds = false
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        meIndex = Int.random(in: 0..<3)
        duration = meIndex == 0 ? 4 : meIndex == 1 ? 6 : 8
        imageBeat.image = Utilities.SVGImage(named: duration == 4 ? "music/note/beat_2_4.svg" : duration == 6 ? "music/note/beat_3_4.svg" : "music/note/beat_4_4.svg").withRenderingMode(.alwaysTemplate)
        imageBeat.tintColor = UIColor.color(hex: "#74B6FF")
        
        while true {
            notes1 = getNotes()
            notes2 = getNotes()
            if notes1.count + notes2.count >= 4 {
                break
            }
        }
        
        music.removeAll()
        for i in 0..<notes.count {
            notes[i].isHidden = false
            notes[i].alpha = 1
        }
        
        for i in 0..<notes1.count {
            let note = "\(notes1[i])\(i < notes1.count - 1 || notes1.count == 1 ? ["c1", "d1", "e1", "f1", "g1", "a1", "b1"].randomElement()! : ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "s"].randomElement()!)"
            let finalNote = (i == 0 && note.starts(with: "b")) ? "a" + note.dropFirst() : note
            notes[i].image = Utilities.SVGImage(named: finalNote.hasSuffix("s") ? "music/note/\(finalNote).svg" : "music/note/\(finalNote).svg").withRenderingMode(.alwaysTemplate)
            notes[i].tintColor = UIColor.color(hex: "#74B6FF")
            music.append(finalNote)
        }
        
        music.append("")
        notes[notes1.count].image = Utilities.SVGImage(named: "music/note/barline.svg").withRenderingMode(.alwaysTemplate)
        notes[notes1.count].tintColor = UIColor.color(hex: "#74B6FF")
        
        for i in 0..<notes2.count {
            let note = "\(notes2[i])\(i < notes2.count - 1 || notes2.count == 1 ? ["c1", "d1", "e1", "f1", "g1", "a1", "b1"].randomElement()! : ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "s"].randomElement()!)"
            notes[notes1.count + 1 + i].image = Utilities.SVGImage(named: "music/note/\(note).svg").withRenderingMode(.alwaysTemplate)
            notes[notes1.count + 1 + i].tintColor = UIColor.color(hex: "#74B6FF")
            music.append(note)
        }
        
        for i in notes1.count + 1 + notes2.count..<notes.count {
            notes[i].isHidden = true
        }
        
        let nhip = ["2/4", "3/4", "4/4"]
        var views: [UIView] = []
        for i in 0..<nhip.count {
            let view = createItemNumber(nhip: nhip[i])
            view.stringTag = "\(i)"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(view)
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
        }
        
        gridLayout.columns = 3
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "music/sochinhip"])
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
        loadPiano()
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay: TimeInterval = playSound("music/sochinhip")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame()
        let textNumber = sender.viewWithStringTag("text_number") as! HeightRatioTextView
        if index == meIndex {
            var delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound()])
            imageBeat.isHidden = false
            textNumber.textColor = UIColor.color(hex: "#87D657")
            for j in 0..<music.count {
                if music[j].isEmpty { continue }
                scheduler.schedule(after: delay) { [weak self] in
                    self?.notes[j].tintColor = nil
                    self?.notes[j].image = self?.notes[j].image?.withRenderingMode(.alwaysOriginal)
                }
                let note = music[j]
                scheduler.schedule(delay: delay) { [weak self] in
                    guard let self = self else { return }
                    if let player = noteToMusicId[note] {
                        player.currentTime = 0
                        player.play()
                    }
                }
                if let player = noteToMusicId[note] {
                    delay += player.duration
                }
            }
            delay += playSound(name: endGameSound(), delay: delay)
            animateCoinIfCorrect(view: textNumber)
            scheduler.schedule(after: delay + 2.0) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            textNumber.textColor = UIColor.color(hex: "#FF7760")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor.color(hex: "#74B6FF")
            }
        }
    }
    
    // MARK: - Helper Methods
    private func getNotes() -> [Int] {
        let notes = [1, 2, 3, 4, 6, 8]
        var rs: [Int] = []
        while true {
            rs.removeAll()
            var sum = 0
            var found = false
            for _ in 0..<3 {
                let note = notes.randomElement()!
                rs.append(note)
                sum += note
                if sum == duration {
                    found = true
                    break
                }
            }
            if found {
                break
            }
        }
        return rs
    }
    
    private func createItemNumber(nhip: String) -> KUButton {
        let view = KUButton()
        let viewBackground = SVGImageView(frame: .zero)
        viewBackground.SVGName = "option_bg_white_shadow"
        view.addSubview(viewBackground)
        viewBackground.makeViewCenterAndKeep(ratio: 346.0 / 373.0)
        
        let textNumber = HeightRatioTextView()
        textNumber.stringTag = "text_number"
        textNumber.setHeightRatio(0.5)
        textNumber.text = nhip
        textNumber.font = .Freude(size: 24)
        textNumber.textColor = UIColor.color(hex: "#74B6FF")
        textNumber.textAlignment = .center
        textNumber.backgroundColor = .clear
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9) // Bias 0.45
        }
        
        return view
    }
}
