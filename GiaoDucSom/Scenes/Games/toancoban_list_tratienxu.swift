//
//  toancoban_list_tratienxu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_tratienxu: NhanBietGameFragment {
    // MARK: - Properties
    private var leftItemContainer: UIView!
    private var rightItemContainer: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var rightView: UIView!
    private var leftView: UIView!
    private var textPayCoin: HeightRatioTextView!
    private var payCoin: Int = 0
    private var zIndex: Int = 100
    private let coins: [Int] = [1, 2, 5]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#E9FDFF")
        
        let skyView = UIView()
        skyView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(skyView)
        skyView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.centerY.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(rightView.snp.height).multipliedBy(1.2) // Ratio 1.2:1
        }
        
        let trayImage = UIImageView(image: Utilities.SVGImage(named: "math_tratien"))
        rightView.addSubview(trayImage)
        trayImage.snp.makeConstraints { make in
            make.height.equalTo(trayImage.snp.width)
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        textPayCoin = HeightRatioTextView()
        textPayCoin.text = "12"
        textPayCoin.setHeightRatio(0.7)
        textPayCoin.textColor = UIColor(hex: "#F68800")
        textPayCoin.font = .Freude(size: 20)
        textPayCoin.textAlignment = .center
        textPayCoin.adjustsFontSizeToFitWidth = true
        textPayCoin.minimumScaleFactor = 0.1
        rightView.addSubview(textPayCoin)
        textPayCoin.snp.makeConstraints { make in
            make.width.equalTo(rightView).multipliedBy(0.3)
            make.height.equalTo(rightView).multipliedBy(0.28)
            make.top.equalToSuperview()
        }
        textPayCoin.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            textPayCoin.snapToHorizontalBias(horizontalBias: 0.32)
        }
        
        rightItemContainer = UIView()
        rightView.addSubview(rightItemContainer)
        rightItemContainer.snp.makeConstraints { make in
            make.width.equalTo(rightView).multipliedBy(0.56)
            make.height.equalTo(rightItemContainer.snp.width).multipliedBy(1.0 / 2.2) // Ratio 2.2:1
        }
        rightItemContainer.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            rightItemContainer.snapToVerticalBias(verticalBias: 0.45)
            rightItemContainer.snapToHorizontalBias(horizontalBias: 0.05)
        }
        
        let rightCoinColumns: [UIView] = [
            createCoinColumn(rightItemContainer, coins: coins[0], biasX: 0.0, biasY: [1.0, 0.67, 0.33, 0.0]),
            createCoinColumn(rightItemContainer, coins: coins[1], biasX: 0.5, biasY: [1.0, 0.67, 0.33, 0.0]),
            createCoinColumn(rightItemContainer, coins: coins[2], biasX: 1.0, biasY: [1.0, 0.67, 0.33, 0.0])
        ]
        rightCoinColumns.forEach { column in
            column.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(handleRightColumnTap(_:))))
            column.subviews.forEach { coin in
                coin.alpha = 0 // Initially hide coins
            }
        }
        
        leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        leftItemContainer = UIView()
        leftView.addSubview(leftItemContainer)
        leftItemContainer.snp.makeConstraints { make in
            make.width.equalTo(leftItemContainer.snp.height).multipliedBy(1.1) // Ratio 1.1:1
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        let leftCoinColumns: [UIView] = [
            createCoinColumn(leftItemContainer, coins: coins[0], biasX: 0.0, biasY: [0.0, 0.33, 0.66, 1.0]),
            createCoinColumn(leftItemContainer, coins: coins[1], biasX: 0.5, biasY: [0.0, 0.33, 0.66, 1.0]),
            createCoinColumn(leftItemContainer, coins: coins[2], biasX: 1.0, biasY: [0.0, 0.33, 0.66, 1.0])
        ]
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        leftItemContainer.addGestureRecognizer(panGesture)
    }
    
    private func createCoinColumn(_ view: UIView, coins: Int, biasX: CGFloat, biasY:[CGFloat]) -> UIView {
        let column = UIView()
        view.addSubview(column)
        column.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            //make.height.equalTo(column.snp.width).multipliedBy(2.0) // Ratio 0.3:0.6
            make.top.bottom.equalToSuperview()
        }
        column.waitForLayout {
            column.snapToHorizontalBias(horizontalBias: biasX)
        }
        
        let coinViews: [UIView] = [
            createCoinView(column, coinValue: coins, biasY: biasY[0]),
            createCoinView(column, coinValue: coins, biasY: biasY[1]),
            createCoinView(column, coinValue: coins, biasY: biasY[2]),
            createCoinView(column, coinValue: coins, biasY: biasY[3])
        ]
        return column
    }
    let bgImageView = Utilities.GetSVGKImage(named: "math_demtien_bg")
    private func createCoinView(_ view:UIView, coinValue: Int, biasY: CGFloat) -> UIView {
        let coinView = UIView()
        view.addSubview(coinView)
        let bgView = SVGKFastImageView(svgkImage: bgImageView)!
        coinView.addSubview(bgView)
        bgView.makeViewCenterAndKeep(ratio: 257.0/272.0)
        
        let textCoinShadow = AutosizeLabel()
        textCoinShadow.text = String(coinValue)
        textCoinShadow.textColor = UIColor(hex: "#F68800")
        textCoinShadow.font = .Freude(size: 20)
        bgView.addSubview(textCoinShadow)
        textCoinShadow.snp.makeConstraints { make in
            make.height.equalTo(bgView.snp.height).multipliedBy(0.7)
            make.width.equalTo(bgView.snp.width).multipliedBy(0.6)
            make.center.equalToSuperview()
        }
        
        let textCoin = AutosizeLabel()
        textCoin.text = String(coinValue)
        textCoin.textColor = UIColor(hex: "#FFF700")
        textCoin.font = .Freude(size: 20)
        bgView.addSubview(textCoin)
        textCoin.snp.makeConstraints { make in
            make.height.equalTo(bgView.snp.height).multipliedBy(0.7)
            make.width.equalTo(bgView.snp.width).multipliedBy(0.6)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95)
        }
       
        
        coinView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.width.equalTo(coinView.snp.height).multipliedBy(257.0/272.0)
        }
        coinView.waitForLayout {
            coinView.snapToVerticalBias(verticalBias: biasY)
        }
        return coinView
    }
    
    // MARK: - Touch Handling
    
    var zPos = 100.0
    var originX: CGFloat = 0
    var originY: CGFloat = 0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        zPos += 1
        leftView.layer.zPosition = CGFloat(zPos)
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: view)
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                let point = gesture.location(in: view.superview)
                dX = currentView.frame.origin.x - point.x
                dY = currentView.frame.origin.y - point.y
                originX = currentView.frame.origin.x
                originY = currentView.frame.origin.y
                currentView.layer.zPosition = CGFloat(zIndex)
                currentView.superview?.layer.zPosition = CGFloat(zIndex + 1)
                zIndex += 2
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                Utilities.vibrate()
            }
        case .changed:
            if let currentView = currentView {
                let point = gesture.location(in: view.superview)
                currentView.frame.origin = CGPoint(x: point.x + dX, y: point.y + dY)
            }
        case .ended:
            if let currentView = currentView {
                Utilities.vibrate()
                let point = currentView.distanceFromLeftToLeft(to: rightView)
                if point.x > -currentView.frame.width / 2 {
                    playSound("effect/word puzzle drop")
                    guard let parent = currentView.superview as? UIView else { return }
                    let count = parent.subviews.filter({ $0.alpha >= 1 && $0.isHidden == false }).count
                    guard
                          let index = leftItemContainer.subviews.firstIndex(of: parent),
                          let targetGroup = rightItemContainer.subviews[safe: index],
                          let targetView = targetGroup.subviews[safe: 4 - count] else {
                        self.currentView = nil
                        return
                    }
                    
                    pauseGame(stopMusic: true)
                    currentView.moveCenter(to: targetView, fitType: .inside, duration: 0.5) { _ in
                        targetView.alpha = 1
                        currentView.alpha = 0
                        currentView.transform = .identity
                        currentView.frame.origin = CGPoint(x: self.originX, y: self.originY)
                        if !self.checkFinish() {
                            self.scheduler.schedule(after: 0.5) { [weak self] in
                                self?.resumeGame(startMusic: true)
                            }
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.5) {
                        currentView.transform = .identity
                        currentView.frame.origin = CGPoint(x: self.originX, y: self.originY)
                    }
                    playSound("effect/slide2")
                }
            }
            self.currentView = nil
        default:
            break
        }
    }
    
    @objc private func handleRightColumnTap(_ gesture: UITapGestureRecognizer) {
        guard let column = gesture.view else { return }
        zPos += 1
        rightView.layer.zPosition = CGFloat(zPos)
        playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
        
        let count = column.subviews.filter { $0.alpha >= 1 && $0.isHidden == false }.count
        if count > 0 {
            guard let item = column.subviews[safe: count - 1],
                  let index = rightItemContainer.subviews.firstIndex(of: column),
                  let targetGroup = leftItemContainer.subviews[safe: index],
                  let targetView = targetGroup.subviews.first(where: { $0.alpha < 1 || $0.isHidden }) else {
                return
            }
            
            pauseGame(stopMusic: true)
            playSound(delay: 0.3, names: ["effect/word puzzle drop"])
            let origin = item.frame.origin
            item.moveCenter(to: targetView, fitType: .inside, duration: 0.5) { _ in
                targetView.alpha = 1
                targetView.layer.zPosition = CGFloat(self.zIndex)
                self.zIndex += 1
                item.alpha = 0
                item.transform = .identity
                item.frame.origin = origin
                if !self.checkFinish() {
                    self.scheduler.schedule(after: 0.5) { [weak self] in
                        self?.resumeGame(startMusic: true)
                    }
                }
            }
        }
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for child in leftItemContainer.subviews.reversed() {
            guard let group = child as? UIView else { continue }
            let deltaX = group.frame.origin.x
            for item in group.subviews where item.alpha >= 1 && !item.isHidden {
                if x >= item.frame.origin.x + deltaX && x <= item.frame.origin.x + item.frame.width + deltaX &&
                   y >= item.frame.origin.y && y <= item.frame.origin.y + item.frame.height {
                    return item
                }
            }
        }
        return nil
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        payCoin = 5 + Int.random(in: 0..<25)
        textPayCoin.text = String(payCoin)
        let delay = playSound("toan/toan_tra tien")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_tra tien")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func checkFinish() -> Bool {
        let sumCoin = sumCoin()
        if sumCoin == payCoin {
            pauseGame()
            animateCoinIfCorrect(view: leftItemContainer)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
            return true
        }
        return false
    }
    
    private func sumCoin() -> Int {
        var sum = 0
        for (index, item) in rightItemContainer.subviews.enumerated() {
            guard let group = item as? UIView else { continue }
            for coinView in group.subviews where coinView.alpha >= 1 && !coinView.isHidden {
                sum += coins[index]
            }
        }
        return sum
    }
    
    override func createGame() {
        super.createGame()
    }
}



// Giả lập Array extension nếu không có trong context
extension Array {
    subscript(safe index: Int) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}

//  UIView+MoveCenter.swift
//
//  Created by Luna on 17/06/2025.
//
//  An elegant, reusable UIKit extension that animates a view (`self`) so its
//  **center** aligns with the center of an arbitrary `targetView`, even if the
//  two views live in *different* view hierarchies. Optionally resizes `self`
//  according to the provided `FitType`.
//
//  Usage:
//  ```swift
//  avatarImageView.moveCenter(to: headerView, fitType: .inside)
//  ```
//
//  - The animation runs on the main thread.
//  - Works seamlessly with Auto Layout: the frame is animated while existing
//    constraints remain untouched.
//  - Prevents retain cycles by using `[weak self]` inside closures.
//
import UIKit

public extension UIView {
    /// Defines how `self` should be scaled relative to the `targetView` once centred.
    enum FitType {
        /// Scale down/up so that *self* lies completely **inside** `targetView` after animation.
        case inside
        /// Scale so that `targetView` lies completely **inside** *self* after animation (self wraps target).
        case outside
        /// Preserve original size; only move the centre.
        case keepOriginSize
    }

    /// Animates the receiver so that its centre matches the centre of `targetView`.
    /// - Parameters:
    ///   - targetView: The destination view whose centre we want to align to.
    ///   - fitType: Scaling strategy (default = `.keepOriginSize`).
    ///   - duration: Total animation time (default = `0.30`).
    ///   - options: `UIView.AnimationOptions` (default = `.curveEaseInOut`).
    ///   - completion: Optional completion handler mirroring `UIView.animate`.
    func moveCenter(to targetView: UIView,
                    fitType: FitType = .keepOriginSize,
                    duration: TimeInterval = 0.30,
                    options: UIView.AnimationOptions = .curveEaseInOut,
                    completion: ((Bool) -> Void)? = nil) {

        // Ensure this executes on the main run loop.
        DispatchQueue.main.async { [weak self] in
            guard let strongSelf = self else { return }
            // We need a window to establish a common coordinate space.
            guard let window = strongSelf.window ?? targetView.window else {
                assertionFailure("Both views must be in a window before animating")
                return
            }

            // Convert target centre to window coordinates → then into the source superview.
            guard let sourceSuperview = strongSelf.superview else {
                assertionFailure("Source view has no superview — cannot animate frame")
                return
            }

            // Convert target's center to window coordinate space.
            let targetCenterInWindow = targetView.superview?.convert(targetView.center, to: window) ?? targetView.center
            // Convert that window point back into the coordinate space of the source superview.
            let targetCenterInSourceSuperview = window.convert(targetCenterInWindow, to: sourceSuperview)

            // Determine scaling factor based on fitType.
            let scale: CGFloat = {
                switch fitType {
                case .keepOriginSize:
                    return 1.0
                case .inside:
                    return min(targetView.bounds.width / strongSelf.bounds.width,
                               targetView.bounds.height / strongSelf.bounds.height)
                case .outside:
                    return max(targetView.bounds.width / strongSelf.bounds.width,
                               targetView.bounds.height / strongSelf.bounds.height)
                }
            }()

            // Build the transform (uniform scale only — preserves aspect).
            let scaledTransform = CGAffineTransform(scaleX: scale, y: scale)

            // Perform the animation.
            UIView.animate(withDuration: duration,
                           delay: 0,
                           options: options,
                           animations: {
                // Update position & scale inside animation block.
                strongSelf.center = targetCenterInSourceSuperview
                strongSelf.transform = scaledTransform
                // Ensure any pending layout updates inside superview are applied.
                sourceSuperview.layoutIfNeeded()
            }, completion: completion)
        }
    }
}
