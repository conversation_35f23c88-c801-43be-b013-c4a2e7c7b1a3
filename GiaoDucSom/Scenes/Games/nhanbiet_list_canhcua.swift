//
//  nhanbiet_list_canhcua.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_canhcua: NhanBietGameFragment {
    // MARK: - Properties
    private var door1: UIControl!
    private var door2: UIControl!
    private var door3: UIControl!
    private var doorSVG: SVGKImage?
    private var svgView1: SVGImageView!
    private var svgView2: SVGImageView!
    private var svgView3: SVGImageView!
    private var svgItemView1: SVGImageView!
    private var svgItemView2: SVGImageView!
    private var svgItemView3: SVGImageView!
    private var textView1: UILabel!
    private var textView2: UILabel!
    private var textView3: UILabel!
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_door"))
        bgImage.contentMode = .scaleToFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.9)
            make.top.left.right.equalToSuperview()
        }
        
        let doorContainer = UIView()
        doorContainer.clipsToBounds = false
        mainContainer.addSubview(doorContainer)
        doorContainer.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(2.4) // Ratio 2.4:1            
            make.bottom.left.right.equalToSuperview()
        }
        
        door1 = createDoorView(tag: 0)
        svgView1 = door1.viewWithStringTag("svg_view_1") as! SVGImageView
        svgItemView1 = door1.viewWithStringTag("svg_item_view") as! SVGImageView
        textView1 = door1.viewWithStringTag("text_view") as! UILabel
        doorContainer.addSubview(door1)
        door1.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(0.8) // Ratio 0.8:1
            make.height.equalToSuperview().multipliedBy(0.9)
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        door2 = createDoorView(tag: 1)
        svgView2 = door2.viewWithStringTag("svg_view_2") as! SVGImageView
        svgItemView2 = door2.viewWithStringTag("svg_item_view") as! SVGImageView
        textView2 = door2.viewWithStringTag("text_view") as! UILabel
        doorContainer.addSubview(door2)
        door2.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(0.8) // Ratio 0.8:1
            make.height.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        door3 = createDoorView(tag: 2)
        svgView3 = door3.viewWithStringTag("svg_view_3") as! SVGImageView
        svgItemView3 = door3.viewWithStringTag("svg_item_view") as! SVGImageView
        textView3 = door3.viewWithStringTag("text_view") as! UILabel
        doorContainer.addSubview(door3)
        door3.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(0.8) // Ratio 0.8:1
            make.height.equalToSuperview().multipliedBy(0.9)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    var frames: [UIImage] = []
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(), listItems.count >= 3 else { return }
        
        doorSVG = Utilities.GetSVGKImage(named: "animations/animation_door.svg")
        for k in 0..<doorSVG!.caLayerTree.sublayers!.count {
            doorSVG?.caLayerTree.sublayers!.forEach { g in
                g.isHidden = g != doorSVG!.caLayerTree.sublayers![k]
            }
            frames.append(doorSVG!.uiImage)
        }
        svgView1.image = frames[0]
        svgView2.image = frames[0]
        svgView3.image = frames[0]
        
        svgItemView1.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(listItems[0].path!)").uiImage
        svgItemView2.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(listItems[1].path!)").uiImage
        svgItemView3.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(listItems[2].path!)").uiImage
        
        let language = getLanguage()
        textView1.text = language == "vi" ? listItems[0].name.vi : listItems[0].name.en
        textView2.text = language == "vi" ? listItems[1].name.vi : listItems[1].name.en
        textView3.text = language == "vi" ? listItems[2].name.vi : listItems[2].name.en
        
        meIndex = Int.random(in: 0..<3)
        
        var delay: TimeInterval = 0.5
        let article = language == "en" ? "_" + ArticleHelper.getArticle(listItems[meIndex].name.en ?? "") : ""
        delay += playSound(delay: delay, names: [
            "\(language)/nhanbiet/nhanbiet_door\(article)",
            "\(language)/topics/\(folder)/\(listItems[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let listItems = getListItems(), let folder = getFolder(), listItems.count > meIndex else { return }
            let language = getLanguage() ?? "vi"
            let article = language == "en" ? "_" + ArticleHelper.getArticle(listItems[meIndex].name.en ?? "") : ""
            let delay = playSound(delay: 0, names: [
                "\(language)/nhanbiet/nhanbiet_door\(article)",
                "\(language)/topics/\(folder)/\(listItems[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemClick(_ sender: UIControl) {
        let index = sender.tag
        guard let doorSVG = doorSVG else { return }
        let doorImage = sender.viewWithStringTag(index == 0 ? "svg_view_1" : index == 1 ? "svg_view_2" : "svg_view_3") as! SVGImageView
        
        pauseGame(stopMusic: false)
        
        let step: TimeInterval = 0.07
        let groups = doorSVG.caLayerTree?.sublayers ?? []
        
        if index == meIndex {
            var delay: TimeInterval = 0
            if let folder = getFolder(), let listItems = getListItems(), listItems.count > meIndex {
                delay += playSound(delay: delay, names: [
                    "effect/door_open",
                    "effect/answer_end",
                    getCorrectHumanSound(),
                    "\(getLanguage())/topics/\(folder)/\(listItems[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            }
            
            for i in 0..<groups.count {
                scheduler.schedule(after: TimeInterval(i) * step) {
                    [weak self, doorImage] in
                    guard let self = self else { return }
                    doorImage.image = self.frames[i]
                }
            }
            
            scheduler.schedule(after: 1.0) { [weak self] in
                self?.animateCoinIfCorrect(view: sender)
            }
            
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            let delay = playSound("effect/door_open")
            for i in 0..<groups.count {
                scheduler.schedule(after: TimeInterval(i) * step) {
                    [weak self, doorImage] in
                    guard let self = self else { return }
                    doorImage.image = self.frames[i]
                }
                scheduler.schedule(after: 2.0 + TimeInterval(groups.count * 2 - i + 5) * step) {
                    [weak self, doorImage] in
                    guard let self = self else { return }
                    doorImage.image = self.frames[i]
                }
            }
            
            scheduler.schedule(after: max(TimeInterval(groups.count) * step, delay + 0.1)) { [weak self] in
                self?.playSound("effect/answer_wrong")
            }
            
            scheduler.schedule(after: 3.0 + TimeInterval(groups.count) * step) { [weak self] in
                self?.playSound("effect/door_close")
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createDoorView(tag: Int) -> UIControl {
        let door = UIControl()
        door.clipsToBounds = false
        door.tag = tag
        door.addTarget(self, action: #selector(itemClick), for: .touchUpInside)
        
        let bgView = UIView()
        bgView.isUserInteractionEnabled = false
        bgView.backgroundColor = UIColor(hex: "#300A00")
        door.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        let svgItemView = SVGImageView(frame: .zero)
        svgItemView.stringTag = "svg_item_view"
        door.addSubview(svgItemView)
        svgItemView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalTo(svgItemView.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview().offset(0.02 * door.frame.width) // Horizontal bias 0.48
            //make.centerY.equalToSuperview().multipliedBy(1.8) // Vertical bias 0.9
        }
        addActionOnLayoutSubviews {
            svgItemView.snapToVerticalBias(verticalBias: 0.9)
        }
        
        let svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view_\(tag + 1)"
        door.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.height).multipliedBy(139.0/170.0) // Ratio 139:170
            make.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        let textView = UILabel()
        textView.stringTag = "text_view"
        textView.textColor = UIColor(hex: "#FDEAC9")
        textView.textAlignment = .center
        textView.font = UIFont(name: "SVN-Freude", size: 100)
        textView.adjustsFontSizeToFitWidth = true
        textView.minimumScaleFactor = 0.1
        textView.numberOfLines = 1
        door.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalToSuperview().multipliedBy(0.1)
            //make.centerX.equalToSuperview().offset(0.01 * door.frame.width) // Horizontal bias 0.51
            //make.centerY.equalToSuperview().multipliedBy(0.3) // Vertical bias 0.15
        }
        addActionOnLayoutSubviews{
            textView.snapToVerticalBias(verticalBias: 0.15)
            textView.snapToHorizontalBias(horizontalBias: 0.53)
        }
        
        return door
    }
}

import Foundation

class ArticleHelper {
    /**
     Trả về "a" hoặc "an" dựa trên từ được cung cấp.
     
     - Parameter noun: Danh từ hoặc cụm từ
     - Returns: Chuỗi "a" hoặc "an"
     - Throws: Lỗi nếu danh từ là nil hoặc rỗng
     */
    static func getArticle(_ noun: String?) -> String {
        guard let noun = noun, !noun.isEmpty else {
            return ""
        }
        
        // Lấy ký tự đầu tiên của từ (chuyển về chữ thường)
        guard let firstChar = noun.lowercased().first else {
            return "a" // Mặc định trả về "a" nếu không có ký tự
        }
        
        // Kiểm tra xem ký tự đầu có phải là nguyên âm không
        return isVowel(firstChar) ? "an" : "a"
    }
    
    /**
     Kiểm tra ký tự có phải là nguyên âm hay không.
     
     - Parameter c: Ký tự
     - Returns: True nếu ký tự là nguyên âm, ngược lại false
     */
    private static func isVowel(_ c: Character) -> Bool {
        return "aeiou".contains(c)
    }
}
