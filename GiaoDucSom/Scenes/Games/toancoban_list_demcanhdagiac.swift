//
//  toancoban_list_demcanhdagiac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_demcanhdagiac: NhanBietGameFragment {
    // MARK: - Properties
    private var count: Int = 0
    private var gridLayout: MyGridView!
    private var itemContainer: UIView!
    private var polygonView: ConcavePolygonView!
    private let rightBg = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1)
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        itemContainer = UIView()
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        polygonView = ConcavePolygonView()
        itemContainer.addSubview(polygonView)
        polygonView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(polygonView.snp.height) // Ratio 1:1
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        count = 3 + Int.random(in: 0..<10) // 3 to 12
        polygonView.setNumPoints(count: count)
        buildGrid(grid: gridLayout, count: count)
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_dem canh da giac")
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem canh da giac")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
        //AnimationUtils.setTouchEffect(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == count
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound(), "topics/Numbers/\(value)", endGameSound()])
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}

// MARK: - ConcavePolygonView
class ConcavePolygonView: UIView {
    private let scheduler = Scheduler()
    private var paint: Paint = Paint()
    private var numPoints: Int = 5
    private var points: [CGPoint] = []
    private var allAnglesLessThan120: Bool = false
    private var colors: [UIColor] = []
    private var textPaint: Paint = Paint()
    private var labels: [String] = []
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }
    
    private func initView() {
        backgroundColor = .clear
        paint.strokeWidth = 10
        paint.color = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        //paint.style = .stroke
        paint.strokeJoin = .round
        paint.strokeCap = .round
        paint.isAntiAlias = true
        
        textPaint.color = .black
        textPaint.textSize = 40
        textPaint.isAntiAlias = true
        textPaint.isFakeBoldText = true
        textPaint.textAlign = .center
        
        labels = (0..<numPoints).map { String(UnicodeScalar("A".unicodeScalars.first!.value + UInt32($0))!) }
        generatePolygonUntilValid()
    }
    
    func getNumPoints() -> Int {
        return numPoints
    }
    
    @discardableResult
    func setNumPoints(count: Int) -> ConcavePolygonView {
        self.numPoints = count
        labels = (0..<numPoints).map { String(UnicodeScalar("A".unicodeScalars.first!.value + UInt32($0))!) }
        generatePolygonUntilValid()
        setNeedsDisplay()
        return self
    }
    
    private func generatePolygonUntilValid() {
        let width = frame.width
        let height = frame.height
        
        if width == 0 || height == 0 {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.generatePolygonUntilValid()
            }
            return
        }
        
        repeat {
            generatePolygon()
        } while !allAnglesLessThan120
    }
    
    private func generatePolygon() {
        points = []
        
        let width = frame.width
        let height = frame.height
        
        if width == 0 || height == 0 { return }
        
        let centerX = width / 2
        let centerY = height / 2
        let maxRadius = min(width, height) / 2 - 20
        
        for i in 0..<numPoints {
            let angle = (2 * Double.pi / Double(numPoints)) * Double(i)
            let radius = maxRadius * 0.3 + CGFloat.random(in: 0..<1) * (maxRadius * 0.7)
            let x = centerX + radius * cos(CGFloat(angle))
            let y = centerY + radius * sin(CGFloat(angle))
            points.append(CGPoint(x: x, y: y))
        }
        
        allAnglesLessThan120 = checkAngles(points: points)
        colors = (0..<numPoints).map { _ in randomColor() }
        adjustPointsForEqualPadding()
    }
    
    private func randomColor() -> UIColor {
        return UIColor(
            red: CGFloat.random(in: 0..<1),
            green: CGFloat.random(in: 0..<1),
            blue: CGFloat.random(in: 0..<1),
            alpha: 1
        )
    }
    
    private func adjustPointsForEqualPadding() {
        var minX = CGFloat.greatestFiniteMagnitude, maxX = -CGFloat.greatestFiniteMagnitude
        var minY = CGFloat.greatestFiniteMagnitude, maxY = -CGFloat.greatestFiniteMagnitude
        
        for point in points {
            minX = min(minX, point.x)
            maxX = max(maxX, point.x)
            minY = min(minY, point.y)
            maxY = max(maxY, point.y)
        }
        
        let width = frame.width
        let height = frame.height
        let padding: CGFloat = 20
        
        let extraLeft = minX - padding
        let extraRight = width - padding - maxX
        let extraTop = minY - padding
        let extraBottom = height - padding - maxY
        
        let shiftX = (extraRight - extraLeft) / 2
        let shiftY = (extraBottom - extraTop) / 2
        
        points = points.map { CGPoint(x: $0.x + shiftX, y: $0.y + shiftY) }
    }
    
    private func checkAngles(points: [CGPoint]) -> Bool {
        let n = points.count
        for i in 0..<n {
            let prev = points[(i - 1 + n) % n]
            let current = points[i]
            let next = points[(i + 1) % n]
            
            let vectorPrev = CGPoint(x: prev.x - current.x, y: prev.y - current.y)
            let vectorNext = CGPoint(x: next.x - current.x, y: next.y - current.y)
            
            let dotProduct = vectorPrev.x * vectorNext.x + vectorPrev.y * vectorNext.y
            let magnitudePrev = hypot(vectorPrev.x, vectorPrev.y)
            let magnitudeNext = hypot(vectorNext.x, vectorNext.y)
            
            var cosTheta = dotProduct / (magnitudePrev * magnitudeNext)
            cosTheta = max(-1.0, min(1.0, cosTheta))
            let angleRad = acos(cosTheta)
            let angleDeg = angleRad * 180 / .pi
            
            if angleDeg >= 135.0 { return false }
        }
        return true
    }
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        guard let context = UIGraphicsGetCurrentContext(), !points.isEmpty else { return }
        
        context.setStrokeColor(paint.color.cgColor)
        context.setLineWidth(paint.strokeWidth)
        context.setLineJoin(paint.strokeJoin)
        context.setLineCap(paint.strokeCap)
        
        let path = CGMutablePath()
        path.move(to: points[0])
        for i in 1..<points.count {
            path.addLine(to: points[i])
        }
        path.closeSubpath()
        context.addPath(path)
        context.strokePath()
        
        let width = frame.width
        let height = frame.height
        let centerX = width / 2
        let centerY = height / 2
        
        for i in 0..<points.count {
            let point = points[i]
            let dx = point.x - centerX
            let dy = point.y - centerY
            let distance = hypot(dx, dy)
            let labelDistance = distance + 50
            let ratio = labelDistance / distance
            let labelX = centerX + dx * ratio
            let labelY = centerY + dy * ratio
            
            labels[i].draw(
                at: CGPoint(x: labelX, y: labelY),
                withAttributes: [
                    .font: UIFont.boldSystemFont(ofSize: textPaint.textSize),
                    .foregroundColor: textPaint.color
                ]
            )
        }
        
        if !allAnglesLessThan120 {
            "Có góc ≥ 120°".draw(
                at: CGPoint(x: 50, y: 50),
                withAttributes: [
                    .font: UIFont.systemFont(ofSize: 50),
                    .foregroundColor: UIColor.red
                ]
            )
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        paint.strokeWidth = min(frame.width, frame.height) / 100
        generatePolygonUntilValid()
        setNeedsDisplay()
    }    
}

// MARK: - Supporting Structures
class Paint {
    var strokeWidth: CGFloat = 0
    var color: UIColor = .black
    var style: FillType = .fill
    var strokeJoin: CGLineJoin = .miter
    var strokeCap: CGLineCap = .butt
    var isAntiAlias: Bool = false
    var textSize: CGFloat = 0
    var isFakeBoldText: Bool = false
    var textAlign: NSTextAlignment = .left
}

enum FillType {
    case fill
    case stroke
}
