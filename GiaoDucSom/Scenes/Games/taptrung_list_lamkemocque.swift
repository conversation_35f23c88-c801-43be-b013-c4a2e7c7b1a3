//
//  taptrung_list_lamkemocque.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 21/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_lamkemocque: NhanBietGameFragment {
    // MARK: - Properties
    private var imageWheel: SVGImageView!
    private var viewWheel: UIView!
    private var itemPlaceHolder0: UIImageView!
    private var itemPlaceHolder1: UIImageView!
    private var itemPlaceHolder2: UIImageView!
    private var itemPlaceHolder3: UIImageView!
    private var itemPlaceHolder4: UIImageView!
    private var itemRightPlaceHolder1: UIImageView!
    private var itemRightPlaceHolder2: UIImageView!
    private var itemRightPlaceHolder3: UIImageView!
    private var itemRightPlaceHolder4: UIImageView!
    private var layoutHead1: UIView!
    private var layoutHead2: UIView!
    private var boyXamlView: XAMLAnimationView!
    private var itemCount: Int = 0
    private var sampleIceCream: [String] = []
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    var loadingSounds = false
    
    private let itemIds = [
        "taptrung_kem_item_blueberry",
        "taptrung_kem_item_lime",
        "taptrung_kem_item_mint",
        "taptrung_kem_item_mango",
        "taptrung_kem_item_chocolate",
        "taptrung_kem_item_strawberry"
    ]
    private let headIds = [
        "taptrung_kem_head_blueberry",
        "taptrung_kem_head_cone1",
        "taptrung_kem_head_lime",
        "taptrung_kem_head_mint",
        "taptrung_kem_head_mango",
        "taptrung_kem_head_cone2",
        "taptrung_kem_head_chocolate",
        "taptrung_kem_head_strawberry"
    ]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFC0E1")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
            make.width.equalTo(mainContainer.snp.height).multipliedBy(2.2)
        }
        
        let innerContainer = UIView()
        innerContainer.clipsToBounds = false
        mainContainer.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
            make.width.equalTo(innerContainer.snp.height).multipliedBy(1.243)
        }
        //innerContainer.makeViewCenterAndKeep(ratio: 1.243)
        
        let wheelContainer = UIView()
        wheelContainer.clipsToBounds = false
        innerContainer.addSubview(wheelContainer)
        wheelContainer.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
            make.right.equalToSuperview().multipliedBy(0.7)
        }
        
        let bgImage = SVGImageView(frame: .zero)
        bgImage.SVGName = "taptrung_kem_bg"
        wheelContainer.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.width.equalTo(bgImage.snp.height).multipliedBy(1729.0 / 1431.0) // Ratio 1729:1431
            make.bottom.left.right.equalToSuperview()
        }
        
        let headContainer = UIView()
        headContainer.clipsToBounds = false
        wheelContainer.addSubview(headContainer)
        headContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.91)
            make.centerX.top.equalToSuperview()
            make.top.equalToSuperview()
            make.width.equalTo(headContainer.snp.height)
        }
        
        layoutHead1 = UIView()
        layoutHead1.clipsToBounds = false
        headContainer.addSubview(layoutHead1)
        layoutHead1.snp.makeConstraints { make in
            make.width.equalTo(layoutHead1.snp.height)
            make.width.equalToSuperview().multipliedBy(1.08) // width_percent=1.08
            make.center.equalToSuperview()
        }
        
        let head1Images = [
            ("head_1_blueberry", 0.0,"taptrung_kem_head_blueberry"),
            ("head_1_strawberry", 45.0,"taptrung_kem_head_strawberry"),
            ("head_1_chocolate", 90.0,"taptrung_kem_head_chocolate"),
            ("head_1_cone2", 135.0,"taptrung_kem_head_cone2"),
            ("head_1_mango", 180.0,"taptrung_kem_head_mango"),
            ("head_1_mint", 225.0,"taptrung_kem_head_mint"),
            ("head_1_lime", 270.0,"taptrung_kem_head_lime"),
            ("head_1_cone1", 315.0,"taptrung_kem_head_cone1")
        ]
        
        for (id, rotation,image) in head1Images {
            let container = UIView()
            container.transform = CGAffineTransform(rotationAngle: rotation * .pi / 180)
            layoutHead1.addSubview(container)
            container.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.3)
                make.center.equalToSuperview()
                make.top.bottom.equalToSuperview()
            }
            
            let image = UIImageView(image: Utilities.SVGImage(named: image))
            image.contentMode = .scaleAspectFit
            container.addSubview(image)
            image.snp.makeConstraints { make in
                make.left.bottom.right.equalToSuperview()
                make.height.equalTo(image.snp.width).multipliedBy(190.0/462.1)
            }
        }
        
        itemPlaceHolder1 = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_item_cone2"))
        itemPlaceHolder1.alpha = 0.01
        wheelContainer.addSubview(itemPlaceHolder1)
        itemPlaceHolder1.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.15)
            make.height.equalTo(itemPlaceHolder1.snp.width) // Ratio 1:1
            make.centerX.bottom.equalToSuperview()
        }
        
        itemPlaceHolder2 = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_item_strawberry"))
        itemPlaceHolder2.alpha = 0.01
        wheelContainer.addSubview(itemPlaceHolder2)
        itemPlaceHolder2.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.15)
            make.height.equalTo(itemPlaceHolder2.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.bottom.equalToSuperview().multipliedBy(0.96) // verticalBias=0.92
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.itemPlaceHolder2.snapToVerticalBias(verticalBias: 0.92)
        }
        
        itemPlaceHolder3 = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_item_blueberry"))
        itemPlaceHolder3.alpha = 0.01
        wheelContainer.addSubview(itemPlaceHolder3)
        itemPlaceHolder3.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.15)
            make.height.equalTo(itemPlaceHolder3.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.bottom.equalToSuperview().multipliedBy(0.94) // verticalBias=0.88
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.itemPlaceHolder3.snapToVerticalBias(verticalBias: 0.88)
        }
        itemPlaceHolder4 = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_item_lime"))
        itemPlaceHolder4.alpha = 0.01
        wheelContainer.addSubview(itemPlaceHolder4)
        itemPlaceHolder4.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.15)
            make.height.equalTo(itemPlaceHolder4.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.bottom.equalToSuperview().multipliedBy(0.92) // verticalBias=0.84
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.itemPlaceHolder4.snapToVerticalBias(verticalBias: 0.84)
        }
        
        itemPlaceHolder0 = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_item_lime"))
        wheelContainer.addSubview(itemPlaceHolder0)
        itemPlaceHolder0.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.15)
            make.height.equalTo(itemPlaceHolder0.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.bottom.equalToSuperview().multipliedBy(0.81) // verticalBias=0.62
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.itemPlaceHolder0.snapToVerticalBias(verticalBias: 0.62)
        }
        
        let head2Container = UIView()
        head2Container.clipsToBounds = false
        wheelContainer.addSubview(head2Container)
        head2Container.snp.makeConstraints { make in
            make.edges.equalTo(headContainer)
        }
        
        layoutHead2 = UIView()
        layoutHead2.clipsToBounds = false
        headContainer.addSubview(layoutHead2)
        layoutHead2.snp.makeConstraints { make in
            make.edges.equalTo(layoutHead1)
        }
        
        for (id, rotation, image) in head1Images {
            let container = UIView()
            container.transform = CGAffineTransform(rotationAngle: rotation * .pi / 180)
            layoutHead2.addSubview(container)
            container.snp.makeConstraints { make in
                make.width.equalTo(layoutHead2).multipliedBy(0.3)
                make.centerX.top.bottom.equalToSuperview()
            }
            
            let image = UIImageView(image: Utilities.SVGImage(named: "taptrung_kem_head_top"))
            image.contentMode = .scaleAspectFit
            container.addSubview(image)
            image.snp.makeConstraints { make in
                make.left.bottom.right.equalToSuperview()
                make.height.equalTo(image.snp.width).multipliedBy(190.0/462.1)
            }
        }
        
        imageWheel = SVGImageView(frame: .zero)
        imageWheel.SVGName = "taptrung_kem_wheel"
        wheelContainer.addSubview(imageWheel)
        imageWheel.snp.makeConstraints { make in
            make.width.equalTo(wheelContainer).multipliedBy(0.9)
            make.height.equalTo(imageWheel.snp.width) // Ratio 1:1
            make.centerX.top.equalToSuperview()
        }
        
        let bgTopImage = SVGImageView(frame: .zero)
        bgTopImage.SVGName = "taptrung_kem_bgtop"
        wheelContainer.addSubview(bgTopImage)
        bgTopImage.snp.makeConstraints { make in
            make.width.equalTo(bgTopImage.snp.height).multipliedBy(1728.0 / 1071.0) // Ratio 1728:1071
            make.left.top.right.equalToSuperview()
        }
        
        viewWheel = UIView()
        //viewWheel.backgroundColor = UIColor.red.withAlphaComponent(0.6) // #0f00
        wheelContainer.addSubview(viewWheel)
        viewWheel.snp.makeConstraints { make in
            make.edges.equalTo(headContainer)
        }
        // Placeholder for XamlAnimationView
        boyXamlView = XAMLAnimationView()
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "taptrung_kem")!)
            boyXamlView.loadView(from: xamlData)
        } catch {}
        //boyXamlView.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        mainContainer.addSubview(boyXamlView)
        boyXamlView.snp.makeConstraints { make in
            make.width.equalTo(boyXamlView.snp.height) // Ratio 1:1
            make.top.bottom.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.boyXamlView.snapToHorizontalBias(horizontalBias: 0.9)
        }
        
        let rightContainer = UIView()
        //rightContainer.backgroundColor = UIColor.cyan.withAlphaComponent(0.06) // #0FF0
        mainContainer.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(rightContainer.snp.height) // Ratio 1:1
            make.height.equalToSuperview().multipliedBy(0.4)
        }
        addActionOnLayoutSubviews {
            rightContainer.snapToVerticalBias(verticalBias: 0.22)
            rightContainer.snapToHorizontalBias(horizontalBias: 0.91)
        }
        
        let rightPlaceHolders = [
            (id: 0, image: "taptrung_kem_item_cone2", verticalBias: 0.6),
            (id: 1, image: "taptrung_kem_item_strawberry", verticalBias: 0.35),
            (id: 2, image: "taptrung_kem_item_blueberry", verticalBias: 0.2),
            (id: 3, image: "taptrung_kem_item_lime", verticalBias: 0.05)
        ]
        
        for (index,ph) in rightPlaceHolders.enumerated() {
            let placeHolder = UIImageView(image: Utilities.SVGImage(named: ph.image))
            rightContainer.addSubview(placeHolder)
            placeHolder.snp.makeConstraints { make in
                make.width.equalTo(rightContainer).multipliedBy(0.3)
                make.height.equalTo(placeHolder.snp.width) // Ratio 1:1
                make.centerX.equalToSuperview()
                //make.bottom.equalToSuperview().multipliedBy((1 + ph.verticalBias) / 2)
            }
            if index == 0 {
                itemRightPlaceHolder1 = placeHolder
            }
            if index == 1 {
                itemRightPlaceHolder2 = placeHolder
            }
            if index == 2 {
                itemRightPlaceHolder3 = placeHolder
            }
            if index == 3 {
                itemRightPlaceHolder4 = placeHolder
            }
            addActionOnLayoutSubviews {
                placeHolder.snapToVerticalBias(verticalBias: ph.verticalBias)
            }
        }
        
        
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(makeIceCream))
        view.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/lam kem"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
        
        sampleIceCream = SampleIceCream()
        itemRightPlaceHolder1.image = Utilities.SVGImage(named: sampleIceCream[0])
        itemRightPlaceHolder2.image = Utilities.SVGImage(named: sampleIceCream[1])
        itemRightPlaceHolder3.image = Utilities.SVGImage(named: sampleIceCream[2])
        itemRightPlaceHolder4.image = Utilities.SVGImage(named: sampleIceCream[3])
         
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay: TimeInterval = playSound("taptrung/lam kem")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    var touchInside = false
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        let gesture = touches.first!
        touchInside = gesture.placeInView(view: viewWheel)
        let view = viewWheel
        let location = gesture.location(in: view)
        previousAngle = calculateAngle(x: location.x, y: location.y)
        originRotation = imageWheel.transform.rotationAngleInDegress()
    }
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if !touchInside { return }
        let gesture = touches.first!
        let view = viewWheel
        let location = gesture.location(in: view)
        print("\(location.x)-\(location.y)")
        let currentAngle = calculateAngle(x: location.x, y: location.y)
        let rotation = currentAngle - previousAngle
        let newRotation = originRotation + rotation
        //print(newRotation)
        imageWheel.transform = CGAffineTransform(rotationAngle: newRotation * .pi / 180)
        layoutHead1.transform = CGAffineTransform(rotationAngle: newRotation * .pi / 180)
        layoutHead2.transform = CGAffineTransform(rotationAngle: newRotation * .pi / 180)
        if abs(latestGearRotation - newRotation) > 360 / 8 / 3 {
            if player == nil {
                if let url = Utilities.url(soundPath: "effect/gear2") {
                    do {
                        player = try AVAudioPlayer(contentsOf: url)
                        player?.prepareToPlay()
                    } catch {
                    }
                }
            }
            if player != nil {
                player?.currentTime = 0
                player?.play()
            }
            latestGearRotation = newRotation
        }
    }
    var player : AVAudioPlayer?
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if !touchInside { return }
        let gesture = touches.first!
        let view = viewWheel
        let location = gesture.location(in: view)
        let rotation = imageWheel.transform.rotationAngleInDegress()
        let roundedRotation = round(rotation / 45.0) * 45
        print(rotation)
        print(roundedRotation)
        UIView.animate(withDuration: 0.1) {
            self.imageWheel.transform = CGAffineTransform(rotationAngle: roundedRotation * .pi / 180)
            self.layoutHead1.transform = CGAffineTransform(rotationAngle: roundedRotation * .pi / 180)
            self.layoutHead2.transform = CGAffineTransform(rotationAngle: roundedRotation * .pi / 180)
        }
    }
    
    
    private var previousAngle: CGFloat = 0
    private var originRotation: CGFloat = 0
    private var latestGearRotation: CGFloat = 0
    
    private func calculateAngle(x: CGFloat, y: CGFloat) -> CGFloat {
        let centerX = viewWheel.frame.width / 2
        let centerY = viewWheel.frame.height / 2
        return atan2(y - centerY, x - centerX) * 180 / .pi
    }
    
    @objc private func makeIceCream() {
        boyXamlView.startAnimationStoryboard(with: "sb2")
        var valid = true
        let index = Int((round(imageWheel.transform.rotationAngleInDegress() / 45.0) + 16).truncatingRemainder(dividingBy: 8))
        if index == 1 || index == 5 {
            if itemCount != 0 {
                valid = false
            }
        } else {
            if itemCount == 0 {
                valid = false
            }
        }
        
        let itemIds = [
            "taptrung_kem_item_blueberry",
            "taptrung_kem_item_cone1",
            "taptrung_kem_item_lime",
            "taptrung_kem_item_mint",
            "taptrung_kem_item_mango",
            "taptrung_kem_item_cone2",
            "taptrung_kem_item_chocolate",
            "taptrung_kem_item_strawberry"
        ]
        
        valid = itemCount < 4 && itemIds[index] == sampleIceCream[itemCount]
        let finished = valid && itemCount == 3
        itemCount += 1
        
        let destImageView: UIImageView = {
            switch itemCount {
            case 1: return itemPlaceHolder1
            case 2: return itemPlaceHolder2
            case 3: return itemPlaceHolder3
            case 4: return itemPlaceHolder4
            default: return itemPlaceHolder0
            }
        }()
        
        itemPlaceHolder0.image = Utilities.SVGImage(named: itemIds[index])
        destImageView.image = Utilities.SVGImage(named: itemIds[index])
        
        let head1View = layoutHead1.viewWithStringTag(headIds[index])
        let head2View = layoutHead2.viewWithStringTag(headIds[index])
        UIView.animate(withDuration: 0.5) {
            head1View?.transform = CGAffineTransform(translationX: 0, y: self.itemPlaceHolder0.frame.height / 3)
            head2View?.transform = CGAffineTransform(translationX: 0, y: self.itemPlaceHolder0.frame.height / 3)
        } completion: { _ in
            UIView.animate(withDuration: 0.5) {
                head1View?.transform = .identity
                head2View?.transform = .identity
            }
        }
        
        if !valid {
            setGameWrong()
            playSound("effect/slide2")
            pauseGame(stopMusic: false)
            UIView.animate(withDuration: 0.5, delay: 0, options: .curveLinear) {
                self.itemPlaceHolder0.transform = CGAffineTransform(translationX: 0, y: self.itemPlaceHolder0.frame.height * 4)
            } completion: { _ in
                UIView.animate(withDuration: 0.5, delay: 0, options: .curveLinear) {
                    [self.itemPlaceHolder1, self.itemPlaceHolder2, self.itemPlaceHolder3, self.itemPlaceHolder4].forEach {
                        $0.transform = CGAffineTransform(translationX: 0, y: self.itemPlaceHolder0.frame.height * 4)
                    }
                }
                self.scheduler.schedule(after: 2.0) { [weak self] in
                    self?.resetGame()
                }
            }
            return
        } else {
            playSound("effect/word puzzle drop")
        }
        
        itemPlaceHolder0.moveToCenter(of: destImageView, duration: 0.5) { [weak self] _ in
            guard let self = self else { return }
            destImageView.alpha = 1.0
            self.itemPlaceHolder0.transform = .identity
        }
        
        if finished {
            pauseGame()
            animateCoinIfCorrect(view: itemPlaceHolder0)
            let delay: TimeInterval = 1.0 + playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func resetGame() {
        itemCount = 0
        itemPlaceHolder1.alpha = 0.01
        itemPlaceHolder2.alpha = 0.01
        itemPlaceHolder3.alpha = 0.01
        itemPlaceHolder4.alpha = 0.01
        itemPlaceHolder0.transform = .identity
        itemPlaceHolder1.transform = .identity
        itemPlaceHolder2.transform = .identity
        itemPlaceHolder3.transform = .identity
        itemPlaceHolder4.transform = .identity
        resumeGame(startMusic: false)
    }
    
    private func SampleIceCream() -> [String] {
        let items = [
            "taptrung_kem_item_blueberry",
            "taptrung_kem_item_lime",
            "taptrung_kem_item_mint",
            "taptrung_kem_item_mango",
            "taptrung_kem_item_chocolate",
            "taptrung_kem_item_strawberry"
        ]
        let indexes = (0..<items.count).shuffled().prefix(3).map { $0 }
        return [
            ["taptrung_kem_item_cone1", "taptrung_kem_item_cone2"].randomElement()!,
            items[indexes[0]],
            items[indexes[1]],
            items[indexes[2]]
        ]
    }
}
