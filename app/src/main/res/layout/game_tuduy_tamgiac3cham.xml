<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#74B6FF"
    android:clipChildren="false"
    android:clipToPadding="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginHorizontal="20dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2.2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_left"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.935"
                app:layout_constraintWidth_percent="0.5" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.935"
                app:layout_constraintWidth_percent="0.5" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.07"
                app:layout_constraintWidth_percent="0.5" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac60"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.625"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.72" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac180"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.499"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.93" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac300"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                android:visibility="invisible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.374"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.72" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:splitMotionEvents="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1.1"
            app:layout_constraintHorizontal_bias="0.96"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac4"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5" />


            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/tamgiac5"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_tamgiac3cham"
                android:rotation="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>