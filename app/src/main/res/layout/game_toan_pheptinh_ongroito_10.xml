<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#663535"
    android:clipChildren="false"
    android:clipToPadding="false">


    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/nhanbiet_bg_garden" />

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.33">

        <com.kidsup.giaoducsom.view.MathNumpad
            android:id="@+id/numpad"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.05" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="3:2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/grid_layout"
        app:layout_constraintTop_toTopOf="parent">

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="197:245"
            app:layout_constraintHeight_percent="0.7"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/math_ongroito_hive"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:pcl_layout_marginPercent="0.1" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="-1"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.85"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.15"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="-1"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.15"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.8"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.4"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.15"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.8"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.7"
                app:layout_constraintWidth_percent="0.35" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="-1"
                android:src="@drawable/math_ongroito_bee"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.85"
                app:layout_constraintWidth_percent="0.35" />
        </com.kidsup.giaoducsom.view.PercentConstraintLayout>

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/math_ongroito_flower"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="197:245"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.67"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.5"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.25"
            app:layout_constraintWidth_percent="0.13" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_ongroito_bee"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.25"
            app:layout_constraintWidth_percent="0.13" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4.4"
            app:layout_constraintHorizontal_bias="0.1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.95"
            app:layout_constraintWidth_percent="0.7"
            app:pcl_layout_marginPercent="0.05">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout59"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout58"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_a"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="?"
                    android:textColor="#FFD31E"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout58"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.6"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout60"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout59"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="-"
                    android:textColor="#FFD31E"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout60"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout61"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout58"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_b"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="?"
                    android:textColor="#FFD31E"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout61"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.6"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout63"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout60"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="="
                    android:textColor="#FFD31E"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout63"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout61"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_result"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="?"
                    android:textColor="#FFD31E"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>