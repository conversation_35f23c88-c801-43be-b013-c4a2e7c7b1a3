//
//  phonics_list_whatsoundisthis.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_whatsoundisthis: GameFragment {
    private var values : [String] = []
    var bottomView = SVGImageView(SVGName: "btn bg blue").then {
        $0.isUserInteractionEnabled = true
        $0.alpha = 0
    }
    var answerLayout = MyGridView()
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#466DCE")
        $0.text = "a"
        //$0.backgroundColor = .red
    }
    var topUpcase = Bool.random()
    var meIndex = 0
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#849BFE")
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#7BD2FF")
        addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        let rightView = UIView()
        rightView.backgroundColor = .init(hex: "#849BFD")
        addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(leftView.snp.right)
        }
        bottomView.isUserInteractionEnabled = true
        let rightBg = SVGImageView(SVGName: "option_bg_purple_shadow")
        rightView.addSubview(rightBg)
        rightBg.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(rightBg.snp.width)
        }
        rightBg.addSubview(textName)
        leftView.addSubview(bottomView)
        bottomView.makeViewCenterAndKeep(ratio: 400.0/151.0)
        bottomView.addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(1)
            make.height.equalToSuperview().multipliedBy(1)
            make.center.equalToSuperview()
        }
        textName.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
            make.height.equalTo(textName.snp.width).multipliedBy(0.75)
        }
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = self.playSound(delay: 0, names: self.parseIntroText()!)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        meIndex = Int.random(in: 0..<values.count)
        textName.text = topUpcase ? values[meIndex].uppercased() : values[meIndex].lowercased()
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
            self.showBottom()
        })
        var listViews : [UIView] = []
        for i in 0..<values.count {
            let view = createItem(text: topUpcase ? values[i].lowercased() : values[i].uppercased())
            view.tag = 100 + i
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 1
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.03
        answerLayout.columns = 3
        answerLayout.reloadItemViews(views: listViews)
    }
    override func parseIntroText() -> [String]? {
        guard let text = topUpcase ? data?.text2 : data?.text1 else { return nil }
        return text.split(separator: "#").map { String($0).lowercased() }
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func showBottom(){
        let delta = bottomView.bounds.height * 1.5
        bottomView.transform = CGAffineTransformMakeTranslation(0, delta)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [delta, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue)
            self.bottomView.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#68C1FF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        if values[meIndex].lowercased() == label.text!.lowercased() {
            pauseGame()
            animateCoinIfCorrect(view: sender)
            label.textColor = .color(hex: "#73D048")
            var delay = 0.5
            delay += playSound(delay: delay, names: ["\(values[meIndex])1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
        } else {
            pauseGame()
            setGameWrong()
            incorrect += 1
            label.textColor = .color(hex: "#FF7761")
            var delay = 0.3
            delay += playSound(name: "\(label.text!)1", delay: delay)
            delay += playSound(delay: delay, names: chooseWrongSounds())
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
                self.resumeGame()
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}

