<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@drawable/btn_blue">

    <TextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:autoSizeMaxTextSize="500dp"
        app:autoSizeTextType="uniform"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:text="A"
        android:textColor="#fff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0" />
</androidx.constraintlayout.widget.ConstraintLayout>