//
//  toancoban_list_timsomaxminpv100.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_timsomaxminpv100: toancoban_list_bia {
    // MARK: - Properties
    private var lonnhat: Bool = false
    
    // MARK: - Game Logic
    override func buildData() {
        lonnhat = Bool.random()
        numbers = (1...99).shuffled().prefix(21).map { $0 }
        let maxmin = lonnhat ? numbers.max() ?? 0 : numbers.min() ?? 0
        validNumbers = [maxmin]
    }
    
    override func createGame() {
        super.createGame()
        pauseGame()
        let delay = playSound(openGameSound(), "toan/toan_tim so max min_\(lonnhat ? "max" : "min")")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_tim so max min_\(lonnhat ? "max" : "min")")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}
