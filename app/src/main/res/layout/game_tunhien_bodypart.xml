<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EBFBFC"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.85"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="1">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/item_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.caverock.androidsvg.SVGImageView
                    android:id="@+id/svg_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />

                <com.caverock.androidsvg.SVGImageView
                    android:id="@+id/svg_lines_view"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <LinearLayout
        android:id="@+id/ll_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:animateLayoutChanges="true"
        android:gravity="center"
        android:orientation="horizontal"
        app:layout_constraintHeight_percent="0.15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"></LinearLayout>

    <LinearLayout
        android:id="@+id/ll_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:animateLayoutChanges="true"
        android:gravity="center"
        android:orientation="horizontal"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.15"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"></LinearLayout>

    <View
        android:id="@+id/touch_view"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>