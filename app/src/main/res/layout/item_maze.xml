<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <View
        android:id="@+id/center_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.9" />

    <View
        android:id="@+id/top_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FF9FC4"
        android:visibility="invisible"
        app:layout_constraintBottom_toTopOf="@id/center_view"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintWidth_percent="1.1" />

    <View
        android:id="@+id/bottom_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FF9FC4"
        android:visibility="invisible"
        app:layout_constraintHeight_percent="0.1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/center_view"
        app:layout_constraintWidth_percent="1.1" />

    <View
        android:id="@+id/left_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FF9FC4"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="1.1"
        app:layout_constraintRight_toLeftOf="@id/center_view"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.1" />

    <View
        android:id="@+id/right_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FF9FC4"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="1.1"
        app:layout_constraintLeft_toRightOf="@id/center_view"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.1" />
</androidx.constraintlayout.widget.ConstraintLayout>