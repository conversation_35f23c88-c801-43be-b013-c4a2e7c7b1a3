<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/option_bg_white_shadow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.95"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.46"
            app:layout_constraintWidth_percent="0.8">

            <ImageView
                android:id="@+id/image_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_congchamtron5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.2"
                app:layout_constraintEnd_toStartOf="@+id/image_2"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/image_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_congchamtron5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.2"
                app:layout_constraintEnd_toStartOf="@+id/text_plus"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_1"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_plus"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="+"
                android:textColor="#74B6FF"
                app:heightRatio="0.4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.2"
                app:layout_constraintEnd_toStartOf="@+id/image_3"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_2"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/image_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_congchamtron5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.2"
                app:layout_constraintEnd_toStartOf="@+id/image_4"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/text_plus"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/image_4"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_congchamtron5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.2"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/image_3"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>