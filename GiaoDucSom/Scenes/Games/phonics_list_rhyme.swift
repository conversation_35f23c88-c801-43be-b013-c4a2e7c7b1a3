//
//  phonics_list_rhyme.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_rhyme: GameFragment {
    private var values : [String] = []
    var gridview = MyGridView()
    var rightAnswer: String = ""
    var correctCount = 0
    var corrects : [UIView] = []
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(gridview)
        gridview.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        while true {
            // Assuming data.values is an array of Strings.
            let _values = values.randomOrder().take(count: 3)

            let v0 = _values[0].prefix(1)
            let v1 = _values[1].prefix(1)
            let v2 = _values[2].prefix(1)

            var unique: [Substring] = [v0]
            

            if !unique.contains(v1) {
                unique.append(v1)
            } else {
                rightAnswer = v1.string
            }

            if !unique.contains(v2) {
                unique.append(v2)
            } else {
                rightAnswer = v2.string
            }

            if unique.count == 2 {
                values = _values
                break
                // Break the loop
            }
        }
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        var listViews : [UIView] = []
        for value in values {
            let view = createGridItem()
            let label: AutosizeLabel = view.viewWithTag(1) as! AutosizeLabel
            label.text = value.dropFirst(2).replacingOccurrences(of: "_", with: "")
            view.tag = 100 + values.firstIndex(of: value)!
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        gridview.columns = 0
        gridview.itemRatio = 2.3
        gridview.itemSpacingRatio = 0.01
        gridview.reloadItemViews(views: listViews)
        delay += 0.2
        let duration = 1.5
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.gridview.playSound = false
            self.gridview.step = duration
            self.gridview.showItems()
            for i in 0..<values.count {
                self.playSound(name: values[i].dropFirst(2).replacingOccurrences(of: "_", with: ""), delay: duration * Double(i))
            }
        })
        delay += duration * Double(values.count)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue long")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 2.3)
        let label = AutosizeLabel()
        label.textColor = .color(hex: "#68C1FF")
        label.tag = 1
        background.addSubview(label)
        label.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            for i in 0..<values.count {
                delay += self.playSound(name: values[i].dropFirst(2).replacingOccurrences(of: "_", with: ""), delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    @objc func itemClick(_ sender: UIControl){
        let tag = sender.tag - 100
        let textView = sender.viewWithTag(1) as! AutosizeLabel
        // Assuming view is a UIView and values is an array of Strings.
        let key = String(values[tag].prefix(1))
        pauseGame()
        playSound(name: values[tag].dropFirst(2).replacingOccurrences(of: "_", with: ""), delay: 0.3)
        if key == rightAnswer {
            if !corrects.contains(sender) {
                correctCount += 1
                corrects.append(sender)
            }
            // playSound("effects/true")
            playSound(answerCorrect1EffectSound())
            
            let value = String(values[tag].dropFirst(2))
            if let i1 = value.findPosition(of: "_"), let i2 = value.findLastPosition(of: "_") {
                let spannable = NSMutableAttributedString(string: value.replacingOccurrences(of: "_", with: ""))
                spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#73D048"), range: NSRange(location: i1, length: i2 - i1 - 1))
                textView.attributedText = spannable
            }
            
            if correctCount == 2 {
                scheduler.schedule(delay: 1, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    animateCoinIfCorrect(view: sender)
                    var delay = 0.5
                    delay += self.playSound(name: "effects/cheer\(Int.random(in: 1...4))", delay: delay)
                    delay += self.playSound(name: "effects/end game", delay: delay)
                    delay += 1
                    scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.finishGame()
                    })
                })
            } else {
                resumeGame()
            }
        } else {
            // playSound("effects/fail")
            playSound(answerWrongEffectSound())
            let value = String(values[tag].dropFirst(2))
            if let i1 = value.findPosition(of: "_"), let i2 = value.findLastPosition(of: "_") {
                let spannable = NSMutableAttributedString(string: value.replacingOccurrences(of: "_", with: ""))
                spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#FF7761"), range: NSRange(location: i1, length: i2 - i1 - 1))
                textView.attributedText = spannable
            }
            scheduler.schedule(delay: 0.2, execute: {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            })
            setGameWrong()
            incorrect += 1
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return Float(corrects.count) / Float(corrects.count + 2*incorrect)
    }
}
