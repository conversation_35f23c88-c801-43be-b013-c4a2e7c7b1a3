//
//  phonics_list_sound.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 22/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_sound: GameFragment {
    var textName = AutosizeLabel()
    override func configureLayout(_ view: UIView) {
        clipsToBounds = true
        backgroundColor = .color(hex: "#7CD2FF")
        textName.textColor = .color(hex: "#1497E0")
        self.addSubview(textName)
        textName.snp.makeConstraints{ make in
            make.width.height.equalToSuperview().multipliedBy(0.67)
            make.center.equalToSuperview()
        }
    }
    override func createGame() {
        super.createGame()
        if game.value1!.contains(" ") || game.value1!.contains("\n") {
            textName.numberOfLines = 2
        }
        textName.text = game.value1
        textName.alpha = 0
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [0,1]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textName.transform = CGAffineTransformMakeScale(finalValue, finalValue)
            self?.textName.alpha = value
        })
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            timeChange.animate(1, duration: 0.5){}
        })        
        self.playSound(name: "effects/slide1", delay: 0.7)
        var delay = 1.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += 1
        if game.value2 != nil && game.text2 != nil {
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.textName.text = game.value2
            })
            delay += self.playSound(delay: delay, names: game.text2!.lowercased().split(separator: "#").map{$0.string})
            delay += 1
        }
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.finishGame()
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening,.GameReading]
    }
}
