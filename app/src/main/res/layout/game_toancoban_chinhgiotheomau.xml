<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercent="0.05">

        <com.kidsup.giaoducsom.view.ClockDigital
            android:id="@+id/clock_digital"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/math_clock2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="40:25"
        app:layout_constraintHorizontal_bias="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0"
        app:layout_constraintWidth_percent="0.25"
        app:pcl_layout_marginPercentPerHeight="0.03">

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_hour"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/utm_avo"
            android:gravity="center_vertical|right"
            android:text="23:"
            android:textColor="#006EB8"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.7"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.53" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_minute"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/utm_avo"
            android:gravity="center_vertical|left"
            android:text="58"
            android:textColor="#E73C2B"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.7"
            app:layout_constraintHorizontal_bias="1.0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.47" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>