//
//  TuDuyMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class TuDuyMainViewController: SubjectMainViewController {
    override var backgroundMusicFile: String? {
        "bg_list6"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Tư Duy")
        titleColor = UIColor(hex: "#849BFD")
        backgroundColor = UIColor(hex: "#F1E2FF")
    }
    
    override func setupData() {
        // Section 1: Xếp hình
        let xepHinhSection = SectionData(
            header: "Xếp hình",
            items: [
                TroChoiNgonNguItem(data: "tuduy_list_tangram", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_xepgach", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_xepkimcuong", isTapDocGame: false)
            ]
        )
        
        // Section 2: Tì<PERSON> đường
        let timDuongSection = SectionData(
            header: "Tìm đường",
            items: [
                TroChoiNgonNguItem(data: "tuduy_list_chuechxanh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_dieukhienoto", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_robothutbui", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_khobaudaiduong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_noidaydenmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_ongnuoc", isTapDocGame: false)
            ]
        )
        
        // Section 3: Quy luật
        let quyLuatSection = SectionData(
            header: "Quy luật",
            items: [
                TroChoiNgonNguItem(data: "tuduy_list_thaybongden", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_quyluathinhmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_quyluathinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_xauhatmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_quyluattron", isTapDocGame: false)
            ]
        )
        
        // Section 4: Sắp xếp
        let sapXepSection = SectionData(
            header: "Sắp xếp",
            items: [
                TroChoiNgonNguItem(data: "tuduy_list_rotnuoc", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_thaphanoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_hatmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_domino", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_xeptamgiac", isTapDocGame: false)
            ]
        )
        
        // Section 5: Tưởng tượng
        let tuongTuongSection = SectionData(
            header: "Tưởng tượng",
            items: [
                TroChoiNgonNguItem(data: "tuduy_list_manhtamgiac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_catgiay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_dovatlan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_dovatnoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_timnha", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_hophoavan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_tetris", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_chonggiaymau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_thekhoetlo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_diemxuyenchonggiay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_bongnuoc", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_manhghep", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_mattrang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_phanbuchunhat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_2manhghep", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_khoigachmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_2viengach", isTapDocGame: false),
                TroChoiNgonNguItem(data: "tuduy_list_gapkhoihop", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            xepHinhSection,
            timDuongSection,
            quyLuatSection,
            sapXepSection,
            tuongTuongSection
        ]
    }  
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = checkFreeGameByThumbnail(troChoiItem.data)
            runPremiumFeatures({
                [weak self] in
                guard let self = self else { return }
                let veTungNetVC = TangramMainViewController()
                navigationController?.pushViewController(veTungNetVC, animated: true)
            }, inTrialTime || freetoplay)
        }
    }
    
    
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự TuDuyGameNameHelper.getGameByThumb
        return thumbnail
    }
}
