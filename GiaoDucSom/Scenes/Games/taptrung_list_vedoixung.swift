//
//  taptrung_list_vedoixung.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/6/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics


// MARK: - HalfDrawingGameFragment
class taptrung_list_vedoixung: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: SVGImageView!
    private var halfSvgView: SVGImageView!
    private var halfDrawingView: HalfDrawingView!
    var filename: String?
    private var halfIndex: Int?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#55E2F9")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(hex: "#90FF6C")
        view.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.makeViewCenterAndKeep(ratio: 1)
        mainContainer.snp.makeConstraints { make in
            make.height.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view"
        svgView.backgroundColor = UIColor.white
        mainContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        halfSvgView = SVGImageView(frame: .zero)
        halfSvgView.stringTag = "half_svg_view"
        mainContainer.addSubview(halfSvgView)
        halfSvgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        halfDrawingView = HalfDrawingView()
        halfDrawingView.stringTag = "half_drawing_view"
        mainContainer.addSubview(halfDrawingView)
        halfDrawingView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        halfDrawingView.setListener(
            onDone: { [weak self] in self?.onDone() },
            onStroke: { [weak self] path, end in self?.onStroke(path: path, end: end) },
            onStart: { [weak self] in self?.onStart() }
        )
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        if let url = Utilities.SVGURL(of: "half drawing/0.svg") {
            let image = SVGKImage(contentsOf: url)
            svgView.contentMode = .scaleAspectFit
            svgView.image = image?.uiImage
        }
        let delay = playSound(delay: 0, names: [openGameSound(), "\(getLanguage() ?? "vi")/taptrung/ve doi xung"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        filename = filename ?? "half drawing/\(Int.random(in: 1...60)).svg"
        
        let svg = Utilities.GetSVGKImage(named: filename!)
        halfIndex = halfIndex ?? Int.random(in: 0..<2)
        let layer = svg.caLayerTree.sublayers![halfIndex!]
        layer.sublayers?.forEach { pathLayer in
            if let pathLayer = pathLayer as? CAShapeLayer {
                pathLayer.fillColor = UIColor.clear.cgColor
                pathLayer.strokeColor = halfIndex! == 0 ? UIColor(hex: "#09D622").cgColor : UIColor(hex: "#00C9EA").cgColor
                //pathLayer.lineWidth = 0.015 * UIScreen.main.bounds.width
                pathLayer.lineCap = .round
                pathLayer.lineJoin = .round
            }
        }
        let otherHalf = svg.caLayerTree.sublayers![1 - halfIndex!]
        otherHalf.opacity = 0
        halfSvgView.image = svg.uiImage
        
        halfDrawingView.setSvg(svg, 1 - halfIndex!)
        halfDrawingView.setPaintColor(halfIndex! == 1 ? UIColor(hex: "#09D622") : UIColor(hex: "#00C9EA"))
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage() ?? "vi")/taptrung/ve doi xung")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func onDone() {
        pauseGame(stopMusic: false)
        let delay = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
        animateCoinIfCorrect(view: svgView)
        if let filename = filename {
            playVeDoiXung(filename.replacingOccurrences(of: "images/", with: "").replacingOccurrences(of: ".svg", with: ""))
        }
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
    
    private func onStroke(path: CGPath, end: Bool) {
        // Placeholder for stroke handling
    }
    
    private func onStart() {
        // Placeholder for start handling
    }
    
    private func playVeDoiXung(_ mask: String) {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        var masks = Set(defaults.stringArray(forKey: "vedoixung_played_\(profileId)") ?? [])
        masks.insert(mask)
        defaults.set(Array(masks), forKey: "vedoixung_played_\(profileId)")
    }
    
    // MARK: - Public Methods
    func setData(_ data: String) {
        filename = "half drawing/\(data).svg"
    }
    
    func setHalfIndex(_ index: Int) {
        halfIndex = index
    }
}


// MARK: - HalfDrawingView
class HalfDrawingView: UIView {
    // MARK: - Properties
    private var player: AVAudioPlayer?
    private var listPoints: [[CGPoint]] = []
    private var onDone: (() -> Void)?
    private var onStroke: ((CGPath, Bool) -> Void)?
    private var onStart: (() -> Void)?
    private let dashedPaint = Paint()
    private let paint = Paint()
    private let paint2 = Paint()
    private let dashed2Paint = Paint()
    private var points: [CGPoint] = []
    private var currentPoints: [CGPoint] = []
    private var leftPoints: [CGPoint] = []
    private var listLeftPoints: [[CGPoint]] = []
    private var paths: [CGPath] = []
    private var newPath: CGMutablePath?
    private var newPathJustCreated: Bool = false
    private var startPoint: CGPoint?
    private var startIndex: Int = 0
    private var wrong: Bool = false
    private var latestPoint: CGPoint?
    private var started: Bool = false
    private var svg: CALayer?
    private var path: CGPath?
    private var path2: CGMutablePath?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
        dashedPaint.color = UIColor.white
                
        paint.color = UIColor(hex: "#FFB924")
                
        dashed2Paint.color = UIColor.red
        
        paint2.color = UIColor(hex: "#8CC63F")
        
        playWritingSound()
    }
    
    // MARK: - Public Methods
    func setListener(onDone: @escaping () -> Void, onStroke: @escaping (CGPath, Bool) -> Void, onStart: @escaping () -> Void) {
        self.onDone = onDone
        self.onStroke = onStroke
        self.onStart = onStart
    }
    
    func setPaintColor(_ color: UIColor) {
        paint2.color = color
    }
    
    func setSvg(_ image: SVGKImage, _ index: Int) {
        self.svg = image.caLayerTree.sublayers![index]
        let tenDp = bounds.width / 100
        //dashedPaint.lineDashPattern = [0.5 * tenDp, 2 * tenDp]
        //dashed2Paint.lineDashPattern = [0.2 * tenDp, tenDp]
        
        points = []
        path2 = CGMutablePath()
        listPoints = []
        listLeftPoints = []
        let scale = frame.width / image.size.width
        var contourIndex = 0
        svg?.sublayers?.forEach { path in
            let p = path
            if let path = (path as? CAShapeLayer)?.path {
                print("\(path.length)")
                let point = p.convert( CGRect.zero, to: image.caLayerTree)
                var currentPoints = getPoints(path: UIBezierPath(cgPath: path))
                currentPoints = currentPoints.map{CGPoint(x: (point.minX+$0.x) * scale, y: (point.minY+$0.y) * scale)}
                listPoints.append(currentPoints)
                listLeftPoints.append(currentPoints)
                points.append(contentsOf: currentPoints)
            }
        }
        leftPoints = points
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        for path in paths {
            context.addPath(path)
            context.setStrokeColor(paint2.color.cgColor)
            context.setLineWidth(paint2.strokeWidth)
            context.setLineCap(.round)
            context.setLineJoin(.round)
            context.strokePath()
        }
        
        if let newPath = newPath {
            context.addPath(newPath)
            context.setStrokeColor(paint2.color.cgColor)
            context.setLineWidth(paint2.strokeWidth)
            context.setLineCap(.round)
            context.setLineJoin(.round)
            context.strokePath()
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            newPath = CGMutablePath()
            newPathJustCreated = true
            startPoint = nearest
            startIndex = points.firstIndex(of: nearest) ?? 0
            newPath?.move(to: nearest)
            wrong = false
            player?.currentTime = 0
            player?.play()
            Utils.vibrate()
            onStart?()
            setNeedsDisplay()
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if wrong {
            player?.pause()
            return
        }
        
        if let nearest = nearestPointInCurrentPath(to: point) {
            if newPath == nil {
                newPath = CGMutablePath()
                newPathJustCreated = true
                startPoint = nearest
                startIndex = points.firstIndex(of: nearest) ?? 0
                newPath?.move(to: nearest)
            } else {
                lineTo2(nearest)
            }
            setNeedsDisplay()
            if !(player?.isPlaying ?? false) {
                player?.play()
            }
        } else {
            player?.pause()
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
                onStroke?(newPath, false)
            }
            self.newPath = nil
            setNeedsDisplay()
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        player?.pause()
        if let newPath = newPath, !newPathJustCreated {
            paths.append(newPath)
            onStroke?(newPath, true)
        }
        self.newPath = nil
        let longestContinueLeftPoints = findLongestContinueLeftPoints()
        if longestContinueLeftPoints < 20 {
            onDone?()
        }
        setNeedsDisplay()
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func nearestPoint(to touch: CGPoint) -> CGPoint? {
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        
        // Check endpoints first
        for points in listPoints {
            let distanceToStart = Utilities.distance(from: touch, to: points[0])
            let distanceToEnd = Utilities.distance(from: touch, to: points[points.count - 1])
            if distanceToStart < minDistance {
                minDistance = distanceToStart
                nearest = points[0]
                currentPoints = points
            }
            if distanceToEnd < minDistance {
                minDistance = distanceToEnd
                nearest = points[points.count - 1]
                currentPoints = points
            }
        }
        if minDistance < bounds.width / 20 {
            return nearest
        }
        
        // Check middle points
        nearest = nil
        minDistance = CGFloat.greatestFiniteMagnitude
        for point in points {
            let distance = Utilities.distance(from: touch, to: point)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance < bounds.width / 20 {
            latestPoint = nearest
            for listPoint in listPoints {
                if listPoint.contains(where: { $0 == nearest }) {
                    currentPoints = listPoint
                    break
                }
            }
            return nearest
        }
        
        // Check endpoints again with larger threshold
        minDistance = CGFloat.greatestFiniteMagnitude
        for points in listPoints {
            let distanceToStart = Utilities.distance(from: touch, to: points[0])
            let distanceToEnd = Utilities.distance(from: touch, to: points[points.count - 1])
            if distanceToStart < minDistance {
                minDistance = distanceToStart
                nearest = points[0]
                currentPoints = points
            }
            if distanceToEnd < minDistance {
                minDistance = distanceToEnd
                nearest = points[points.count - 1]
                currentPoints = points
            }
        }
        if minDistance < bounds.width / 10 {
            return nearest
        }
        
        // Check middle points again
        nearest = nil
        minDistance = CGFloat.greatestFiniteMagnitude
        for point in points {
            let distance = Utilities.distance(from: touch, to: point)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        latestPoint = nearest
        for listPoint in listPoints {
            if listPoint.contains(where: { $0 == nearest }) {
                currentPoints = listPoint
                break
            }
        }
        return nearest
    }
    
    private func nearestPointInCurrentPath(to touch: CGPoint) -> CGPoint? {
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        for point in currentPoints {
            let distance = Utilities.distance(from: touch, to: point)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        latestPoint = nearest
        return nearest
    }
    
    private func lineTo2(_ point: CGPoint) {
        guard let endIndex = points.firstIndex(of: point) else { return }
        if endIndex == startIndex { return }
        
        let max = Swift.max(startIndex, endIndex)
        let min = Swift.min(startIndex, endIndex)
        let distance = Swift.min(max - min, min + points.count - max)
        
        if distance > 10 {
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = CGMutablePath()
            self.newPath?.move(to: point)
            startIndex = endIndex
            newPathJustCreated = true
            return
        }
        
        if distance == max - min {
            if startIndex < endIndex {
                for i in (startIndex + 1)...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (endIndex...startIndex - 1).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        } else {
            if startIndex < endIndex {
                if startIndex > 0 {
                    for i in (0...(startIndex - 1)).reversed() {
                        newPath?.addLine(to: points[i])
                        leftPoints.removeAll { $0 == points[i] }
                    }
                }
                for i in (endIndex...(points.count - 1)).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (startIndex + 1)..<points.count {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
                for i in 0...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        }
        
        startIndex = endIndex
        newPathJustCreated = false
    }
    
    private func findLongestContinueLeftPoints() -> Int {
        var max = 0
        var count = 0
        for point in points {
            if leftPoints.contains(point) {
                count += 1
            } else {
                max = Swift.max(max, count)
                count = 0
            }
        }
        return Swift.max(max, count)
    }
    
    private func playWritingSound() {
        if let url = Bundle.main.url(forResource: "writing", withExtension: "mp3", subdirectory: "Sounds/effect") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.numberOfLoops = -1
                player?.prepareToPlay()
            } catch {
                if BuildConfig.DEBUG {
                    print(error)
                }
            }
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        paint.strokeWidth = bounds.width / 26
        dashedPaint.strokeWidth = bounds.width / 60
        paint2.strokeWidth = bounds.width / 70
    }
    
    deinit {
        player?.stop()
        player = nil
    }
    
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        var points: [CGPoint] = []
        let count = Int(path.length)
        return path.evenlySpacedPointsUsingDash(count: count)
    }
}
