//
//  tuduy_list_manhghep.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_manhghep: NhanBietGameFragment {
    // MARK: - Properties
    private var meIndex: Int = 0
    private var svgView: UIImageView!
    private var gridLayout: MyGridView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        let leftPaddingView = UIView()
        leftView.addSubviewWithPercentInset(subview: leftPaddingView, percentInset: 15)
        
        svgView = UIImageView()
        svgView.alpha = 0.001
        svgView.contentMode = .scaleAspectFit
        leftPaddingView.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
                
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_manh ghep")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        svgView.moveToCenter(of: self, duration: 0)
        svgView.alpha = 1
        gridLayout.alpha = 0
        
        let filename = "images/toan_manhghep\(random(1, 2, 3)).svg"
        let svg = Utilities.GetSVGKImage(named: filename)
        self.meIndex = Int.random(in: 0..<svg.caLayerTree.sublayers!.count)
        svg.caLayerTree.sublayers![self.meIndex].sublayers![0].opacity = 0.5
        svg.caLayerTree.sublayers![self.meIndex].sublayers![1].opacity = 0
        self.svgView.image = svg.uiImage
        svg.caLayerTree.sublayers![self.meIndex].sublayers![0].opacity = 1
        svg.caLayerTree.sublayers![self.meIndex].sublayers![1].opacity = 1
        self.buildGrid(grid: self.gridLayout, svg: svg)
    }
    
    // MARK: - Helper Methods
    private func buildGrid(grid: MyGridView, svg: SVGKImage) {
        var views: [UIView] = []
        for i in 0..<4 {
            //guard let value = svg.groups[i].trim() else { continue }
            let value = svg
            for k in 0..<value.caLayerTree.sublayers!.count {
                value.caLayerTree.sublayers![k].isHidden = k != i
            }
            let view = createItemManhGhep(svg: value, rotation: random(0, 90, 180, 270))
            view.tag = i
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
            views.append(view)
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views.shuffled())
        
        let delay = playSound(openGameSound(), "toan/toan_manh ghep")
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.svgView.transform = .identity
                UIView.animate(withDuration: 0.5, delay: 0.5) {
                    self.gridLayout.alpha = 1
                }
            }
            grid.showItems(startDelay: 0.5)
        }
                    
        scheduler.schedule(after: delay + 1.0) { [weak self] in
            self?.startGame()
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let viewZoom = view.viewWithStringTag("view_zoom") as? UIView,
              let viewBackground = view.viewWithStringTag("view_background") as? UIImageView,
              let viewRotation = view.viewWithStringTag("view_rotation") as? UIView,
              let svgThumbnail = view.viewWithStringTag("svg_thumbnail") as? UIImageView else { return }
        
        view.bringSubviewToFront(self)
        pauseGame()
        let index = view.tag
        if index == meIndex {
            viewBackground.image = nil
            animateCoinIfCorrect(view: view)
            var delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            UIView.animate(withDuration: 0.5) {
                viewRotation.transform = .identity
            } completion: { _ in
                var scale = CGFloat(self.svgView.frame.width)/CGFloat(viewZoom.frame.width)
                self.playSound("effect/slide1")
                self.playSound(name: "effect/word puzzle drop", delay: 0.8)
                //svgThumbnail.backgroundColor = .red.withAlphaComponent(0.4)
                self.scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    view.moveToCenter(of: self.svgView, duration: 0.8){_ in
                        
                    }
                    UIView.animate(withDuration: 0.8) {
                        svgThumbnail.transform = .identity
                        viewRotation.transform = CGAffineTransformMakeScale(scale, scale)
                    }
                }
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemManhGhep(svg: SVGKImage, rotation: Int) -> UIView {
        let view = UIView()
        let viewBackground = UIImageView()
        viewBackground.stringTag = "view_background"
        viewBackground.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(viewBackground)
        viewBackground.makeViewCenterAndKeep(ratio: 1)
        let viewRotation = UIView()
        viewRotation.stringTag = "view_rotation"
        viewBackground.addSubview(viewRotation)
        viewRotation.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        var bound : CGRect = .zero
        for i in 0..<svg.caLayerTree.sublayers!.count {
            let layer = svg.caLayerTree.sublayers![i]
            if !layer.isHidden {
                bound = layer.shapeContentBounds!
                print(layer.frame)
                print(svg.size)
                let scale = min(svg.size.width / layer.shapeContentBounds!.width, svg.size.height / layer.shapeContentBounds!.height)
                print(scale)
            }
        }
        
        let viewZoom = UIView()
        viewZoom.stringTag = "view_zoom"
        viewRotation.addSubview(viewZoom)
        viewZoom.snp.makeConstraints { make in
            let ratio = svg.size.width / svg.size.height
            make.width.equalTo(viewRotation).multipliedBy(0.74) // Margin 0.13
            make.height.equalTo(viewZoom.snp.width).dividedBy(ratio)
            make.center.equalToSuperview()
        }
        
        let svgThumbnail = UIImageView(image: svg.uiImage)
        svgThumbnail.stringTag = "svg_thumbnail"
        svgThumbnail.contentMode = .scaleAspectFit
        viewRotation.transform = CGAffineTransform(rotationAngle: CGFloat(rotation) * .pi / 180)
        viewZoom.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            let scale = min(svg.size.width / viewZoom.frame.width, svg.size.height / viewZoom.frame.height)
            zoomViewB(toFitRectC: CGRectMake( bound.minX / scale, bound.minY / scale, bound.width / scale, bound.height / scale), inViewA: viewZoom, viewB: svgThumbnail)
        }
        
        return view
    }
   
}

func zoomViewB(toFitRectC rectC: CGRect, inViewA viewA: UIView, viewB: UIView) {
    // Kích thước của view A
    let viewASize = viewA.bounds.size
    
    // Tính tỷ lệ scale để rect C vừa với view A (aspect fit)
    let scaleX = viewASize.width / rectC.width
    let scaleY = viewASize.height / rectC.height
    let scale = min(scaleX, scaleY)
    
    
    let centerRect = CGPoint(x: rectC.midX, y: rectC.midY)
    
    let centerB = viewB.center
    // Áp dụng constraints mới với SnapKit
    viewB.transform = CGAffineTransformMakeScale(scale, scale).translatedBy(x: centerB.x - centerRect.x, y: centerB.y - centerRect.y)
    
    // Yêu cầu layout lại
    viewA.layoutIfNeeded()
}

extension CGAffineTransform {
    func removingRotation() -> CGAffineTransform {
        // Tính scale
        let scaleX = sqrt(a * a + b * b)
        let scaleY = sqrt(c * c + d * d)
        
        // Giữ nguyên translation (tx, ty)
        // Tạo ma trận mới với scale và translation, không có rotation
        return CGAffineTransform(a: scaleX, b: 0, c: 0, d: scaleY, tx: tx, ty: ty)
    }
}
