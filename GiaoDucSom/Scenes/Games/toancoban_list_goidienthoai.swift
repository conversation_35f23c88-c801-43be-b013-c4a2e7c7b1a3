//
//  toancoban_list_goidienthoai.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_goidienthoai: NhanBietGameFragment {
    // MARK: - Properties
    private var answerLayout: UIView!
    private var numpad: MathNumpad!
    private var answerText: UILabel!
    private var phone: String = ""
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        
        let leftView = UIView()
        leftView.backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.415)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        numpad.setMaxLength(10)
        numpad.setListener(self)
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        
        let imageBoy = UIImageView(image: Utilities.SVGImage(named: "math_goidienthoai"))
        imageBoy.contentMode = .scaleAspectFit
        addSubview(imageBoy)
        imageBoy.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.33)
            make.height.equalTo(imageBoy.snp.width) // Ratio 1:1
            make.left.centerY.equalToSuperview()
        }
        
        answerLayout = UIView()
        answerLayout.backgroundColor = .white
        answerLayout.layer.cornerRadius = 10 // Giả lập CornerDrawable với radius
        scheduler.schedule(delay: 0.2) {
            [weak self] in
            guard let self = self else { return }
            self.answerLayout.layer.cornerRadius = answerLayout.frame.height / 2
        }
        addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.44)
            make.height.equalTo(answerLayout.snp.width).multipliedBy(1/4.5) // Ratio 4.5
            make.top.equalTo(imageBoy.snp.top)
        }
        addActionOnLayoutSubviews {
            self.answerLayout.snapToHorizontalBias(horizontalBias: 0.41)
        }
        
        answerText = AutosizeLabel()
        answerText.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
        answerText.font = .Freude(size: 20)
        answerText.textAlignment = .center
        answerText.adjustsFontSizeToFitWidth = true
        answerText.minimumScaleFactor = 0.1
        answerLayout.addSubview(answerText)
        answerText.snp.makeConstraints { make in
            make.height.equalTo(answerLayout).multipliedBy(0.9)
            make.center.equalToSuperview()
            make.left.right.equalToSuperview()
        }
        
        // Cập nhật nút check thành icon "math_numpad_call"
        if let checkButton = numpad.subviews.first(where: { $0.accessibilityIdentifier == "math_numpad_check" }) as? KUButton {
            checkButton.setImage(Utilities.SVGImage(named: "math_numpad_call"), for: .normal)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "toan/toan_goi dien thoai_intro")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_goi dien thoai_intro")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_goidienthoai: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        phone += String(number)
        answerText.text = phone
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        playSound("effect/phone_number")
    }
    
    func onDelClick(value: Int) {
        if !phone.isEmpty {
            phone = String(phone.dropLast())
        }
        answerText.text = phone
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    }
    
    func onCheckClick(value: Int) {
        let validPrefixes = ["03", "05", "07", "08", "09"]
        if phone.count == 10 && validPrefixes.contains(where: { phone.hasPrefix($0) }) {
            animateCoinIfCorrect(view: answerText)
            let delay = playSound("toan/toan_goi dien thoai", endGameSound())
            answerText.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            pauseGame()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong")
            answerText.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
        }
    }
}
