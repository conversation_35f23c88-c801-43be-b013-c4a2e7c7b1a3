//
//  HomeViewController.swift
//  KidsUPTiengViet
//
//  Created by <PERSON> on 05/01/2023.
//

import Firebase
import FirebaseDatabase
import KeychainAccess
import SVGKit
import SnapKit
import Then
import UIKit

final class HomeViewController: BaseViewController, UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    // MARK: - Components
    private lazy var logoImageView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "logo"))!.then { img in
        img.contentMode = .scaleAspectFit
    }
    
    private lazy var bgHomeImageView = SVGKFastImageView(frame: .zero).then { img in
        img.contentMode = .scaleAspectFill
    }
    
    private lazy var btnGallery = SVGButton(SVGIcon: "home_btn_sticker_book")
    private lazy var btnPremium = SVGButton(SVGIcon: "btn_premium_home")
    private lazy var btnSettings = SVGButton(SVGIcon: "btn_settings")
    private lazy var btnProfile = SVGButton(SVGIcon: "btn_report")
    private lazy var btnSearch = SVGButton(SVGIcon: "btn_search")
    private var imageAvatar = SVGKFastImageView(svgkImage: nil)!
    
    private var collectionView: UICollectionView!
    
    // MARK: - Properties
    override var backgroundMusicFile: String? {
        "bg_home"
    }
    
    private let images: [String] = [
        "btn_subject_thu_thach_ngay",
        "btn_subject_trao_the",
        "btn_subject_nhan_biet",
        "btn_subject_ngon_ngu",
        "btn_subject_toan_co_ban",
        "btn_subject_toan_nang_cao",
        "btn_subject_phonics",
        "btn_subject_vocabulary",
        "btn_subject_tap_trung",
        "btn_subject_tu_duy",
        "btn_subject_am_nhac",
        "btn_subject_my_thuat"
    ]
    
    private let names: [String] = [
        "",
        "Tráo thẻ",
        "Nhận biết",
        "Ngôn ngữ",
        "Toán học",
        "Toán nâng cao",
        "Phonics",
        "Vocabulary",
        "Tập trung",
        "Tư duy",
        "Âm nhạc",
        "Mỹ thuật"
    ]
    
    private let colors: [UIColor] = [
        UIColor(hex: "#FF6008"), // Thử thách ngày
        UIColor(hex: "#E53939"), // Tráo thẻ
        UIColor(hex: "#74BC46"), // Nhận biết
        UIColor(hex: "#00ADE2"), // Ngôn ngữ
        UIColor(hex: "#4FDB59"), // Toán học
        UIColor(hex: "#6C7CEF"), // Toán nâng cao
        UIColor(hex: "#D844B5"), // Phonics
        UIColor(hex: "#D844B5"), // Vocabulary
        UIColor(hex: "#3DBDD3"), // Tập trung
        UIColor(hex: "#BBA2F9"), // Tư duy
        UIColor(hex: "#848484"), // Âm nhạc
        UIColor(hex: "#E53939")  // Mỹ thuật
    ]
    
    private var challengeCompleted: Bool = false
    private var timer: Timer?
    
    // MARK: - Life Cycle
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    init() {
        super.init(nibName: nil, bundle: nil)
    }
    
    override func loadView() {
        super.loadView()
        let coins = CoinManager.shared.getCoin()
        #if targetEnvironment(simulator)
            SVGKImage(contentsOf: Utilities.SVGURL(of: "coin"))
        #endif
        view = UIView().then {
            $0.backgroundColor = .white
        }
        //logoImageView.tintColor = .white
        //logoImageView.setTintColor(.red)
        QueueSoundPlayer.shared.loadBubbles()
        if AppSettings.isUserInVietnam {
            if DataManager.shared.currentProfile?.name != DataManager.DEFAULT_USERNAME {
                showSplashScreen()
            }
        }
    }
    func showSplashScreen() {
        let splashViewController = SplashDialogFragment()
        splashViewController.modalPresentationStyle = .fullScreen
        present(splashViewController, animated: false, completion: nil)
    }
    private func setupHierarchy() {
        view.addSubview(bgHomeImageView)
        view.addSubview(logoImageView)
        view.addSubview(btnGallery)
        view.addSubview(btnPremium)
        view.addSubview(btnSettings)
        view.addSubview(btnSearch)
        view.addSubview(btnProfile)
        imageAvatar.isUserInteractionEnabled = false
        btnProfile.addSubviewWithPercentInset(subview: imageAvatar, percentInset: 0.1)
        addBottomList()
        //logoImageView.image = SVGKImage(contentsOf: Utilities.SVGURL(of: "logo"))?.uiImage.withRenderingMode(.alwaysTemplate)
        #if DEBUG
        //let numpad = MathNumpad()
        //numpad.isEnabled = true
        //view.addSubviewWithInset(subview: numpad, inset: 0)
        //let moduleName = getAppModuleName()
        //let classType = NSClassFromString(moduleName! + ".toancoban_list_capbophan") as! BaseFragmentView.Type
        //let x = classType.create(frame: .zero)
        //view.addSubviewWithInset(subview: x, inset: 0)
        Scheduler().schedule(delay: 2) {
            [weak self] in
            guard let self = self else { return }
            //let vc = TwoClickExitActivity()
            //vc.modalPresentationStyle = .fullScreen
            //self.present(vc, animated: true)
            //self.navigationController?.pushViewController(vc, animated: true)
            /*
            let vc = ParentGateDialogFragment()
            vc.listener = {
                
            }
            vc.show()
             */
        }
        #endif
        /*
        let dialog = MessageDialogView()
        dialog.setTitle("Xin chào")
            .setMessage("Đăng ký KidsUP Premium để truy cập không giới hạn các bài học? Bạn khỏe không?")
            .setButtonOkText("Đồng ý")
            .setButtonLaterText("Để sau")
            .setSvgLogoPath("btn_back")
            .setSvgLogoColor(.red)
            .setShowLaterButton(true)
            .setShowOkButton(true)
            .setListener(onConfirm: {
                print("Confirmed (Later tapped)")
            }, onClose: { buttonIndex in
                switch buttonIndex {
                case .ok: print("OK tapped")
                case .confirm: print("Later tapped")
                case .close: print("Close tapped")
                }
            })

        dialog.showIn(view)
         */
        if AppSettings.isUserInVietnam {
            let whiteView = UIView()
            whiteView.backgroundColor = .white
            view.addSubview(whiteView)
            whiteView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            scheduler.schedule(delay: 2) {
                [weak self] in
                guard let self = self else { return }
                whiteView.removeFromSuperview()
            }
        }
        checkLicenseState()
        checkUserLocation()
    }
    
    private func setupConstraints() {
        bgHomeImageView.makeViewCenterFillAndKeep(ratio: 2688.0/1236.0)
        /*
        bgHomeImageView.snp.makeConstraints { make in
            make.top.left.lessThanOrEqualToSuperview()
            make.right.bottom.greaterThanOrEqualToSuperview()
            make.width.equalTo(bgHomeImageView.snp.height).multipliedBy(2688.0/1236.0)
            make.width.equalToSuperview().priority(.high)
            make.height.equalToSuperview().priority(.high)
            make.center.equalToSuperview()
        }*/
        logoImageView.snp.makeConstraints { make in
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.1)
            make.centerX.equalToSuperview()
            make.width.equalTo(logoImageView.snp.height).multipliedBy(1055.0/584.0)
            make.bottom.equalToSuperview().multipliedBy(0.5)
        }
        btnGallery.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.1)
            make.width.equalTo(btnGallery.snp.height)
            make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
        }
        btnGallery.snpLeftTop(ratio: 1)
        btnSettings.snp.makeConstraints { make in
            make.top.height.width.equalTo(btnGallery)
        }
        btnSettings.snpRightTop(ratio: 1)
        
        btnPremium.snp.makeConstraints { make in
            make.top.height.width.equalTo(btnGallery)
            make.right.equalTo(btnGallery.snp.right).multipliedBy(2)
        }
        
        btnProfile.snp.makeConstraints { make in
            make.top.width.height.equalTo(btnGallery)
        }
        btnProfile.snpRightTop(ratio: 4.0)
        
        btnSearch.snp.makeConstraints { make in
            make.top.width.height.equalTo(btnGallery)
        }
        btnSearch.snpRightTop(ratio: 7)
        
        btnPremium.addTarget(self, action: #selector(onPremiumClicked), for: .touchUpInside)
        btnProfile.addTarget(self, action: #selector(onProfileClicked), for: .touchUpInside)
        btnGallery.addTarget(self, action: #selector(toGallery), for: .touchUpInside)
        btnSettings.addTarget(self, action: #selector(toSetting), for: .touchUpInside)
        btnSearch.addTarget(self, action: #selector(toSearch), for: .touchUpInside)
    }
    @objc private func onProfileClicked() {
        navigationController?.pushViewController(ProfileDialogViewController().setParentMode(false).setProfile(DataManager.shared.currentProfile!), animated: true)
    }
    
    @objc private func onPremiumClicked() {
        let vc = ParentGateDialogFragment()
        vc.listener = {
            [weak self] in
            guard let self = self else { return }
            self.navigationController?.pushViewController(ActiveAndBuyDialogViewController(), animated: true)
        }
        vc.show(parrent: self)
    }

    override func viewDidLoad() {
        super.viewDidLoad()
        
        // Call loginFirebase
        FirebaseUtils.shared.loginFirebase {
            //FirebaseUtils.shared.saveLocalToFirebaseIfNeed()
            FirebaseUtils.shared.loadRemoteProfiles()
        }
        
        UIApplication.shared.statusBarStyle = .default
        setupUI()
        
        if UserDefaultsHelper().loadNotificationSetting() == true {
            AppDelegate.shared.requestPermissionNotifications()
        }
        handleProfileChanged()
        /*
        let profile = DataManager.shared.createProfile(languageCode: "vi", name: "Vỹ Nguyễn \(arc4random_uniform(1000))", yearOfBirth: 2020)
        DataManager.shared.selectProfile(profile.id)
         */
        NotificationCenter.default.addObserver(self, selector: #selector(handleProfileChanged), name: .ProfileChanged, object: nil)
    }
    
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateBackgroundImage()
        startUpdateThuThachNgay()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        stopUpdateThuThachNgay()
    }
    
    deinit {
        print(#function, self)
        NotificationCenter.default.removeObserver(self)
        stopUpdateThuThachNgay()
    }
    
    // MARK: - Private Methods
    private func setupUI() {
        view.backgroundColor = .color(hex: "#849BFE")
        setupHierarchy()
        setupConstraints()
    }
    
    private func updateBackgroundImage() {
        let isNight = isNightTime()
        bgHomeImageView.image = Utilities.GetSVGKImage(named: isNight ? "bg_home2" : "bg_home")
    }
    
    private func isNightTime() -> Bool {
        let calendar = Calendar.current
        let hour = calendar.component(.hour, from: Date())
        return hour >= 18 || hour < 6
    }

    
    private func startUpdateThuThachNgay() {
        challengeCompleted = ChallengeManager.shared.isChallengeCompleted()
        timer?.invalidate()
        timer = Timer.scheduledTimer(withTimeInterval: 1.0, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            if !self.challengeCompleted {
                // Lấy cell tại vị trí đầu tiên (item 0, section 0)
                if let cell = self.collectionView.cellForItem(at: IndexPath(item: 0, section: 0)) as? ItemHomeCell {
                    // Cập nhật trực tiếp text của cell
                    let timeRemaining = getTimeRemainingUntilMidnight()
                    cell.textView.text = timeRemaining
                }
            }
        }
    }
    
    private func stopUpdateThuThachNgay() {
        timer?.invalidate()
        timer = nil
    }
    
    func addBottomList() {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 16
        layout.minimumLineSpacing = 16
        
        collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(ItemHomeCell.self, forCellWithReuseIdentifier: "ItemHomeCell")
        collectionView.backgroundColor = .clear
        // Ẩn thanh cuộn ngang
        collectionView.showsHorizontalScrollIndicator = false
        view.addSubview(collectionView)
        
        collectionView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.3)
            make.bottom.equalToSuperview().multipliedBy(0.92)
        }
    }
    
    // MARK: - UICollectionViewDataSource
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return names.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ItemHomeCell", for: indexPath) as! ItemHomeCell
        let imageName = indexPath.row == 0 && challengeCompleted ? "btn_subject_thu_thach_ngay_done" : images[indexPath.row]
        let text = indexPath.row == 0 && !challengeCompleted ? getTimeRemainingUntilMidnight() : names[indexPath.row]
        cell.configure(image: imageName, name: text, color: colors[indexPath.row])
        return cell
    }
    
    // MARK: - UICollectionViewDelegate
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        checkTimeoutAndRun {
            [weak self] in
            guard let self = self else { return }
            self.openSubject(subjectIndex: indexPath.row)
        }
        return
        let dialog = MessageDialogView()
        dialog.setTitle("Chú ý")
            .setMessage("Bạn có chắc muốn mở " + names[indexPath.row])
            .setButtonOkText("Đồng ý")
            .setButtonLaterText("Để sau")
            .setSvgLogoPath("btn_back")
            .setSvgLogoColor(.red)
            .setShowLaterButton(true)
            .setShowOkButton(true)
            .setListener(onConfirm: {
               
            }, onClose: { [weak self] buttonIndex in
                guard let self = self else { return } // unwrap self an toàn
                switch buttonIndex {
                case .ok:
                    openSubject(subjectIndex: indexPath.row)
                case .confirm: print("Later tapped")
                case .close: print("Close tapped")
                }
            })

        dialog.showIn(view)
    }
    func openSubject(subjectIndex: Int){
        switch subjectIndex {
        case 0: // Thử thách ngày
            let vc = ThuThachNgayViewController()
            self.navigationController?.pushViewController(vc, animated: true)
            break
        case 1: // Tráo thẻ
            let vc = FlashcardMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
            break
        case 2: // Nhận biết
            let vc = IdentifyMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 3: // Ngôn ngữ
            let vc = LanguageMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 4: // Toán học
            let vc = ToanMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 5: // Toán nâng cao
            let vc = ToanNangCaoMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 6: // Phonics
            let vc = PhonicsMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 7: // Vocabulary
            let vc = EnglishVocabMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
            break
        case 8: // Tập trung
            let vc = TapTrungMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 9: // Tư duy
            let vc = TuDuyMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 10: // Âm nhạc
            let vc = MusicMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        case 11: // Mỹ thuật
            let vc = MyThuatMainViewController()
            self.navigationController?.pushViewController(vc, animated: true)
        default:
            break
        }
    }
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let height = collectionView.bounds.height
        return CGSize(width: height, height: height) // Chiều rộng = Chiều cao (ratio = 1:1)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, insetForSectionAt section: Int) -> UIEdgeInsets {
        let height = collectionView.bounds.height
        return UIEdgeInsets(top: 0, left: height/10, bottom: 0, right: height/10)
    }
    
    // MARK: - Existing Methods (Giữ nguyên)
    func checkPromo() {
        if !AppSettings.isUserInVietnam {
            return
        }
        AppSettings.appStartCount = (AppSettings.appStartCount ?? 0) + 1
        let startTime = AppSettings.appStartTime ?? 0
        if startTime == 0 {
            AppSettings.appStartTime = Date().timeIntervalSince1970
        } else {
            var timeDelta = Date().timeIntervalSince1970 - startTime
            #if DEBUG
                timeDelta = 70 * 60 * 24
            #endif
            if timeDelta > 60 * 60 * 24 {
                if (AppSettings.appStartCount ?? 0) > 3 {
                    if !AppSettings.isFull() {
                        showPromoDialog()
                    }
                }
            }
        }
    }
    
    func showPromoDialog() {
        #if DEBUG
            return
        #endif
        var expiredPromoTime = AppSettings.expiredPromotionTime ?? 0
        if expiredPromoTime == 0 {
            AppSettings.expiredPromotionTime = Date().timeIntervalSince1970 + Double(60 * 60 * 24)
            expiredPromoTime = AppSettings.expiredPromotionTime ?? 0
        }
        if Date().timeIntervalSince1970 < expiredPromoTime {
            KUAPI.request(.purchases(promo_code: "UYS32D"), APIPurchases.self) { result, error in
                if error == nil {
                    if result?.saleOff ?? 0 > 0 {
                        result?.purchases = result?.purchases?.filter { $0.saleOff ?? 0 > 0 }
                        let vc = FlashsaleViewController()
                        vc.applyPopPresenter()
                        vc.disableTapToClose()
                        vc.purchases = result
                        if self.presentedViewController != nil {
                            self.presentedViewController?.dismiss(animated: true, completion: { [weak self] in
                                guard let self = self else { return }
                                self.present(vc, animated: true)
                            })
                        } else {
                            self.present(vc, animated: true)
                        }
                    }
                }
            }
        }
    }
    
    func handleLineseState(result: APIUnlock?, error: APIError?) {
        if error != nil {
            if error?.code == -1001 {
                AppSettings.expiresAt = 0
                AppSettings.activateCode = nil
                let vc = MessagePopupViewController()
                vc.applyPopPresenter()
                vc.popupTitle = "Chú ý"
                vc.popupDescription = "Đã gỡ mã kích hoạt khỏi ứng dụng thành công"
                self.present(vc, animated: true)
            } else if error?.code == -1002 {
                let oneDayInMiliseconds = 24 * 60 * 60 * 1000.0
                let yesterday = Int(Date().timeIntervalSince1970 * 1000 - oneDayInMiliseconds)
                AppSettings.expiresAt = yesterday
                let vc = MessagePopupViewController()
                vc.applyPopPresenter()
                vc.hideConfirm = false
                vc.confirmAction = { [weak self] in
                    guard let self = self else { return }
                    Scheduler().schedule(delay: 1) { [weak self] in
                        guard let self = self else { return }
                        let vc = PurchasesViewController()
                        vc.modalPresentationStyle = .fullScreen
                        self.present(vc, animated: true)
                    }
                }
                vc.popupTitle = "Chú ý"
                vc.rootView.buttonConfirm.titleLabel?.text = "Gia hạn"
                vc.rootView.buttonClose.titleLabel?.text = "Đóng"
                vc.popupDescription = "Gói học của bé đã hết hạn, gia hạn gói học để không gián đoạn việc học của bé nhé!"
                self.present(vc, animated: true)
            } else if error?.code != 17 {
                AppSettings.expiresAt = 0
            }
        } else if result != nil {
            AppSettings.expiresAt = result!.id == "full" ? -1 : result!.expiresAt!
        }
    }
    
    func checkLicenseState() {
        if AppSettings.activateCode != nil {
            KUAPI.request(.deviceCheck, APIUnlock.self) { [weak self] result, error in
                guard let self = self else { return }
                self.handleLineseState(result: result, error: error)
            }
        }
    }
    
    func checkUserLocation() {
        #if DEBUG
            //AppSettings.registerPhone = ""
        #endif
        KUAPI.request(.country, APICountry.self) { [weak self] result, error in
            guard let self = self else { return }
            if result != nil {
                if (result?.country ?? "") != "" {
                    AppSettings.country = result!.country!
                }
                /*
                if !AppSettings.isFull() && AppSettings.isUserInVietnam && (AppSettings.registerPhone ?? "") == "" {
                    let vc = ActivateCodeViewController()
                    vc.onActived = { [weak self] in
                        guard let self = self else { return }
                    }
                    vc.applyPopPresenter()
                    (vc.contentView as? ActivateCodeView)?.showRegisterButton = true
                    (vc.contentView as? ActivateCodeView)?.trial = true
                    (vc.contentView as? ActivateCodeView)?.buttonRegister.set(title: "Học thử")
                }*/
            }
            if AppSettings.isUserInVietnam {
                showHocThuInNeeded()
            }
        }
    }
    
    func showHocThuInNeeded() {
        if (AppSettings.registerPhone == nil || AppSettings.registerPhone == "") && !AppSettings.isFull() {
            
            let trialDialog = TrialDialogFragment()
            trialDialog.onClosed = { [weak self] in
                self?.scheduler.schedule(delay: 0.5) {
                    [weak self] in
                    guard let self = self else { return }
                    self.createProfileInNeeded()
                }
            }
            trialDialog.modalPresentationStyle = .fullScreen
            self.present(trialDialog, animated: false)
        } else {
            createProfileInNeeded()
        }
    }
    
    func createProfileInNeeded() {
        let profiles = DataManager.shared.getProfiles()
        var foundProfile = false
        
        for profile in profiles {
            if profile.name != DataManager.DEFAULT_USERNAME {
                foundProfile = true
                break
            }
        }
        
        if !foundProfile {
            let newProfileDialog = NewProfileDialogFragment()
            newProfileDialog.onClosed = { [weak self] in
                self?.removeDefaultProfile()
            }
            newProfileDialog.modalPresentationStyle = .fullScreen
            newProfileDialog.isCancelable = false
            self.waitAndDismissIfNeededThenPresent(newProfileDialog, animated: true)
        } else {
            removeDefaultProfile()
        }
    }

    private func removeDefaultProfile() {
        DataManager.shared.removeDefaultProfiles()
        if let deleted = DataManager.shared.getCurrentProfile()?.deleted, deleted {
            if let firstProfileId = DataManager.shared.getProfiles().first?.id {
                DataManager.shared.selectProfile(firstProfileId)
            }
        }
        FirebaseUtils.shared.loginFirebase { [weak self] in
            self?.loginSuccess()
        }
    }

    private func loginSuccess() {
        //FirebaseUtils.utils.saveLocalToFirebaseIfNeed()
        FirebaseUtils.shared.loadRemoteProfiles(completion: nil)
    }
    
    @objc func handleProfileChanged() {
        if let profile = DataManager.shared.currentProfile {
            imageAvatar.image = Utilities.GetSVGKImage(named: "Animal Heads/\(BirthdayHelper.shared.getPhoto(year: profile.yearOfBirth)).svg")
        }
    }
    
    @objc private func toGallery() {
        let vc = GalleryMainViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    @objc private func toSetting() {
        let vc = SettingsDialogViewController()
        BlurPanelKit.present(vc, from: self)
    }
    @objc private func toSearch() {
        let vc = TimKiemMainViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
}

// MARK: - ItemHomeCell
class ItemHomeCell: UICollectionViewCell {
    private let imageView = SVGImageView(frame: .zero)
    private let imageWaveView = SVGImageView(frame: .zero)
    public let textView = AutosizeLabel()
    private let containerView = UIView()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        
        // Container View
        containerView.clipsToBounds = true
        contentView.addSubview(containerView)
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Image View
        imageView.contentMode = .scaleAspectFit
        containerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Image View
        imageWaveView.contentMode = .scaleAspectFit
        containerView.addSubview(imageWaveView)
        imageWaveView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Text View
        textView.textAlignment = .center
        textView.font = .Freude(size: 20)
        containerView.addSubview(textView)
        textView.snp.makeConstraints { make in
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalTo(textView.snp.width).multipliedBy(0.3)
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(image: String, name: String, color: UIColor) {
        imageView.SVGName = image
        textView.text = name
        textView.textColor = color
        imageWaveView.SVGName = "btn_subject_wave"
    }
}


func getTimeRemainingUntilMidnight() -> String {
    let calendar = Calendar.current
    let now = Date()
    let midnight = calendar.startOfDay(for: calendar.date(byAdding: .day, value: 1, to: now)!)
    let components = calendar.dateComponents([.hour, .minute, .second], from: now, to: midnight)
    
    let hours = components.hour ?? 0
    let minutes = components.minute ?? 0
    let seconds = components.second ?? 0
    return String(format: "%02d:%02d:%02d", hours, minutes, seconds)
}
