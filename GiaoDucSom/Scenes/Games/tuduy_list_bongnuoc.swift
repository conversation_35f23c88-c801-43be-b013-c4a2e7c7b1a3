//
//  tuduy_list_bongnuoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_bongnuoc: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var boatView: UIView!
    private var boatBottomView: UIView!
    private var imageLunar2: UIImageView!
    private var shadowView: UIImageView!
    var answerList: [BNData] = []
    let rightBgView = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 66/255, green: 102/255, blue: 151/255, alpha: 1) // #426697
        
        let leftContainer = UIView()
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        let topView = UIView()
        topView.backgroundColor = UIColor(red: 27/255, green: 56/255, blue: 90/255, alpha: 1) // #1B385A
        leftContainer.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(leftContainer).multipliedBy(0.6)
        }
        
        boatView = UIView()
        boatView.clipsToBounds = false
        topView.addSubview(boatView)
        boatView.snp.makeConstraints { make in
            make.height.equalTo(topView).multipliedBy(0.8)
            make.left.bottom.right.equalToSuperview()
        }
        
        let boat1 = createItemBongNuoc()
        boatView.addSubview(boat1)
        boat1.makeViewCenterAndKeep(ratio: 1)
        
        
        shadowView = UIImageView()
        shadowView.image = Utilities.SVGImage(named: "tuduy_bongnuoc_thuyen2")
        boatView.addSubview(shadowView)
        shadowView.snp.makeConstraints { make in
            make.height.equalTo(boatView).multipliedBy(0.8)
            make.width.equalTo(shadowView.snp.height).multipliedBy(1.2) // Ratio 1.2:1
            make.centerX.equalToSuperview()
            make.top.equalTo(boat1.snp.bottom)
        }
        
        imageLunar2 = UIImageView(image: Utilities.SVGImage(named: "tuduy_bongnuoc_trang2"))
        shadowView.addSubview(imageLunar2)
        imageLunar2.snp.makeConstraints { make in
            make.top.bottom.equalTo(shadowView)
            make.width.equalTo(imageLunar2.snp.height).multipliedBy(0.3) // Ratio 0.4:1
        }
        
        let bottomContainer = UIView()
        bottomContainer.transform = CGAffineTransform(scaleX: 1, y: 0.72)
        bottomContainer.layer.anchorPoint = CGPoint(x: 0.5, y: 0.7)
        leftContainer.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(leftContainer).multipliedBy(0.48)
            make.top.equalTo(topView.snp.bottom).offset(1)
        }
        
        boatBottomView = UIView()
        boatBottomView.alpha = 0
        boatBottomView.transform = CGAffineTransform(scaleX: 1, y: -1)
        bottomContainer.addSubview(boatBottomView)
        boatBottomView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let boat2 = createItemBongNuoc()
        boatBottomView.addSubview(boat2)
        boat2.makeViewCenterAndKeep(ratio: 1)
        
        rightBgView.backgroundColor = UIColor(red: 100/255, green: 159/255, blue: 228/255, alpha: 1) // #649FE4
        view.addSubview(rightBgView)
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBgView.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
    }
    
    // MARK: - Game Logic
    
    override func updateData() {
        super.updateData()
        
        let data = randomBNData()
        answerList = [data]
        for _ in 0..<3 {
            while true {
                let d = randomBNData(old: data)
                if !answerList.contains(where: { $0 == d }) {
                    answerList.append(d)
                    break
                }
            }
        }
        
        updateBNData(view: boatView, data: data)
        updateBNData(view: boatBottomView, data: data)
        
        imageLunar2.transform = CGAffineTransform(scaleX: data.lunarFlip ? 1 : -1, y: 1)
        addActionOnLayoutSubviews { [self] in
            imageLunar2.snapToHorizontalBias(horizontalBias: Double(data.lunaInPosition) / 2)
        }
        
        var views: [UIView] = []
        for i in 0..<answerList.count {
            let d = answerList[i]
            let view = createItemBongNuocList(data: d)
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
            view.tag = i
            views.append(view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views.shuffled())
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_bong nuoc")
        gridLayout.showItems(startDelay: delay)
        scheduler.schedule(after: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
     
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_bong nuoc")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        pauseGame()
        let index = view.tag
        let selectedBNData = answerList[index]
        if selectedBNData == answerList[0] { // BNData đúng là answerList[0]
            let firstChild = view.subviews[0]
            animateCoinIfCorrect(view: firstChild)
            var delay = playSound(answerCorrect1EffectSound())
            UIView.animate(withDuration: 0.5, delay: delay) {
                self.boatBottomView.alpha = 0.5
                self.shadowView.alpha = 0
            }
            delay += 0.5
            delay += playSound(delay: delay, names: [self.getCorrectHumanSound(), self.endGameSound()])
            delay += 2.0
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemBongNuoc() -> UIView {
        let view = UIView()
        let container = UIView()
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let imageBoat = UIImageView(image: Utilities.SVGImage(named: "tuduy_bongnuoc_thuyen"))
        imageBoat.stringTag = "image_boat"
        container.addSubview(imageBoat)
        imageBoat.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let imageFlag = UIImageView(image: Utilities.SVGImage(named: "tuduy_bongnuoc_co"))
        imageFlag.stringTag = "image_flag"
        container.addSubview(imageFlag)
        imageFlag.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let imageLunar = UIImageView(image: Utilities.SVGImage(named: "tuduy_bongnuoc_trang"))
        imageLunar.stringTag = "image_lunar"
        container.addSubview(imageLunar)
        imageLunar.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.top.bottom.equalToSuperview()
        }
        
        return view
    }
    
    private func createItemBongNuocList(data: BNData) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "tuduy_bongnuoc_option_bg")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let innerContainer = UIView()
        innerContainer.transform = CGAffineTransform(scaleX: 1, y: -1)
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let item = createItemBongNuoc()
        innerContainer.addSubview(item)
        item.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.7)
            make.height.equalTo(item.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        updateBNData(view: item, data: data)
        return view
    }
    
    private func updateBNData(view: UIView, data: BNData) {
        guard let imageFlag = view.viewWithStringTag("image_flag") as? UIImageView,
              let imageBoat = view.viewWithStringTag("image_boat") as? UIImageView,
              let imageLunar = view.viewWithStringTag("image_lunar") as? UIImageView else { return }
        
        imageFlag.transform = CGAffineTransform(scaleX: data.flagFlip ? 1 : -1, y: 1)
        imageBoat.transform = CGAffineTransform(scaleX: data.boatFlip ? 1 : -1, y: 1)
        imageLunar.transform = CGAffineTransform(scaleX: data.lunarFlip ? 1 : -1, y: 1)
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            imageLunar.snapToHorizontalBias(horizontalBias: Double(data.lunaInPosition)/2)
        }
    }
    
    private func randomBNData() -> BNData {
        var d = BNData()
        d.flagFlip = Bool.random()
        d.boatFlip = Bool.random()
        d.lunarFlip = Bool.random()
        d.lunaInPosition = Int.random(in: 0..<3)
        return d
    }
    
    private func randomBNData(old: BNData) -> BNData {
        var d = BNData()
        d.flagFlip = Int.random(in: 0..<5) < 4 ? old.flagFlip : Bool.random()
        d.boatFlip = Int.random(in: 0..<5) < 4 ? old.boatFlip : Bool.random()
        d.lunarFlip = Int.random(in: 0..<5) < 4 ? old.lunarFlip : Bool.random()
        d.lunaInPosition = Int.random(in: 0..<5) < 4 ? old.lunaInPosition : Int.random(in: 0..<3)
        return d
    }
}

// MARK: - Supporting Structures
struct BNData: Equatable {
    var flagFlip: Bool = false
    var boatFlip: Bool = false
    var lunarFlip: Bool = false
    var lunaInPosition: Int = 0
    
    static func ==(lhs: BNData, rhs: BNData) -> Bool {
        return lhs.flagFlip == rhs.flagFlip &&
               lhs.boatFlip == rhs.boatFlip &&
               lhs.lunarFlip == rhs.lunarFlip &&
               lhs.lunaInPosition == rhs.lunaInPosition
    }
}

