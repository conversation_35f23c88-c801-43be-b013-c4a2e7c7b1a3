//
//  tuduy_list_quyluattron.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_quyluattron: NhanBietGameFragment {
    // MARK: - Properties
    private var topGridLayout: MyGridView!
    private var gridLayout: MyGridView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        topGridLayout = MyGridView()
        view.addSubview(topGridLayout)
        topGridLayout.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.bottom.equalTo(view.snp.centerY)
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
            make.top.equalTo(topGridLayout.snp.bottom)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let data = createData()
        var views: [UIView] = []
        for i in 0..<data.count {
            let d = data[i]
            let view = createItemQuyLuatTron(data: d, isLast: i == data.count - 1)
            views.append(view)
        }
        topGridLayout.columns = data.count
        topGridLayout.itemRatio = 1
        topGridLayout.itemSpacingRatio = 0.02
        topGridLayout.insetRatio = 0.02
        topGridLayout.reloadItemViews(views: views)
        
        var answerList: [DataQLT] = []
        let rightItem = data.last!
        answerList.append(rightItem)
        for _ in 0..<3 {
            while true {
                let d = randomData(old: rightItem)
                if !answerList.contains(where: { $0 == d }) {
                    answerList.append(d)
                    break
                }
            }
        }
        let meIndex = 0
        
        views = []
        for i in 0..<answerList.count {
            let d = answerList[i]
            let view = createItemQuyLuatTron(data: d, isLast: false)
            view.tag = i
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
            views.append(view)
        }
        
        gridLayout.columns = topGridLayout.subviews.count
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.insetRatio = 0.02
        gridLayout.reloadItemViews(views: views.shuffled())
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_quy luat tron") + 0.5
        gridLayout.showItems(startDelay: delay)
        scheduler.schedule(after: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_quy luat tron")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        pauseGame()
        
        if index == 0 { // meIndex = 0 (rightItem)
            if let imageBg = view.subviews.first?.subviews.first(where: { $0 is UIImageView }) as? UIImageView {
                animateCoinIfCorrect(view: view)
            }
            let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(after: 1.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createData() -> [DataQLT] {
        var data: [DataQLT] = []
        let bgRotation = random(0, 90, 90, 180, 270, 270)
        let circleFill = Bool.random()
        let smallCircleRotation = random(0, 90, 90, 180, 270, 270)
        let smallCircleFill = Bool.random()
        
        var d = randomData()
        data.append(d)
        
        let size = random(3, 4, 5)
        for _ in 0..<size {
            let last = data.last!
            d = DataQLT()
            d.bg_rotation = (last.bg_rotation + Float(bgRotation)).truncatingRemainder(dividingBy: 360)
            d.circle_fill = circleFill ? !last.circle_fill : last.circle_fill
            d.small_circle_rotation = (last.small_circle_rotation + Float(smallCircleRotation)).truncatingRemainder(dividingBy: 360)
            d.small_circle_fill = smallCircleFill ? !last.small_circle_fill : last.small_circle_fill
            data.append(d)
        }
        return data
    }
    
    private func randomData() -> DataQLT {
        var d = DataQLT()
        d.bg_rotation = Float(random(0, 90, 180, 270))
        d.circle_fill = Bool.random()
        d.small_circle_rotation = Float(random(0, 90, 180, 270))
        d.small_circle_fill = Bool.random()
        return d
    }
    
    private func randomData(old: DataQLT) -> DataQLT {
        var d = DataQLT()
        d.bg_rotation = arc4random_uniform(5) < 4 ? old.bg_rotation : Float(random(0, 90, 180, 270))
        d.circle_fill = arc4random_uniform(5) < 4 ? old.circle_fill : Bool.random()
        d.small_circle_rotation = arc4random_uniform(5) < 4 ? old.small_circle_rotation : Float(random(0, 90, 180, 270))
        d.small_circle_fill = arc4random_uniform(5) < 4 ? old.small_circle_fill : Bool.random()
        return d
    }
    
    private func createItemQuyLuatTron(data: DataQLT, isLast: Bool) -> UIView {
        let view = UIView()
        let container = UIView()
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height) // Ratio 1:1
        }
        
        let imageBg = UIImageView(image: Utilities.SVGImage(named: isLast ? "tuduy_quyluattron4" : "tuduy_quyluattron1"))
        imageBg.contentMode = .scaleAspectFit
        if !isLast {
            imageBg.transform = CGAffineTransform(rotationAngle: CGFloat(data.bg_rotation) * .pi / 180)
        }
        container.addSubview(imageBg)
        imageBg.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let imageBigCircle = UIImageView(image: Utilities.SVGImage(named: data.circle_fill ? "tuduy_quyluattron3_a" : "tuduy_quyluattron3_b"))
        imageBigCircle.contentMode = .scaleAspectFit
        imageBigCircle.isHidden = isLast
        container.addSubview(imageBigCircle)
        imageBigCircle.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let imageSmallCircle = UIImageView(image: Utilities.SVGImage(named: data.small_circle_fill ? "tuduy_quyluattron2_a" : "tuduy_quyluattron2_b"))
        imageSmallCircle.contentMode = .scaleAspectFit
        imageSmallCircle.transform = CGAffineTransform(rotationAngle: CGFloat(data.small_circle_rotation) * .pi / 180)
        imageSmallCircle.isHidden = isLast
        container.addSubview(imageSmallCircle)
        imageSmallCircle.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return view
    }
}

// MARK: - Supporting Structures
struct DataQLT: Equatable {
    var bg_rotation: Float = 0
    var circle_fill: Bool = false
    var small_circle_rotation: Float = 0
    var small_circle_fill: Bool = false
    
    static func ==(lhs: DataQLT, rhs: DataQLT) -> Bool {
        return lhs.bg_rotation == rhs.bg_rotation &&
               lhs.circle_fill == rhs.circle_fill &&
               lhs.small_circle_rotation == rhs.small_circle_rotation &&
               lhs.small_circle_fill == rhs.small_circle_fill
    }
}


