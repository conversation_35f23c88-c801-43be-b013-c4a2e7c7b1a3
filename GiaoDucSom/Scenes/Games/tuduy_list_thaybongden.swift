//
//  tuduy_list_thaybongden.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_thaybongden: NhanBietGameFragment {
    // MARK: - Properties
    private let lightIds = [
        R12.drawable.tuduy_pattern_light_green,
        R12.drawable.tuduy_pattern_light_yellow,
        R12.drawable.tuduy_pattern_light_red
    ]
    private var colorIndexes: [Int] = []
    private var lightsView: UIView!
    private var btnGreen: UIView!
    private var btnYellow: UIView!
    private var btnRed: UIView!
    private var lightsList: [UIImageView] = []
    private var lightRotation: UIImageView!
    private var animationView: XAMLAnimationView!
    private var pattern: [Int] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 138/255, green: 230/255, blue: 255/255, alpha: 1) // #8AE6FF
        
        let bgContainer = UIView()
        view.addSubview(bgContainer)
        bgContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(bgContainer.snp.width).multipliedBy(155.0 / 1000.0) // Ratio 1000:155
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "tuduy_pattern_bg"))
        bgImage.contentMode = .scaleAspectFit
        bgContainer.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        btnGreen = UIView()
        btnGreen.backgroundColor = UIColor.red.withAlphaComponent(0.01) // #01ff0000
        bgContainer.addSubview(btnGreen)
        btnGreen.snp.makeConstraints { make in
            make.height.equalTo(bgContainer).multipliedBy(0.6)
            make.width.equalTo(btnGreen.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.btnGreen.snapToHorizontalBias(horizontalBias: 0.565)
        }
        btnGreen.tag = 0
        let greenTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        btnGreen.addGestureRecognizer(greenTapGesture)
        btnGreen.isUserInteractionEnabled = true
        
        btnYellow = UIView()
        btnYellow.backgroundColor = UIColor.red.withAlphaComponent(0.01) // #01ff0000
        bgContainer.addSubview(btnYellow)
        btnYellow.snp.makeConstraints { make in
            make.height.equalTo(bgContainer).multipliedBy(0.6)
            make.width.equalTo(btnYellow.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.btnYellow.snapToHorizontalBias(horizontalBias: 0.68)
        }
        btnYellow.tag = 1
        let yellowTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        btnYellow.addGestureRecognizer(yellowTapGesture)
        btnYellow.isUserInteractionEnabled = true
        
        btnRed = UIView()
        btnRed.backgroundColor = UIColor.red.withAlphaComponent(0.01) // #01ff0000
        bgContainer.addSubview(btnRed)
        btnRed.snp.makeConstraints { make in
            make.height.equalTo(bgContainer).multipliedBy(0.6)
            make.width.equalTo(btnRed.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.btnRed.snapToHorizontalBias(horizontalBias: 0.8)
        }
        btnRed.tag = 2
        let redTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        btnRed.addGestureRecognizer(redTapGesture)
        btnRed.isUserInteractionEnabled = true
        animationView = XAMLAnimationView()
        animationView.alpha = 0.01
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "dancing boy")!)
            animationView.loadView(from: xamlData)
        } catch {}
        bgContainer.addSubview(animationView)
        animationView.snp.makeConstraints { make in
            make.width.equalTo(bgContainer).multipliedBy(0.3)
            make.height.equalTo(animationView.snp.width) // Ratio 1:1
            make.bottom.equalTo(bgContainer).multipliedBy(0.55) // Guideline 0.55
        }
        addActionOnLayoutSubviews {
            self.animationView.snapToHorizontalBias(horizontalBias: 0.18)
            self.animationView.alpha = 1
        }
        
        let lightsContainer = UIView()
        view.addSubview(lightsContainer)
        lightsContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.width.equalTo(lightsContainer.snp.height).multipliedBy(9.0) // Ratio 9:1
        }
        
        let bgOverlay = UIView()
        bgOverlay.backgroundColor = UIColor(red: 74/255, green: 74/255, blue: 133/255, alpha: 1) // #4A4A85
        lightsContainer.addSubview(bgOverlay)
        bgOverlay.snp.makeConstraints { make in
            make.width.equalTo(lightsContainer).multipliedBy(0.8)
            make.height.equalTo(lightsContainer).multipliedBy(0.4)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        lightsView = UIView()
        lightsContainer.addSubview(lightsView)
        lightsView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let lightImages = [
            ("tuduy_pattern_light_green", 21),
            ("tuduy_pattern_light_red", 20),
            ("tuduy_pattern_light_yellow", 22),
            ("tuduy_pattern_light_green", 23),
            ("tuduy_pattern_light_red", 24),
            ("tuduy_pattern_light", 25)
        ]
        
        var previousView: UIImageView?
        for (index, light) in lightImages.enumerated() {
            let imageView = UIImageView(image: Utilities.SVGImage(named: light.0))
            imageView.contentMode = .scaleAspectFit
            imageView.tag = light.1
            lightsView.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.height.equalTo(lightsView).multipliedBy(0.9)
                make.width.equalTo(imageView.snp.height).multipliedBy(1.3 / 1.0) // Ratio 1.3
                make.bottom.equalToSuperview()
                if index == 0 {
                    //make.left.equalToSuperview()
                } else {
                    make.left.equalTo(previousView!.snp.right)
                }
                if index == lightImages.count - 1 {
                    make.right.equalToSuperview().multipliedBy(0.89)
                }
            }
            lightsList.append(imageView)
            previousView = imageView
        }
        
        lightRotation = UIImageView(image: Utilities.SVGImage(named: "tuduy_pattern_dot"))
        lightRotation.contentMode = .scaleAspectFit
        lightsContainer.addSubview(lightRotation)
        lightRotation.snp.makeConstraints { make in
            make.height.equalTo(lightsContainer).multipliedBy(0.65)
            make.width.equalTo(lightRotation.snp.height) // Ratio 1:1
            //make.centerY.equalToSuperview().multipliedBy(1.94) // Bias 0.97
        }
        addActionOnLayoutSubviews {
            self.lightRotation.snapToHorizontalBias(horizontalBias: 0.85)
            self.lightRotation.snapToVerticalBias(verticalBias: 0.97)
        }
        
        let rotation = CABasicAnimation(keyPath: "transform.rotation")
        rotation.fromValue = 0
        rotation.toValue = 2 * CGFloat.pi
        rotation.duration = 5.0
        rotation.repeatCount = .infinity
        rotation.timingFunction = CAMediaTimingFunction(name: .linear)
        lightRotation.layer.add(rotation, forKey: "rotationAnimation")
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        colorIndexes = [0, 1, 2].shuffled()
        pattern = getPattern()
        for i in 0..<(pattern.count - 1) {
            lightsList[i].image = Utilities.SVGImage(named: lightIds[colorIndexes[pattern[i]]].toDrawableNameTBD())
        }
        let delay = playSound(openGameSound(), "tuduy/pattern")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/pattern")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleButtonTap(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let colorIndex = view.tag
        pauseGame()
        
        if colorIndex == colorIndexes[pattern[5]] {
            animateCoinIfCorrect(view: view)
            animationView.startAnimationStoryboard(with: "sb")
            playSound("effect/answer_correct1")
            lightsList[5].image = Utilities.SVGImage(named: lightIds[colorIndexes[pattern[5]]].toDrawableNameTBD())
            UIView.animate(withDuration: 0.5) {
                self.lightRotation.alpha = 0
            }
            let delay = 3.0 + playSound(delay: 3.0, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func getPattern() -> [Int] {
        let totalPatterns: [[Int]] = [
            [0, 1, 0, 1, 0, 1],
            [0, 1, 2, 0, 1, 2],
            [0, 1, 1, 0, 1, 1],
            [0, 1, 0, 0, 1, 0],
            [1, 1, 0, 1, 1, 0],
            [0, 0, 1, 1, 2, 2],
            [0, 0, 0, 1, 1, 1],
            [0, 1, 2, 2, 1, 0]
        ]
        return totalPatterns.randomElement()!
    }
}

// MARK: - Supporting Structures


struct R12 {
    struct drawable {
        static let tuduy_pattern_light_green = 1
        static let tuduy_pattern_light_yellow = 2
        static let tuduy_pattern_light_red = 3
        static let tuduy_pattern_light = 4
        static let tuduy_pattern_dot = 5
        static let tuduy_pattern_bg = 6
    }
}

extension Int {
    func toDrawableNameTBD() -> String {
        switch self {
        case R12.drawable.tuduy_pattern_light_green:
            return "tuduy_pattern_light_green"
        case R12.drawable.tuduy_pattern_light_yellow:
            return "tuduy_pattern_light_yellow"
        case R12.drawable.tuduy_pattern_light_red:
            return "tuduy_pattern_light_red"
        case R12.drawable.tuduy_pattern_light:
            return "tuduy_pattern_light"
        case R12.drawable.tuduy_pattern_dot:
            return "tuduy_pattern_dot"
        case R12.drawable.tuduy_pattern_bg:
            return "tuduy_pattern_bg"
        default:
            return "empty"
        }
    }
}

