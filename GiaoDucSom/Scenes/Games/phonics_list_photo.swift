//
//  phonics_list_photo.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_photo: GameFragment {
    private var values : [String] = []
    let topContainer = UIView()
    let labelTop = AutosizeLabel().then{
        $0.textColor = .white
    }
    let bottomContainer = UIView()
    let bottomBackground = SVGImageView(SVGName: "bg obj white")
    let bottomImage = SVGImageView(frame: CGRect<PERSON>ero)
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#88D35A")
        addSubview(topContainer)
        topContainer.snp.makeConstraints{ make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.2)
        }
        topContainer.addSubview(labelTop)
        labelTop.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
        }
        labelTop.text = "Text"
        addSubview(bottomContainer)
        bottomContainer.addSubview(bottomBackground)
        bottomBackground.addSubview(bottomImage)
        bottomContainer.snp.makeConstraints{ make in
            make.centerX.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.top.equalTo(topContainer.snp.bottom)
        }
        bottomBackground.makeViewCenterAndKeep(ratio: 1)
        bottomImage.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        bottomContainer.alpha = 0.01
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        let values2 = (game.values2?.compactMap { $0.value as? String })!
        labelTop.text = values[0]
        bottomImage.SVGName = "flashcards/\(values2[0]).svg"
        var delay = 0.5
        delay += self.playSound(name: values[0], delay: delay)
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            let easy = BackEaseInterpolater()
            easy.mode = .easeOut
            let animValues: [Double] = [0,1]
            let timeChange = Interpolate(values: [0,1],
            apply: { [weak self] (value) in
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                self?.bottomContainer.transform = CGAffineTransformMakeScale(finalValue, finalValue)
                self?.bottomContainer.alpha = value
            })
            timeChange.animate(1, duration: 0.6){
                
            }
        })
        delay += 2
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.finishGame()
        })
    }
    var enableStartGameSound: Bool {
        false
    }
}
