//
//  tuduy_list_diemxuyenchonggiay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_diemxuyenchonggiay: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: UIImageView!
    private var counts: [Int] = []
    private var answer: Int = 0
    private var textIntro: UITextView!
    private var gridLayout: MyGridView!
    private var correctIndex = 0
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFFFFF
        
        svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        let rotations = [0, 90, 180, 270]
        svgView.transform = CGAffineTransform(rotationAngle: CGFloat(rotations.randomElement()!) * .pi / 180)
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(view.snp.centerX)
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        
        textIntro = UITextView()
        textIntro.isEditable = false
        textIntro.backgroundColor = .clear
        textIntro.isHidden = true
        textIntro.font = .systemFont(ofSize: 30)
        view.addSubview(textIntro)
        textIntro.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    var svg: SVGKImage!
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let filename = "images/tuduy_diemxuyenchonggiay\(random(1, 2, 3, 4, 5, 6, 7, 8, 9)).svg"
        svg = Utilities.GetSVGKImage(named: filename)
        let groups = svg.caLayerTree.sublayers!
        for i in 0..<4 {
            groups[i].sublayers?[0].opacity = 1
        }
        
        svgView.transform = svgView.transform.scaledBy(x: 0.8, y: 0.8)
        
        let lastGroup = groups.last!
        counts = []
        for i in 0..<lastGroup.sublayers!.count {
            let path = lastGroup.sublayers![i]
            
            let bound = path.shapeContentBounds!
            let x = bound.midX
            let y = bound.midY
            var count = 0
            for j in 0..<4 {
                if let layer = groups[j].hitTest(CGPointMake(x, y)) {
                    count += 1
                }
            }
            counts.append(count)
        }
        
        var items: [Int]
        while true {
            items = Utils.generatePermutation(4, size: counts.count).shuffled()
            if let foundItem = items.first(where: { item in
                let count = counts[item]
                if count == 1 { return false }
                return items.filter { counts[$0] == count }.count == 1
            }) {
                answer = counts[foundItem]
                correctIndex = foundItem
                break
            }
        }
        
        let colors = [
            UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1), // #FF7760
            UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1), // #87D657
            UIColor(red: 184/255, green: 86/255, blue: 255/255, alpha: 1), // #B856FF
            UIColor(red: 48/255, green: 161/255, blue: 255/255, alpha: 1)  // #30A1FF
        ]
        
        for i in 0..<lastGroup.sublayers!.count {
            let path = lastGroup.sublayers![i]
            path.opacity = items.contains(i) ? 1 : 0
            if items.contains(i) {
                path.setFillColor(color: colors[items.firstIndex(of: i)!])
            }
        }
        svgView.image = svg.uiImage
        textIntro.text = "Hãy chọn điểm xuyên qua \(answer) tờ giấy"
        
        var views: [UIView] = []
        for i in 0..<colors.count {
            let view = createItemDiemXuyenChongGiay(color: colors[i])
            view.tag = counts[items[i]]
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
            views.append(view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        let delay = self.playSound(self.openGameSound(), "tuduy/tuduy_diem xuyen chong giay\(self.answer == 4 ? "" : "\(self.answer)")")
        self.gridLayout.showItems(startDelay: delay)
        self.scheduler.schedule(after: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_diem xuyen chong giay\(answer == 4 ? "" : "\(answer)")")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        pauseGame()
        let tag = view.tag
        if tag == answer {
            let firstChild = view.subviews[0]
            animateCoinIfCorrect(view: firstChild)
        
            var delay = playSound("effect/answer_end")
            
            // Tô màu chấm trên SVG
            guard let groups = svg.caLayerTree.sublayers,
                  let lastGroup = groups.last else { return }
            
            let index = correctIndex
            let path = lastGroup.sublayers![index]
            let bound = path.shapeContentBounds!
            let x = bound.midX
            let y = bound.midY
            
            let colors = [
                UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1), // #FF7760
                UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1), // #87D657
                UIColor(red: 184/255, green: 86/255, blue: 255/255, alpha: 1), // #B856FF
                UIColor(red: 48/255, green: 161/255, blue: 255/255, alpha: 1)  // #30A1FF
            ]
            
            for j in 0..<4 {
                let finalJ = j
                scheduler.schedule(after: delay + TimeInterval(j) * 0.5) {
                    [weak self] in
                    guard let self = self else { return }
                    let contains = groups[finalJ].hitTest(CGPointMake(x, y)) != nil
                    if contains {
                        groups[finalJ].sublayers![0].setFillColor(color: colors[finalJ])
                        groups[finalJ].sublayers![0].opacity = 0.3
                        self.svgView.image = self.svg.uiImage
                        self.playSound("effect/bubble\(1 + finalJ)")
                    }
                    self.svgView.setNeedsDisplay() // Cập nhật SVG
                }
            }
            
            delay += 2.0
            delay += playSound(delay: delay, names: [self.getCorrectHumanSound(), self.endGameSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemDiemXuyenChongGiay(color: UIColor) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height) // Ratio 1:1
        }
        
        let viewDot = UIView()
        viewDot.accessibilityIdentifier = "view_dot"
        viewDot.backgroundColor = color
        viewDot.layer.cornerRadius = viewDot.frame.width / 2 // Giả lập CornerDrawable radius 50%
        container.addSubview(viewDot)
        viewDot.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.25)
            make.height.equalTo(viewDot.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return view
    }
}

