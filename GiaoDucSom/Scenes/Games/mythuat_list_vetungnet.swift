//
//  mythuat_list_vetungnet.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/6/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics


// MARK: - VeTungNetGameFragment
class mythuat_list_vetungnet: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var tracingView: TracingVeTungNetView!
    private var hintContainer: UIView!
    private var doneContainer: UIView!
    var filename: String?
    private var currentStroke: Int = 0
    private var path: CAShapeLayer?
    private var randomFilename: Bool = false
    private var coinView: UIView!
    private var svg: SVGKImage?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFFFFF")
        
        itemContainer = UIView()
        itemContainer.stringTag = "item_container"
        //itemContainer.backgroundColor = .gray.withAlphaComponent(0.2)
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8).priority(.high)
            make.height.equalToSuperview().multipliedBy(0.8).priority(.high)
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.8)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.8)
            //make.width.equalTo(itemContainer.snp.height).multipliedBy(596.0/400.0)
        }
        
        doneContainer = UIView()
        doneContainer.stringTag = "done_container"
        itemContainer.addSubview(doneContainer)
        doneContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        hintContainer = UIView()
        hintContainer.stringTag = "hint_container"
        hintContainer.alpha = 0.3
        itemContainer.addSubview(hintContainer)
        hintContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tracingView = TracingVeTungNetView()
        tracingView.stringTag = "tracing_view"
        itemContainer.addSubview(tracingView)
        tracingView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.stringTag = "coin_view"
        coinView.isUserInteractionEnabled = false
        view.addSubview(coinView)
        coinView.makeViewCenterAndKeep(ratio: 1)
        coinView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        
        tracingView.setListener { [weak self] in
            self?.onDone()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }        
        if filename == nil {
            var filenames: [String] = []
            let folders = StorageManager.manager.list(path: "draw tutorial")
            for folder in folders {
                let files = StorageManager.manager.list(path: "draw tutorial/\(folder)")
                filenames.append(contentsOf: files.map { "draw tutorial/\(folder)/\($0)" })
            }
            filename = filenames.randomElement()
            randomFilename = true
        }
    }
    
    override func createGame() {
        super.createGame()
        guard let filename = filename else { return }
        let svg = Utilities.GetSVGKImage(named: filename)
        self.svg = svg
        itemContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer.snp.height).multipliedBy(svg.size.width/svg.size.height)
        }
        scheduler.schedule(delay: 0.5) {
            [weak self] in
            guard let self = self else { return }
            loadStroke(0)
        }
    }
    
    // MARK: - Helper Methods
    private func loadStroke(_ strokeIndex: Int) {
        currentStroke = strokeIndex
        guard let svg = svg, strokeIndex < svg.caLayerTree.sublayers!.count else { return }
        path = svg.caLayerTree.sublayers![strokeIndex] as! CAShapeLayer
        tracingView.setSvg(svg, index: strokeIndex)
        tracingView.setColor(path?.strokeColor?.uiColor ?? UIColor.black)
        
        let pathView = PathView2()
        if let cgPath = path?.path {
            let scale = tracingView.bounds.width / svg.size.width
            let point = svg.caLayerTree.sublayers![strokeIndex].convert(CGRect.zero, to: svg.caLayerTree)
            // Scale a lần trước, rồi translate b/a (vì translate sẽ bị scale theo)
            var transform = CGAffineTransform(scaleX: scale, y: scale)
            transform = transform.translatedBy(x: point.minX, y: point.minY)

            let transformedPath = cgPath.copy(using: &transform)!
            pathView.setPath(transformedPath)
        }
        pathView.setColor(path?.strokeColor?.uiColor ?? UIColor.black)
        hintContainer.subviews.forEach { $0.removeFromSuperview() }
        hintContainer.addSubview(pathView)
        pathView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func onDone() {
        guard let path = path else { return }
        let pathMeasure = CGPathMeasure(path: path.path!)
        let length = pathMeasure.length
        let startPoint = pathMeasure.position(at: 0.01) ?? .zero
        let endPoint = pathMeasure.position(at: length) ?? .zero
        let distance = Utilities.distance(from: startPoint, to: endPoint)
        guard let svg = svg else { return }
        let pathView = PathView2()
        if let cgPath = path.path {
            let scale = tracingView.bounds.width / svg.size.width
            let point = svg.caLayerTree.sublayers![currentStroke].convert(CGRect.zero, to: svg.caLayerTree)
            // Scale a lần trước, rồi translate b/a (vì translate sẽ bị scale theo)
            var transform = CGAffineTransform(scaleX: scale, y: scale)
            transform = transform.translatedBy(x: point.minX, y: point.minY)

            let transformedPath = cgPath.copy(using: &transform)!
            pathView.setPath(transformedPath)
        }
        pathView.setColor(path.strokeColor?.uiColor ?? UIColor.black)
        if let fillColor = path.fillColor {
            pathView.setFillColor(fillColor.uiColor!)
        }
        doneContainer.addSubview(pathView)
        pathView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        if currentStroke == svg.caLayerTree.sublayers!.count - 1 {
            animateCoinIfCorrect(view: coinView)
            hintContainer.isHidden = true
            tracingView.isHidden = true
            pauseGame(stopMusic: false)
            if !randomFilename, let filename = filename {
                playVeTungNet(filename.replacingOccurrences(of: "images/", with: ""))
            }
            let delay = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            loadStroke(currentStroke + 1)
            playSound("effect/answer_correct")
        }
    }
    
    private func playVeTungNet(_ mask: String) {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        var masks = Set(defaults.stringArray(forKey: "vetungnet_played_\(profileId)") ?? [])
        masks.insert(mask)
        defaults.set(Array(masks), forKey: "vetungnet_played_\(profileId)")
    }
    
    // MARK: - Public Methods
    func setData(_ data: String) {
        filename = data.isEmpty ? nil : "draw tutorial/\(data)"
    }
}

// MARK: - PathView
class PathView2: UIView {
    private var path: CGPath?
    private var color: UIColor = UIColor.black
    private var fillColor: UIColor = UIColor.clear
    private let paint = Paint()
    private let fillPaint = Paint()

    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }

    private func initPaints() {
        backgroundColor = .clear
        paint.color = color
        paint.strokeWidth = 5.0
        fillPaint.color = fillColor
        fillPaint.style = .fill
    }

    func setPath(_ path: CGPath) {
        self.path = path
        setNeedsDisplay()
    }

    func setColor(_ color: UIColor) {
        self.color = color
        paint.color = color
        setNeedsDisplay()
    }

    func setFillColor(_ fillColor: UIColor) {
        self.fillColor = fillColor
        fillPaint.color = fillColor
        setNeedsDisplay()
    }

    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext(), let path = path else { return }
        context.addPath(path)
        context.setFillColor(fillPaint.color.cgColor)
        context.fillPath()
        context.addPath(path)
        context.setStrokeColor(paint.color.cgColor)
        context.setLineWidth(paint.strokeWidth * rect.height / 400.0)
        context.setLineCap(.round)
        context.setLineJoin(.round)
        context.strokePath()
    }
}

// MARK: - TracingVeTungNetView
class TracingVeTungNetView: UIView {
    // MARK: - Properties
    private var player: AVAudioPlayer?
    private let paint = Paint()
    private let paint2 = Paint()
    private var points: [CGPoint] = []
    private var leftPoints: [CGPoint] = []
    private var paths: [CGPath] = []
    private var onDone: (() -> Void)?
    private var color: UIColor = UIColor.black
    private var newPath: CGMutablePath?
    private var newPathJustCreated: Bool = false
    private var startPoint: CGPoint?
    private var startIndex: Int = 0
    private var svg: SVGKImage?
    private var path: CGPath?
    private var path2: CGMutablePath?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
        paint.color = UIColor(hex: "#FFB924")
        paint.strokeWidth = 5.0
        paint2.color = color
        playWritingSound()
    }
    
    // MARK: - Public Methods
    func setListener(onDone: @escaping () -> Void) {
        self.onDone = onDone
    }
    
    func setColor(_ color: UIColor) {
        self.color = color
        paint2.color = color
    }
    
    func setSvg(_ svg: SVGKImage, index: Int) {
        self.svg = svg
        paths = []
        let tenDp = bounds.width / 50
        guard let path = (svg.caLayerTree.sublayers?[index] as? CAShapeLayer)?.path else { return }
        self.path = path
        let scale = self.bounds.width / svg.size.width
        let p = svg.caLayerTree.sublayers![index].convert(CGRect.zero, to: svg.caLayerTree)
        let pathMeasure = CGPathMeasure(path: path)
        let pathLength = pathMeasure.length
        points = []
        let distance = tenDp / 5
        var currentDistance: CGFloat = 0.01
        path2 = CGMutablePath()
        // có thể thử getPoints
        while currentDistance < pathLength {
            if var point = pathMeasure.position(at: currentDistance) {
                point.x = (point.x + p.minX) * scale
                point.y = (point.y + p.minY) * scale
                points.append(point)
                if points.count == 1 {
                    path2?.move(to: point)
                } else {
                    path2?.addLine(to: point)
                }
            }
            currentDistance += distance
        }
        leftPoints = points
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        for path in paths {
            context.addPath(path)
            context.setStrokeColor(paint2.color.cgColor)
            context.setLineWidth(paint2.strokeWidth * rect.height / 400.0)
            context.setLineCap(.round)
            context.setLineJoin(.round)
            context.strokePath()
        }
        
        if let newPath = newPath {
            context.addPath(newPath)
            context.setStrokeColor(paint2.color.cgColor)
            context.setLineWidth(paint2.strokeWidth * rect.height / 400.0)
            context.setLineCap(.round)
            context.setLineJoin(.round)
            context.strokePath()
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            newPath = CGMutablePath()
            newPathJustCreated = true
            startPoint = nearest
            startIndex = points.firstIndex(of: nearest) ?? 0
            newPath?.move(to: nearest)
            setNeedsDisplay()
            player?.currentTime = 0
            player?.play()
            Utilities.vibrate()
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            if newPath == nil {
                newPath = CGMutablePath()
                newPathJustCreated = true
                startPoint = nearest
                startIndex = points.firstIndex(of: nearest) ?? 0
                newPath?.move(to: nearest)
            } else {
                lineTo2(nearest)
            }
            setNeedsDisplay()
            if !(player?.isPlaying ?? false) {
                player?.play()
            }
        } else {
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = nil
            setNeedsDisplay()
            player?.pause()
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let newPath = newPath, !newPathJustCreated {
            paths.append(newPath)
        }
        self.newPath = nil
        let longestContinueLeftPoints = findLongestContinueLeftPoints()
        if longestContinueLeftPoints < 20 {
            onDone?()
        }
        player?.pause()
        setNeedsDisplay()
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func nearestPoint(to touch: CGPoint) -> CGPoint? {
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        for point in points {
            let distance = Utilities.distance(from: touch, to: point)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        return nearest
    }
    
    private func lineTo2(_ point: CGPoint) {
        guard let endIndex = points.firstIndex(of: point) else { return }
        if endIndex == startIndex { return }
        
        let max = Swift.max(startIndex, endIndex)
        let min = Swift.min(startIndex, endIndex)
        let distance = Swift.min(max - min, min + points.count - max)
        
        if distance > 10 {
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = CGMutablePath()
            self.newPath?.move(to: point)
            startIndex = endIndex
            newPathJustCreated = true
            return
        }
        
        if distance == max - min {
            if startIndex < endIndex {
                for i in (startIndex + 1)...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (endIndex...startIndex - 1).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        } else {
            if startIndex < endIndex {
                if startIndex > 0 {
                    for i in (0...(startIndex - 1)).reversed() {
                        newPath?.addLine(to: points[i])
                        leftPoints.removeAll { $0 == points[i] }
                    }
                }
                for i in (endIndex...(points.count - 1)).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (startIndex + 1)..<points.count {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
                for i in 0...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        }
        
        startIndex = endIndex
        newPathJustCreated = false
    }
    
    private func findLongestContinueLeftPoints() -> Int {
        var max = 0
        var count = 0
        for point in points {
            if leftPoints.contains(point) {
                count += 1
            } else {
                max = Swift.max(max, count)
                count = 0
            }
        }
        return Swift.max(max, count)
    }
    
    private func playWritingSound() {
        if let url = Bundle.main.url(forResource: "writing", withExtension: "mp3", subdirectory: "Sounds/effect") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.numberOfLoops = -1
                player?.prepareToPlay()
            } catch {
                if BuildConfig.DEBUG {
                    print(error)
                }
            }
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        let strokeWidth = bounds.height * 5.0 / 400.0
        paint.strokeWidth = strokeWidth
        paint2.strokeWidth = strokeWidth
    }
    
    deinit {
        player?.stop()
        player = nil
    }
}
