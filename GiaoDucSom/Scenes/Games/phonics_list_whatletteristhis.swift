import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_whatletteristhis: GameFragment {
    private var values1 : [String] = []
    private var values2 : [String] = []
    private var answers : [Int] = []
    private var randomOrder1: [Int] = []
    private var randomOrder2: [Int] = []
    private var contentLayout: MyGridView = MyGridView()
    private var answerLayout: MyGridView = MyGridView()
    private var meIndex = 0
    private var step = 0
    private var topViews: [UIView] = []
    private var bottomViews: [UIView] = []
    private var allViews: [UIView] = []
    private var selectedView: UIView?
    private var hand: UIView = UIView()
    private var lineContainer = UIView()
    private var drawingLine = LinesBetweenViews()
    private var viewConnectedToView: [UIView : UIView] = [:]
    private var viewConnectedToLine: [UIView : LinesBetweenViews] = [:]
    var coinView = UIView()
    
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#849BFE")
        addSubview(contentLayout)
        addSubview(answerLayout)
        addSubview(lineContainer)
        addSubview(hand)
        addSubviewWithInset(subview: drawingLine, inset: 0)
        
        drawingLine.isHidden = true
        hand.frame = CGRectMake(0, 0, 1, 1)
        hand.backgroundColor = .clear
                
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
        
        addSubview(coinView)
        coinView.snp.makeConstraints{ make in
            make.width.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        coinView.isUserInteractionEnabled = false
        
        contentLayout.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        answerLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        lineContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        guard let touch = touches.first else { return }
        for view in allViews {
            if touch.placeInView(view: view){
                selectedView = view
                hand.frame = CGRectMake(selectedView!.frame.minX + selectedView!.frame.width/2, selectedView!.frame.maxY, 1, 1)
                if let label = selectedView?.viewWithTag(1) as? UILabel {
                    let names = label.text!.lowercased().split(separator: " ").map{"\($0.string)1"}
                    self.playSound(delay: 0, names: names)
                }
                drawingLine.views[0] = selectedView!
                if let line = viewConnectedToLine[selectedView!] {
                    line.alpha = 0.3
                }
                return;
            }
        }
        selectedView = nil
        return
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        values1 = (game.values?.compactMap { String(($0.value as! String).lowercased()[0]) })!
        values2 = (game.values?.compactMap { String(($0.value as! String).uppercased()[0]) })!
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
        loadNextStep()
    }
    
    func loadNextStep(){
        if step >= 1 {
            pauseGame()
            let delay = playSound([answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            return
        }
        randomOrder1 = [Int](0..<values1.count).randomOrder()
        randomOrder2 = [Int](0..<values2.count).randomOrder()
        step += 1
        allViews = []
        topViews = []
        lineContainer.subviews.forEach{$0.removeFromSuperview()}
        for i in 0..<values1.count {
            let view = createItem(text: values1[randomOrder1[i]])
            view.tag = 100 + randomOrder1[i]
            topViews.append(view)
            allViews.append(view)
        }
        contentLayout.itemRatio = 1
        contentLayout.insetRatio = 0.2
        contentLayout.columns = values1.count
        contentLayout.reloadItemViews(views: topViews)
        
        bottomViews = []
        for i in 0..<values2.count {
            let view = createItem(text: values2[randomOrder2[i]])
            view.tag = 100 + randomOrder2[i]
            bottomViews.append(view)
            allViews.append(view)
        }
        answerLayout.itemRatio = 1
        answerLayout.insetRatio = 0.2
        answerLayout.columns = values2.count
        answerLayout.reloadItemViews(views: bottomViews)
        hand.tag = -1
        drawingLine.views = [hand, hand]
    }
        
    func connect(top:UIView, bottom:UIView){
        //playSound(answerCorrect1EffectSound())
        if top.superview == bottom.superview {
            return
        }
        if let line = viewConnectedToLine[top] {
            line.removeFromSuperview()
        }
        if let line = viewConnectedToLine[bottom] {
            line.removeFromSuperview()
        }
        if let other = viewConnectedToView[top] {
            viewConnectedToView[other] = nil
        }
        if let other = viewConnectedToView[bottom] {
            viewConnectedToView[other] = nil
        }
        let line = LinesBetweenViews()
        line.views = [top,bottom]
        line.backgroundColor = .clear
        line.clipsToBounds = false
        lineContainer.addSubviewWithInset(subview: line, inset: 0)
        scheduler.schedule(delay: 0.01) {
            line.createPath()
        }
        viewConnectedToLine[top] = line
        viewConnectedToLine[bottom] = line
        viewConnectedToView[top] = bottom
        viewConnectedToView[bottom] = top
        checkFinish()
    }
    
    func checkFinish(){
        if lineContainer.subviews.count == values1.count {
            var valid = true
            for view in lineContainer.subviews {
                if let view = view as? LinesBetweenViews {
                    if view.views[0].tag != view.views[1].tag{
                        valid = false
                        break
                    }
                }
            }
            if valid {
                playSound(name: answerCorrect1EffectSound())
                pauseGame()
                animateCoinIfCorrect(view: coinView)
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    var bottomViewsInOrder : [UIView] = []
                    var distances : [CGFloat] = []
                    for i in 0..<self.topViews.count {
                        for j in 0..<self.bottomViews.count {
                            let topView = self.topViews[i]
                            let bottomView = self.bottomViews[j]
                            if topView.tag == bottomView.tag {
                                let distance = topView.distanceFromCenterToCenter(to: bottomView)
                                bottomViewsInOrder.append(bottomView)
                                distances.append(distance.x)
                                break
                            }
                        }
                    }
                    let animValues: [Double] = [0,1]
                    let timeChange = Interpolate(values: animValues,
                    apply: { [weak self] (value) in
                        guard let self = self else { return }
                        for i in 0..<self.topViews.count {
                            let bottomView = bottomViewsInOrder[i]
                            let distance = distances[i]
                            bottomView.transform = CGAffineTransformMakeTranslation(distance * value, 0)
                            self.viewConnectedToLine[bottomView]?.createPath()
                        }
                    })
                    timeChange.animate(1, duration: 0.5)
                    self.scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        let height = self.bounds.height
                        let animValues: [Double] = [0,1]
                        let timeChange = Interpolate(values: animValues,
                        apply: { [weak self] (value) in
                            guard let self = self else { return }
                            for i in 0..<self.topViews.count {
                                let topView = self.topViews[i]
                                let bottomView = bottomViewsInOrder[i]
                                let distance = distances[i]
                                topView.transform = CGAffineTransformMakeTranslation(0, value * height / 9)
                                bottomView.transform = CGAffineTransformMakeTranslation(distance, -value * height / 9)
                                self.viewConnectedToLine[bottomView]?.createPath()
                            }
                        })
                        self.playSound(name: "effects/slide")
                        timeChange.animate(1,duration: 0.3)
                        var delay = 0.5
                        delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
                        delay += 1
                        self.scheduler.schedule(delay: delay) {
                            [weak self] in
                            guard let self = self else { return }
                            self.finishGame()
                        }
                    }
                }
            } else {
                playSound(answerWrongEffectSound())
                incorrect += 1
                playSound(name: "effects/fail", delay: 0.5)
                pauseGame()
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.lineContainer.subviews.forEach{$0.removeFromSuperview()}
                    self.viewConnectedToLine = [:]
                    self.viewConnectedToView = [:]
                    self.resumeGame()
                }
                setGameWrong()
            }
        }
    }
    
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        if selectedView != nil {
            let p = sender.location(in: self)
            hand.frame = CGRectMake(p.x, p.y, 1, 1)
            drawingLine.views[1] = hand
            drawingLine.createPath()
            drawingLine.isHidden = false
        }
        
        if state == .ended || state == .cancelled || state == .failed {
            drawingLine.isHidden = true
            if let line = viewConnectedToLine[selectedView!] {
                line.alpha = 1
            }
            var nextView: UIView?
            for view in allViews {
                if sender.placeInView(view: view){
                    nextView = view
                    if let label = nextView?.viewWithTag(1) as? UILabel {
                        let names = label.text!.lowercased().split(separator: " ").map{"\($0.string)1"}
                        self.playSound(delay: 0, names: names)
                    }
                    if selectedView != nil && nextView != nil {
                        connect(top: selectedView!, bottom: nextView!)
                    }
                    return;
                }
            }
        }
    }
    
    class LinesBetweenViews: UIView {
        var views: [UIView] = []
        var shapeLayer: CAShapeLayer { return self.layer as! CAShapeLayer }
        public var pathColor: UIColor = .color(hex: "#FFFFFF"){
            didSet {
                guard let layer = self.layer as? CAShapeLayer else { return }
                layer.strokeColor = pathColor.cgColor
            }
        }
        override class var layerClass : AnyClass {
            return CAShapeLayer.self
        }
        override var bounds: CGRect {
            didSet {
                //createPath()
            }
        }
        override func didMoveToSuperview() {
            shapeLayer.strokeColor = pathColor.cgColor
            shapeLayer.fillColor = UIColor.clear.cgColor
            shapeLayer.lineWidth = Utilities.isIPad ? 20 : 10
            shapeLayer.borderWidth = 1
            shapeLayer.lineCap = .round
            shapeLayer.borderColor = UIColor.color(hex: "#FFFFFF").cgColor
        }
        override func didAddSubview(_ subview: UIView) {
            createPath()
        }
        public func createPath() {
            let path = UIBezierPath()
            var view1 = views[0]
            var view2 = views[1]
            var p1 = views[0].convert(CGPoint.zero, to: self)
            var p2 = views[1].convert(CGPoint.zero, to: self)
            if p1.y > p2.y {
                view1 = views[1]
                view2 = views[0]
                let p = p1
                p1 = p2
                p2 = p
            }
            p1.x += view1.bounds.width / 2
            p1.y += view1.bounds.height
            p2.x += view2.bounds.width / 2
            path.move(to: p1)
            path.addLine(to: p2)
            shapeLayer.path = path.cgPath
        }
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    
    func createItem(text:String)->UIView{
        let view = UIView()
        let background = SVGImageView(SVGName: "home report btn")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.6)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        autoLabel.textColor = .color(hex: "#849BFE")
        return view
    }
    
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}

// Extension cho gọn, thích thì copy xài chung 💖
extension StringProtocol {
    /// Bỏ ký tự cuối nếu nó là số
    var droppingTrailingNumber: String {
        guard let last = self.last, last.isNumber else { return String(self) }
        return String(dropLast())
    }
}
