//
//  phonics_list_readtheword.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_readtheword: GameFragment {
    var bottomGrid = MyGridView()
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#1497E0")
    }
    private var values : [String] = []
    var meIndex = 0
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(bottomGrid)
        bottomGrid.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.6)
        }
        addSubview(textName)
        textName.snp.makeConstraints{ make in
            make.left.right.equalToSuperview().inset(50)
            make.top.equalTo(bottomGrid.snp.bottom).offset(0)
            make.height.equalToSuperview().multipliedBy(0.3)
        }
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 4)
        meIndex = Int.random(in: 0..<values.count)
        textName.text = values[meIndex]
        var delay = self.playSound(name: openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        //let values1 = values.randomOrder()
        var listViews : [UIView] = []
        for value in values {
            let view = createGridItem()
            let svgView: SVGImageView = view.viewWithTag(1) as! SVGImageView
            svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(value)))!] : "english phonics/\(game.level!)/\(value).svg"
            svgView.contentMode = .scaleAspectFit
            view.tag = 100 + values.firstIndex(of: value)!
            view.alpha = 0.01
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        bottomGrid.columns = 0
        bottomGrid.itemRatio = 400 / 423.0
        bottomGrid.reloadItemViews(views: listViews.randomOrder())
        delay += bottomGrid.showItems(startDelay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    @objc func itemClick(_ sender: UIControl){
        let index = (game.values!.firstIndex(of: AnyCodable(values[sender.tag - 100])))!
        if sender.tag == meIndex + 100 {
            pauseGame()
            animateCoinIfCorrect(view: sender)
            var delay = 0.5
            delay += self.playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound()])
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![index] : values[meIndex], delay: delay)
            delay += self.playSound(name: endGameSound(), delay: delay)
            delay += 1
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
        } else {
            pauseGame()
            setGameWrong()
            playSound(answerWrongEffectSound())
            incorrect += 1
            var delay = 0.4
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![index] : values[sender.tag - 100], delay: delay)
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            })
        }
    }
    
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 400/423)
        let svgView = SVGImageView(frame: CGRectZero)
        svgView.transform = CGAffineTransformMakeScale(0.75, 0.75)
        svgView.tag = 1
        background.addSubviewWithInset(subview: svgView, inset: 0)
        return view
    }
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}
