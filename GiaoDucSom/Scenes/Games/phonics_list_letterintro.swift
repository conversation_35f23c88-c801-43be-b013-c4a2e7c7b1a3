//
//  phonics_list_letterintro.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 09/11/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_letterintro: GameFragment {
    var leftText = ""
    var rightText = ""
    let leftLabel = UILabel()
    let rightLabel = UILabel()
    let centerView = UIView()
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        //leftLabel.backgroundColor = .red.withAlphaComponent(0.3)
        //rightLabel.backgroundColor = .red.withAlphaComponent(0.3)
        leftLabel.font = .Freude(size: 50)
        leftLabel.textColor = .color(hex: "#1497E0")
        rightLabel.font = leftLabel.font
        rightLabel.textColor = .color(hex: "#1497E0")
        leftLabel.alpha = 0
        rightLabel.alpha = 0
        addSubview(leftLabel)
        addSubview(rightLabel)
        addSubview(centerView)
        centerView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalToSuperview()
        }
        leftLabel.snp.makeConstraints{ make in
            make.centerY.equalToSuperview()
            make.right.equalTo(centerView.snp.left)
        }
        rightLabel.snp.makeConstraints{ make in
            make.centerY.equalToSuperview()
            make.left.equalTo(centerView.snp.right)
        }
        leftText = game.values![0].value as! String
        rightText = game.values![1].value as! String
        leftLabel.text = leftText
        rightLabel.text = rightText
    }
   
    
    override func createGame() {
        super.createGame()
        let fontSize = min(bounds.width/4, bounds.height/2)
        leftLabel.font = .Freude(size: fontSize)
        rightLabel.font = leftLabel.font
        scheduler.schedule(delay: 0.1, execute: {
            [weak self] in
            guard let self = self else { return }
            leftLabel.transform = CGAffineTransformMakeTranslation(leftLabel.bounds.width/2 + centerView.bounds.width/2, 0)
            rightLabel.transform = CGAffineTransformMakeTranslation(-rightLabel.bounds.width/2 - centerView.bounds.width/2, 0)
            leftLabel.alpha = 1
        })
        var delay = 0.5
        var steps: [Double] = []
        for text in self.parseIntroText()! {
            if text == "@" {
                steps.append(delay)
            } else {
                delay += self.playSound(name: text, delay: delay)
            }
        }
        scheduler.schedule(delay: steps[0], execute: {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 1.0, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 1, options: .curveEaseOut, animations: {
                self.leftLabel.transform = CGAffineTransform(translationX: 0, y: 0)
                self.rightLabel.transform = CGAffineTransform(translationX: 0, y: 0)
                self.rightLabel.alpha = 1
            }, completion: { _ in
                
            })

        })
        scheduler.schedule(delay: steps[1], execute: {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.2) {
                self.leftLabel.transform = CGAffineTransformConcat(self.leftLabel.transform, CGAffineTransformMakeScale(1.2, 1.2))
            } completion: { ok in
                UIView.animate(withDuration: 0.2, delay: 0.2) {
                    self.leftLabel.transform = CGAffineTransformMakeTranslation(self.leftLabel.transform.tx, 0)
                } completion: { ok in
                    
                }
            }
        })
        scheduler.schedule(delay: steps[2], execute: {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.2) {
                self.rightLabel.transform = CGAffineTransformConcat(self.leftLabel.transform, CGAffineTransformMakeScale(1.2, 1.2))
            } completion: { ok in
                UIView.animate(withDuration: 0.2, delay: 0.2) {
                    self.rightLabel.transform = CGAffineTransformMakeTranslation(self.leftLabel.transform.tx, 0)
                } completion: { ok in
                    
                }
            }
        })
        delay += 1
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.finishGame()
        })
    }
}
