//
//  phonics_list_memory.swift
//  KidsUPTiengViet
//
//  Created by <PERSON><PERSON> on 21/01/2023.
//

import Foundation
import Then
import UIKit
import Interpolate
import AnyCodable
import SVGKit

class phonics_list_memory : GameFragment {
    

    private var letters : [String] = []
    private var allLetters : [String] = []
    private var openViews : [Int] = []
    private var views : [MemoryCard] = []
    private var normalColor = UIColor(hexString: "#6395FC")
    private var rightColor = UIColor(hexString: "#73D048")
    private var wrongColor = UIColor(hexString: "#FF7761")
    private var coinView = UIView()
    override func configureLayout(_ view: UIView) {
        clipsToBounds = true
        let bgView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_cloud"))!
        addSubview(bgView)
        bgView.makeViewCenterFillAndKeep(ratio: 2688.0/1236.2)
        var myGridView = MyGridView()
        addSubviewWithInset(subview: myGridView, inset: 0)
        views = []
        for i in 1...6 {
            let view = MemoryCard(text: "")
            view.isUserInteractionEnabled = true
            view.tag = i - 1
            views.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        myGridView.isUserInteractionEnabled = true
        myGridView.itemRatio = 1
        myGridView.reloadItemViews(views: views)
        myGridView.alpha = 0.01
        UIView.animate(withDuration: 0.3, delay: 1, animations: {
            myGridView.alpha = 1
        })
        coinView.isUserInteractionEnabled = false
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    override func BackgroundMusicName() -> String? {
        return "effects/games/bg_sea"
    }
    
    @objc func itemClick(_ sender: UIControl){
        let viewIndex = sender.tag
        if openViews.contains(viewIndex) || viewIndex == -1 {
            return
        }
        openViews.append(viewIndex)
        show(viewIndex: viewIndex)
        let sound = tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(allLetters[viewIndex])))!] : allLetters[viewIndex]
        playSound(name: sound)
        if openViews.count >= 2 && openViews.count % 2 == 0 {
            pauseGame()
            let index1 = openViews[openViews.count - 2]
            let index2 = openViews[openViews.count - 1]
            if allLetters[index1] == allLetters[index2] {
                //views[index2].animateCoin(answer: true)
                //playSound(name: allLetters[index1], delay: 0.3)
                scheduler.schedule(delay: 0.3) {
                    [weak self] in
                    guard let self = self else { return }
                    self.views[index1].label.textColor = self.rightColor
                    self.views[index2].label.textColor = self.rightColor
                    self.scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.views[index1].label.textColor = self.normalColor
                        self.views[index2].label.textColor = self.normalColor
                    }
                }
                self.views[index1].tag = -1
                self.views[index2].tag = -1
                if openViews.count == allLetters.count {
                    pauseGame()
                    scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        animateCoinIfCorrect(view: coinView)
                        // play sound khen
                        var delay = 0.5
                        delay += self.playSound(name: getCorrectHumanSound(), delay: delay)
                        delay += self.playSound(name: endGameSound(), delay: delay)
                        delay += 1
                        scheduler.schedule(delay: delay, execute: {
                            [weak self] in
                            guard let self = self else { return }
                            self.finishGame()
                        })
                    }
                } else {
                    scheduler.schedule(delay: 0.1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.resumeGame()
                    }
                }
            } else {
                incorrect += 1
                //views[index2].animateCoin(answer: false)
                scheduler.schedule(delay: 0.3) {
                    [weak self] in
                    guard let self = self else { return }
                    self.views[index1].label.textColor = self.wrongColor
                    self.views[index2].label.textColor = self.wrongColor
                    self.scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.views[index1].label.textColor = self.normalColor
                        self.views[index2].label.textColor = self.normalColor
                    }
                }
                scheduler.schedule(delay: 1.5) {
                    [weak self] in
                    guard let self = self else { return }
                    self.hide(viewIndex: index1)
                    self.hide(viewIndex: index2)
                }
                openViews.remove(at: openViews.count - 1)
                openViews.remove(at: openViews.count - 1)
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.resumeGame()
                }
            }
        }
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        letters = (game.values?.compactMap{$0.value as? String}.randomOrder().take(count: 3))!
        allLetters = []
        letters.forEach{ allLetters.append($0) }
        letters.forEach{ allLetters.append($0) }
        allLetters = allLetters.randomOrder()
        var state2 : [Int] = []
        var choose : [String] = []
        while state2.count < allLetters.count / 2 {
            let index = Int.random(in: 0..<allLetters.count)
            let value = allLetters[index]
            if !choose.contains(value) {
                state2.append(index)
                choose.append(value)
            }
        }
        openViews = []
        for i in 0..<6 {
            if game.gameDataType == "picture" {
                views[i].thumbView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(allLetters[i])))!] :  "flashcards/\(game.level!)/\( state2.contains(i) ? allLetters[i] : "\(allLetters[i])2" ).svg"
            } else {
                views[i].label.text = state2.contains(i) ? "\(allLetters[i].dropLast(1).uppercased())" :  "\(allLetters[i].dropLast(1).lowercased())"
            }
            delay += 0.3
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                views[i].hideCard()
            })
        }
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    final class MemoryCard : KUButton {
        var text: String?
        var cardBg1 = SVGImageView(SVGName: "nhanbiet_bg_card1")
        var cardBg2 = SVGImageView(SVGName: "nhanbiet_bg_card2")
        var thumbView = SVGImageView(frame: CGRectZero).then{
            $0.contentMode = .scaleAspectFit
        }
        lazy var label : AutosizeLabel = AutosizeLabel().then{
            $0.font = .UTMAvo(size: 15)
            $0.textColor = UIColor(hexString: "#6395FC")
        }
        convenience init(text: String) {
            self.init(type: .custom)
            self.text = text
            label.text = text
        }
        override func setupView() {
            addSubviewWithInset(subview: cardBg2, inset: 0)
            cardBg2.addSubview(label)
            cardBg2.addSubviewWithInset(subview: thumbView, inset: 0)
            label.snp.makeConstraints { make in
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(0.9)
                make.width.equalToSuperview().multipliedBy(0.6)
                make.height.equalToSuperview().multipliedBy(0.5)
            }
            addSubviewWithInset(subview: cardBg1, inset: 0)
            cardBg1.isHidden = true
        }
        func showCard() {
            flipCard(backView: cardBg1, frontView: cardBg2)
            //animateViewFlip(cardBg1)
            //animateViewFlip(cardBg2)
        }
        func hideCard() {
            flipCard(backView: cardBg2, frontView: cardBg1)
            //animateViewFlip(cardBg1)
            //animateViewFlip(cardBg2)
        }
        private func flipCard ( backView: UIView, frontView:UIView){
            guard let parentView = frontView.superview else {
                return
            }
            QueueSoundPlayer.shared.play(sound: "effect/slide1", delay: 0)
            UIView.animate(withDuration: 0.3 , animations: {
                // Apply 3D transform to the view's layer
                var transform = CATransform3DIdentity
                transform.m34 = -1.0 / 400 // Add perspective effect
                transform = CATransform3DRotate(transform, .pi/2.0, 0, 1, 0) // Flip around the Y-axis (180 degrees)
                backView.superview?.layer.transform = transform
            }, completion: { _ in
                frontView.isHidden = false
                frontView.transform = CGAffineTransformMakeScale(-1, 1)
                backView.isHidden = true
                backView.transform = .identity
                // Once the first animation is completed, reverse the flip animation
                UIView.animate(withDuration: 1.2, delay: 0, usingSpringWithDamping: 0.3, initialSpringVelocity: 0.1, options: .curveEaseOut, animations: {
                    var transform = CATransform3DIdentity
                    transform.m34 = -1.0 / 200 // Add perspective effect
                    transform = CATransform3DRotate(transform, .pi, 0, 1, 0) // Flip around the Y-axis (180 degrees)
                    backView.superview?.layer.transform = transform
                }, completion: { _ in
                    backView.superview?.layer.transform = CATransform3DIdentity
                    backView.transform = .identity
                    frontView.transform = .identity
                })
            })
            /*
            let transitionOptions: UIView.AnimationOptions = [.transitionFlipFromRight, .showHideTransitionViews]
            let duration: TimeInterval = 0.5
            UIView.transition(with: parentView, duration: duration, options: transitionOptions, animations: {
                frontView.isHidden = false
                backView.isHidden = true
            }, completion: nil)
             */
        }
        func animateViewFlip(_ view: UIView) {
            // Perform the flip animation with ElasticEase
            UIView.animate(withDuration: 01.8, delay: 0, usingSpringWithDamping: 0.3, initialSpringVelocity: 0.1, options: .curveEaseOut, animations: {
                // Apply 3D transform to the view's layer
                var transform = CATransform3DIdentity
                transform.m34 = -1.0 / 100 // Add perspective effect
                transform = CATransform3DRotate(transform, .pi, 0, 1, 0) // Flip around the Y-axis (180 degrees)
                view.layer.transform = transform
            }, completion: { _ in
                // Once the first animation is completed, reverse the flip animation
                UIView.animate(withDuration: 01.8, delay: 0, usingSpringWithDamping: 0.3, initialSpringVelocity: 0.1, options: .curveEaseOut, animations: {
                    // Reset the transform to identity (original state)
                    view.layer.transform = CATransform3DIdentity
                })
            })
        }


        // Call the function with the desired view to animate the flip effect
        // For example: animateViewFlip(yourView)

    }
    func show(viewIndex:Int){
        playSound("effect/slide1")
        views[viewIndex].showCard()
    }
    func hide(viewIndex:Int){
        playSound("effect/slide1")
        views[viewIndex].hideCard()
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 6.0 / (4.0 + Float(incorrect))
    }
}
