//
//  taptrung_list_timbongdongvat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 22/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_timbongdongvat: NhanBietGameFragment {
    // MARK: - Properties
    private var gridView: MyGridView!
    private var svgView: SVGImageView!
    private var svgs: [SVGKImage] = []
    private var indexes: [Int] = (0..<4).shuffled()
    private var pack: Folder?
    private var items: [Item] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftBg = UIView()
        leftBg.backgroundColor = UIColor.color(hex: "#FFF")
        view.addSubview(leftBg)
        leftBg.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.color(hex: "#D7FBFF")
        gridView.clipsToBounds = false
        view.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit // contentScale=0.7
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let packs = FlashcardsManager.shared.getPacks()
        while true {
            pack = packs.randomElement()
            if let pack = pack, pack.folder.contains("Animals") || pack.folder.contains("Insects") {
                items = pack.items.shuffled().prefix(4).map { $0 }
                break
            }
        }
        
        var svgPaths: [String] = []
        if let pack = pack, let firstItem = items.first {
            svgPaths.append("topics/\(pack.folder)/\(firstItem.path!)")
            for item in items {
                svgPaths.append("topics/\(pack.folder)/shadow/\(item.path!)")
            }
        }
        svgs = svgPaths.map{Utilities.GetSVGKImage(named: $0)}
        self.loadData()
    }
    
    private func loadData() {
        svgView.image = svgs.first?.uiImage
        
        var views: [UIView] = []
        for i in 0..<svgs.count - 1 {
            let view = KUButton()
            view.setImage(svgs[indexes[i] + 1].uiImage, for: .normal)
            view.backgroundColor = .clear
            view.stringTag = "\(indexes[i])"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.itemSpacingRatio = 0.05
        gridView.insetRatio = 0.05
        gridView.reloadItemViews(views: views.shuffled())
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_tim bong"])
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.svgView.transform = .identity
        }
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5, delay: 0.5) {
                self.gridView.alpha = 1
            }
        }
        
        delay += 1.0
        delay += gridView.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        svgView.transform = .identity
        gridView.alpha = 0
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound(delay: 0, names: ["taptrung/taptrung_tim bong"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == 0 {
            animateCoinIfCorrect(view: svgView)
            var delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound()])
            if let pack = pack, let item = items.first, let button = sender as? KUButton {
                let svg = Utilities.GetSVGKImage(named: "topics/\(pack.folder)/\(item.path!)")
                button.setImage(svg.uiImage, for: .normal)
                delay += playSound(delay: delay, names: ["topics/\(pack.folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))", endGameSound()])
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
