//
//  taptrung_list_noidaychun.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_noidaychun: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout1: MyGridView!
    private var gridLayout2: MyGridView!
    private var views: [UIView] = []
    private var views2: [UIView] = []
    private var lines: [BeltView] = []
    private var leftContainer: UIImageView!
    private var rightContainer: UIImageView!
    private var rightList: [UIView] = []
    private var snapRatio: CGFloat = 0.5
    private var arrow1: UIImageView!
    private var arrow2: UIImageView!
    private var arrow3: UIImageView!
    private var arrow4: UIImageView!
    private var points: [Point] = GridPathGenerator().generateRandomPath()
    private var beltView: BeltView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.white
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        let paddingView = UIView()
        view.addSubviewWithPercentInset(subview: paddingView, percentInset: 5)
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        paddingView.addSubview(mainContainer)
        mainContainer.makeViewCenterAndKeep(ratio: 1.62)
        
        let arrowContainer = UIView()
        arrowContainer.clipsToBounds = false
        //arrowContainer.alpha = 0.1
        mainContainer.addSubview(arrowContainer)
        arrowContainer.snp.makeConstraints { make in
            make.width.equalTo(mainContainer).multipliedBy(0.15)
            make.height.equalTo(arrowContainer.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        arrow1 = UIImageView(image: Utilities.SVGImage(named: "taptrung_daychun_arrow_1"))
        arrowContainer.addSubview(arrow1)
        arrow1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        arrow2 = UIImageView(image: Utilities.SVGImage(named: "taptrung_daychun_arrow_2"))
        arrowContainer.addSubview(arrow2)
        arrow2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        arrow3 = UIImageView(image: Utilities.SVGImage(named: "taptrung_daychun_arrow_3"))
        arrowContainer.addSubview(arrow3)
        arrow3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        arrow4 = UIImageView(image: Utilities.SVGImage(named: "taptrung_daychun_arrow_4"))
        arrowContainer.addSubview(arrow4)
        arrow4.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        leftContainer = UIImageView()
        leftContainer.clipsToBounds = false
        let leftBgView = UIImageView()
        leftBgView.image = Utilities.SVGImage(named: "taptrung_daychun")
        leftContainer.addSubview(leftBgView)
        leftBgView.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.92)
            make.width.equalToSuperview().multipliedBy(0.96)
        }
        mainContainer.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.height.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.95)
            make.width.equalTo(mainContainer).multipliedBy(0.4)
        }
        
        rightContainer = UIImageView()
        rightContainer.isUserInteractionEnabled = true
        rightContainer.clipsToBounds = false
        let rightBgView = UIImageView()
        rightBgView.image = leftBgView.image
        rightContainer.addSubview(rightBgView)
        rightBgView.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.91)
            make.width.equalToSuperview().multipliedBy(0.97)
        }
        mainContainer.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.height.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.93)
            make.width.equalTo(mainContainer).multipliedBy(0.4)
        }
        
        gridLayout1 = MyGridView()
        leftContainer.addSubview(gridLayout1)
        gridLayout1.snp.makeConstraints { make in
            make.centerX.equalToSuperview().multipliedBy(1.025)
            make.centerY.equalToSuperview().multipliedBy(1.093)
            make.width.equalToSuperview().multipliedBy(1.03)
            make.height.equalToSuperview().multipliedBy(1.091)
        }
        
        gridLayout2 = MyGridView()
        rightContainer.addSubview(gridLayout2)
        gridLayout2.snp.makeConstraints { make in
            make.centerX.equalToSuperview().multipliedBy(1.025)
            make.centerY.equalToSuperview().multipliedBy(1.093)
            make.width.equalToSuperview().multipliedBy(1.03)
            make.height.equalToSuperview().multipliedBy(1.091)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        rightContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        views = []
        for i in 0..<6 {
            for j in 0..<4 {
                let view = RoundedView()
                view.backgroundColor = UIColor.color(hex: "#CE8D61")
                view.transform = CGAffineTransform(scaleX: 0.33, y: 0.33)
                let view2 = UIView()
                view2.addSubview(view)
                view.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                views.append(view2)
            }
        }
        gridLayout1.columns = 4
        gridLayout1.itemRatio = 1
        gridLayout1.itemSpacingRatio = 0
        gridLayout1.reloadItemViews(views: views)
        
        views2 = []
        for i in 0..<6 {
            for j in 0..<4 {
                let view = RoundedView()
                view.backgroundColor = UIColor.color(hex: "#CE8D61")
                view.transform = CGAffineTransform(scaleX: 0.33, y: 0.33)
                let view2 = UIView()
                view2.addSubview(view)
                view.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                view2.stringTag = "\(i * 4 + j)"
                views2.append(view2)
            }
        }
        gridLayout2.columns = 4
        gridLayout2.itemRatio = 1
        gridLayout2.itemSpacingRatio = 0
        gridLayout2.reloadItemViews(views: views2)
    }
    
    override func createGame() {
        super.createGame()
        
        for i in 0..<points.count - 1 {
            let point1 = points[i]
            let point2 = points[i + 1]
            let viewA = views2[point1.y * 4 + point1.x]
            let viewB = views2[point2.y * 4 + point2.x]
            let pointA = CGPoint(x: viewA.frame.midX, y: viewA.frame.midY)
            let pointB = CGPoint(x: viewB.frame.midX, y: viewB.frame.midY)
            let radius = viewA.frame.width / 2 * 0.33 * 1.1
            
            if i < 2 {
                let beltView = BeltView(pointA: pointA, pointB: pointB, radius: radius)
                rightContainer.addSubview(beltView)
                beltView.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                rightList.append(viewA)
                rightList.append(viewB)
                lines.append(beltView)
            }
            
            let beltView = BeltView(pointA: pointA, pointB: pointB, radius: radius)
            leftContainer.addSubview(beltView)
            beltView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        beltView = BeltView(pointA: .zero, pointB: CGPoint(x: 100, y: 100), radius: views[0].frame.width / 2 * 0.33 * 1.1)
        rightContainer.addSubview(beltView)
        beltView.isHidden = true
        beltView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        animateArrow()
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_day chun"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.leftContainer.transform = .identity
                self.rightContainer.transform = .identity
            }
            delay += 1.0
            self.scheduler.schedule(after: delay) { [weak self] in
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_day chun")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            startTime = CACurrentMediaTime()
            startX = location.x
            startY = location.y
            startView = findViewAtPosition(x: location.x, y: location.y)
            if let startView = startView {
                beltView.pointA = CGPoint(x: startView.frame.midX, y: startView.frame.midY)
            }
            beltView.alpha = 1.0
            rubbleStretchSound = playSound("effect/rubble_stretch")
            Utils.vibrate()
            
        case .changed:
            Utils.vibrate()
            if let startView = startView {
                endView = findViewAtPosition(x: location.x, y: location.y)
                if let endView = endView, endView != startView {
                    beltView.pointB = CGPoint(x: endView.frame.midX, y: endView.frame.midY)
                } else {
                    beltView.pointB = location
                }
                beltView.isHidden = false
                beltView.setNeedsDisplay()
            }
            
        case .ended:
            let clickDuration = CACurrentMediaTime() - startTime
            let endX = location.x
            let endY = location.y
            let distance = sqrt(pow(endX - startX, 2) + pow(endY - startY, 2))
            
            if clickDuration < 0.2 && distance < 15 {
                var removeSomething = false
                for i in 0..<rightList.count / 2 {
                    let line = lines[i]
                    if line.isHidden { continue }
                    let view1 = rightList[i * 2]
                    let view2 = rightList[i * 2 + 1]
                    let d = pointToSegmentDistance(point: location, view1: view1, view2: view2)
                    if d < view1.frame.width / 6 {
                        UIView.animate(withDuration: 0.2, animations: {
                            line.alpha = 0
                        }, completion: { _ in
                            line.removeFromSuperview()
                        })
                        lines.remove(at: i)
                        rightList.remove(at: i * 2)
                        rightList.remove(at: i * 2)
                        removeSomething = true
                        checkFinish()
                        break
                    }
                }
                if removeSomething {
                    playSound("effect/rubber_release")
                }
                return
            }
            
            playSound("effect/rubber_release")
            beltView.isHidden = true
            if let startView = startView, let endView = endView {
                let index = checkNewLine(startView: startView, endView: endView)
                if index == -1 {
                    let beltView = BeltView(pointA: CGPoint(x: startView.frame.midX, y: startView.frame.midY),
                                           pointB: CGPoint(x: endView.frame.midX, y: endView.frame.midY),
                                           radius: startView.frame.width / 2 * 0.33 * 1.1)
                    rightContainer.addSubview(beltView)
                    beltView.snp.makeConstraints { make in
                        make.edges.equalToSuperview()
                    }
                    rightList.append(startView)
                    rightList.append(endView)
                    lines.append(beltView)
                    checkFinish()
                } else {
                    beltView.alpha = 0
                    let tobeRemove = lines[index]
                    UIView.animate(withDuration: 0.2, animations: {
                        tobeRemove.alpha = 0
                    }, completion: { _ in
                        tobeRemove.removeFromSuperview()
                    })
                    lines.remove(at: index)
                    rightList.remove(at: index * 2)
                    rightList.remove(at: index * 2)
                    scheduler.schedule(after: 0.2) { [weak self] in
                        self?.checkFinish()
                    }
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private var startTime: CFTimeInterval = 0
    private var startX: CGFloat = 0
    private var startY: CGFloat = 0
    private var startView: UIView?
    private var endView: UIView?
    private var rubbleStretchSound: TimeInterval = 0
    
    private func pointToSegmentDistance(point: CGPoint, view1: UIView, view2: UIView) -> CGFloat {
        let ax = point.x
        let ay = point.y
        let bx = view1.frame.midX
        let by = view1.frame.midY
        let cx = view2.frame.midX
        let cy = view2.frame.midY
        
        let lab = hypot(cx - bx, cy - by)
        if lab == 0 {
            return hypot(ax - bx, ay - by)
        }
        
        let t = ((ax - bx) * (cx - bx) + (ay - by) * (cy - by)) / (lab * lab)
        if t < 0 {
            return hypot(ax - bx, ay - by)
        } else if t > 1 {
            return hypot(ax - cx, ay - cy)
        } else {
            let px = bx + t * (cx - bx)
            let py = by + t * (cy - by)
            return hypot(ax - px, ay - py)
        }
    }
    
    private func checkNewLine(startView: UIView, endView: UIView) -> Int {
        for i in 0..<rightList.count / 2 {
            let view1 = rightList[i * 2]
            let view2 = rightList[i * 2 + 1]
            if (view1 == startView && view2 == endView) || (view1 == endView && view2 == startView) {
                return i
            }
        }
        return -1
    }
    
    private func checkFinish() {
        var finish = true
        if points.count == lines.count + 1 {
            var checkPoints = points
            for j in 0..<checkPoints.count - 1 {
                let point1 = checkPoints[j]
                let point2 = checkPoints[j + 1]
                var found = false
                for i in 0..<rightList.count / 2 {
                    let view1 = rightList[i * 2]
                    let view2 = rightList[i * 2 + 1]
                    let index1 = views2.firstIndex(of: view1)!
                    let index2 = views2.firstIndex(of: view2)!
                    let x1 = index1 % 4
                    let y1 = index1 / 4
                    let x2 = index2 % 4
                    let y2 = index2 / 4
                    if (point1.x == x1 && point1.y == y1 && point2.x == x2 && point2.y == y2) ||
                       (point1.x == x2 && point1.y == y2 && point2.x == x1 && point2.y == y1) {
                        found = true
                        break
                    }
                }
                if !found {
                    finish = false
                    break
                }
            }
        } else {
            finish = false
        }
        
        if finish {
            pauseGame(stopMusic: false)
            animateCoinIfCorrect(view: gridLayout2)
            let delay: TimeInterval = playSound(delay: 0, names: [
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                endGameSound()
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func findViewAtPosition(x: CGFloat, y: CGFloat) -> UIView? {
        for view in views2 {
            let padding = view.frame.width * (1 - snapRatio) / 2
            if x >= view.frame.minX + padding && x <= view.frame.maxX - padding &&
               y >= view.frame.minY + padding && y <= view.frame.maxY - padding {
                return view
            }
        }
        return nil
    }
    var arrowIndex = 0
    private func animateArrow() {
        arrowIndex += 1
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveLinear], animations: {
            [weak self] in
            guard let self = self else { return }
            self.arrow1.alpha = self.getAlpha(index: 0)
            self.arrow2.alpha = self.getAlpha(index: 1)
            self.arrow3.alpha = self.getAlpha(index: 2)
            self.arrow4.alpha = self.getAlpha(index: 3)
        }, completion: {
            [weak self] _ in
            guard let self = self else { return }
            self.animateArrow()
        })
    }
    
    func getAlpha(index: Int)->CGFloat {
        var rs = 1.00 - CGFloat(abs(self.arrowIndex%4) - index)*0.25
        return rs < 0 ? 0 : rs
    }
    
    // MARK: - BeltView
    class BeltView: UIView {
        private var paint: Paint!
        var pointA: CGPoint
        var pointB: CGPoint
        private var radius: CGFloat
        
        init(pointA: CGPoint, pointB: CGPoint, radius: CGFloat) {
            self.pointA = pointA
            self.pointB = pointB
            self.radius = radius
            super.init(frame: .zero)
            initPaints()
        }
        
        required init?(coder: NSCoder) {
            fatalError("init(coder:) has not been implemented")
        }
        
        private func initPaints() {
            backgroundColor = .clear
            paint = Paint()
            paint.color = UIColor.color(hex: "#F2BA1D")
            paint.strokeWidth = 5
            //paint.style = .stroke
            paint.isAntiAlias = true
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            paint.strokeWidth = frame.width / 100.0
        }
        
        override func draw(_ rect: CGRect) {
            super.draw(rect)
            guard let context = UIGraphicsGetCurrentContext() else { return }
            context.setStrokeColor(paint.color.cgColor)
            context.setLineWidth(paint.strokeWidth)
            context.addPath(calculateBeltPath3(center1: pointA, center2: pointB, r: radius).cgPath)
            context.strokePath()
        }
        
        private func calculateBeltPath3(center1: CGPoint, center2: CGPoint, r: CGFloat) -> UIBezierPath {
            let path = UIBezierPath()
            
            let dx = center2.x - center1.x
            let dy = center2.y - center1.y
            let D = hypot(dx, dy)
            
            if D <= 2 * r {
                // Two wheels overlap or touch
                return path
            }
            
            let ux = dx / D
            let uy = dy / D
            let nx = -uy
            let ny = ux
            
            let x1a = center1.x + r * nx
            let y1a = center1.y + r * ny
            let x1b = center1.x - r * nx
            let y1b = center1.y - r * ny
            let x2a = center2.x + r * nx
            let y2a = center2.y + r * ny
            let x2b = center2.x - r * nx
            let y2b = center2.y - r * ny
            
            path.move(to: CGPoint(x: x1a, y: y1a))
            path.addLine(to: CGPoint(x: x2a, y: y2a))
            
            let rect2 = CGRect(x: center2.x - r, y: center2.y - r, width: 2 * r, height: 2 * r)
            let startAngle2 = atan2(y2a - center2.y, x2a - center2.x) * 180 / .pi
            let endAngle2 = atan2(y2b - center2.y, x2b - center2.x) * 180 / .pi
            var sweepAngle2 = endAngle2 - startAngle2
            if sweepAngle2 < 0 {
                sweepAngle2 += 360
            }
            path.addArc(withCenter: center2, radius: r, startAngle: startAngle2 * .pi / 180, endAngle: endAngle2 * .pi / 180, clockwise: false)
            
            path.addLine(to: CGPoint(x: x1b, y: y1b))
            
            let rect1 = CGRect(x: center1.x - r, y: center1.y - r, width: 2 * r, height: 2 * r)
            let startAngle1 = atan2(y1b - center1.y, x1b - center1.x) * 180 / .pi
            let endAngle1 = atan2(y1a - center1.y, x1a - center1.x) * 180 / .pi
            var sweepAngle1 = endAngle1 - startAngle1
            if sweepAngle1 < 0 {
                sweepAngle1 += 360
            }
            path.addArc(withCenter: center1, radius: r, startAngle: startAngle1 * .pi / 180, endAngle: endAngle1 * .pi / 180, clockwise: false)
            
            path.close()
            return path
        }
    }
    
    // MARK: - Paint
    class Paint {
        var color: UIColor = .black
        var strokeWidth: CGFloat = 1
        var strokeCap: CGLineCap = .butt
        var strokeJoin: CGLineJoin = .miter
        //var style: CGContext.DrawingStyle = .fill
        var isAntiAlias: Bool = false
    }
    
    // MARK: - Point
    struct Point: Hashable {
        let x: Int
        let y: Int
    }
    
    // MARK: - GridPathGenerator
    class GridPathGenerator {
        private let WIDTH = 4
        private let HEIGHT = 6
        private let MIN_LENGTH = 8
        private let MAX_LENGTH = 15
        
        private var visited: [[Bool]] = []
        private var path: [Point] = []
        private var edges: Set<Edge> = []
        
        func generateRandomPath() -> [Point] {
            while true {
                visited = Array(repeating: Array(repeating: false, count: WIDTH), count: HEIGHT)
                path = []
                edges = []
                
                let startX = Int.random(in: 0..<WIDTH)
                let startY = Int.random(in: 0..<HEIGHT)
                
                if dfs(x: startX, y: startY) {
                    return path
                }
            }
        }
        
        private func dfs(x: Int, y: Int) -> Bool {
            visited[y][x] = true
            path.append(Point(x: x, y: y))
            
            if path.count >= MIN_LENGTH && Int.random(in: MIN_LENGTH...MAX_LENGTH) <= path.count {
                return true
            }
            
            var neighbors = getUnvisitedNeighbors(x: x, y: y)
            neighbors.shuffle()
            
            for neighbor in neighbors {
                let nx = neighbor.x
                let ny = neighbor.y
                let newEdge = Edge(p1: Point(x: x, y: y), p2: Point(x: nx, y: ny))
                
                if !visited[ny][nx] && !isCrossing(newEdge: newEdge) {
                    edges.insert(newEdge)
                    if dfs(x: nx, y: ny) {
                        return true
                    }
                    edges.remove(newEdge)
                }
            }
            
            visited[y][x] = false
            path.removeLast()
            return false
        }
        
        private func getUnvisitedNeighbors(x: Int, y: Int) -> [Point] {
            var neighbors: [Point] = []
            for dx in -1...1 {
                for dy in -1...1 {
                    if dx == 0 && dy == 0 { continue }
                    let nx = x + dx
                    let ny = y + dy
                    if nx >= 0 && nx < WIDTH && ny >= 0 && ny < HEIGHT && !visited[ny][nx] {
                        neighbors.append(Point(x: nx, y: ny))
                    }
                }
            }
            return neighbors
        }
        
        private func isCrossing(newEdge: Edge) -> Bool {
            for edge in edges {
                if edgesAreCrossing(edge: edge, newEdge: newEdge) {
                    return true
                }
            }
            return false
        }
        
        private func edgesAreCrossing(edge: Edge, newEdge: Edge) -> Bool {
            if edge.isAdjacentTo(other: newEdge) { return false }
            return linesIntersect(
                x1: edge.p1.x, y1: edge.p1.y, x2: edge.p2.x, y2: edge.p2.y,
                x3: newEdge.p1.x, y3: newEdge.p1.y, x4: newEdge.p2.x, y4: newEdge.p2.y
            )
        }
        
        private func linesIntersect(x1: Int, y1: Int, x2: Int, y2: Int, x3: Int, y3: Int, x4: Int, y4: Int) -> Bool {
            return ccw(x1: x1, y1: y1, x2: x2, y2: y2, x3: x3, y3: y3) !=
                   ccw(x1: x1, y1: y1, x2: x2, y2: y2, x3: x4, y3: y4) &&
                   ccw(x1: x1, y1: y1, x2: x3, y2: y3, x3: x2, y3: y2) !=
                   ccw(x1: x1, y1: y1, x2: x3, y2: y3, x3: x4, y3: y4)
        }
        
        private func ccw(x1: Int, y1: Int, x2: Int, y2: Int, x3: Int, y3: Int) -> Bool {
            return (y3 - y1) * (x2 - x1) > (y2 - y1) * (x3 - x1)
        }
        
        struct Edge: Hashable {
            let p1: Point
            let p2: Point
            
            init(p1: Point, p2: Point) {
                if p1.x < p2.x || (p1.x == p2.x && p1.y <= p2.y) {
                    self.p1 = p1
                    self.p2 = p2
                } else {
                    self.p1 = p2
                    self.p2 = p1
                }
            }
            
            func isAdjacentTo(other: Edge) -> Bool {
                return p1 == other.p1 || p1 == other.p2 || p2 == other.p1 || p2 == other.p2
            }
        }
    }
}
