<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="15dp"
        android:background="@drawable/bg_flashcards_card"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="544:364"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/tv_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMaxTextSize="500dp"
            android:breakStrategy="simple"
            android:fontFamily="@font/utm_avo_bold"
            android:gravity="center"
            android:hyphenationFrequency="none"
            android:lines="2"
            android:text="Bốt-ni-a và Héc-dê-go-vi-na"
            android:textColor="@color/flashcard_color"
            app:autoSizeTextType="uniform"
            app:heightRatio="0.15"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9" />

        <com.caverock.androidsvg.SVGImageView
            android:id="@+id/iv_logo"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9" />

        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/bg_flashcards_card_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintHorizontal_bias="0.93"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.08" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>