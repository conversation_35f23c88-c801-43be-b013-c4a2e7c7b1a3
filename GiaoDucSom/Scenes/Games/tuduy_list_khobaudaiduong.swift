//
//  tuduy_list_khobaudaiduong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_khobaudaiduong: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private let column = 7
    private let row = 5
    private var boatView: UIView!
    private var boatImage: UIImageView!
    private var hintText: HeightRatioTextView!
    private var arrows: [UIView] = []
    private var points: [KBPoint] = []
    private var startPoint: KBPoint!
    private var endPoint: KBPoint!
    private var currentPointIndex: Int = 0
    private var views: [UIView] = []
    private var svgAnimationView: UIImageView!
    private var mapView: UIImageView!
    private var animationExplore2: SVGKImage?
    private var coinView: UIView!
    var indexToView:[Int: UIView] = [:]
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        alpha = 0.001
        let bgView = UIImageView()
        bgView.image = Utilities.SVGImage(named: "tuduy_khobau_bg")
        bgView.contentMode = .scaleAspectFill
        view.addSubviewWithInset(subview: bgView, inset: 0)
        
        let contentLayout = UIView()
        //contentLayout.backgroundColor = .red
        contentLayout.clipsToBounds = false
        addSubview(contentLayout)
        contentLayout.makeViewCenterAndKeep(ratio: 2.2)
        
        mapView = UIImageView()
        mapView.image = Utilities.SVGImage(named: "tuduy_khobau_hint")
        contentLayout.addSubview(mapView)
        mapView.snp.makeConstraints { make in
            make.height.equalTo(contentLayout).multipliedBy(0.6)
            make.width.equalTo(mapView.snp.height).multipliedBy(146.8 / 176.6) // Ratio 146.8:176.6
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.mapView.snapToHorizontalBias(horizontalBias: 0.05)
        }
        let mapTapGesture = UITapGestureRecognizer(target: self, action: #selector(handleMapTap(_:)))
        mapView.addGestureRecognizer(mapTapGesture)
        mapView.isUserInteractionEnabled = true
        
        hintText = HeightRatioTextView()
        hintText.font = .UTMAvoBold(size: 12)
        hintText.textColor = UIColor(red: 219/255, green: 131/255, blue: 58/255, alpha: 1) // #DB833A
        hintText.textAlignment = .center
        hintText.setHeightRatio(0.7)
        hintText.backgroundColor = .clear
        mapView.addSubview(hintText)
        hintText.snp.makeConstraints { make in
            make.width.equalTo(mapView).multipliedBy(0.7)
            make.height.equalTo(mapView).multipliedBy(0.5)
            make.center.equalToSuperview()
        }
        
        let mapContainer = UIImageView()
        mapContainer.isUserInteractionEnabled = true
        mapContainer.image = Utilities.SVGImage(named: "tuduy_khobau_map")
        contentLayout.addSubview(mapContainer)
        mapContainer.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.97)
            make.height.equalTo(mapContainer.snp.width).multipliedBy(1236.2 / 1803.6) // Ratio 1803.6:1236.2
        }
        
        let gridContainer = UIView()
        //gridContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        mapContainer.addSubview(gridContainer)
        gridContainer.snp.makeConstraints { make in
            make.width.equalTo(mapContainer).multipliedBy(0.89)
            make.height.equalTo(mapContainer).multipliedBy(0.85)
        }
        
        addActionOnLayoutSubviews {
            gridContainer.snapToHorizontalBias(horizontalBias: 0.66)
            gridContainer.snapToVerticalBias(verticalBias: 0.8)
        }
        
        boatView = UIView()
        //boatView.backgroundColor = .red.withAlphaComponent(0.3)
        gridContainer.addSubview(boatView)
        boatView.snp.makeConstraints { make in
            make.width.equalTo(gridContainer).multipliedBy(0.1428571428571429)
            make.height.equalTo(gridContainer).multipliedBy(0.2)
            make.left.top.equalToSuperview()
        }
        
        boatImage = UIImageView(image: Utilities.SVGImage(named: "tuduy_khobau_boat"))
        boatImage.contentMode = .scaleAspectFit
        boatView.addSubview(boatImage)
        boatImage.snp.makeConstraints { make in
            make.width.equalTo(boatView).multipliedBy(1.4)
            make.height.equalTo(boatView).multipliedBy(1.4)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        svgAnimationView = UIImageView()
        svgAnimationView.contentMode = .scaleAspectFit
        boatView.addSubview(svgAnimationView)
        svgAnimationView.snp.makeConstraints { make in
            make.width.equalTo(boatView).multipliedBy(2.0)
            make.height.equalTo(boatView).multipliedBy(2.0)
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        gridLayout.itemRatio = 1.1
        gridLayout.itemSpacingRatio = 0.001
        gridLayout.columns = column
        gridLayout.insetRatio = 0.001
        gridContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView() // Giả định từ item_coin_view.xml
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.height.equalTo(50) // Giả định kích thước, cần thay bằng thực tế từ item_coin_view
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    
    override func updateData() {
        super.updateData()
        points = GridPathGenerator().generatePath()
        startPoint = points[0]
        endPoint = points[points.count - 1]
        
        views = []
        for y in 0..<row {
            for x in 0..<column {
                let view = UIView()
                view.clipsToBounds = false
                views.append(view)
                
                let isStartPoint = startPoint.row == y && startPoint.col == x
                let isEndPoint = endPoint.row == y && endPoint.col == x
                let cloudImage = UIImageView(image: Utilities.SVGImage(named: isStartPoint || isEndPoint ? "empty" : Bool.random() ? "tuduy_khobau_cloud1" : "tuduy_khobau_cloud2"))
                cloudImage.contentMode = .scaleAspectFit
                cloudImage.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
                view.addSubview(cloudImage)
                cloudImage.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                
                if isEndPoint {
                    let imageChest = UIImageView(image: Utilities.SVGImage(named: "tuduy_khobau_chest"))
                    imageChest.contentMode = .scaleAspectFit
                    view.addSubview(imageChest)
                    imageChest.snp.makeConstraints { make in
                        make.edges.equalToSuperview().inset(view.frame.width * 0.15) // 0.7 height percent
                    }
                }
                
                view.tag = y * column + x // Encode Point vào tag
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleCellTap(_:)))
                view.addGestureRecognizer(tapGesture)
                view.isUserInteractionEnabled = true
                indexToView[y*column+x] = view
            }
        }
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "tuduy/kho bau dai duong")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        animationExplore2 = Utilities.GetSVGKImage(named: "animations/animation_explore2.svg")
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/kho bau dai duong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        //boatView.frame.origin.y = CGFloat(startPoint.row) * gridLayout.frame.height / CGFloat(row)
        scheduler.schedule(delay: 0.1) {
            [weak self] in
            guard let self = self else { return }
            boatView.moveToCenter(of: gridLayout.subviews[startPoint.row * gridLayout.columns], duration: 0.1) {
                [weak self] _ in
                guard let self = self else { return }
                self.alpha = 1
            }
        }
        updateHint()
    }
    
    // MARK: - Touch Handling
    @objc private func handleMapTap(_ gesture: UITapGestureRecognizer) {
        let text = hintText.text!.lowercased()
        playSound("topics/Alphabet/\(text.prefix(1))2", "topics/Numbers/\(text.dropFirst())")
    }
    
    @objc private func handleCellTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view as? UIView else { return }
        pauseGame(stopMusic: false)
        
        if currentPointIndex > points.count - 1 {
            resumeGame(startMusic: false)
            return
        }
        
        let point = KBPoint(row: view.tag / column, col: view.tag % column)
        let currentPoint = points[currentPointIndex]
        if abs(currentPoint.col - point.col) + abs(currentPoint.row - point.row) == 1 {
            playSound("effect/boat_move")
            let correct = points.contains { $0 == point }
            if let cloudImage = view.subviews.first as? UIImageView {
                UIView.animate(withDuration: 0.5) {
                    cloudImage.alpha = 0
                    cloudImage.transform = CGAffineTransform(translationX: CGFloat(self.random(-1, 1)) * cloudImage.frame.width, y: 0)
                }
            }
            
            if !correct {
                let bombImage = UIImageView(image: Utilities.SVGImage(named: "tuduy_khobau_bomb"))
                view.addSubview(bombImage)
                bombImage.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                UIView.animate(withDuration: 0.2) {
                    bombImage.alpha = 1
                }
                pauseGame(stopMusic: false)
                playSound(name: "effect/boom", delay: 0.5)
                UIView.animate(withDuration: 0.3, delay: 0.5) {
                    bombImage.alpha = 0
                }
                guard let animationExplore2 = animationExplore2 else { return }
                for i in 0..<animationExplore2.caLayerTree.sublayers!.count {
                    let svg = animationExplore2.caLayerTree.sublayers![i]
                    scheduler.schedule(after: 0.5 + Double(i) * 0.05) {
                        // self.svgAnimationView.image = svg.uiImage
                    }
                }
                scheduler.schedule(after: 0.5 + Double(animationExplore2.caLayerTree.sublayers!.count) * 0.05) {
                    self.svgAnimationView.isHidden = true
                }
                scheduler.schedule(after: 0.5) {
                    self.boatImage.image = Utilities.SVGImage(named: "tuduy_khobau_boat2")
                }
                let delay = 1.5 + playSound(delay: 1.5, names: ["effect/boom"])
                setGameWrong()
                coinView.frame = self.boatView.frame
                animateCoinIfCorrect(view: self.coinView)
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                currentPointIndex = points.firstIndex(of: point) ?? 0
                if currentPointIndex == points.count - 1 {
                    pauseGame(stopMusic: false)
                    scheduler.schedule(after: 0.5) {
                        self.animateCoinIfCorrect(view: self.boatView)
                    }
                    removeCurrentArrows()
                    let delay = 0.5
                    scheduler.schedule(after: delay) {
                        view.subviews.forEach { $0.removeFromSuperview() }
                    }
                    let totalDelay = delay + playSound(delay: delay, names: ["effect/answer_correct1", self.getCorrectHumanSound(), self.endGameSound()])
                    scheduler.schedule(delay: totalDelay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    playSound("effect/answer_correct")
                    updateHint()
                }
            }
            scheduler.schedule(delay: 0.5) {
                [weak self] in
                guard let self = self else { return }
                self.boatView.moveToCenter(of: indexToView[point.row*gridLayout.columns+point.col]!,duration: 0.2)
            }
        } else {
            playSound("effect/answer_wrong")
            resumeGame(startMusic: false)
        }
    }
    
    // MARK: - Helper Methods
    private func removeCurrentArrows() {
        arrows.forEach { $0.removeFromSuperview() }
        arrows = []
    }
    
    private func updateHint() {
        let nextPoint = points[currentPointIndex + 1]
        hintText.text = getColumnName(col: nextPoint.col) + getRowName(row: nextPoint.row)
        removeCurrentArrows()
        
        let currentPoint = points[currentPointIndex]
        let directions = [
            [-1, 0, R10.drawable.tuduy_khobau_arrow4], // Up
            [1, 0, R10.drawable.tuduy_khobau_arrow2],  // Down
            [0, -1, R10.drawable.tuduy_khobau_arrow3], // Left
            [0, 1, R10.drawable.tuduy_khobau_arrow1]   // Right
        ]
        let saveIndex = currentPointIndex
        scheduler.schedule(after: 0.5) { [weak self] in
            guard let self = self, saveIndex == self.currentPointIndex else { return }
            for direction in directions {
                let newRow = currentPoint.row + direction[0]
                let newCol = currentPoint.col + direction[1]
                if newRow >= 0 && newRow < self.row && newCol >= 0 && newCol < self.column {
                    let arrowImage = UIImageView(image: Utilities.SVGImage(named: direction[2] == R10.drawable.tuduy_khobau_arrow1 ? "tuduy_khobau_arrow1" : direction[2] == R10.drawable.tuduy_khobau_arrow2 ? "tuduy_khobau_arrow2" : direction[2] == R10.drawable.tuduy_khobau_arrow3 ? "tuduy_khobau_arrow3" : "tuduy_khobau_arrow4"))
                    arrowImage.contentMode = .scaleAspectFit
                    let cell = self.views[newRow * self.column + newCol]
                    cell.addSubview(arrowImage)
                    cell.superview?.bringSubviewToFront(cell)
                    arrowImage.snp.makeConstraints { make in
                        make.edges.equalToSuperview()
                    }
                    self.arrows.append(arrowImage)
                    UIView.animate(withDuration: 0.2) {
                        arrowImage.alpha = 1
                    }
                }
            }
            if self.currentPointIndex != 0 {
                self.resumeGame( startMusic: false)
            }
        }
    }
    
    private func getColumnName(col: Int) -> String {
        return String(UnicodeScalar("A".unicodeScalars.first!.value + UInt32(col))!)
    }
    
    private func getRowName(row: Int) -> String {
        return String(row + 1)
    }
}

// MARK: - Supporting Structures
struct KBPoint: Equatable {
    let row: Int
    let col: Int
    
    static func ==(lhs: KBPoint, rhs: KBPoint) -> Bool {
        return lhs.row == rhs.row && lhs.col == rhs.col
    }
}

class GridPathGenerator {
    private let ROWS = 5
    private let COLS = 7
    private let MIN_LENGTH = 10
    private let MAX_LENGTH = 20
    
    private var visited: [[Bool]] = []
    private var path: [KBPoint] = []
    
    func generatePath() -> [KBPoint] {
        while true {
            visited = Array(repeating: Array(repeating: false, count: COLS), count: ROWS)
            path = []
            
            let startRow = Int.random(in: 0..<ROWS)
            let startPoint = KBPoint(row: startRow, col: 0)
            path.append(startPoint)
            visited[startRow][0] = true
            
            let endRow = Int.random(in: 0..<ROWS)
            let endPoint = KBPoint(row: endRow, col: COLS - 1)
            
            if dfs(current: startPoint, endPoint: endPoint) {
                return path
            }
        }
    }
    
    private func dfs(current: KBPoint, endPoint: KBPoint) -> Bool {
        if current == endPoint && path.count >= MIN_LENGTH && path.count <= MAX_LENGTH {
            return true
        }
        
        if path.count > MAX_LENGTH {
            return false
        }
        
        let neighbors = getNeighbors(p: current).shuffled()
        for neighbor in neighbors {
            let row = neighbor.row
            let col = neighbor.col
            
            if !visited[row][col] && !isAdjacentToPreviousPoints(p: neighbor) {
                visited[row][col] = true
                path.append(neighbor)
                
                if dfs(current: neighbor, endPoint: endPoint) {
                    return true
                }
                
                visited[row][col] = false
                path.removeLast()
            }
        }
        return false
    }
    
    private func getNeighbors(p: KBPoint) -> [KBPoint] {
        var neighbors: [KBPoint] = []
        let directions = [[-1, 0], [0, -1], [1, 0], [0, 1]]
        
        for d in directions {
            let newRow = p.row + d[0]
            let newCol = p.col + d[1]
            if isValidCell(row: newRow, col: newCol) {
                neighbors.append(KBPoint(row: newRow, col: newCol))
            }
        }
        return neighbors
    }
    
    private func isValidCell(row: Int, col: Int) -> Bool {
        return row >= 0 && row < ROWS && col >= 0 && col < COLS
    }
    
    private func isAdjacentToPreviousPoints(p: KBPoint) -> Bool {
        for i in 0..<(path.count - 1) {
            if areAdjacent(p1: p, p2: path[i]) {
                return true
            }
        }
        return false
    }
    
    private func areAdjacent(p1: KBPoint, p2: KBPoint) -> Bool {
        let rowDiff = abs(p1.row - p2.row)
        let colDiff = abs(p1.col - p2.col)
        return rowDiff + colDiff == 1
    }
}

struct R10 {
    struct drawable {
        static let tuduy_khobau_arrow1 = 1
        static let tuduy_khobau_arrow2 = 2
        static let tuduy_khobau_arrow3 = 3
        static let tuduy_khobau_arrow4 = 4
        static let tuduy_khobau_boat = 5
        static let tuduy_khobau_boat2 = 6
        static let tuduy_khobau_cloud1 = 7
        static let tuduy_khobau_cloud2 = 8
        static let tuduy_khobau_chest = 9
        static let tuduy_khobau_bomb = 10
        static let tuduy_khobau_bg = 11
        static let tuduy_khobau_hint = 12
        static let tuduy_khobau_map = 13
    }
}
