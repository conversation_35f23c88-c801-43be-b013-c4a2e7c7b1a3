//
//  tuduy_list_dovatlan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_dovatlan: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var items: MyList<Item> = MyList<Item>()
    private var folders: [String] = []
    private var indexes: [Int] = []
    private var meIndex: Int = 0
    
    // MARK: - Setup Layout
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 39/255, green: 57/255, blue: 75/255, alpha: 1) // #27394B
        
        // Background Image
        let backgroundImage = UIImageView(image: Utilities.SVGImage(named: "tuduy_dovatlan_bg"))
        backgroundImage.contentMode = .scaleAspectFill
        addSubviewWithPercentInset(subview: backgroundImage, percentInset: 0)
        backgroundImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Grid Layout
        gridLayout = MyGridView()
        addSubviewWithPercentInset(subview: gridLayout, percentInset: 0)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true
    }
    
    // MARK: - GameFragment Methods
    override func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        setupLayout()
    }
    
    override func updateData() {
        super.updateData()
        
        // Lấy items và folders từ FlashcardsManager
        for pack in FlashcardsManager.shared.getPacks() {
            for item in pack.items {
                if item.rolling != nil {
                    items.addAndReturn(item)
                    folders.append(pack.folder)
                }
            }
        }
        
        // Chọn indexes
        indexes = Utils.generatePermutation(items.count)
        var chooseIndexes: [Int] = []
        for index in indexes {
            if items[index].rolling == -1 {
                chooseIndexes.append(index)
                if chooseIndexes.count == 2 { break }
            }
        }
        for index in indexes {
            if items[index].rolling == 1 {
                chooseIndexes.append(index)
                meIndex = chooseIndexes.count - 1
                break
            }
        }
        
        // Tạo views
        var views: [UIView] = []
        for i in 0..<chooseIndexes.count {
            let index = chooseIndexes[i]
            let item = items[index]
            let folder = folders[index]
            let view = createItemLandovat(item: item, folder: folder)
            
            let verticalBias: CGFloat = i % 2 == 0 ? 0.85 : 0.15
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(itemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
            view.tag = i // Lưu index để xác định meIndex
            
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
            views.append(view)
        }
        
        // Cấu hình gridLayout
        gridLayout.columns = views.count
        gridLayout.itemRatio = 0.78
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
        /*
        gridLayout.soundProvider = { index in
            let item = self.items[self.chooseIndexes[index]]
            let folder = self.folders[self.chooseIndexes[index]]
            return "topics/\(folder)/\(item.path.replacingOccurrences(of: ".svg", with: ""))"
        }
        gridLayout.soundDelay = { _ in 1.5 }
        */
        // Animation và âm thanh
        var delay = playSound(openGameSound(), "tuduy/lan do vat")
        gridLayout.playSound = false
        gridLayout.step = 1.5
        for i in 0..<chooseIndexes.count {
            let index = chooseIndexes[i]
            let item = items[index]
            let folder = folders[index]
            playSound(name: "topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))", delay: delay + 0.15 + gridLayout.step * Double(i))
        }
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/lan do vat")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Event Handling
    @objc private func itemTapped(_ sender: UITapGestureRecognizer) {
        guard let view = sender.view else { return }
        let index = view.tag
        
        pauseGame()
        if index == meIndex {
            let textview = view.viewWithTag(101) as? UILabel
            animateCoinIfCorrect(view: textview!)
            let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemLandovat(item: Item, folder: String) -> UIView {
        let view = UIView()
        view.clipsToBounds = false
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 0.78)
        
        let backgroundView = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        backgroundView.contentMode = .scaleAspectFit
        itemContainer.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(backgroundView.snp.width)
        }
        
        let svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        svgView.tag = 100
        svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item.path!)").uiImage
        itemContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(svgView.snp.width)
        }
        
        let textName = AutosizeLabel()
        textName.tag = 101
        textName.text = item.name.vi
        textName.textColor = UIColor(red: 223/255, green: 163/255, blue: 112/255, alpha: 1) // #DFA370
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.1
        itemContainer.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalTo(itemContainer).multipliedBy(0.2)
        }
        
        return view
    }
}


