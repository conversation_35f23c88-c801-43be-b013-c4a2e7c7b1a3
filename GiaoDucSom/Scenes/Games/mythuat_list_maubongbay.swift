//
//  mythuat_list_maubongbay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/5/25.
//


import UIKit
import SnapKit
import SVGKit
import Interpolate

class mythuat_list_maubongbay: NhanBietGameFragment {
    // MARK: - Properties
    private let colorNames = ["Red", "Yellow", "Orange", "Blue", "Green", "Purple", "White", "Black", "Gray", "Pink"]
    private let colors: [UIColor] = [
        UIColor(red: 1, green: 0, blue: 0, alpha: 1), // #FF0000
        UIColor(red: 1, green: 1, blue: 0, alpha: 1), // #FFFF00
        UIColor(red: 1, green: 165/255, blue: 0, alpha: 1), // #FFA500
        UIColor(red: 0, green: 0, blue: 1, alpha: 1), // #0000FF
        UIColor(red: 0, green: 128/255, blue: 0, alpha: 1), // #008000
        UIColor(red: 128/255, green: 0, blue: 128/255, alpha: 1), // #800080
        UIColor.white, // #FFFFFF
        UIColor.black, // #000000
        UIColor(red: 128/255, green: 128/255, blue: 128/255, alpha: 1), // #808080
        UIColor(red: 1, green: 192/255, blue: 203/255, alpha: 1) // #FFC0CB
    ]
    private var meIndex: Int = 0
    private var chooseIndexes: [Int] = []
    private var gridLayout: MyGridView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_cloud"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        chooseIndexes = Utils.generatePermutation(4, size: colorNames.count)
        meIndex = chooseIndexes.randomElement() ?? 0
        // Debug mode (commented out as in Java)
        // if DEBUG { chooseIndexes[0] = 3; meIndex = 3 }
    }
    
    override func createGame() {
        super.createGame()
        var delay = 1.0
        delay += playSound(delay: delay, names: ["mythuat/mythuat_bong mau", "topics/Colors/\(colorNames[meIndex].lowercased())"])
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        let files = [1, 2, 3, 4, 5].shuffled().prefix(chooseIndexes.count).map { "images/nhanbiet_bongmau\($0).svg" }
        let delayTimes = Utils.generatePermutation(4, size: 4)
        var views: [UIView] = []
        
        for (i, file) in files.enumerated() {
            let svgImage = Utilities.GetSVGKImage(named: file)
            if let paths = svgImage.caLayerTree.sublayers {
                for path in paths {
                    if path.name as? String == "color" {
                        path.setFillColor(color: self.colors[self.chooseIndexes[i]].cgColor)
                    }
                }
            }
            
            let svgView = UIImageView()
            svgView.contentMode = .scaleAspectFit
            svgView.image = svgImage.uiImage
            svgView.isUserInteractionEnabled = true
            svgView.accessibilityIdentifier = "\(chooseIndexes[i])" // Lưu index để kiểm tra
            views.append(svgView)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            svgView.addGestureRecognizer(tapGesture)
            
            svgView.alpha = 0
            scheduler.schedule(delay: 0.2 * Double(1 + delayTimes[i])) {
                [weak self] in
                guard let self = self else { return }
                let easy = BackEaseInterpolater()
                easy.mode = .easeOut
                let animValues: [CGFloat] = [self.gridLayout.frame.height, 0]
                let timeChange = Interpolate(values: [0,1],
                                             apply: { [weak self] (value) in
                    guard let self = self else { return }
                    let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                    svgView.transform = CGAffineTransformMakeTranslation(0, finalValue)
                    svgView.alpha = 1
                })
                timeChange.animate(1, duration: 2.0){
                    
                }
                /*
                UIView.animate(withDuration: 2.0, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                    svgView.alpha = 1
                    svgView.transform = CGAffineTransform(translationX: 0, y: -self.gridLayout.frame.height)
                }) { _ in
                    svgView.transform = .identity
                }*/
            }
        }
        
        gridLayout.columns = views.count
        gridLayout.itemRatio = 250.0 / 400.0
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("mythuat/mythuat_bong mau", "topics/Colors/\(colorNames[meIndex].lowercased())")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let indexStr = view.accessibilityIdentifier,
              let index = Int(indexStr) else { return }
        
        if index == meIndex {
            pauseGame(stopMusic: false)
            let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
            animateCoinIfCorrect(view: gridLayout)
            
            let easy = BackEaseInterpolater()
            easy.mode = .easeIn
            let animValues: [CGFloat] = [0, -self.gridLayout.frame.height]
            let timeChange = Interpolate(values: [0,1],
                                         apply: { [weak self] (value) in
                guard let self = self else { return }
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                view.transform = CGAffineTransformMakeTranslation(0, finalValue)
            })
            timeChange.animate(1, duration: 0.5){
                
            }
            
            
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            pauseGame(stopMusic: false)
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            let height = view.frame.height / 20
            view.transform = CGAffineTransform(translationX: 0, y: height)
            UIView.animate(withDuration: 5.0, delay: 0, usingSpringWithDamping: 0.2, initialSpringVelocity: 0, options: [.curveEaseOut], animations: {
                view.transform = .identity
            }) { _ in
                
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
