//
//  nhanbiet_list_xepthanhngang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_xepthanhngang: NhanBietGameFragment {
    // MARK: - Properties
    private var gridBottomLayout: MyGridView!
    private var gridBottom2Layout: MyGridView!
    private var svgView: SVGImageView!
    private var answer: String = "abcd"
    private var svg: SVGKImage?
    private var listViews2: [UIView] = []
    private var originListViews: [UIView] = []
    private var itemRatio: CGFloat = 8.0
    private var move: Int = 0
    private var zIndex: CGFloat = 10
    private var soundIndex: Int = 0
    private var distance: CGFloat = 0
    private var busy: Bool = false
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bar_horizontal"))!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.makeViewCenterFillAndKeep(ratio: 2688.0/1236.2)
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        
        gridBottomLayout = MyGridView()
        gridBottomLayout.alpha = 0.01
        gridBottomLayout.isHidden = true
        mainContainer.addSubview(gridBottomLayout)
        gridBottomLayout.makeViewCenterAndKeep(ratio: 2)
        
        gridBottom2Layout = MyGridView()
        gridBottom2Layout.clipsToBounds = false
        mainContainer.addSubview(gridBottom2Layout)
        gridBottom2Layout.makeViewCenterAndKeep(ratio: 2)
        
        svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view"
        mainContainer.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let folder = getFolder(), let item = getItem(), let path = item.path, !path.isEmpty else { return }
        
        svg = Utilities.GetSVGKImage(named: "topics/\(folder)/\(path)")
        
        let delay = playSound(openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_bar_horizontal")
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_bar_horizontal")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        var listViews: [UIView] = []
        for i in 0..<4 {
            let view = UIView()
            let innerView = UIView()
            //innerView.backgroundColor = UIColor(hex: "#0F00")
            view.addSubview(innerView)
            innerView.makeViewCenterAndKeep(ratio: 1937.6/259.3)
            
            let itemBackground = UIImageView()
            itemBackground.image = Utilities.SVGImage(named: "nhanbiet_btn_bar_horizontal1")
            //itemBackground.transform = CGAffineTransform(scaleX: 1.24, y: 1.24)
            innerView.addSubview(itemBackground)
            itemBackground.snp.makeConstraints { make in
                make.center.equalToSuperview()
                make.width.height.equalToSuperview().multipliedBy(1.24)
            }
            
            let textView = UILabel()
            textView.stringTag = "textview"
            let text = String(answer[answer.index(answer.startIndex, offsetBy: i)])
            textView.text = text
            textView.textColor = UIColor(hex: "#68C1FF")
            textView.textAlignment = .center
            textView.font = UIFont(name: "SVN-Freude", size: 1500)
            textView.adjustsFontSizeToFitWidth = true
            textView.minimumScaleFactor = 0.1
            textView.numberOfLines = 1
            textView.isHidden = true
            innerView.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.8)
                make.height.equalToSuperview().multipliedBy(0.8)
                make.centerX.equalToSuperview()
                make.top.equalToSuperview()
            }
            
            let percentContainer = UIView()
            //percentContainer.clipsToBounds = true
            view.addSubview(percentContainer)
            percentContainer.makeViewCenterAndKeep(ratio: 1937.6/259.3)
            
            let topLine = UIView()
            percentContainer.addSubview(topLine)
            topLine.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalToSuperview().multipliedBy(0.12)
                make.top.equalToSuperview()
            }
            
            let svgContainer = UIView()
            svgContainer.clipsToBounds = true
            percentContainer.addSubview(svgContainer)
            svgContainer.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalToSuperview()
                make.top.equalTo(topLine.snp.bottom)
            }
            
            let svgImageView = SVGImageView(frame: .zero)
            svgImageView.stringTag = "svg_view"
            svgImageView.image = svg?.uiImage
            svgImageView.contentMode = .scaleAspectFill
            svgContainer.addSubview(svgImageView)
            svgImageView.snp.makeConstraints { make in
                make.width.equalToSuperview()
                make.height.equalTo(svgImageView.snp.width)
                make.centerX.equalToSuperview()
                make.top.equalToSuperview()
            }
            
            view.stringTag = text
            listViews.append(view)
        }
        gridBottomLayout.columns = 1
        gridBottomLayout.itemRatio = Float(itemRatio)
        gridBottomLayout.itemSpacingRatio = 0.0
        gridBottomLayout.reloadItemViews(views: listViews)
        originListViews = listViews
        
        var randomIndexes: [Int]
        repeat {
            randomIndexes = (0..<answer.count).shuffled()
        } while randomIndexes.enumerated().allSatisfy { $0.element == $0.offset }
        
        listViews2 = []
        for i in 0..<answer.count {
            let view = UIView()
            let innerView = UIView()
            //innerView.backgroundColor = UIColor(hex: "#0F00")
            view.addSubview(innerView)
            innerView.makeViewCenterAndKeep(ratio: 1937.6/259.3)
            
            let itemBackground = UIImageView()
            let text = String(answer[answer.index(answer.startIndex, offsetBy: randomIndexes[i])])
            itemBackground.image =  Utilities.SVGImage(named: text.lowercased() == "a" ? "nhanbiet_btn_bar_horizontal4" :
                                                                                   text.lowercased() == "b" ? "nhanbiet_btn_bar_horizontal3" :
                                                                                   text.lowercased() == "c" ? "nhanbiet_btn_bar_horizontal2" :
                                                                                   "nhanbiet_btn_bar_horizontal1")
            itemBackground.transform = CGAffineTransform(scaleX: 1.24, y: 1.24)
            innerView.addSubview(itemBackground)
            itemBackground.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let textView = UILabel()
            textView.stringTag = "textview"
            textView.text = text
            textView.textColor = UIColor(hex: "#68C1FF")
            textView.textAlignment = .center
            textView.font = UIFont(name: "SVN-Freude", size: 1500)
            textView.adjustsFontSizeToFitWidth = true
            textView.minimumScaleFactor = 0.1
            textView.numberOfLines = 1
            textView.isHidden = true
            innerView.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.8)
                make.height.equalToSuperview().multipliedBy(0.8)
                make.centerX.equalToSuperview()
                make.top.equalToSuperview()
            }
            
            let percentContainer = UIView()
            //percentContainer.clipsToBounds = true
            view.addSubview(percentContainer)
            percentContainer.makeViewCenterAndKeep(ratio: 1937.6/259.3)
            
            let topLine = UIView()
            percentContainer.addSubview(topLine)
            topLine.snp.makeConstraints { make in
                make.width.equalToSuperview()
                make.height.equalToSuperview().multipliedBy(0.12)
                make.top.equalToSuperview()
            }
            
            let svgContainer = UIView()
            svgContainer.clipsToBounds = true
            percentContainer.addSubview(svgContainer)
            svgContainer.snp.makeConstraints { make in
                make.left.right.equalToSuperview()
                make.height.equalToSuperview()
                make.top.equalTo(topLine.snp.bottom)
            }
            
            let svgImageView = SVGImageView(frame: .zero)
            svgImageView.stringTag = "svg_view"
            svgImageView.image = svg?.uiImage
            svgImageView.contentMode = .scaleAspectFill
            svgContainer.addSubview(svgImageView)
            svgImageView.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(4)
                make.height.equalTo(svgImageView.snp.width)
                make.centerX.equalToSuperview()
                make.top.equalToSuperview()
            }
            scheduler.schedule(delay: 0.2) { [weak self] in
                guard let self = self else { return }
                svgImageView.snapToVerticalBias(verticalBias: 0.33333 * CGFloat(randomIndexes[i]))
            }
            
            view.stringTag = text
            listViews2.append(view)
            
            let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
            view.addGestureRecognizer(panGesture)
        }
        gridBottom2Layout.columns = 1
        gridBottom2Layout.itemRatio = Float(itemRatio)
        gridBottom2Layout.itemSpacingRatio = 0.0
        gridBottom2Layout.reloadItemViews(views: listViews2)
        
        distance = originListViews.first.map { view1 in
            originListViews.dropFirst().first.map { view2 in
                let vector = view1.distanceFromCenterToCenter(to: view2)
                return hypot(vector.x, vector.y)
            } ?? 0
        } ?? 0
        
        reorderItems()
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        var dX: CGFloat = 0
        var dY: CGFloat = 0
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: gridBottom2Layout)
            dX = view.frame.minX - location.x
            dY = view.frame.minY - location.y
            view.layer.zPosition = zIndex
            zIndex += 1
            for i in 0..<listViews2.count {
                if let textView = listViews2[i].viewWithStringTag("textview") as? UILabel {
                    textView.textColor = UIColor(hex: "#68C1FF")
                }
            }
            UIView.animate(withDuration: 0.1) {
                view.transform = view.transform.scaledBy(x: 1.1, y: 1.1)
            }
            
        case .changed:
            let translation = gesture.translation(in: view)
            view.center = CGPoint(
                x: view.center.x ,
                y: view.center.y + translation.y
            )
            gesture.setTranslation(.zero, in: view)
            
            //let translation = gesture.translation(in: gridBottom2Layout)
            //view.frame.origin = CGPoint(x: translation.x + dX, y: translation.y + dY)
            
            if !busy {
                let oldIndex = listViews2.firstIndex(of: view) ?? 0
                for i in 0..<listViews2.count {
                    let view2 = listViews2[i]
                    if view == view2 { continue }
                    let vector = view.distanceFromCenterToCenter(to: view2)
                    let dist = hypot(vector.x, vector.y)
                    if dist < view.frame.height * 0.45 {
                        soundIndex = min(soundIndex + 1, 4)
                        playSound("effect/bubble\(soundIndex)")
                        Utils.vibrate()
                        busy = true
                        scheduler.schedule(after: 0.3) { [weak self] in
                            self?.busy = false
                        }
                        let index = i
                        let delta = index > oldIndex ? 1 : -1
                        var start = oldIndex
                        while start != index {
                            let viewTMP = self.listViews2[start]
                            self.listViews2[start] = self.listViews2[start + delta]
                            self.listViews2[start + delta] = viewTMP
                            start += delta
                        }
                        for j in 0..<self.listViews2.count {
                            if self.listViews2[j] != view {
                                self.listViews2[j].moveToCenter(of: self.originListViews[j], duration: 0.2)
                            }
                        }
                        break
                    }
                }
            }
            
        case .ended, .cancelled:
            let index = listViews2.firstIndex(of: view) ?? 0
            view.moveToCenter(of: originListViews[index], duration: 0.2)
            for j in 0..<listViews2.count {
                if let textView = listViews2[j].viewWithStringTag("textview") as? UILabel,
                   let textView2 = originListViews[j].viewWithStringTag("textview") as? UILabel {
                    let rightOrder = textView.text == textView2.text
                    textView.textColor = UIColor(hex: rightOrder ? "#73D048" : "#68C1FF")
                }
            }
            view.moveToCenter(of: self.originListViews[self.listViews2.firstIndex(of: view)!], duration: 0.1)
            UIView.animate(withDuration: 0.1) {
                //view.transform = .identity
            }
            move += 1
            reorderItems()
            checkFinish()
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func reorderItems() {
        for i in 0..<listViews2.count {
            let view = listViews2[listViews2.count - 1 - i]
            view.layer.zPosition = zIndex
            zIndex += 1
        }
    }
    
    private func checkFinish() {
        let finish = listViews2.enumerated().allSatisfy { i, view in
            if let textView = view.viewWithStringTag("textview") as? UILabel,
               let textView2 = originListViews[i].viewWithStringTag("textview") as? UILabel {
                return textView.text == textView2.text
            }
            return false
        }
        
        if finish {
            pauseGame(stopMusic: false)
            animateCoinIfCorrect(view: gridBottomLayout)
            let delay = playSound(delay: 0, names: [answerCorrect1EffectSound(), itemSound()!, getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay + 1.0) { [weak self] in
                self?.finishGame()
            }
        }
    }
}
