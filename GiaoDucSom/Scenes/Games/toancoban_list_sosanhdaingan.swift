//
//  toancoban_list_sosanhdaingan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sosanhdaingan: NhanBietGameFragment {
    // MARK: - Properties
    private var leftView: UIView!
    private var centerView: UIView!
    private var rightView: UIView!
    private var textLeft: UILabel!
    private var textCenter: UILabel!
    private var textRight: UILabel!
    private var pathViews: [PathView] = []
    private var paths: [[(Int, Int)]] = [] // Chuyển int[] sang tuple (row, col)
    private var count: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        
        let middleContainer = UIView()
        addSubview(middleContainer)
        middleContainer.makeViewCenterAndKeep(ratio: 17.0 / 7.0)
        
        let itemContainer = UIView()
        itemContainer.backgroundColor = .white
        middleContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer.snp.height).multipliedBy(17.0 / 5.0) // Ratio 17:5
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
        }
        addActionOnLayoutSubviews {
            itemContainer.snapToVerticalBias(verticalBias: 0.5)
        }
        
        let backgroundImage = UIImageView(image: Utilities.SVGImage(named: "math_hinhdainhat_bg"))
        backgroundImage.contentMode = .scaleAspectFill
        itemContainer.addSubview(backgroundImage)
        backgroundImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        leftView = UIView()
        leftView.clipsToBounds = false
        itemContainer.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.width.equalTo(leftView.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.6)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.leftView.snapToHorizontalBias(horizontalBias: 0.1435)
        }
        
        centerView = UIView()
        itemContainer.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.width.equalTo(centerView.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.6)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        rightView = UIView()
        itemContainer.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.width.equalTo(rightView.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.6)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.rightView.snapToHorizontalBias(horizontalBias: 0.8565)
        }
        
        textLeft = createTextView()
        middleContainer.addSubview(textLeft)
        textLeft.snp.makeConstraints { make in
            make.width.equalTo(middleContainer).multipliedBy(0.1)
            make.height.equalTo(middleContainer).multipliedBy(0.15)
        }
        addActionOnLayoutSubviews {
            self.textLeft.snapToHorizontalBias(horizontalBias: 0.21)
            self.textLeft.snapToVerticalBias(verticalBias: 0.97)
        }
        
        textCenter = createTextView()
        middleContainer.addSubview(textCenter)
        textCenter.snp.makeConstraints { make in
            make.width.equalTo(middleContainer).multipliedBy(0.1)
            make.height.equalTo(middleContainer).multipliedBy(0.15)
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.textCenter.snapToVerticalBias(verticalBias: 0.97)
        }
        
        textRight = createTextView()
        middleContainer.addSubview(textRight)
        textRight.snp.makeConstraints { make in
            make.width.equalTo(middleContainer).multipliedBy(0.1)
            make.height.equalTo(middleContainer).multipliedBy(0.15)
        }
        addActionOnLayoutSubviews {
            self.textRight.snapToHorizontalBias(horizontalBias: 0.79)
            self.textRight.snapToVerticalBias(verticalBias: 0.97)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        count = 10 + random.nextInt(bound: 6) // 10 to 15
        let numbers = [count, count - 1, count - 1].shuffled()
        let views = [leftView!, centerView!, rightView!]
        paths.removeAll()
        pathViews.removeAll()
        
        for i in 0..<numbers.count {
            let number = numbers[i]
            let path = PathFinder().make(startRow: random.nextInt(bound: 4), startCol: random.nextInt(bound: 4), pathLength: number)
            paths.append(path)
            let pathView = PathView()
            pathView.setPath(path: path)
            views[i].addSubview(pathView)
            pathView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            views[i].tag = number
            pathViews.append(pathView)
            
            let tap = UITapGestureRecognizer(target: self, action: #selector(onPathTapped(_:)))
            views[i].addGestureRecognizer(tap)
            views[i].isUserInteractionEnabled = true
        }
        
        let delay = playSound(openGameSound(), "toan/toan_hinh dai nhat")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_hinh dai nhat")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func onPathTapped(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UIView else { return }
        pauseGame()
        let number = view.tag
        let pathView = pathViews.first { $0.superview == view }!
        
        if number == count {
            var delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound())
            animateCoinIfCorrect(view: view)
            pathView.updateColor(color: UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1)) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                guard let self = self else { return }
                pathView.updateColor(color: UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1)) // #74B6FF
                for j in 0..<self.count {
                    scheduler.schedule(delay: Double(j) * 0.8) { [weak self] in
                        guard let self = self else { return }
                        for pathView in self.pathViews {
                            pathView.setSelectedIndex(index: j)
                            if j < self.paths[0].count - 1 {
                                self.textLeft.text = String(j + 1)
                            }
                            if j < self.paths[1].count - 1 {
                                self.textCenter.text = String(j + 1)
                            }
                            if j < self.paths[2].count - 1 {
                                self.textRight.text = String(j + 1)
                            }
                        }
                        self.playSound("topics/Numbers/\(j + 1)")
                    }
                }
                delay = playSound(delay: Double(self.count) * 0.8 + 1.0, names: [self.endGameSound()])
                scheduler.schedule(delay: Double(self.count) * 0.8 + 2.5) { [weak self] in
                    self?.finishGame()
                }
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            pathView.updateColor(color: UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1)) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                pathView.updateColor(color: UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1)) // #74B6FF
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createTextView() -> UILabel {
        let textView = AutosizeLabel()
        textView.textColor = .white
        textView.font = .Freude(size: 20)
        textView.textAlignment = .center
        textView.adjustsFontSizeToFitWidth = true
        textView.minimumScaleFactor = 0.1
        return textView
    }
}

// MARK: - PathFinder
class PathFinder {
    private static let SIZE = 4
    private let DIRECTIONS = [(-1, 0), (1, 0), (0, -1), (0, 1)] // Up, Down, Left, Right
    private let random = Random()
    
    func make(startRow: Int, startCol: Int, pathLength: Int) -> [(Int, Int)] {
        var visited = Array(repeating: Array(repeating: false, count: PathFinder.SIZE), count: PathFinder.SIZE)
        visited[startRow][startCol] = true
        
        var path: [(Int, Int)] = [(startRow, startCol)]
        
        if findRandomPath(row: startRow, col: startCol, remainingSteps: pathLength, visited: &visited, path: &path) {
            return path
        }
        return [] // No valid path found
    }
    
    private func findRandomPath(row: Int, col: Int, remainingSteps: Int, visited: inout [[Bool]], path: inout [(Int, Int)]) -> Bool {
        if remainingSteps == 0 {
            return true
        }
        
        let directions = DIRECTIONS.shuffled()
        for direction in directions {
            let newRow = row + direction.0
            let newCol = col + direction.1
            
            if isValidMove(row: newRow, col: newCol, visited: visited) {
                visited[newRow][newCol] = true
                path.append((newRow, newCol))
                
                if findRandomPath(row: newRow, col: newCol, remainingSteps: remainingSteps - 1, visited: &visited, path: &path) {
                    return true
                }
                
                visited[newRow][newCol] = false
                path.removeLast()
            }
        }
        return false
    }
    
    private func isValidMove(row: Int, col: Int, visited: [[Bool]]) -> Bool {
        return row >= 0 && row < PathFinder.SIZE && col >= 0 && col < PathFinder.SIZE && !visited[row][col]
    }
}

// MARK: - PathView
class PathView: UIView {
    private static let GRID_SIZE = 3
    private var pathPaint: UIColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    private var selectedPaint: UIColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
    private var path: [(Int, Int)] = []
    private var cellSize: CGFloat = 0
    private var selectedIndex: Int = -1
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        backgroundColor = .clear
        clipsToBounds = false
        transform = CGAffineTransformMakeScale(1.05, 1.05)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func updateColor(color: UIColor) {
        pathPaint = color
        setNeedsDisplay()
    }
    
    func setPath(path: [(Int, Int)]) {
        self.path = path
        setNeedsDisplay()
    }
    
    func setSelectedIndex(index: Int) {
        selectedIndex = index
        setNeedsDisplay()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        //setNeedsDisplay()
    }
    
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        
        let strokeWidth = bounds.width / 20
        let highlightStrokeWidth = strokeWidth * 1.0 // Độ dày lớn hơn cho đoạn được chọn
        let maxStrokeWidth = highlightStrokeWidth // Độ dày lớn nhất cần tính padding
        
        // Thêm padding để chứa độ dày, chia đều cho 2 bên
        let padding = maxStrokeWidth / 2
        let availableWidth = bounds.width - maxStrokeWidth
        let availableHeight = bounds.height - maxStrokeWidth
        cellSize = min(availableWidth, availableHeight) / CGFloat(PathView.GRID_SIZE)
        
        guard !path.isEmpty else { return }
        
        // Vẽ từng đoạn thẳng với offset để tránh cắt mép
        for i in 0..<path.count - 1 {
            let start = path[i]
            let end = path[i + 1]
            
            let startX = padding + CGFloat(start.1) * cellSize
            let startY = padding + CGFloat(start.0) * cellSize
            let endX = padding + CGFloat(end.1) * cellSize
            let endY = padding + CGFloat(end.0) * cellSize
            
            let linePath = UIBezierPath()
            linePath.move(to: CGPoint(x: startX, y: startY))
            linePath.addLine(to: CGPoint(x: endX, y: endY))
            
            // Chọn màu và độ dày dựa trên selectedIndex
            if i == selectedIndex {
                selectedPaint.setStroke()
                linePath.lineWidth = highlightStrokeWidth
            } else {
                pathPaint.setStroke()
                linePath.lineWidth = strokeWidth
            }
            
            linePath.lineCapStyle = .round
            linePath.lineJoinStyle = .round
            linePath.stroke()
        }
    }
}
