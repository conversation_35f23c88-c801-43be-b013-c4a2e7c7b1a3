//
//  toancoban_list_giohoatdong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_giohoatdong
class toancoban_list_giohoatdong: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var leftClockDigital: ClockDigital!
    private var coinView: UIView!
    private var activityId = 0
    private var deltaHour = 0
    private var meIndex = 0
    private var targetHour = 0
    private var hours: [Int] = []

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")

        leftClockDigital = ClockDigital()
        leftClockDigital.stringTag = "left_clock_digital"
        view.addSubview(leftClockDigital)
        leftClockDigital.snp.makeConstraints { make in
            make.centerX.equalToSuperview().multipliedBy(0.6)
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalTo(leftClockDigital.snp.width)
        }

        gridLayout = MyGridView()
        gridLayout.stringTag = "grid_layout"
        gridLayout.backgroundColor = UIColor(hex: "#D6FAFF")
        gridLayout.itemRatio = 1.0
        gridLayout.columns = 2
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.4)
        }

        coinView = UIView()
        coinView.stringTag = "coin_view"
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        activityId = Int.random(in: 0..<4)
        targetHour = activityId == 0 ? [10, 11, 12].randomElement()! :
                     activityId == 1 ? [8, 9, 10, 1].randomElement()! :
                     activityId == 2 ? [6, 7, 12, 1].randomElement()! :
                     [11, 12, 4, 5].randomElement()!
        if targetHour < 0 { targetHour += 12 }
        if targetHour >= 12 { targetHour -= 12 }
        deltaHour = activityId == 0 ? [1, 2, 3].randomElement()! :
                    activityId == 1 ? [-1, -2, -3].randomElement()! :
                    activityId == 2 ? [-1, -2, -3].randomElement()! :
                    [1, 2, 3].randomElement()!
        var hour = targetHour + deltaHour
        if hour < 0 { hour += 12 }
        if hour >= 12 { hour -= 12 }
        leftClockDigital.setTime(hour: hour, minute: 0)

        while true {
            hours = Array(0..<12).shuffled().prefix(4).map { $0 }
            if let index = hours.firstIndex(of: targetHour) {
                meIndex = index
                break
            }
        }
        var views : [UIView] = []
        for hour in hours {
            let button = KUButton()
            let clock = ClockDigital()
            clock.isUserInteractionEnabled = false
            clock.setTime(hour: hour, minute: 0)
            button.addSubviewWithInset(subview: clock, inset: 0)
            button.alpha = 0.1
            let tap = UITapGestureRecognizer(target: self, action: #selector(handleClockTap(_:)))
            button.addGestureRecognizer(tap)
            views.append(button)
        }
        gridLayout.reloadItemViews(views: views)
        gridLayout.alpha = 0
    }

    override func createGame() {
        super.createGame()
        leftClockDigital.moveToCenter(of: self, duration: 0)
        let sound: [String]
        switch activityId {
        case 0: sound = ["toan/toan_gio hoat dong1", getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong2"]
        case 1: sound = [getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong3"]
        case 2: sound = [getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong4"]
        default: sound = ["toan/toan_gio hoat dong5", getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong6"]
        }
        var delay = playSound([openGameSound()] + sound)
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.leftClockDigital.transform = .identity
        }
        delay += 0.5
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.gridLayout.alpha = 1
                self.leftClockDigital.transform = .identity
            }
        }
        delay += gridLayout.showItems(startDelay: delay)
        delay += 0.5
        scheduler.schedule(delay: delay) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let sound: [String]
            switch activityId {
            case 0: sound = ["toan/toan_gio hoat dong1", getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong2"]
            case 1: sound = [getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong3"]
            case 2: sound = [getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong4"]
            default: sound = ["toan/toan_gio hoat dong5", getNumberSound(abs(deltaHour)), "toan/toan_gio hoat dong6"]
            }
            let delay = playSound(sound)
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }

    // MARK: - Actions
    @objc private func handleClockTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = gridLayout.subviews.firstIndex(of: view as! KUButton) else { return }
        pauseGame()
        if index == meIndex {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound(), getNumberSound(targetHour == 0 ? 12 : targetHour), "toan/giờ", endGameSound())
            scheduler.schedule(delay: Double(delay)) {
                self.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}

