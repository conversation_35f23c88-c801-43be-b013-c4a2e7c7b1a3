<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout

        android:id="@+id/layout_content"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/bg_pack1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="789:521"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/text_package_title"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMinTextSize="5dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="15 tháng"
            android:textColor="@color/blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2"
            app:layout_constraintWidth_percent="0.55" />

        <TextView
            android:id="@+id/text_package_price_old"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMinTextSize="5dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/utm_avo"
            android:gravity="center"
            android:text="490.000đ"
            android:textColor="@color/blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.4"
            app:layout_constraintWidth_percent="0.7" />

        <TextView
            android:id="@+id/text_package_price"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMinTextSize="5dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/utm_avo_bold"
            android:gravity="center"
            android:text="190.000đ"
            android:textColor="@color/blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.6"
            app:layout_constraintWidth_percent="0.7" />

        <TextView
            android:id="@+id/text_package_price_month"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMinTextSize="5dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/utm_avo_bold"
            android:gravity="center"
            android:text="250.000đ/tháng"
            android:textColor="@color/blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.17"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8"
            app:layout_constraintWidth_percent="0.8" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>