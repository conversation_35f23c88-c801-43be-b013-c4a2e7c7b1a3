//
//  phonics_list_sound2.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 04/08/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_sound2: GameFragment {
    var textName = AutosizeLabel()
    var textName2 = AutosizeLabel()
    var textName3 = UILabel()
    var leftContainer = UIView()
    var leftButton = SVGButton(SVGIcon: "bot btn replay")
    var rightContainer = UIView()
    var rightButton = SVGButton(SVGIcon: "bot btn next")
    var thumbImage = UIImageView()
    var thumbContainerImage = SVGImageView(SVGName: "bg obj white")
    var hasThumb = false
    override func configureLayout(_ view: UIView) {
        clipsToBounds = true
        backgroundColor = .color(hex: "#7CD2FF")
        textName.textColor = .color(hex: "#1497E0")
        self.addSubview(textName)
        textName2.textColor = .color(hex: "#DCFDFF")
        self.addSubview(textName2)
        self.addSubview(textName3)
        self.addSubview(thumbContainerImage)
        thumbContainerImage.addSubview(thumbImage)
        thumbContainerImage.contentMode = .scaleAspectFit
        thumbImage.contentMode = .scaleAspectFit
        thumbContainerImage.isHidden = true
        do {
            let name = "flashcards/vocab/\(game.value1!).svg"
            let uri = Utilities.SVGURL(of: name)
            if let uri = uri {
                let thumb = SVGKImage(contentsOf: uri)
                thumbImage.image = thumb?.uiImage
                hasThumb = true
                thumbContainerImage.isHidden = false
            } else {
                thumbContainerImage.isHidden = true
            }
        } catch {
            
        }
        textName3.font = .Freude(size: Utilities.isIPad ? 30 : 20)
        textName3.textColor = .color(hex: "#C7FFFF")
        textName3.textAlignment = .center
        textName3.numberOfLines = 0
        addSubview(leftContainer)
        addSubview(rightContainer)
        let leftBackground = SVGImageView(SVGName: "bot bg left")
        let rightBackground = SVGImageView(SVGName: "bot bg right")
        leftContainer.addSubview(leftBackground)
        rightContainer.addSubview(rightBackground)
        leftContainer.addSubview(leftButton)
        rightContainer.addSubview(rightButton)
        leftContainer.snp.makeConstraints{ make in
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        rightContainer.snp.makeConstraints{ make in
            make.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        leftBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.1)
            make.width.equalTo(leftBackground.snp.height).multipliedBy(628.0/401.0)
        }
        rightBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.85)
            make.width.equalTo(rightBackground.snp.height).multipliedBy(628.0/401.0)
        }
        leftButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        leftButton.addTarget(self, action: #selector(replay), for: .touchUpInside)
        rightButton.addTarget(self, action: #selector(loadNext), for: .touchUpInside)
    
        textName.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            if hasThumb {
                make.top.equalToSuperview{$0.snp.centerY}
            } else {
                make.bottom.equalToSuperview{$0.snp.centerY}
            }
            make.height.equalToSuperview().multipliedBy(0.1)
        }
        textName2.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.top.equalTo(textName.snp.bottom)
            make.height.equalToSuperview().multipliedBy(0.1)
        }
        textName3.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.top.equalTo(textName2.snp.bottom).offset(30)
        }
        let thumbPadding = Utilities.isIPad ? 50 : 30
        thumbContainerImage.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(self.snp.centerY).offset(-thumbPadding)
            make.top.equalToSuperview().offset(thumbPadding)
            make.left.right.equalToSuperview().inset(thumbPadding)
        }
        thumbImage.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
    }
    override func createGame() {
        super.createGame()
        leftContainer.alpha = 0
        rightContainer.alpha = 0
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            self.leftContainer.alpha = 1
            self.rightContainer.alpha = 1
        })
        hideMenu()
        reload()
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening,.GameReading]
    }
    override func parseIntroText()->[String]{
        if game.text == nil {
            return []
        }
        return game.text!.split(separator: "#").map{ game.path != nil && game.path != "" ? "\(game.path!)/\($0.string)" : "sentence/\($0.string)"}
    }
    @objc func loadNext(){
        finishGame()
    }
    @objc func replay(){
        hideMenu()
        reload()
    }
    func reload(){
        textName.numberOfLines = 1
        textName2.numberOfLines = 1
        textName.text = game.value1
        textName.alpha = 0
        textName2.text = game.value2
        textName2.alpha = 0
        textName3.text = game.value3 ?? ""
        textName3.alpha = 0
        thumbContainerImage.alpha = 0
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [0,1]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textName.transform = CGAffineTransformMakeScale(finalValue, finalValue)
            self?.textName.alpha = value
            self?.textName2.transform = CGAffineTransformMakeScale(finalValue, finalValue)
            self?.textName2.alpha = value
            self?.textName3.alpha = value
            self?.thumbContainerImage.transform = CGAffineTransformMakeScale(finalValue, finalValue)
            self?.thumbContainerImage.alpha = value
        })
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            timeChange.animate(1, duration: 0.5){}
        })
        self.playSound(name: "effects/slide1", delay: 0.7)
        var delay = 1.5
        delay += self.playSound(delay: delay, names: self.parseIntroText())
        delay += 1
        #if DEBUG
        //delay = 1
        #endif
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.showMenu()
        })
    }
    func hideMenu(){
        leftContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -leftContainer.bounds.width * 1.2 - 20.0]
        let animValues2: [Double] = [0, rightContainer.bounds.width * 1.2 + 20.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showMenu(){
        startGame()
        leftContainer.transform = CGAffineTransformMakeTranslation(-leftContainer.bounds.width, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(rightContainer.bounds.width, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [-leftContainer.bounds.width, 0]
        let animValues2: [Double] = [rightContainer.bounds.width, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
}
