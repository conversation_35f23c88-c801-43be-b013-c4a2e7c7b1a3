//
//  mythuat_list_tranhtomau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/6/25.
//

import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics

// MARK: - MyPenItem
class MyPenItem: Hashable {
    let color: String
    let svg: SVGKImage
    let id = UUID()

    init(color: String, svg: SVGKImage) {
        self.color = color
        self.svg = svg
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: MyPenItem, rhs: MyPenItem) -> Bool {
        lhs.id == rhs.id
    }
}

// MARK: - ColoringGameFragment
class mythuat_list_tranhtomau: NhanBietGameFragment {
    // MARK: - Properties
    private let noColor: [UIColor] = [UIColor(hex: "#000000"), UIColor(hex: "#00000000")]
    private var answerCount: Int = 0
    private var originalSVG: SVGKImage?
    var filename: String?
    private var fillMap: [String: Double] = [:]
    private var svg: SVGKImage?
    private var selectedColorView: UIImageView?
    private var selectedColorIndex: Int = 0
    private var selectedPenImage: SVGImageView?
    private var pathColors: [CAShapeLayer: UIColor] = [:]
    private var selectedColor: UIColor = UIColor.clear
    private var colors: [UIColor] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#F4FAFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let penRecyclerView = UICollectionView(frame: .zero, collectionViewLayout: UICollectionViewFlowLayout())
        penRecyclerView.stringTag = "pen_recycler_view"
        penRecyclerView.backgroundColor = UIColor.black.withAlphaComponent(0.12)
        view.addSubview(penRecyclerView)
        penRecyclerView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.15)
            make.left.equalToSuperview().offset(0.2 * view.frame.height)
            make.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        let scrollView = UIScrollView()
        scrollView.stringTag = "scroll_view"
        scrollView.minimumZoomScale = 1.0
        scrollView.maximumZoomScale = 5.0
        scrollView.showsHorizontalScrollIndicator = true
        scrollView.showsVerticalScrollIndicator = true
        scrollView.delegate = self
        scrollView.contentInsetAdjustmentBehavior = .never
        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.top.equalTo(penRecyclerView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-0.15 * view.frame.height)
        }
        
        let contentView = UIView()
        scrollView.addSubview(contentView)
        contentView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(400).priority(.high)
            make.width.height.greaterThanOrEqualTo(scrollView)
        }
        
        let svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view"
        contentView.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
        
        
        let svgPreview = SVGImageView(frame: .zero)
        svgPreview.stringTag = "svg_preview"
        svgPreview.isHidden = true
        view.addSubview(svgPreview)
        svgPreview.snp.makeConstraints { make in
            make.width.height.equalTo(100)
            make.top.left.equalToSuperview().inset(10)
        }
        
        let penContainer = UIStackView()
        penContainer.stringTag = "pen_container"
        penContainer.isHidden = true
        penContainer.axis = .horizontal
        penContainer.distribution = .fillEqually
        penContainer.spacing = 0
        view.addSubview(penContainer)
        penContainer.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(penContainer.snp.width).dividedBy(3)
            make.width.lessThanOrEqualTo(500)
        }
        
        let penView = UIView()
        penView.stringTag = "pen_view"
        penView.isHidden = true
        view.addSubview(penView)
        penView.snp.makeConstraints { make in
            make.width.height.equalTo(60)
            make.top.right.equalToSuperview().inset(20)
        }
        
        let coloringSelected = UIImageView(image: UIImage(named: "coloring_selected"))
        coloringSelected.contentMode = .scaleAspectFit
        penView.addSubview(coloringSelected)
        coloringSelected.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let penImage = UIImageView(image: UIImage(named: "color_selected"))
        penImage.stringTag = "pen_image"
        penImage.tintColor = UIColor(hex: "#FFFFFF")
        penView.addSubview(penImage)
        penImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let colorGrid = UIView()
        colorGrid.stringTag = "color_grỉd"
        colorGrid.backgroundColor = UIColor(hex: "#E2EEFE")
        colorGrid.isHidden = true
        view.addSubview(colorGrid)
        colorGrid.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let squareGrid = UIStackView()
        squareGrid.stringTag = "square_grid"
        squareGrid.backgroundColor = UIColor(hex: "#FFFFFF")
        squareGrid.axis = .vertical
        squareGrid.distribution = .fillEqually
        squareGrid.spacing = 2
        squareGrid.layer.cornerRadius = 12
        colorGrid.addSubview(squareGrid)
        squareGrid.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(squareGrid.snp.width)
            make.width.lessThanOrEqualTo(500)
            make.edges.equalToSuperview().inset(20)
        }
        
        let colorRows: [UIColor] = [
            UIColor(hex: "#BE1E2D"), UIColor(hex: "#FF3B36"), UIColor(hex: "#FF6C1A"), UIColor(hex: "#F9FF10"), UIColor(hex: "#BAFF22"),
            UIColor(hex: "#2DEA36"), UIColor(hex: "#28AD34"), UIColor(hex: "#0ABCAA"), UIColor(hex: "#2EEDD6"), UIColor(hex: "#68C1FF"),
            UIColor(hex: "#262262"), UIColor(hex: "#1B75BC"), UIColor(hex: "#1B75BC"), UIColor(hex: "#27AAE1"), UIColor(hex: "#849BFE"),
            UIColor(hex: "#8B42CC"), UIColor(hex: "#C431C4"), UIColor(hex: "#F748B8"), UIColor(hex: "#FFCA85"), UIColor(hex: "#E5A361"),
            UIColor(hex: "#754C29"), UIColor(hex: "#BCBEC0"), UIColor(hex: "#6D6E71"), UIColor(hex: "#2D2D2D"), UIColor(hex: "#141414")
        ]
        
        for row in 0..<5 {
            let rowStack = UIStackView()
            rowStack.axis = .horizontal
            rowStack.distribution = .fillEqually
            rowStack.spacing = 2
            squareGrid.addArrangedSubview(rowStack)
            rowStack.snp.makeConstraints { make in
                make.edges.equalToSuperview().inset(UIEdgeInsets(top: 20, left: 20, bottom: 20, right: 20))
            }
            
            for col in 0..<5 {
                let index = row * 5 + col
                let colorView = UIImageView(image: UIImage(named: "item_circle"))
                colorView.tintColor = colorRows[index]
                colorView.contentMode = .scaleAspectFit
                rowStack.addArrangedSubview(colorView)
                colorView.snp.makeConstraints { make in
                    make.height.equalToSuperview().offset(-6)
                }
                
                colorView.isUserInteractionEnabled = true
                colorView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(colorTapped(_:))))
            }
        }
        
        // Setup touch handling for svgView
        svgView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(svgViewTapped(_:))))
        
        // Setup click handling for penView and colorGrid
        penView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(penViewTapped)))
        colorGrid.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(colorGridTapped)))
        
        // Initialize SVG and fillMap
        guard let filename = filename else { return }
        let preferences = UserDefaults.standard
        if let fillMapData = preferences.string(forKey: "editing_\(DataManager.shared.currentProfile?.id ?? "default")_\(filename)") {
            fillMap = try! JSONDecoder().decode([String: Double].self, from: fillMapData.data(using: .utf8)!)
        }
        if fillMap.isEmpty { fillMap = [:] }
        self.svg = Utilities.GetSVGKImage(named: "pencil2")
        self.originalSVG = Utilities.GetSVGKImage(named: "coloring/\(filename).svg")
        let svgImage = originalSVG
        
        self.colors = []
        guard let layers = svg?.caLayerTree.sublayers else { return }
        for (i, layer) in layers.enumerated() {
            if let path = layer as? CAShapeLayer, let fill = path.fillColor?.uiColor {
                if !self.noColor.contains(fill) {
                    if !self.colors.contains(fill) {
                        self.colors.append(fill)
                    }
                    self.pathColors[path] = fill
                    let fillColor = self.fillMap["\(i)"] != nil ? UIColor(rgb: Int(self.fillMap["\(i)"]!)) : UIColor(hex: "#FAFAFA")
                    path.fillColor = fillColor.cgColor
                }
            }
        }
        svgView.image = svgImage?.uiImage
        svgPreview.image = self.originalSVG?.uiImage
        
        let penContainerView = view.viewWithStringTag("pen_container") as! UIStackView
        penContainerView.snp.remakeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(penContainerView.snp.width).dividedBy(max(Double(self.colors.count) * 1.2, 5.0))
            if self.colors.count == 2 {
                make.height.equalTo(penContainerView.snp.width).dividedBy(3)
            }
            make.width.lessThanOrEqualTo(max(500, self.colors.count * 100 + 200))
        }
        self.loadPens(view)
        
        scheduler.schedule(delay: 0.2) {
            self.playSound("effects/games/coloring")
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }
    
    // MARK: - Helper Methods
    private func loadPens(_ view: UIView) {
        guard let svg = svg else {
            scheduler.schedule(delay: 0.1) { [weak self] in
                self?.loadPens(view)
            }
            return
        }
        
        let penRecyclerView = view.viewWithStringTag("pen_recycler_view") as! UICollectionView
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        penRecyclerView.collectionViewLayout = layout
        
        let items = ColorManager.shared.selectedColors.map { color in
            MyPenItem(color: color, svg: svg)
        }
                
        penRecyclerView.register(PenCell.self, forCellWithReuseIdentifier: "PenCell")
        //penRecyclerView.dataSource = self
        //penRecyclerView.delegate = self
    }
    
    @objc private func svgViewTapped(_ gesture: UITapGestureRecognizer) {
        guard gesture.numberOfTouches == 1, selectedColor != .clear//, let svg = (gesture.view as? SVGImageView)?.svg
        else {
            playSound("effects/fail")
            return
        }
        
        let location = gesture.location(in: gesture.view)
        let size = svg?.size ?? CGSize(width: 1, height: 1)
        let isHeight = size.height / size.width > gesture.view!.frame.height / gesture.view!.frame.width
        var unitX = location.x / gesture.view!.frame.width
        var unitY = location.y / gesture.view!.frame.height
        
        if isHeight {
            let w = size.width / size.height * gesture.view!.frame.height
            let deltaX = (gesture.view!.frame.width - w) / 2
            unitX = (location.x - deltaX) / w
        } else {
            let h = size.height / size.width * gesture.view!.frame.width
            let deltaY = (gesture.view!.frame.height - h) / 2
            unitY = (location.y - deltaY) / h
        }
        
        guard let layers = svg?.caLayerTree.sublayers else { return }
        for i in stride(from: layers.count - 1, through: 0, by: -1) {
            if let path = layers[i] as? CAShapeLayer, path.opacity >= 0.5, !noColor.contains(path.fillColor?.uiColor ?? .black) {
                if path.contains(CGPoint(x: unitX * size.width, y: unitY * size.height)) {
                    fillMap["\(i)"] = Double(selectedColor.rgb)
                    path.fillColor = selectedColor.cgColor
                    (gesture.view as? SVGImageView)?.image = svg?.uiImage
                    playSound("effects/true2")
                    break
                }
            }
        }
    }
    
    @objc private func penViewTapped() {
        playSound("effects/button")
        if let squareGrid = viewWithStringTag("square_grid") as? UIStackView {
            squareGrid.arrangedSubviews.forEach { row in
                (row as? UIStackView)?.arrangedSubviews.forEach { imageView in
                    if let imageView = imageView as? UIImageView, imageView.tintColor == selectedColor {
                        imageView.image = UIImage(named: "selected_color")
                        selectedColorView = imageView
                    }
                }
            }
        }
        viewWithStringTag("color_grỉd")?.isHidden = false
    }
    
    @objc private func colorGridTapped() {
        viewWithStringTag("color_grỉd")?.isHidden = true
    }
    
    @objc private func colorTapped(_ gesture: UITapGestureRecognizer) {
        guard let colorView = gesture.view as? UIImageView, let defaultColor = colorView.tintColor else { return }
        selectedColor = defaultColor
        selectedColorView?.image = UIImage(named: "item_circle")
        viewWithStringTag("color_grỉd")?.isHidden = true
        playSound("effects/button")
        updatePenImage()
    }
    
    private func updatePenImage() {
        if let penImage = viewWithStringTag("pen_image") as? UIImageView {
            penImage.tintColor = selectedColor
        }
    }
    
    override func removeFromSuperview() {
        super.removeFromSuperview()
        guard let filename = filename else { return }
        let preferences = UserDefaults.standard
        if let fillMapData = try? JSONEncoder().encode(fillMap) {
            preferences.set(String(data: fillMapData, encoding: .utf8), forKey: "editing_\(DataManager.shared.currentProfile?.id ?? "default")_\(filename)")
        }
    }
    /*
    override func pause() {
        super.pause()
    }*/
    
    private func resetPhoto() {
        let dialog = MessageDialogView()
            .setTitle("Reset colors")
            .setMessage("Do you want to reset all colors?")
            .setImageResId("icon_notce")
            .setListener { [weak self] in
                guard let self = self, let filename = self.filename else { return }
                let svg = Utilities.GetSVGKImage(named: "coloring/\(filename)")
                self.fillMap = [:]
                self.originalSVG = svg//?.deepClone()
                self.colors = []
                guard let layers = svg.caLayerTree.sublayers else { return }
                for (i, layer) in layers.enumerated() {
                    if let path = layer as? CAShapeLayer, let fill = path.fillColor?.uiColor {
                        if !self.noColor.contains(fill) {
                            if !self.colors.contains(fill) {
                                self.colors.append(fill)
                            }
                            self.pathColors[path] = fill
                            let fillColor = self.fillMap["\(i)"] != nil ? UIColor(rgb: Int(self.fillMap["\(i)"]!)) : UIColor(hex: "#FAFAFA")
                            path.fillColor = fillColor.cgColor
                        }
                    }
                }
                if let svgView = self.viewWithStringTag("svg_view") as? SVGImageView {
                    svgView.image = svg.uiImage
                }
                if let svgPreview = self.viewWithStringTag("svg_preview") as? SVGImageView {
                    svgPreview.image = self.originalSVG?.uiImage
                }
                if let penContainerView = self.viewWithStringTag("pen_container") as? UIStackView {
                    penContainerView.snp.remakeConstraints { make in
                        make.bottom.left.right.equalToSuperview()
                        make.height.equalTo(penContainerView.snp.width).dividedBy(max(Double(self.colors.count) * 1.2, 5.0))
                        if self.colors.count == 2 {
                            make.height.equalTo(penContainerView.snp.width).dividedBy(3)
                        }
                        make.width.lessThanOrEqualTo(max(500, self.colors.count * 100 + 200))
                    }
                }
                self.loadPens(self)
            }
        dialog.showIn(self)
    }
}

// MARK: - UIScrollViewDelegate
extension mythuat_list_tranhtomau: UIScrollViewDelegate {
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return scrollView.subviews.first
    }
    func scrollViewDidZoom(_ scrollView: UIScrollView) {
        let contentView = scrollView.subviews.first!
        let scrollViewSize = scrollView.bounds.size
        let contentViewSize = contentView.frame.size
        
        //scrollView.contentSize = CGSize(width: contentViewSize.width, height: contentViewSize.height)
    }
    
    func scrollViewDidEndZooming(_ scrollView: UIScrollView, with view: UIView?, atScale scale: CGFloat) {
        let contentView = scrollView.subviews.first!
        let scrollViewSize = scrollView.bounds.size
        let contentViewSize = contentView.frame.size
        
        //scrollView.contentSize = CGSize(width: contentViewSize.width, height: contentViewSize.height)
    }
}

// MARK: - PenCell
class PenCell: UICollectionViewCell {
    private let penImage = SVGImageView(frame: .zero)
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        contentView.addSubview(penImage)
        penImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    func configure(with item: MyPenItem, isSelected: Bool, selectedPenImage: inout SVGImageView?) {
        penImage.image = item.svg.uiImage
        if isSelected {
            UIView.animate(withDuration: 0.1) {
                self.penImage.transform = CGAffineTransform(translationX: 0, y: -10)
            }
            selectedPenImage = penImage
        } else {
            UIView.animate(withDuration: 0.1) {
                self.penImage.transform = CGAffineTransform(translationX: 0, y: -self.penImage.frame.height / 3)
            }
        }
    }
}

extension UIColor {
    var rgb: Int {
        var red: CGFloat = 0
        var green: CGFloat = 0
        var blue: CGFloat = 0
        var alpha: CGFloat = 0
        getRed(&red, green: &green, blue: &blue, alpha: &alpha)
        return (Int(red * 255) << 16) | (Int(green * 255) << 8) | Int(blue * 255)
    }
}
