//
//  taptrung_list_xoayhinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_xoayhinh: nhanbiet_list_xoayhinh {
    // MARK: - Game Logic
    override func updateData() {
        let folder = FlashcardsManager.shared.getPacks()
            .filter { $0.recognize == true }
            .shuffled()
            .first!
        let items = folder.items
            .shuffled()
            .prefix(3)
            .map { $0 }
        setFolder(folder.folder)
        setItem(items.randomElement()!)
        setListItems(items)
        super.updateData()
    }
}
