//
//  tuduy_list_mattrang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 9/4/25.
//


import UIKit
import SnapKit

class tuduy_list_mattrang: NhanBietGameFragment {
    // MARK: - Properties
    private var items: [Int] = []
    private var gridLayout: MyGridView!
    private var meIndex: Int = 0
    private var imageView: UIImageView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let backgroundImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_space"))
        backgroundImage.contentMode = .scaleAspectFill
        addSubview(backgroundImage)
        backgroundImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 162/255, green: 133/255, blue: 210/255, alpha: 0.52) // #DDA285D2
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.5)
        }
        
        let rightView = UIView()
        addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        rightView.addSubviewWithPercentInset(subview: imageView, percentInset: 10)
        
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        imageView.transform = CGAffineTransform(translationX: 0, y: 0)
        gridLayout.alpha = 0
        
        items = [0, 1, 2, 3].shuffled()
        meIndex = Int.random(in: 0..<4)
        imageView.image = Utilities.SVGImage(named: "toan_mattrang\(1 + items[meIndex])")
        imageView.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0..<360) * .pi / 180)
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let item = createItemMatTrang(index: i)
            item.tag = i
            item.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
            item.addGestureRecognizer(tapGesture)
            //AnimationUtils.setTouchEffect(view: item)
            views.append(item)
            //item.transform = CGAffineTransform(scaleX: 0, y: 0)
            //item.alpha = 0
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.columns = 2
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "toan/toan_mat trang")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.imageView.transform = .identity
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
        }
        let gridDelay = delay + 1.0 + gridLayout.showItems(startDelay: delay + 1.0)
        scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_mat trang")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int,
              let imageThumbnail = view.subviews.first?.subviews.first as? UIImageView else { return }
        
        pauseGame()
        if index == meIndex {
            UIView.animate(withDuration: 0.5, delay: 0.5, options: .curveEaseInOut) {
                imageThumbnail.transform = CGAffineTransform(rotationAngle: self.imageView.rotationAngle)
            } completion: { _ in
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    imageThumbnail.image = Utilities.SVGImage(named: "toan_mattrang\(1 + self.items[index])")
                }
            }
            animateCoinIfCorrect(view: imageThumbnail)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemMatTrang(index: Int) -> UIView {
        let container = UIView()
        
        let innerContainer = UIImageView()
        innerContainer.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(innerContainer.snp.height) // Ratio 1:1
        }
        
        let imageThumbnail = UIImageView(image: Utilities.SVGImage(named: "toan_mattrang\(1 + items[index])_2"))
        imageThumbnail.contentMode = .scaleAspectFit
        imageThumbnail.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0..<360) * .pi / 180)
        innerContainer.addSubview(imageThumbnail)
        imageThumbnail.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.85)
            make.height.equalTo(imageThumbnail.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return container
    }
}

// MARK: - Supporting Structures

extension UIImageView {
    var rotationAngle: CGFloat {
        let transform = self.transform
        return atan2(transform.b, transform.a)
    }
}
