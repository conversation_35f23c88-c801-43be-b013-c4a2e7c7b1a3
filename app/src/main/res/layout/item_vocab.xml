<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/vocab_bg_list_topic"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="737:559"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.caverock.androidsvg.SVGImageView
            android:id="@+id/image_thumb"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="+"
            android:textColor="#E5630A"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.94"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.85"
            app:layout_constraintDimensionRatio="1" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/tv_folder_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center_vertical"
            android:text="Bảng chữ cái"
            android:textColor="#FFF"
            app:heightRatio="0.2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintHorizontal_bias="0.15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1"
            app:layout_constraintWidth_percent="0.6" />

        <TextView
            android:id="@+id/tv_progress"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="12/20"
            android:textColor="#849BFE"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="3"
            app:layout_constraintHeight_percent="0.11"
            app:layout_constraintHorizontal_bias="0.93"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.07" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>