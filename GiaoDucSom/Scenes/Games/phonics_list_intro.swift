//
//  phonics_list_intro.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 2/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import AnyCodable

class phonics_list_intro: GameFragment {
    // MARK: - Properties
    private var textName: HeightRatioTextView!
    private var animationView: XAMLAnimationView! // Placeholder cho XamlAnimationView
    private var svgView: SVGImageView!
    private var leftLine: UIView!
    private var animationContainer: UIView!
    private var teacherMouth: UIImageView!
    private var btnNext: UIImageView!
    private var btnReplay: UIImageView!
    private var viewLeft: UIView!
    private var viewRight: UIView!
    private var words: [CsvWord] = []
    
    // MARK: - Word Model
    struct Word {
        var text: String
        var start: Double
        var duration: Double
    }
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#849BFE")
        
        textName = HeightRatioTextView()
        textName.setHeightRatio(0.8)
        textName.textAlignment = .center
        textName.textColor = .white
        textName.font = .Freude(size: 24)
        view.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.6)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        svgView.accessibilityIdentifier = "svg_view"
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.6)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        animationContainer = UIView()
        animationContainer.accessibilityIdentifier = "animationContainer"
        animationContainer.clipsToBounds = false
        view.addSubview(animationContainer)
        animationContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(animationContainer.snp.height).multipliedBy(0.5) // Ratio 0.5
            make.left.equalToSuperview()
            make.bottom.equalTo(self)
        }
        
        animationView = XAMLAnimationView() // Placeholder cho XamlAnimationView
        animationContainer.addSubview(animationView)
        animationView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.height.equalTo(animationView.snp.width) // Ratio 1:1
        }
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "teacher")!)
            animationView.loadView(from: xamlData)
        } catch {}
        
        teacherMouth = UIImageView(image: Utilities.SVGImage(named: "teacher_silent"))
        animationContainer.addSubview(teacherMouth)
        teacherMouth.snp.makeConstraints { make in
            make.edges.equalTo(animationView)
        }
        
        viewLeft = UIView()
        viewLeft.clipsToBounds = false
        viewLeft.transform = CGAffineTransform(translationX: -100, y: 0)
        self.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            make.width.height.equalTo(100)
            make.left.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
        
        let leftBg = UIImageView(image: Utilities.SVGImage(named: "bot_bg_left"))
        leftBg.contentMode = .scaleAspectFit
        viewLeft.addSubview(leftBg)
        leftBg.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.height.equalTo(leftBg.snp.width).multipliedBy(401.0 / 628.0) // Ratio 628:401
        }
        
        btnReplay = UIImageView(image: Utilities.SVGImage(named: "bot_btn_replay"))
        btnReplay.contentMode = .scaleAspectFit
        btnReplay.isUserInteractionEnabled = true
        viewLeft.addSubview(btnReplay)
        btnReplay.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.height.equalTo(btnReplay.snp.width)
        }
        
        viewRight = UIView()
        viewRight.clipsToBounds = false
        viewRight.transform = CGAffineTransform(translationX: 100, y: 0)
        self.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            make.width.height.equalTo(100)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
        
        let rightBg = UIImageView(image: Utilities.SVGImage(named: "bot_bg_right"))
        rightBg.contentMode = .scaleAspectFit
        viewRight.addSubview(rightBg)
        rightBg.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.height.equalTo(rightBg.snp.width).multipliedBy(401.0 / 628.0) // Ratio 628:401
        }
        
        btnNext = UIImageView(image: Utilities.SVGImage(named: "bot_btn_next"))
        btnNext.contentMode = .scaleAspectFit
        btnNext.isUserInteractionEnabled = true
        viewRight.addSubview(btnNext)
        btnNext.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.height.equalTo(btnNext.snp.width) // Ratio 1:1
        }
        
        let tapNext = UITapGestureRecognizer(target: self, action: #selector(onNextTapped))
        btnNext.addGestureRecognizer(tapNext)
        let tapReplay = UITapGestureRecognizer(target: self, action: #selector(onReplayTapped))
        btnReplay.addGestureRecognizer(tapReplay)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        if data?.value_svg == nil && data?.value1 == nil {
            animationContainer.snp.remakeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.8)
                make.width.equalTo(animationContainer.snp.height).multipliedBy(0.5) // Ratio 0.5
                make.centerX.equalToSuperview()
                make.bottom.equalTo(self)
            }
            textName.isHidden = true
            svgView.isHidden = true
        } else {
            animationContainer.snp.remakeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.8)
                make.width.equalTo(animationContainer.snp.height).multipliedBy(0.5) // Ratio 0.5
                make.left.equalToSuperview()
                make.bottom.equalTo(self)
            }
            if let value1 = data?.value1 {
                textName.text = value1
            }
            if let valueSvg = data?.value_svg {
                svgView.image = Utilities.GetSVGKImage(named: "intro/\(valueSvg).svg").uiImage
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        scheduler.schedule(after: 0.1) { [weak self] in
            self?.animationView.startAnimation() // Placeholder cho XamlAnimationView
        }
        scheduler.schedule(after: 1.5) { [weak self] in
            self?.startIntro()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            hideBottom()
            startIntro()
        }
    }
    
    // MARK: - Intro Logic
    private func startIntro() {
        pauseGame(stopMusic: false)
        
        guard let texts = parseIntroText()?.map({ $0.replacingOccurrences(of: "?", with: "") }) else { return }
        var delay: TimeInterval = 0
        
        for text in texts {
            if text == "@point" {
                scheduler.schedule(after: delay) { [weak self] in
                    self?.animationView.startAnimationStoryboard(with: "point") // Placeholder cho XamlAnimationView
                }
                continue
            }
            
            if text.lowercased() != "second" {
                words = parse("\(text)")
                if words.isEmpty {
                    words = parse("\(text.lowercased())")
                }
                
                if !words.isEmpty {
                    let letters = text.split(separator: " ").map { String($0.lowercased()) }
                    for i in 0..<words.count {
                        let word = words[i]
                        let letter = letters[i]
                        let id = getLetterId(letter: letter)
                        print(id)
                        scheduler.schedule(after: delay + word.Start) { [weak self] in
                            self?.teacherMouth.image = Utilities.SVGImage(named: id)
                        }
                        scheduler.schedule(after: delay + word.Start + word.Duration * 0.8) { [weak self] in
                            self?.teacherMouth.image = Utilities.SVGImage(named: "teacher_silent")
                        }
                    }
                } else {
                    scheduler.schedule(after: delay) { [weak self] in
                        let imageId = self?.getLetterId(letter: text.lowercased()) ?? "teacher_silent"
                        print(imageId)
                        self?.teacherMouth.image = Utilities.SVGImage(named: imageId)
                    }
                }
            }
            
            delay += playSound(delay: delay, names: [text])
            scheduler.schedule(after: delay - 0.1) { [weak self] in
                self?.teacherMouth.image = Utilities.SVGImage(named: "teacher_silent")
            }
        }
        
        delay += 1.0
        scheduler.schedule(after: delay) { [weak self] in
            self?.showBottom()
        }
    }
    
    // MARK: - Menu Control
    private func showBottom() {
        startGame()
        playSound(name: "effects/slide1")
        
        UIView.animate(withDuration: 0.5, animations: {
            self.viewLeft.transform = .identity
            self.viewRight.transform = .identity
        })
    }
    
    private func hideBottom() {
        pauseGame(stopMusic: false)
        playSound(name: "effects/slide1")
        
        UIView.animate(withDuration: 0.5, animations: {
            self.viewLeft.transform = CGAffineTransform(translationX: -100, y: 0)
            self.viewRight.transform = CGAffineTransform(translationX: 100, y: 0)
        })
    }
    
    // MARK: - Touch Handling
    @objc private func onNextTapped() {
        pauseGame(stopMusic: false)
        //animateCoinIfCorrect(view: svgView)
        scheduler.schedule(after: 1.0) { [weak self] in
            self?.finishGame()
        }
    }
    
    @objc private func onReplayTapped() {
        hideBottom()
        startIntro()
    }
    
    // MARK: - Helper Methods
    private func getLetterId(letter: String) -> String {
        if letter.starts(with: "th") { return "teacher_th" }
        if letter.starts(with: "ee") { return "teacher_ee" }
        if letter.starts(with: "ch") || letter.starts(with: "j") || letter.starts(with: "sh") { return "teacher_ch_j_sh" }
        if letter.starts(with: "a") || letter.starts(with: "e") || letter.starts(with: "i") { return "teacher_a_e_i" }
        if letter.starts(with: "b") || letter.starts(with: "m") || letter.starts(with: "p") { return "teacher_b_m_p" }
        if letter.starts(with: "c") || letter.starts(with: "d") || letter.starts(with: "g") || letter.starts(with: "k") ||
           letter.starts(with: "n") || letter.starts(with: "s") || letter.starts(with: "t") || letter.starts(with: "x") ||
           letter.starts(with: "y") || letter.starts(with: "z") || letter.starts(with: "h") { return "teacher_c_d_g_k_n_s_t_x_y_z_h" }
        if letter.starts(with: "f") || letter.starts(with: "v") { return "teacher_f_v" }
        if letter.starts(with: "l") { return "teacher_l" }
        if letter.starts(with: "o") { return "teacher_o" }
        if letter.starts(with: "q") || letter.starts(with: "w") { return "teacher_q_w" }
        if letter.starts(with: "r") { return "teacher_r" }
        if letter.starts(with: "u") { return "teacher_u" }
        return "teacher_silent"
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: "Sounds/en/english phonics/\(name)", withExtension: "csv") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for line in lines {
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    if word.Duration != 0 {
                        words.append(word)
                    }
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
    
    // MARK: - Game Skills
    override func getSkills() -> [GameSkill] {
        return [.GameListening, .GameReading]
    }
}
