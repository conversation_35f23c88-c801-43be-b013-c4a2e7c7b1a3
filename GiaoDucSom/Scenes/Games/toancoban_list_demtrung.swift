//
//  toancoban_list_demtrung.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 30/3/25.
//



import UIKit
import SnapKit

class toancoban_list_demtrung: NhanBietGameFragment, UIGestureRecognizerDelegate {

    // MARK: - Properties
    private var svgView1: UIImageView!
    private var svgView2: UIImageView!
    private var imageEggs1: UIImageView!
    private var imageEggs2: UIImageView!
    private var eggs: MyList<Int> = MyList<Int>()
    private var meIndex: Int = 0


    // MARK: - Setup Layout
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 1, green: 1, blue: 217/255, alpha: 1.0) // #FFFFD9

        // Views
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(red: 221/255, green: 1, blue: 214/255, alpha: 1.0) // #DDFFD6
        addSubview(backgroundView)

        let mainContainer = UIView()
        addSubview(mainContainer)

        let leftContainer = UIView()
        mainContainer.addSubview(leftContainer)

        let rightContainer = UIView()
        mainContainer.addSubview(rightContainer)

        svgView1 = UIImageView()
        leftContainer.addSubview(svgView1)

        imageEggs1 = UIImageView()
        imageEggs1.contentMode = .scaleAspectFit
        leftContainer.addSubview(imageEggs1)

        svgView2 = UIImageView()
        rightContainer.addSubview(svgView2)

        imageEggs2 = UIImageView()
        imageEggs2.contentMode = .scaleAspectFit
        rightContainer.addSubview(imageEggs2)

        // Add tap gestures
        svgView1.isUserInteractionEnabled = true
        svgView2.isUserInteractionEnabled = true
        let tapGesture1 = UITapGestureRecognizer(target: self, action: #selector(svgViewTapped(_:)))
        tapGesture1.delegate = self
        svgView1.isExclusiveTouch = true // Ensure only one tap gesture is recognized at a time
        svgView1.addGestureRecognizer(tapGesture1)

        let tapGesture2 = UITapGestureRecognizer(target: self, action: #selector(svgViewTapped(_:)))
        tapGesture2.delegate = self
        svgView2.isExclusiveTouch = true // Ensure only one tap gesture is recognized at a time
        svgView2.addGestureRecognizer(tapGesture2)

        //Constraints
        backgroundView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }

        mainContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        leftContainer.snp.makeConstraints { make in
            make.top.bottom.leading.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }

        rightContainer.snp.makeConstraints { make in
            make.top.bottom.trailing.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }

        svgView1.snp.makeConstraints { make in
            make.top.leading.bottom.trailing.equalToSuperview().inset(10)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.centerX.equalToSuperview().offset(mainContainer.frame.width * 0.25 )
            make.height.equalTo(svgView1.snp.width)
        }

        imageEggs1.snp.makeConstraints { make in
            make.width.height.equalTo(leftContainer.snp.width).multipliedBy(0.4)
            make.centerX.equalToSuperview().offset(-mainContainer.frame.width * 0.25)
            make.bottom.equalToSuperview().inset(10)
            make.top.equalTo(svgView1.snp.bottom)

        }

        svgView2.snp.makeConstraints { make in
            make.top.leading.bottom.trailing.equalToSuperview().inset(10)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.centerX.equalToSuperview().offset(-mainContainer.frame.width * 0.25)
            make.height.equalTo(svgView2.snp.width)

        }

        imageEggs2.snp.makeConstraints { make in
            make.width.height.equalTo(rightContainer.snp.width).multipliedBy(0.4)
            make.centerX.equalToSuperview().offset(mainContainer.frame.width * 0.25)
            make.bottom.equalToSuperview().inset(10)
            make.top.equalTo(svgView2.snp.bottom)

        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true
    }

    // MARK: - GameFragment Methods
    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        //Setup layout programmatically
        setupLayout()
    }

    override open func updateData() {
        super.updateData()

        eggs = MyList(Utils.generatePermutation(3).shuffled().prefix(2).map { $0 + 1 })

        let eggImage1Name = getEggImageName(for: eggs[0], isHen: true)
        let eggImage2Name = getEggImageName(for: eggs[1], isHen: false)
        imageEggs1.image = Utilities.SVGImage(named: eggImage1Name)
        imageEggs2.image = Utilities.SVGImage(named:  eggImage2Name)

        meIndex = Int.random(in: 0..<2)

        let delay = playSound(openGameSound(), "vi/toan/toan_3 trung", "vi/toan/toan_3 trung\(eggs[meIndex])")

        scheduler.schedule(delay: delay) {
            self.startGame()
        }
    }

    func getEggImageName(for eggNumber: Int, isHen: Bool) -> String {
        let baseName = isHen ? "math_3trung_ga_" : "math_3trung_vit_"
        switch eggNumber {
        case 1:
            return baseName + "1"
        case 2:
            return baseName + "2"
        case 3:
            return baseName + "3"
        default:
            return baseName + "1" // Provide a default image
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()

        if gameState == .playing {
            pauseGame()

            let delay = playSound("vi/toan/toan_3 trung", "vi/toan/toan_3 trung\(eggs[meIndex])")

            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }

    override func createGame() {
        super.createGame()

        svgView1.image = Utilities.SVGImage(named: "topics/Farm Animals/hen.svg")
        svgView2.image = Utilities.SVGImage(named: "topics/Farm Animals/duck.svg")
        svgView1.contentMode = .scaleAspectFit
        svgView2.contentMode = .scaleAspectFit
        svgView1.tag = 0
        svgView2.tag = 1
    }

    // MARK: - Event Handling
    @objc func svgViewTapped(_ sender: UITapGestureRecognizer) {
        guard let view = sender.view else { return }
        let index = view.tag

        let itemSound = index == 0 ? "topics/Farm Animals/hen" : "topics/Farm Animals/duck"
        pauseGame()

        if index == meIndex {
            animateCoinIfCorrect(view: view)

            let delay = playSound(itemSound, answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound())

            scheduler.schedule(delay: delay) {
                self.finishGame()
            }
        } else {
            setGameWrong()

            let delay = playSound(itemSound, answerWrongEffectSound(), getWrongHumanSound())

            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldBeRequiredToFailBy otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        return true
    }

}
