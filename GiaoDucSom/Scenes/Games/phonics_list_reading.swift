//
//  phonics_list_reading.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import FlexLayout
import PinLayout

class phonics_list_reading: GameFragment {
    
    private var colorRead = UIColor(hexString: "#032cfc")
    private var colorReading = UIColor(hexString: "#032cfc")
    private var colorNormal = UIColor(hexString: "#0394fc")
    private var colorBackground = UIColor(hexString: "#7197CF")
    private var contentLayout = UIView()
    private var topView = UIView()
    var textContainer = UIView()
    var textBottomContainer = UIView()
    private var values: [String] = []
    private var items: [String] = []
    private var step: Int = 0
    var chooseIndexes : [Int] = []
    private var dragViews : [UIView] = []
    var answer = ""
    override func configureLayout(_ view: UIView) {
        textBottomContainer.accessibilityIdentifier = "textBottomContainer"
        textContainer.accessibilityIdentifier = "textContainer"
        contentLayout.accessibilityIdentifier = "contentLayout"
        addSubview(textBottomContainer)
        addSubview(topView)
        addSubview(textContainer)
        contentLayout.alpha = 0
        textBottomContainer.alpha = 0
        topView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(textBottomContainer.snp.top)
        }
        topView.addSubview(contentLayout)
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        textContainer.flex.layout(mode: .adjustHeight)
        textContainer.pin.left().vCenter().right()
        textBottomContainer.flex.layout(mode: .adjustHeight)
        textBottomContainer.pin.left().bottom(bounds.height/6).right()
        contentLayout.flex.layout(mode: .adjustHeight)
        contentLayout.pin.left().top(bounds.height/6).right()
    }
    func hintButtonTapped() {
        let vc = MessagePopupViewController()
        vc.applyPopPresenter()
        vc.hideIcon = false
        vc.popupTitle = "Mẹo"
        vc.popupDescription = ""
        parentViewController?.navigationController?.present(vc, animated: true)
    }
    override func createGame() {
        super.createGame()
        let values = (game.values?.compactMap { $0.value as? String })!
        let randomOrder = [Int](0..<values.count).randomOrder()
        self.values = randomOrder.map{values[$0]}
        var delay = 0.5
        delay += self.playSound(delay: delay, names:self.parseIntroText()!)
        //values.insert("co co co co", at: 0)
        loadNextStep()
    }
    var isNeedShowHintButton: (Bool){false}
    func loadNextStep(){
        resumeGame()
        if step >= values.count {
            pauseGame()
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            return
        }
        answer = values[step]
        step += 1
        let text = "\(answer.prefix(1).uppercased())\(answer.dropFirst(1))."
        var itemIndex = Int.random(in: 0..<4)
        itemIndex = 1
        colorRead = UIColor(hexString: ["#EC6149","#4C9743","#FFFFFF","#FFFFFF"][itemIndex]) // done
        colorReading = colorRead
        colorNormal = UIColor(hexString: ["#587AC2","#D9FF4D","#6695FF","#8ACE7C"][itemIndex])
        colorBackground = UIColor(hexString: ["#7197CF","#D9FF4D","#6695FF","#8ACE7C"][itemIndex])
        self.backgroundColor = .color(hex: "#88D35A")
                
        items = text.split(separator: " ").map{$0.string}
        var count = items.count / 3
        if count > 4 {count=4}
        if count < 2 {count=2}
        chooseIndexes = [Int](0..<items.count).randomOrder().take(count: count)
        textContainer.subviews.forEach{$0.removeFromSuperview()}
        textBottomContainer.subviews.forEach{$0.removeFromSuperview()}
        contentLayout.subviews.forEach{$0.removeFromSuperview()}
        textBottomContainer.alpha = 0
        contentLayout.alpha = 0
        textContainer.alpha = 1
        dragViews.removeAll()
        textContainer.alpha = 0
        let fontSize = getTextSize(text: text)
        
        textContainer.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.center).alignItems(.start).define { flex in
            for item in items {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = item
                label.textColor = colorRead
                //label.backgroundColor = .green
                flex.addItem(label).marginBottom(5)
                
                label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = " "
                label.textColor = .red
                //label.backgroundColor = .green
                flex.addItem(label)
            }
        }
        textBottomContainer.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.center).alignItems(.start).define { flex in
            var index = 0
            for item in items {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = item
                label.textColor = chooseIndexes.contains(index) ? .clear : colorRead
                label.backgroundColor = chooseIndexes.contains(index) ? colorBackground : .clear
                label.layer.cornerRadius = chooseIndexes.contains(index) ? (item.count>=2 ? fontSize/3 : fontSize/6) : 0
                label.clipsToBounds = chooseIndexes.contains(index)
                flex.addItem(label).marginBottom(5)
                
                label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = " "
                label.textColor = .red
                //label.backgroundColor = .green
                flex.addItem(label)
                index += 1
            }
        }
        contentLayout.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.spaceEvenly).define { flex in
            for index in chooseIndexes {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = items[index]
                label.textColor = colorNormal
                //label.backgroundColor = .green
                flex.addItem(label).marginBottom(fontSize/2)
            }
        }
        showBottom()
        //container.flex.layout()
        setNeedsLayout()
    }
    func showBottom(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeInOut
        let animValues: [Double] = [-bounds.width,0]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textBottomContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
            self?.textBottomContainer.alpha = value
            self?.contentLayout.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            self.resumeGame()
        }
    }
    var swingId = -1
    
    func showCenter(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeInOut
        let animValues: [Double] = [-bounds.width,0]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func jumpAnimation(){
        var delay : Double = 0.5
        let words = parse(answer)
        playSound(name:"\(game.level!)/\(answer.lowercased())", delay: 0.5)
        for i in 0..<words.count {
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                let label = self.textContainer.subviews[i*2] as? UILabel
                label?.textColor = colorNormal
            })
            delay += words[i].Duration
        }
        
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.finishAnimation()
            }
        }
    }
    func MoveToTopAndStartJumpAnimation(){
        playSound(name:"effects/slide", delay: 0)
        for i in 0..<items.count {
            let dst = textContainer.subviews[2*i]
            let src = textBottomContainer.subviews[2*i]
            let d = src.convert(CGPoint.zero, to: dst)
            UIView.animate(withDuration: 0.3) {
                src.transform = CGAffineTransformMakeTranslation(-d.x, -d.y+dst.bounds.height/2-src.bounds.height/2)
            }
        }
        scheduler.schedule(delay: 0.31) {
            [weak self] in
            guard let self = self else { return }
            self.textContainer.alpha = 1
            self.textBottomContainer.alpha = 0
            self.jumpAnimation()
        }
    }
    
    func getTextSize(text:String)->CGFloat{
        return sqrt(bounds.width * bounds.height / CGFloat(text.count)) / 2
    }
    private var selectedView: UIView?
    private var zPosition: CGFloat = 1
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            for view in contentLayout.subviews {
                if dragViews.contains(view) {continue}
                if sender.placeInView(view: view){
                    selectedView = view
                    zPosition += 1
                    view.layer.zPosition = zPosition
                    UIView.animate(withDuration: 0.3) {
                        view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                    }
                    if let label = view as? UILabel {
                        label.textColor = colorRead
                    }
                    return;
                }
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if state == .ended || state == .cancelled || state == .failed {
            if let selectedView = selectedView as? UILabel {
                let textBottoms = textBottomContainer.subviews.map{$0 as! UILabel}
                var closedView: UILabel?
                var distance : Double = 1000000
                for textBottom in textBottoms {
                    var p = textBottom.convert( CGPointMake(textBottom.bounds.width / 2, textBottom.bounds.height / 2), to: selectedView)
                    p.x -= selectedView.bounds.width / 2
                    p.y -= selectedView.bounds.height / 2
                    let d = sqrt(p.x * p.x + p.y * p.y)
                    if d < distance {
                        distance = d
                        closedView = textBottom
                    }
                }
                if distance < selectedView.bounds.height / 2 {
                    if selectedView.text == closedView?.text && closedView?.textColor != colorRead {
                        playSound(answerCorrect1EffectSound())
                        correct += 1
                        processRightDrag(closedView: closedView!, label: selectedView)
                    } else {
                        playSound(answerWrongEffectSound())
                        incorrect += 1
                        UIView.animate(withDuration: 0.2) {
                            selectedView.transform = .identity
                        }
                        selectedView.textColor = colorNormal
                    }
                } else {
                    playSound(answerWrongEffectSound())
                    UIView.animate(withDuration: 0.2) {
                        selectedView.transform = .identity
                    }
                    selectedView.textColor = colorNormal
                }
            }
        }
    }
    func processRightDrag(closedView:UILabel, label: UILabel){
        dragViews.append(label)
        playSound(name:"effects/true2")
        closedView.backgroundColor = .clear
        var p = closedView.convert( CGPointMake(closedView.bounds.width / 2, closedView.bounds.height / 2), to: label)
        p.x -= label.bounds.width / 2
        p.y -= label.bounds.height / 2
        UIView.animate(withDuration: 0.1) {
            var tran = label.transform
            tran.tx += p.x
            tran.ty += p.y
            label.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
        } completion: { done in
            label.textColor = .clear
            closedView.textColor = self.colorRead
        }
        if dragViews.count == chooseIndexes.count {
            pauseGame()
            scheduler.schedule(delay: 0.2) {
                [weak self] in
                guard let self = self else { return }
                self.MoveToTopAndStartJumpAnimation()
            }
        }
    }
    func finishAnimation(){
        CoinAnimationUtils.shared.removeList()
        textContainer.animateCoin(answer: true)
        var delay = 1.0
        delay += self.playSound(name:"effects/cheer\(Int.random(in: 1...4))", delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            let easy = BackEaseInterpolater()
            easy.mode = .easeIn
            let animValues: [Double] = [0,bounds.width]
            let timeChange = Interpolate(values: [0,1],
            apply: { [weak self] (value) in
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                self?.textContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
            })
            timeChange.animate(1, duration: 0.5){
                self.textContainer.transform = .identity
                self.textContainer.alpha = 0
                self.loadNextStep()
            }
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    var correct = 0
    override func getScore()->Float{
        return Float(correct) / Float(correct + incorrect)
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: "Sounds/en/english phonics/\(game.level!)/\(name.lowercased())", withExtension: "csv") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for line in lines {
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    if word.Duration != 0 {
                        words.append(word)
                    }
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
}


// Assuming you have a custom struct or class for Word
class CsvWord {
    var Text: String = ""
    var Start: Double = 0.0
    var Duration: Double = 0.0
}



