<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#D6FAFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.5" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/grid_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercent="0.05">

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_days"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="1"
            android:textColor="#74B6FF"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="ngày"
            android:textColor="#74B6FF"
            app:heightRatio="0.4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="+"
            android:textColor="#74B6FF"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0.25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_weeks"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="2"
            android:textColor="#74B6FF"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="tuần"
            android:textColor="#74B6FF"
            app:heightRatio="0.4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="="
            android:textColor="#74B6FF"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="0.75"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/math_result_bg"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="?"
            android:textColor="#74B6FF"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="ngày"
            android:textColor="#74B6FF"
            app:heightRatio="0.4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>