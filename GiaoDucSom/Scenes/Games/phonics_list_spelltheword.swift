//
//  phonics_list_spellthewordswift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import AnyCodable

class phonics_list_spelltheword: GameFragment {
    // MARK: - Properties
    private var centerGrid: MyGridView!
    private var bottomGrid: MyGridView!
    private var behindGrid: MyGridView!
    private var answer: String = ""
    private var allLetters: String = ""
    private var svgView: SVGImageView!
    private var textResult: HeightRatioTextView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#849BFE")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftContainer = UIView()
        leftContainer.backgroundColor = UIColor.color(hex: "#7CD2FF")
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.67)
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview()
        }
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.33)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        let svgContainer = UIImageView()
        svgContainer.clipsToBounds = false
        svgContainer.image = Utilities.SVGImage(named: "bg_obj_white")
        rightContainer.addSubview(svgContainer)
        svgContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(svgContainer.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(leftContainer.frame.height * 0.1) // Vertical bias 0.6
            make.width.lessThanOrEqualTo(400)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.accessibilityIdentifier = "svg_view"
        svgContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        
        bottomGrid = MyGridView()
        bottomGrid.clipsToBounds = false
        leftContainer.addSubview(bottomGrid)
        bottomGrid.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.4)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        
        behindGrid = MyGridView()
        behindGrid.clipsToBounds = false
        leftContainer.addSubview(behindGrid)
        behindGrid.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.4)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomGrid.snp.top)
        }
        
        centerGrid = MyGridView()
        centerGrid.clipsToBounds = false
        leftContainer.addSubview(centerGrid)
        centerGrid.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.4)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(behindGrid)
        }
        
        
        textResult = HeightRatioTextView()
        textResult.setHeightRatio(0.8)
        textResult.textAlignment = .center
        textResult.textColor = .white
        textResult.font = .Freude(size: 24)
        textResult.isHidden = true
        leftContainer.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(50)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let values = data?.values else { return }
        answer = values.shuffled().first!.value as! String
        allLetters = answer
    }
    
    override func createGame() {
        super.createGame()
        
        var indexes: [Int] = []
        while true {
            indexes = (0..<answer.count).shuffled()
            let ok = indexes.enumerated().contains { $0.offset != $0.element }
            if ok { break }
        }
        
        var bottomViews: [UIView] = []
        for i in 0..<answer.count {
            let view = createItemNumberView(text: String(answer[answer.index(answer.startIndex, offsetBy: indexes[i])]))
            view.stringTag = String(answer[answer.index(answer.startIndex, offsetBy: indexes[i])])
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            bottomViews.append(view)
        }
        
        bottomGrid.columns = answer.count
        bottomGrid.itemRatio = 1
        bottomGrid.itemSpacingRatio = 0.03
        bottomGrid.insetRatio = 0.1
        bottomGrid.reloadItemViews(views: bottomViews)
        
        var centerViews: [UIView] = []
        for i in 0..<answer.count {
            let view = createItemNumberView(text: String(answer[answer.index(answer.startIndex, offsetBy: i)]))
            view.stringTag = String(answer[answer.index(answer.startIndex, offsetBy: i)])
            view.alpha = 0
            centerViews.append(view)
        }
        
        centerGrid.columns = answer.count
        centerGrid.itemRatio = 1
        centerGrid.itemSpacingRatio = 0.03
        centerGrid.insetRatio = 0.1
        centerGrid.reloadItemViews(views: centerViews)
        
        var behindViews: [UIView] = []
        for _ in 0..<answer.count {
            let view = createItemTextViewSell3()
            view.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            behindViews.append(view)
        }
        
        behindGrid.columns = answer.count
        behindGrid.itemRatio = 1
        behindGrid.itemSpacingRatio = 0.03
        behindGrid.insetRatio = 0.1
        behindGrid.reloadItemViews(views: behindViews)
        
        let svgPath = isVocab() ? data?.paths![data?.values!.firstIndex(of: AnyCodable(answer)) ?? 0] ?? "" : "english phonics/\(data?.level ?? "")/\(answer).svg"
        svgView.image = Utilities.GetSVGKImage(named: svgPath).uiImage
        
        var delay: TimeInterval = 0.5
        let texts = parseIntroText()!
        for text in texts {
            let sound = text.replacingOccurrences(of: "@", with: isVocab() ? data?.sounds![data?.values!.firstIndex(of: AnyCodable(answer)) ?? 0] ?? answer : answer)
            delay += playSound(delay: delay, names: [sound])
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay: TimeInterval = 0.5
            let texts = parseIntroText()!
            for text in texts {
                let sound = text.replacingOccurrences(of: "@", with: isVocab() ? data?.sounds![data?.values!.firstIndex(of: AnyCodable(answer)) ?? 0] ?? answer : answer)
                delay += playSound(delay: delay, names: [sound])
            }
            delay += playSound(delay: delay, names: ["\(data?.level ?? "")/\(answer)"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let tag = view.stringTag else { return }
        
        if allLetters.starts(with: tag) {
            view.isUserInteractionEnabled = false
            allLetters = String(allLetters.dropFirst())
            
            UIView.animate(withDuration: 0.5, animations: {
                view.alpha = 0
                view.transform = .identity
            })
            
            for child in centerGrid.subviews {
                if child.alpha == 0 {
                    UIView.animate(withDuration: 0.5) {
                        child.alpha = 1
                        child.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
                    }
                    break
                }
            }
            
            var delay: TimeInterval = 0.2
            delay += playSound(delay: delay, names: ["\(tag)1"])
            
            if allLetters.isEmpty {
                pauseGame(stopMusic: false)
                animateCoinIfCorrect(view: svgView)
                delay += 1.0
                textResult.text = answer
                
                if let textNumber1 = centerGrid.subviews[0].viewWithStringTag("text_number") {
                    textResult.snp.remakeConstraints { make in
                        make.width.equalToSuperview().multipliedBy(0.8)
                        make.height.equalTo(textNumber1.frame.height)
                        make.center.equalToSuperview()
                    }
                }
                
                scheduler.schedule(after: delay) {
                    [weak self] in
                    guard let self = self else { return }
                    UIView.animate(withDuration: 0, animations: {
                        self.behindGrid.alpha = 0
                    }, completion: { _ in
                        UIView.animate(withDuration: 0.3, animations: {
                            self.centerGrid.transform = CGAffineTransform(translationX: 0, y: self.centerGrid.frame.height / 4)
                        }, completion: { _ in
                            for child in self.centerGrid.subviews {
                                UIView.animate(withDuration: 0.5, animations: {
                                    child.alpha = 0
                                    child.moveToCenter(of: self.centerGrid)
                                }, completion: { _ in
                                   
                                })
                            }
                            self.scheduler.schedule(delay: 0.4) {
                                [weak self] in
                                guard let self = self else { return }
                                self.textResult.alpha = 0
                                self.textResult.isHidden = false
                                UIView.animate(withDuration: 0.2) {
                                    self.textResult.alpha = 1
                                }
                            }
                        })
                    })
                }
                
                delay += playSound(delay: delay, names: [
                    isVocab() ? self.data?.sounds![self.data?.values!.firstIndex(of: AnyCodable(self.answer)) ?? 0] ?? self.answer : self.answer,
                    "is spell"
                ])
                for char in answer {
                    delay += playSound(delay: delay, names: ["\(char)1"])
                }
                delay += 1.0
                delay += playSound(delay: delay, names: [self.getCorrectHumanSound(), "effects/end game"])
                scheduler.schedule(after: delay) { [weak self] in
                    self?.finishGame()
                }
            }
        } else {
            playSound(name: "effect/answer_wrong")
            pauseGame(stopMusic: false)
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemNumberView(text: String) -> UIView {
        let view = UIView()
        view.clipsToBounds = false
        
        let backgroundView = UIImageView()
        backgroundView.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(backgroundView.snp.width).multipliedBy(373.0 / 346.0) // Ratio 346:373
        }
        
        let textNumber = HeightRatioTextView()
        textNumber.setHeightRatio(0.8)
        textNumber.textAlignment = .center
        textNumber.textColor = UIColor.color(hex: "#74B6FF")
        textNumber.font = .Freude(size: 24)
        textNumber.text = text
        textNumber.stringTag = "text_number"
        backgroundView.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(backgroundView.frame.height * 0.05) // Vertical bias 0.45
        }
        
        return view
    }
    
    private func createItemTextViewSell3() -> UIView {
        let view = UIView()
        view.clipsToBounds = false
        
        let innerView = UIView()
        view.addSubview(innerView)
        innerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(innerView.snp.width) // Ratio 1:1
        }
        
        let imageView = UIImageView()
        imageView.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white").withRenderingMode(.alwaysTemplate)
        imageView.tintColor = UIColor.color(hex: "#5C7DEA")
        innerView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return view
    }
}
