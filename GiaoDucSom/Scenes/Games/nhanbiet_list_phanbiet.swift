//
//  nhanbiet_list_phanbiet.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_phanbiet: NhanBietGameFragment {
    // MARK: - Properties
    private var svgItemMap: [SVGKImage: Item] = [:]
    private var meIndex: Int = 0
    private var indexes: [Int] = []
    private var svgList: [SVGKImage] = []
    private var svgViewLeft: SVGImageView!
    private var svgViewRight: SVGImageView!
    private var viewLeft: UIControl!
    private var viewRight: UIControl!
    private var normalLayout = false
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let folder = getFolder() ?? ""
        let bgResource: String
        let bg2Resource: String?
        let bg3Resource: String?
        let bg2Constraints: (UIView) -> Void
        let bg3Constraints: (UIView) -> Void
        
        switch folder {
        case "Aquatic Animals":
            bgResource = "nhanbiet_bg_ocean1"
            bg2Resource = "nhanbiet_bg_ocean2"
            bg3Resource = "nhanbiet_bg_ocean3"
            bg2Constraints = {
                [weak self] bg2 in
                guard let self = self else { return }
                bg2.snp.makeConstraints { make in
                    make.width.equalTo(bg2.snp.height).multipliedBy(1328.9/1236.2)
                    make.height.lessThanOrEqualTo(500)
                    make.height.lessThanOrEqualToSuperview()
                    make.centerX.equalToSuperview()
                    //make.centerY.equalToSuperview().multipliedBy(1.6) // Vertical bias 0.8
                }
                self.addActionOnLayoutSubviews {
                    bg2.snapToVerticalBias(verticalBias: 0.8)
                }
            }
            bg3Constraints = { bg3 in
                bg3.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                bg3.makeViewCenterFillAndKeep(ratio: 2688.0/1236.2)
            }
        case "Flying Animals":
            bgResource = "nhanbiet_bg_sky"
            bg2Resource = "nhanbiet_bg_sky2"
            bg3Resource = nil
            bg2Constraints = { bg2 in
                bg2.snp.makeConstraints { make in
                    make.width.equalTo(bg2.snp.height).multipliedBy(1127.0/866.0)
                    make.height.equalToSuperview().multipliedBy(0.7)
                    make.height.lessThanOrEqualTo(700)
                    make.centerX.equalToSuperview()
                    make.bottom.equalToSuperview().multipliedBy(0.8) // Vertical bias 0.1
                }
            }
            bg3Constraints = { _ in }
        case "Bath Room", "Sports":
            bgResource = folder == "Bath Room" ? "nhanbiet_bg_bathroom" : "nhanbiet_bg_stadium"
            bg2Resource = nil
            bg3Resource = nil
            bg2Constraints = { _ in }
            bg3Constraints = { _ in }
        case "Insects":
            bgResource = "nhanbiet_bg_forest"
            bg2Resource = "nhanbiet_bg_forest2"
            bg3Resource = nil
            bg2Constraints = {
                [weak self] bg2 in
                guard let self = self else { return }
                bg2.snp.makeConstraints { make in
                    make.width.equalTo(bg2.snp.height).multipliedBy(634.3/818.7)
                    make.height.equalToSuperview().multipliedBy(0.7)
                    make.height.lessThanOrEqualTo(700)
                    make.centerX.equalToSuperview()
                    //make.centerY.equalToSuperview().multipliedBy(1.6) // Vertical bias 0.8
                }
                self.addActionOnLayoutSubviews {
                    bg2.snapToVerticalBias(verticalBias: 0.8)
                }
            }
            bg3Constraints = { _ in }
        case "Marine Vehicles":
            bgResource = "nhanbiet_bg_beach"
            bg2Resource = "nhanbiet_bg_beach2"
            bg3Resource = nil
            bg2Constraints = { bg2 in
                bg2.snp.makeConstraints { make in
                    make.width.equalToSuperview().multipliedBy(0.3)
                    make.height.equalTo(bg2.snp.width).multipliedBy(835.1/727.0).priority(.high)
                    make.height.lessThanOrEqualTo(700)
                    make.right.equalToSuperview().inset(10)
                    //make.centerY.equalToSuperview().multipliedBy(1.6) // Vertical bias 0.8
                }
                self.addActionOnLayoutSubviews {
                    bg2.snapToVerticalBias(verticalBias: 0.8)
                }
            }
            bg3Constraints = { _ in }
        case "Vehicles":
            bgResource = "nhanbiet_bg_street"
            bg2Resource = "nhanbiet_bg_street2"
            bg3Resource = nil
            bg2Constraints = {
                [weak self] bg2 in
                guard let self = self else { return }
                bg2.snp.makeConstraints { make in
                    make.width.equalToSuperview().multipliedBy(0.2)
                    make.height.equalTo(bg2.snp.width).multipliedBy(691.0/424.5).priority(.high)
                    make.height.lessThanOrEqualTo(700)
                    make.right.equalToSuperview().inset(10)
                    //make.centerY.equalToSuperview().multipliedBy(1.6) // Vertical bias 0.8
                }
                self.addActionOnLayoutSubviews {
                    bg2.snapToVerticalBias(verticalBias: 0.8)
                }
            }
            bg3Constraints = { _ in }
        default:
            normalLayout = true
            bgResource = "nhanbiet_bg_table"
            bg2Resource = nil
            bg3Resource = nil
            bg2Constraints = { _ in }
            bg3Constraints = { _ in }
        }
        
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: bgResource))!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        if normalLayout {
            bgImage.snp.makeConstraints { make in
                make.left.bottom.right.equalToSuperview()
                make.width.equalTo(bgImage.snp.height).multipliedBy(2688.0/1236.2)
            }
            self.backgroundColor = .color(hex: "#D9F9D2")
            view.backgroundColor = .color(hex: "#D9F9D2")
        } else {
            bgImage.makeViewCenterFillAndKeep(ratio: 2688.0/1236.2)
        }
        
        if let bg2Resource = bg2Resource {
            let bg2Image = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: bg2Resource))!
            view.addSubview(bg2Image)
            bg2Constraints(bg2Image)
        }
        
        if let bg3Resource = bg3Resource {
            let bg3Image = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: bg3Resource))!
            view.addSubview(bg3Image)
            bg3Constraints(bg3Image)
        }
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let layoutMargin: CGFloat = folder == "Bath Room" || folder == "Sports" ? 0 : 0.1
        
        viewLeft = UIControl()
        let buttonBg = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        let leftBgView = UIImageView(image: buttonBg)
        viewLeft.addSubviewWithInset(subview: leftBgView, inset: 0)
        viewLeft.stringTag = "0"
        viewLeft.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        itemContainer.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            if folder == "Bath Room" || folder == "Sports" {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.5)
                make.height.equalTo(viewLeft.snp.width)
                make.right.equalToSuperview().multipliedBy(0.45) // Horizontal bias 0.85
                make.centerY.equalToSuperview().multipliedBy(1.2) // Vertical bias 0.6
            } else if folder == "Insects" {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.4)
                make.height.equalTo(viewLeft.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    viewLeft.snpLeftBottom(ratio: 1.5)
                }
            } else if folder == "Marine Vehicles" || folder == "Vehicles" {
                make.width.equalTo(itemContainer.snp.width).multipliedBy(0.275) // 0.55 * 0.5
                make.height.equalTo(viewLeft.snp.width)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                addActionOnLayoutSubviews {
                    [weak self] in
                    guard let self = self else { return }
                    self.viewLeft.snapToHorizontalBias(horizontalBias: 0.1)
                }
            } else if normalLayout {
                make.width.equalTo(itemContainer.snp.width).multipliedBy(0.25)
                make.height.equalTo(viewLeft.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.92)
                make.centerX.equalToSuperview().multipliedBy(0.4)
            } else {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.4)
                make.height.equalTo(viewLeft.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    viewLeft.snpLeftBottom(ratio: 1)
                }
            }
        }
        
        svgViewLeft = SVGImageView(frame: .zero)
        svgViewLeft.stringTag = "svg_view_left"
        viewLeft.addSubview(svgViewLeft)
        svgViewLeft.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.85)
            make.height.equalTo(svgViewLeft.snp.width)
            make.center.equalToSuperview()
        }
        
        viewRight = UIControl()
        let rightBgView = UIImageView(image: buttonBg)
        viewRight.addSubviewWithInset(subview: rightBgView, inset: 0)
        viewRight.stringTag = "1"
        viewRight.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        itemContainer.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            if folder == "Bath Room" || folder == "Sports" {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.5)
                make.height.equalTo(viewRight.snp.width)
                make.left.equalTo(itemContainer.snp.right).multipliedBy(0.55)
                make.centerY.equalToSuperview().multipliedBy(1.2)
            } else if folder == "Insects" {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.4)
                make.height.equalTo(viewRight.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    viewRight.snpRightBottom(ratio: 1.5)
                }
            } else if folder == "Marine Vehicles" || folder == "Vehicles" {
                make.width.equalTo(itemContainer.snp.width).multipliedBy(0.275)
                make.height.equalTo(viewRight.snp.width)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                make.right.equalToSuperview().multipliedBy(0.75)
                addActionOnLayoutSubviews {
                    [weak self] in
                    guard let self = self else { return }
                    //self.viewRight.snapToHorizontalBias(horizontalBias: 0.5)
                }
            } else if normalLayout {
                make.width.equalTo(itemContainer.snp.width).multipliedBy(0.25)
                make.height.equalTo(viewLeft.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.92)
                make.centerX.equalToSuperview().multipliedBy(1.6)
            } else {
                make.height.equalTo(itemContainer.snp.height).multipliedBy(0.4)
                make.height.equalTo(viewRight.snp.width)
                //make.height.lessThanOrEqualTo(250)
                make.bottom.equalToSuperview().multipliedBy(0.9)
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    viewRight.snpRightBottom(ratio: 1)
                }
            }
        }
        
        svgViewRight = SVGImageView(frame: .zero)
        svgViewRight.stringTag = "svg_view_right"
        viewRight.addSubview(svgViewRight)
        svgViewRight.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.85)
            make.height.equalTo(svgViewRight.snp.width)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(), listItems.count >= 2 else { return }
        
        svgList = []
        for item in listItems {
            let svgImage = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item.path!)")
            svgList.append(svgImage)
            svgItemMap[svgImage] = item
        }
        
        indexes = [0, 1, 2].shuffled().prefix(2).sorted()
        svgViewLeft.image = svgList[indexes[0]].uiImage
        svgViewRight.image = svgList[indexes[1]].uiImage
        meIndex = Int.random(in: 0..<2)
    }
    
    override func createGame() {
        super.createGame()
        
        guard let listItems = getListItems(), let folder = getFolder(), indexes.count > meIndex else { return }
        
        var delay: TimeInterval = 0
        if getLanguage() == "en" {
            let article = ArticleHelper.getArticle(listItems[indexes[meIndex]].name.en ?? "")
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(getLanguage())/nhanbiet/nhanbiet_phanbiet_\(article)",
                "\(getLanguage())/topics/\(folder)/\(listItems[indexes[meIndex]].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        } else {
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(getLanguage())/nhanbiet/nhanbiet_phanbiet",
                "\(getLanguage())/topics/\(folder)/\(listItems[indexes[meIndex]].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        }
        
        viewLeft.alpha = 0
        viewLeft.transform = CGAffineTransform(scaleX: 0, y: 0)
        viewRight.alpha = 0
        viewRight.transform = CGAffineTransform(scaleX: 0, y: 0)
        
        scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.4, options: [], animations: {
                self.viewLeft.alpha = 1
                self.viewLeft.transform = .identity
            })
            self.playSound("effect/bubble1")
        }
        
        scheduler.schedule(after: delay + 1.0) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.4, options: [], animations: {
                self.viewRight.alpha = 1
                self.viewRight.transform = .identity
            })
            self.playSound("effect/bubble2")
            self.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let listItems = getListItems(), let folder = getFolder(), indexes.count > meIndex else { return }
            var delay: TimeInterval = 0
            if getLanguage() == "en" {
                let article = ArticleHelper.getArticle(listItems[indexes[meIndex]].name.en ?? "")
                delay += playSound(delay: delay, names: [
                    "\(getLanguage())/nhanbiet/nhanbiet_phanbiet_\(article)",
                    "\(getLanguage())/topics/\(folder)/\(listItems[indexes[meIndex]].path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            } else {
                delay += playSound(delay: delay, names: [
                    "\(getLanguage())/nhanbiet/nhanbiet_phanbiet",
                    "\(getLanguage())/topics/\(folder)/\(listItems[indexes[meIndex]].path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIControl) {
        if gameState != .playing { return }
        guard let index = Int(sender.stringTag ?? "0"),
              let listItems = getListItems(), let folder = getFolder(), indexes.count > index else { return }
        
        pauseGame(stopMusic: false)
        if index == meIndex {
            animateCoinIfCorrect(view: sender)
            let delay = playSound(delay: 0, names: [
                "effect/answer_end",
                getCorrectHumanSound(),
                "\(getLanguage())/topics/\(folder)/\(listItems[indexes[meIndex]].path!.replacingOccurrences(of: ".svg", with: ""))",
                endGameSound()
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: [
                "\(getLanguage())/topics/\(folder)/\(listItems[indexes[index]].path!.replacingOccurrences(of: ".svg", with: ""))",
                answerWrongEffectSound(),
                getWrongHumanSound()
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
