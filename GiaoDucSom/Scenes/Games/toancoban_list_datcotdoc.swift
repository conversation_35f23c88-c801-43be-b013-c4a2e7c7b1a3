//
//  toancoban_list_datcotdoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit

class toancoban_list_datcotdoc: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var textA: UILabel!
    private var textB: UILabel!
    private var textC: UILabel!
    private var textOperator: UILabel!
    private var a: Int = 0
    private var b: Int = 0
    private var itemContainer: UIView!
    private let rightBg = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        let itemContainer5 = UIView()
        leftContainer.addSubviewWithPercentInset(subview: itemContainer5, percentInset: 5)
        
        itemContainer = UIView()
        itemContainer5.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 0.6)
        
        textA = AutosizeLabel()
        textA.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        itemContainer.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.25)
            make.width.equalTo(itemContainer).multipliedBy(0.9)
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        textB = AutosizeLabel()
        textB.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        itemContainer.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.25)
            make.width.equalTo(itemContainer).multipliedBy(0.9)
            make.centerY.equalToSuperview().multipliedBy(0.7) // Bias 0.35
            make.centerX.equalToSuperview()
        }
        
        textOperator = AutosizeLabel()
        textOperator.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
        textOperator.font = .Freude(size: 20)
        textOperator.textAlignment = .center
        textOperator.adjustsFontSizeToFitWidth = true
        textOperator.minimumScaleFactor = 0.1
        itemContainer.addSubview(textOperator)
        textOperator.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.25)
            make.width.equalTo(itemContainer).multipliedBy(0.3 * 0.9) // Width 30% * heightRatio 0.9
            make.centerY.equalToSuperview().multipliedBy(0.7) // Bias 0.35
            make.left.equalToSuperview()
        }
        
        let lineView = UIView()
        lineView.backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        itemContainer.addSubview(lineView)
        lineView.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.02)
            make.width.equalTo(itemContainer).multipliedBy(0.7)
            make.centerY.equalToSuperview().multipliedBy(1.2) // Bias 0.6
            make.centerX.equalToSuperview()
        }
        
        let resultContainer = UIImageView()
        resultContainer.image = Utilities.SVGImage(named: "math_result_bg")
        itemContainer.addSubview(resultContainer)
        resultContainer.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.3)
            make.width.equalTo(resultContainer.snp.height) // Ratio 1:1
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        textC = AutosizeLabel()
        textC.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textC.font = .Freude(size: 20)
        textC.textAlignment = .center
        textC.adjustsFontSizeToFitWidth = true
        textC.minimumScaleFactor = 0.1
        resultContainer.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.height.equalTo(resultContainer).multipliedBy(0.9) // HeightRatio 0.8 * 0.9
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 10 + Int.random(in: 0..<90) // 10 to 99
            b = 10 + Int.random(in: 0..<90) // 10 to 99
            if a + b < 100 && (a % 10 + b % 10) < 10 {
                textA.text = String(a)
                textB.text = String(b)
                textOperator.text = "+"
                break
            }
        }
        
        var numbers: [Int] = []
        while true {
            numbers = Utils.generatePermutation(3, size: 100)
            if numbers.allSatisfy({ $0 > 10 }) && !numbers.contains(a + b) {
                numbers.append(a + b)
                numbers.shuffle()
                break
            }
        }
        
        var views: [UIView] = []
        for i in 0..<numbers.count {
            let value = numbers[i]
            let view = createNumberItem(value: value)
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_dat cot doc")
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dat cot doc")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid Item
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == a + b
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            textC.text = textNumber.text
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            delay += playSound(delay: delay, names: [
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                getNumberSound(a),
                "toan/cộng",
                getNumberSound(b),
                "toan/bằng",
                getNumberSound(a + b),
                endGameSound()
            ])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
