//
//  toancoban_list_taopheptinhpv10.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/6/25.
//


import UIKit
import SnapKit

class toancoban_list_taopheptinhpv10: NhanBietGameFragment {
    // MARK: - Properties
    private var bottomItemContainer: UIStackView!
    private var topItemContainer: UIStackView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var textA: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textResult: AutosizeLabel!
    private var textOperator: AutosizeLabel!
    private var text1: AutosizeLabel!
    private var text2: AutosizeLabel!
    private var text3: AutosizeLabel!
    private var text4: AutosizeLabel!
    private var text5: AutosizeLabel!
    private var containerLayout: UIView!
    private var snapViews: [UIView?] = [nil, nil, nil]
    private var result: Int = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#E9FDFF")
        
        let topGridLayout = UIView()
        topGridLayout.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(topGridLayout)
        topGridLayout.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        topItemContainer = UIStackView()
        topItemContainer.axis = .horizontal
        topItemContainer.alignment = .center
        topItemContainer.distribution = .equalSpacing
        topItemContainer.spacing = 8
        topGridLayout.addSubview(topItemContainer)
        topItemContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(topItemContainer.snp.height).multipliedBy(5.5) // Ratio 5.5:1
        }
        
        let text1Layout = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        topItemContainer.addArrangedSubview(text1Layout)
        text1Layout.snp.makeConstraints { make in
            make.width.equalTo(topItemContainer).multipliedBy(0.95 / 5.5) // Ratio 0.95:1
            make.height.equalTo(text1Layout.snp.width)
            //make.left.top.bottom.equalToSuperview()
        }
        
        text1 = AutosizeLabel()
        text1.text = "1"
        text1.textColor = UIColor(hex: "#74B6FF")
        text1.font = .Freude(size: 20)
        text1.textAlignment = .center
        text1.adjustsFontSizeToFitWidth = true
        text1.minimumScaleFactor = 0.1
        text1Layout.addSubview(text1)
        text1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let text2Layout = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        topItemContainer.addArrangedSubview(text2Layout)
        text2Layout.snp.makeConstraints { make in
            make.width.equalTo(topItemContainer).multipliedBy(0.95 / 5.5)
            make.height.equalTo(text2Layout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(text1Layout.snp.right)
        }
        
        text2 = AutosizeLabel()
        text2.text = "2"
        text2.textColor = UIColor(hex: "#74B6FF")
        text2.font = .Freude(size: 20)
        text2.textAlignment = .center
        text2.adjustsFontSizeToFitWidth = true
        text2.minimumScaleFactor = 0.1
        text2Layout.addSubview(text2)
        text2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let text3Layout = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        topItemContainer.addArrangedSubview(text3Layout)
        text3Layout.snp.makeConstraints { make in
            make.width.equalTo(topItemContainer).multipliedBy(0.95 / 5.5)
            make.height.equalTo(text3Layout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(text2Layout.snp.right)
        }
        
        text3 = AutosizeLabel()
        text3.text = "3"
        text3.textColor = UIColor(hex: "#74B6FF")
        text3.font = .Freude(size: 20)
        text3.textAlignment = .center
        text3.adjustsFontSizeToFitWidth = true
        text3.minimumScaleFactor = 0.1
        text3Layout.addSubview(text3)
        text3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let text4Layout = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        topItemContainer.addArrangedSubview(text4Layout)
        text4Layout.snp.makeConstraints { make in
            make.width.equalTo(topItemContainer).multipliedBy(0.95 / 5.5)
            make.height.equalTo(text4Layout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(text3Layout.snp.right)
        }
        
        text4 = AutosizeLabel()
        text4.text = "+"
        text4.textColor = UIColor(hex: "#87D657")
        text4.font = .Freude(size: 20)
        text4.textAlignment = .center
        text4.adjustsFontSizeToFitWidth = true
        text4.minimumScaleFactor = 0.1
        text4Layout.addSubview(text4)
        text4.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let text5Layout = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        topItemContainer.addArrangedSubview(text5Layout)
        text5Layout.snp.makeConstraints { make in
            make.width.equalTo(topItemContainer).multipliedBy(0.95 / 5.5)
            make.height.equalTo(text5Layout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(text4Layout.snp.right)
        }
        
        text5 = AutosizeLabel()
        text5.text = "-"
        text5.textColor = UIColor(hex: "#FF7760")
        text5.font = .Freude(size: 20)
        text5.textAlignment = .center
        text5.adjustsFontSizeToFitWidth = true
        text5.minimumScaleFactor = 0.1
        text5Layout.addSubview(text5)
        text5.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bottomGridContainer = UIView()
        view.addSubview(bottomGridContainer)
        bottomGridContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        bottomItemContainer = UIStackView()
        bottomItemContainer.axis = .horizontal
        bottomItemContainer.alignment = .center
        bottomItemContainer.distribution = .equalSpacing
        bottomItemContainer.spacing = 8
        bottomGridContainer.addSubview(bottomItemContainer)
        bottomItemContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(bottomItemContainer.snp.height).multipliedBy(5.5) // Ratio 5.5:1
        }
        
        let textALayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        bottomItemContainer.addArrangedSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.width.equalTo(bottomItemContainer).multipliedBy(1.0 / 5.5)
            make.height.equalTo(textALayout.snp.width)
            //make.left.top.bottom.equalToSuperview()
        }
        
        textA = AutosizeLabel()
        textA.text = "1"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textA.alpha = 0
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textOperatorLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        bottomItemContainer.addArrangedSubview(textOperatorLayout)
        textOperatorLayout.snp.makeConstraints { make in
            make.width.equalTo(bottomItemContainer).multipliedBy(1.0 / 5.5)
            make.height.equalTo(textOperatorLayout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(textALayout.snp.right)
        }
        
        textOperator = AutosizeLabel()
        textOperator.text = "+"
        textOperator.textColor = UIColor(hex: "#74B6FF")
        textOperator.font = .Freude(size: 20)
        textOperator.textAlignment = .center
        textOperator.adjustsFontSizeToFitWidth = true
        textOperator.minimumScaleFactor = 0.1
        textOperator.alpha = 0
        textOperatorLayout.addSubview(textOperator)
        textOperator.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        bottomItemContainer.addArrangedSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.width.equalTo(bottomItemContainer).multipliedBy(1.0 / 5.5)
            make.height.equalTo(textBLayout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(textOperatorLayout.snp.right)
        }
        
        textB = AutosizeLabel()
        textB.text = "2"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textB.alpha = 0
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let equalLayout = UIImageView()
        bottomItemContainer.addArrangedSubview(equalLayout)
        equalLayout.snp.makeConstraints { make in
            make.width.equalTo(bottomItemContainer).multipliedBy(0.6 / 5.5)
            make.height.equalTo(equalLayout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(textBLayout.snp.right)
        }
        
        let equalText = AutosizeLabel()
        equalText.text = "="
        equalText.textColor = UIColor(hex: "#74B6FF")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalLayout.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textResultLayout = UIImageView()
        bottomItemContainer.addArrangedSubview(textResultLayout)
        textResultLayout.snp.makeConstraints { make in
            make.width.equalTo(bottomItemContainer).multipliedBy(0.6 / 5.5)
            make.height.equalTo(textResultLayout.snp.width)
            make.top.bottom.equalToSuperview()
            //make.left.equalTo(equalLayout.snp.right)
        }
        
        textResult = AutosizeLabel()
        textResult.text = "3"
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        textResultLayout.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerLayout = view
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        containerLayout.addGestureRecognizer(panGesture)
        
        coinView = UIView()
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        topGridLayout.layer.zPosition = 1000
    }
    
    // MARK: - Touch Handling
    var zPos = 100.0
    var originX: CGFloat = 0
    var originY: CGFloat = 0
    var origins: [UIView: CGPoint] = [:]
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: view)
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                if origins[currentView] == nil {
                    origins[currentView] = currentView.frame.origin
                }
                let point = gesture.location(in: view.superview)
                dX = currentView.frame.origin.x - point.x
                dY = currentView.frame.origin.y - point.y
                originX = currentView.frame.origin.x
                originY = currentView.frame.origin.y
                //currentView.bringToFront()
                zPos += 1
                currentView.layer.zPosition = CGFloat(zPos)
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                Utilities.vibrate()
            }
        case .changed:
            if let currentView = currentView {
                let point = gesture.location(in: view.superview)
                currentView.frame.origin = CGPoint(x: point.x + dX, y: point.y + dY)
            }
        case .ended:
            if let currentView = currentView {
                Utilities.vibrate()
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for i in 0..<3 {
                    let view = bottomItemContainer.subviews[i]
                    let point = currentView.distanceFromCenterToCenter(to: view)
                    let distance = hypot(point.x, point.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = view
                    }
                }
                if let targetView = targetView, minDistance < currentView.frame.width / 2 {
                    playSound("effect/word puzzle drop")
                    let index = bottomItemContainer.subviews.firstIndex(of: targetView)!
                    if let existingView = snapViews[index] {
                        UIView.animate(withDuration: 0.2) {
                            existingView.transform = .identity
                            existingView.frame.origin = self.origins[existingView]!
                        }
                    }
                    currentView.moveCenter(to: targetView, fitType: .inside, duration: 0.2) { _ in
                        self.snapViews[index] = currentView
                        if self.snapViews.allSatisfy({ $0 != nil }) {
                            self.checkFinish()
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.8) {
                        currentView.transform = .identity
                        currentView.frame.origin = CGPoint(x: self.originX, y: self.originY)
                    }
                    playSound("effect/slide2")
                }
                self.currentView = nil
            }
        default:
            break
        }
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        let topX = topItemContainer.frame.origin.x
        let topY = topItemContainer.frame.origin.y
        let adjustedX = x - topX
        let adjustedY = y - topY
        for child in topItemContainer.subviews.reversed() {
            if adjustedX >= child.frame.origin.x && adjustedX <= child.frame.origin.x + child.frame.width &&
               adjustedY >= child.frame.origin.y && adjustedY <= child.frame.origin.y + child.frame.height {
                return child
            }
        }
        return nil
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            let values = [Int.random(in: 0..<10), Int.random(in: 0..<10), Int.random(in: 0..<10)]
            let sign = Int.random(in: 0...1) == 0 ? 1 : -1
            result = values[0] + sign * values[1]
            if result >= 0 && result <= 9 {
                textA.text = String(values[0])
                textB.text = String(values[1])
                textResult.text = String(result)
                textOperator.text = sign == 1 ? "+" : "-"
                let indexes = Utils.generatePermutation(3, size: 3)
                text1.text = String(values[indexes[0]])
                text2.text = String(values[indexes[1]])
                text3.text = String(values[indexes[2]])
                break
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_tao phep tinh")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_tao phep tinh")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func checkFinish() {
        guard let text1 = (snapViews[0]?.subviews.first as? AutosizeLabel)?.text,
              let text2 = (snapViews[1]?.subviews.first as? AutosizeLabel)?.text,
              let text3 = (snapViews[2]?.subviews.first as? AutosizeLabel)?.text else {
            return
        }
        
        pauseGame()
        let isOperator = { (text: String) -> Bool in ["+", "-"].contains(text.lowercased()) }
        if !isOperator(text1) && isOperator(text2) && !isOperator(text3) {
            guard let a = Int(text1), let b = Int(text3) else { return }
            let sign = text2 == "+" ? 1 : -1
            let correct = result == a + sign * b
            if correct {
                coinView.moveCenter(to: textResult, fitType: .inside, duration: 0)
                animateCoinIfCorrect(view: coinView)
                let delay = playSound(
                    answerEndEffectSound(),
                    getCorrectHumanSound(),
                    "topics/Numbers/\(a)",
                    sign == 1 ? "toan/cộng" : "toan/trừ",
                    "topics/Numbers/\(b)",
                    "toan/bằng",
                    "topics/Numbers/\(result)",
                    endGameSound()
                )
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
                return
            }
        }
        
        setGameWrong()
        let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
        scheduler.schedule(after: 2.0) {
            [weak self] in
            guard let self = self else { return }
            for view in self.snapViews ?? [] {
                if let view = view {
                    UIView.animate(withDuration: 0.8) {
                        view.transform = .identity
                        view.frame.origin = self.origins[view]!
                    }
                }
            }
            self.snapViews = [nil, nil, nil]
            self.scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
}

