//
//  toancoban_list_timso.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_timso: toancoban_list_bia {
    // MARK: - Properties
    private let gameLonNhat = 1
    private let gameChanLe = 2
    private let gameHangDonVi = 3
    private var soChan: Bool = false
    private var lonNhat: Bool = false
    private var game: Int = 0
    private var soChuSo: Int = 0
    private var donVi: Int = 0
    
    // MARK: - Game Logic
    override func buildData() {
        game = random(gameLonNhat, gameChanLe, gameHangDonVi)
        numbers = []
        validNumbers = []
        
        if game == gameLonNhat {
            while true {
                soChan = Bool.random()
                lonNhat = Bool.random()
                let remain = soChan ? 0 : 1
                let _numbers = (1...99).shuffled().prefix(21).map { $0 }
                let filterNumbers = _numbers.filter { $0 % 2 == remain }
                let value = lonNhat ? filterNumbers.max() ?? 0 : filterNumbers.min() ?? 0
                let size = filterNumbers.filter { $0 == value }.count
                if size == 1 {
                    numbers = _numbers
                    validNumbers = [value]
                    break
                }
            }
        }
        if game == gameChanLe {
            while true {
                soChan = Bool.random()
                soChuSo = random(1, 2)
                let remain = soChan ? 0 : 1
                let _numbers = (1...99).shuffled().prefix(21).map { $0 }
                let filterNumbers = _numbers.filter { $0 % 2 == remain && String($0).count == soChuSo }
                if !filterNumbers.isEmpty {
                    numbers = _numbers
                    validNumbers = filterNumbers
                    break
                }
            }
        }
        if game == gameHangDonVi {
            while true {
                donVi = Int.random(in: 0..<10)
                let _numbers = (1...99).shuffled().prefix(21).map { $0 }
                let filterNumbers = _numbers.filter { $0 % 10 == donVi }
                if !filterNumbers.isEmpty {
                    numbers = _numbers
                    validNumbers = filterNumbers
                    break
                }
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        pauseGame()
        
        var delay: TimeInterval
        switch game {
        case gameLonNhat:
            delay = playSound(openGameSound(), "toan/toan_tim so_\(soChan ? "chan" : "le")", "toan/toan_tim so_\(lonNhat ? "1" : "2")")
        case gameChanLe:
            delay = playSound(openGameSound(), "toan/toan_so chan le 2 chu so_\(soChan ? "chan" : "le")", "toan/toan_so chan le 2 chu so_\(soChuSo == 1 ? "1" : "2")")
        case gameHangDonVi:
            delay = playSound(openGameSound(), "toan/toan_so hang don vi", "topics/Numbers/\(donVi)")
        default:
            return
        }
        
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            var delay: TimeInterval
            switch game {
            case gameLonNhat:
                delay = playSound("toan/toan_tim so_\(soChan ? "chan" : "le")", "toan/toan_tim so_\(lonNhat ? "1" : "2")")
            case gameChanLe:
                delay = playSound("toan/toan_so chan le 2 chu so_\(soChan ? "chan" : "le")", "toan/toan_so chan le 2 chu so_\(soChuSo == 1 ? "1" : "2")")
            case gameHangDonVi:
                delay = playSound("toan/toan_so hang don vi", "topics/Numbers/\(donVi)")
            default:
                return
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

