//
//  taptrung_list_lanbi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreMotion

class taptrung_list_lanbi: NhanBietGameFragment {
    // MARK: - Properties
    private let THRESHOLD: CGFloat = 1.0
    private var busy = false
    private let mazeColumns = 4
    private let mazeRows = 4
    private var grid: [[MazeCell]] = []
    private var currentRow = 0
    private var currentColumn = 0
    private var endRow = 0
    private var endColumn = 0
    private var ballView: UIImageView!
    private var holeView: UIImageView!
    private var loaded = false
    private var views: [UIView] = []
    private var gridLayout: MyGridView!
    private var motionManager: CMMotionManager?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        monitorSensors()
        backgroundColor = UIColor.color(hex: "#FFCEFF")        
        
        let mazeContainer = UIImageView()
        mazeContainer.clipsToBounds = false
        mazeContainer.image = Utilities.SVGImage(named: "taptrung_lanbi_bg")
        view.addSubview(mazeContainer)
        mazeContainer.makeViewCenterAndKeep(ratio: 1)
        
        gridLayout = MyGridView()
        gridLayout.clipsToBounds = false
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
        mazeContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.88)
            make.height.equalTo(gridLayout.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        holeView = UIImageView(image: UIImage(named: "taptrung_lanbi_hole"))
        mazeContainer.addSubview(holeView)
        holeView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(holeView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        ballView = UIImageView(image: UIImage(named: "taptrung_lanbi_ball"))
        mazeContainer.addSubview(ballView)
        ballView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(ballView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Lifecycle
    func monitorSensors() {
                
        // Lock screen orientation to landscape
        UIDevice.current.setValue(UIInterfaceOrientation.landscapeLeft.rawValue, forKey: "orientation")
        
        // Initialize motion manager and accelerometer
        motionManager = CMMotionManager()
        if let motionManager = motionManager, motionManager.isAccelerometerAvailable {
            motionManager.accelerometerUpdateInterval = 1.0 / 60.0 // 60 Hz
            motionManager.startAccelerometerUpdates(to: .main) { [weak self] (data, error) in
                guard let self = self else {
                    return
                }
                guard let data = data else {
                    return
                }
                guard self.loaded else {
                    return
                }
                
                // Adjust axes based on orientation
                var x = data.acceleration.x
                var y = data.acceleration.y
                let orientation = UIDevice.current.orientation
                switch orientation {
                case .landscapeLeft:
                    x = -data.acceleration.y
                    y = data.acceleration.x
                case .landscapeRight:
                    x = data.acceleration.y
                    y = -data.acceleration.x
                case .portrait:
                    x = data.acceleration.x
                    y = data.acceleration.y
                case .portraitUpsideDown:
                    x = -data.acceleration.x
                    y = -data.acceleration.y
                default:
                    break
                }
                
                var deltaX = 0
                var deltaY = 0
                
                if abs(x) > THRESHOLD {
                    deltaX = x > 0 ? -1 : 1
                }
                if abs(y) > THRESHOLD {
                    deltaY = y > 0 ? -1 : 1
                }
                
                if deltaX != 0 || deltaY != 0 {
                    if deltaX != 0 && deltaY != 0 {
                        if abs(x) > abs(y) {
                            deltaY = 0
                        } else {
                            deltaX = 0
                        }
                    }
                    self.updateBallPosition(deltaColumn: deltaX, deltaRow: -deltaY)
                }
            }
        }
    }
    
    func stopMonitorSensors() {
        
        // Stop accelerometer updates
        motionManager?.stopAccelerometerUpdates()
        motionManager = nil
        
        // Unlock screen orientation
        UIDevice.current.setValue(UIInterfaceOrientation.unknown.rawValue, forKey: "orientation")
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        ballView.alpha = 0.01
        holeView.alpha = 0.01
        
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            
            // Initialize maze
            endRow = mazeRows - 1
            endColumn = mazeColumns - 1
            grid = (0..<mazeRows).map { _ in (0..<self.mazeColumns).map { _ in MazeCell() } }
            generateMaze(x: 0, y: 0)
            
            // Create maze cells
            views = []
            for x in 0..<mazeRows {
                for y in 0..<mazeColumns {
                    let cell = grid[x][y]
                    let view = UIView()
                    view.clipsToBounds = false
                    
                    let centerView = UIView()
                    centerView.stringTag = "center_view"
                    view.addSubview(centerView)
                    centerView.snp.makeConstraints { make in
                        make.width.equalToSuperview().multipliedBy(0.9)
                        make.height.equalTo(centerView.snp.width) // Ratio 1:1
                        make.center.equalToSuperview()
                    }
                    
                    if cell.rightWall {
                        let rightView = RoundedView()
                        rightView.stringTag = "right_view"
                        rightView.backgroundColor = UIColor.color(hex: "#FF9FC4")
                        view.addSubview(rightView)
                        rightView.snp.makeConstraints { make in
                            make.width.equalToSuperview().multipliedBy(0.1)
                            make.height.equalToSuperview().multipliedBy(1.1)
                            make.left.equalTo(centerView.snp.right)
                            make.centerY.equalToSuperview()
                        }
                    }
                    
                    if cell.bottomWall {
                        let bottomView = RoundedView()
                        bottomView.stringTag = "bottom_view"
                        bottomView.backgroundColor = UIColor.color(hex: "#FF9FC4")
                        view.addSubview(bottomView)
                        bottomView.snp.makeConstraints { make in
                            make.width.equalToSuperview().multipliedBy(1.1)
                            make.height.equalToSuperview().multipliedBy(0.1)
                            make.top.equalTo(centerView.snp.bottom)
                            make.centerX.equalToSuperview()
                        }
                    }
                    
                    views.append(view)
                }
            }
            
            // Update UI on main thread
            DispatchQueue.main.async { [weak self] in
                guard let self = self else { return }
                gridLayout.columns = mazeColumns
                gridLayout.itemRatio = 1
                gridLayout.itemSpacingRatio = 0
                gridLayout.insetRatio = 0
                gridLayout.reloadItemViews(views: views)
                
                scheduler.schedule(after: 0.1) { [weak self] in
                    guard let self = self else { return }
                    let dest = getCell(row: currentRow, column: currentColumn).viewWithStringTag("center_view")!
                    let holeDest = getCell(row: endRow, column: endColumn).viewWithStringTag("center_view")!
                    self.ballView.moveToCenter(of: dest, duration: 0.01) {
                        [weak self]  _ in
                        guard let self = self else { return }
                        self.ballView.alpha = 1
                    }
                    self.holeView.moveToCenter(of: holeDest, duration: 0.01) {
                        [weak self] _ in
                        guard let self = self else { return }
                        self.holeView.alpha = 1
                    }
                                        
                    self.loaded = true
                }
            }
        }
        
        let delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_lan bi me cung"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_lan bi me cung")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func BackgroundMusicName() -> String? {
        return nil
    }
    
    // MARK: - Helper Methods
    private func getCell(row: Int, column: Int) -> UIView {
        return views[row * mazeColumns + column]
    }
    
    private func updateBallPosition(deltaColumn: Int, deltaRow: Int) {
        guard !busy, deltaRow == 0 || deltaColumn == 0 else { return }
        
        let newRow = currentRow + deltaRow
        let newColumn = currentColumn + deltaColumn
        
        var twoStepValid = true
        let twoStepRow = currentRow + 2 * deltaRow
        let twoStepColumn = currentColumn + 2 * deltaColumn
        if twoStepRow >= 0 && twoStepRow < mazeRows && twoStepColumn >= 0 && twoStepColumn < mazeColumns {
            let newCell = grid[newRow][newColumn]
            if (deltaColumn == 1 && !newCell.rightWall) ||
               (deltaColumn == -1 && !newCell.leftWall) ||
               (deltaRow == 1 && !newCell.bottomWall) ||
               (deltaRow == -1 && !newCell.topWall) {
                // Valid
            } else {
                twoStepValid = false
            }
        } else {
            twoStepValid = false
        }
        
        if newRow >= 0 && newRow < mazeRows && newColumn >= 0 && newColumn < mazeColumns {
            let cell = grid[currentRow][currentColumn]
            if (deltaColumn == 1 && !cell.rightWall) ||
               (deltaColumn == -1 && !cell.leftWall) ||
               (deltaRow == 1 && !cell.bottomWall) ||
               (deltaRow == -1 && !cell.topWall) {
                busy = true
                currentRow = newRow
                currentColumn = newColumn
                
                if !twoStepValid {
                    playSound("effect/ball_tap")
                    scheduler.schedule(after: 0.5) {
                        Utils.vibrate()
                    }
                }
                
                let dest = getCell(row: newRow, column: newColumn).viewWithStringTag("center_view")!
                playSound("effect/ball_rolling")
                UIView.animate(withDuration: 0.5, animations: {
                    self.ballView.center = dest.center
                }, completion: { _ in
                    if self.currentRow == self.endRow && self.currentColumn == self.endColumn {
                        self.animateCoinIfCorrect(view: dest)
                        let delay = self.playSound(delay: 0, names: [
                            "effect/word puzzle drop",
                            self.endGameSound(),
                            self.getCorrectHumanSound(),
                            self.endGameSound()
                        ])
                        UIView.animate(withDuration: 0.2) {
                            self.ballView.transform = CGAffineTransform(scaleX: 0.3, y: 0.3).rotated(by: 360.degreesToRadians)
                            self.ballView.alpha = 0
                        }
                        self.scheduler.schedule(after: delay) { [weak self] in
                            self?.finishGame()
                        }
                    } else {
                        self.busy = false
                    }
                })
            }
        }
    }
    
    private func generateMaze(x: Int, y: Int) {
        grid[x][y].visited = true
        
        let dx = [-1, 1, 0, 0]
        let dy = [0, 0, -1, 1]
        var dirs = [0, 1, 2, 3]
        dirs.shuffle()
        
        for dir in dirs {
            let nx = x + dx[dir]
            let ny = y + dy[dir]
            
            if nx >= 0 && nx < mazeRows && ny >= 0 && ny < mazeColumns && !grid[nx][ny].visited {
                if dir == 0 {
                    grid[x][y].topWall = false
                    grid[nx][ny].bottomWall = false
                } else if dir == 1 {
                    grid[x][y].bottomWall = false
                    grid[nx][ny].topWall = false
                } else if dir == 2 {
                    grid[x][y].leftWall = false
                    grid[nx][ny].rightWall = false
                } else if dir == 3 {
                    grid[x][y].rightWall = false
                    grid[nx][ny].leftWall = false
                }
                generateMaze(x: nx, y: ny)
            }
        }
    }
    
    // MARK: - RoundedView
    class RoundedView: UIView {
        override func layoutSubviews() {
            super.layoutSubviews()
            layer.cornerRadius = min(bounds.width,bounds.height) / 2
            layer.masksToBounds = true
        }
    }
    
    // MARK: - MazeCell
    class MazeCell {
        var topWall = true
        var bottomWall = true
        var leftWall = true
        var rightWall = true
        var visited = false
    }
    
    deinit{
        stopMonitorSensors()
    }
}


