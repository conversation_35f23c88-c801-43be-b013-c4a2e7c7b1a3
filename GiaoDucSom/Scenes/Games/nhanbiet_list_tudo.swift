//
//  nhanbiet_list_tudo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_tudo: NhanBietGameFragment {
    // MARK: - Properties
    private var door1: UIButton!
    private var door2: UIButton!
    private var door3: UIButton!
    private var door1_2: UIImageView!
    private var door2_2: UIImageView!
    private var door3_2: UIImageView!
    private var textView1: HeightRatioTextView!
    private var textView2: HeightRatioTextView!
    private var textView3: HeightRatioTextView!
    private var svgView1: SVGImageView!
    private var svgView2: SVGImageView!
    private var svgView3: SVGImageView!
    private var meIndex: Int = 0
    private var items: [Item] = []
    let coinView = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_storage"))!
        bgImage.contentMode = .scaleToFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.74)
            make.center.equalToSuperview()
        }
        
        let doorContainer = UIView()
        doorContainer.backgroundColor = UIColor(hex: "#186193")
        doorContainer.clipsToBounds = false
        mainContainer.addSubview(doorContainer)
        doorContainer.snp.makeConstraints { make in
            make.width.equalTo(doorContainer.snp.height).multipliedBy(2) // Ratio 2:1
            make.left.right.bottom.equalToSuperview()
        }
        
        let door1Container = UIImageView()
        door1Container.isUserInteractionEnabled = true
        door1Container.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_2")
        doorContainer.addSubview(door1Container)
        door1Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.9)
            //make.left.equalToSuperview()
            //make.centerX.equalToSuperview().multipliedBy(0.08) // Horizontal bias 0.04
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            door1Container.snapToHorizontalBias(horizontalBias: 0.04)
        }
        
        svgView1 = SVGImageView(frame: .zero)
        svgView1.stringTag = "svg_view_1"
        door1Container.addSubview(svgView1)
        svgView1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgView1.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        door1 = UIButton()
        door1.stringTag = "0"
        door1.setImage(Utilities.SVGImage(named: "nhanbiet_bg_storage2_3"), for: .normal)
        door1.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        door1Container.addSubview(door1)
        door1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        door1_2 = UIImageView()
        door1_2.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_1")
        door1_2.alpha = 0.01
        //door1_2.transform = CGAffineTransform(scaleX: 0, y: 1)
        door1Container.addSubview(door1_2)
        door1_2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        textView1 = HeightRatioTextView()
        textView1.setHeightRatio(0.1)
        textView1.stringTag = "text_view_1"
        textView1.textColor = UIColor(hex: "#C0DEFF")
        textView1.textAlignment = .center
        textView1.font = UIFont(name: "SVN-Freude", size: 100)
        textView1.adjustsFontSizeToFitWidth = true
        textView1.minimumScaleFactor = 0.1
        textView1.numberOfLines = 1
        door1Container.addSubview(textView1)
        textView1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.98)
            make.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        let door2Container = UIImageView()
        door2Container.isUserInteractionEnabled = true
        door2Container.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_2")
        doorContainer.addSubview(door2Container)
        door2Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        svgView2 = SVGImageView(frame: .zero)
        svgView2.stringTag = "svg_view_2"
        door2Container.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgView2.snp.width)
            make.center.equalToSuperview()
        }
        
        door2 = UIButton()
        door2.stringTag = "1"
        door2.setImage(Utilities.SVGImage(named: "nhanbiet_bg_storage2_3"), for: .normal)
        door2.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        door2Container.addSubview(door2)
        door2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        door2_2 = UIImageView()
        door2_2.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_1")
        door2_2.alpha = 0.01
        //door2_2.transform = CGAffineTransform(scaleX: 0.01, y: 1)
        door2Container.addSubview(door2_2)
        door2_2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        textView2 = HeightRatioTextView()
        textView2.setHeightRatio(0.1)
        textView2.stringTag = "text_view_2"
        textView2.textColor = UIColor(hex: "#C0DEFF")
        textView2.textAlignment = .center
        textView2.font = UIFont(name: "SVN-Freude", size: 100)
        textView2.adjustsFontSizeToFitWidth = true
        textView2.minimumScaleFactor = 0.1
        textView2.numberOfLines = 1
        door2Container.addSubview(textView2)
        textView2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.98)
            make.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        let door3Container = UIImageView()
        door3Container.isUserInteractionEnabled = true
        door3Container.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_2")
        doorContainer.addSubview(door3Container)
        door3Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.9)
            //make.right.equalToSuperview()
            //make.centerX.equalToSuperview().multipliedBy(1.92) // Horizontal bias 0.96
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            door3Container.snapToHorizontalBias(horizontalBias: 0.96)
        }
        
        svgView3 = SVGImageView(frame: .zero)
        svgView3.stringTag = "svg_view_3"
        door3Container.addSubview(svgView3)
        svgView3.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgView3.snp.width)
            make.center.equalToSuperview()
        }
        
        door3 = UIButton()
        door3.stringTag = "2"
        door3.setImage(Utilities.SVGImage(named: "nhanbiet_bg_storage2_3"), for: .normal)
        door3.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        door3Container.addSubview(door3)
        door3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        door3_2 = UIImageView()
        door3_2.image = Utilities.SVGImage(named: "nhanbiet_bg_storage2_1")
        door3_2.alpha = 0.01
        //door3_2.transform = CGAffineTransform(scaleX: 0, y: 1)
        door3Container.addSubview(door3_2)
        door3_2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        textView3 = HeightRatioTextView()
        textView3.setHeightRatio(0.1)
        textView3.stringTag = "text_view_3"
        textView3.textColor = UIColor(hex: "#C0DEFF")
        textView3.textAlignment = .center
        textView3.font = UIFont(name: "SVN-Freude", size: 100)
        textView3.adjustsFontSizeToFitWidth = true
        textView3.minimumScaleFactor = 0.1
        textView3.numberOfLines = 1
        door3Container.addSubview(textView3)
        textView3.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.98)
            make.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(), listItems.count >= 3,
              listItems.allSatisfy({ $0.path != nil && !$0.path!.isEmpty }) else { return }
        
        items = listItems.shuffled()
        
        svgView1.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(items[0].path!)").uiImage
        svgView2.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(items[1].path!)").uiImage
        svgView3.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(items[2].path!)").uiImage
        
        let language = getLanguage() ?? "vi"
        textView1.text = " \(language == "vi" ? items[0].name.vi! : items[0].name.en!) "
        textView2.text = " \(language == "vi" ? items[1].name.vi! : items[1].name.en!) "
        textView3.text = " \(language == "vi" ? items[2].name.vi! : items[2].name.en!) "
        
        meIndex = Int.random(in: 0..<3)
        
        var delay: TimeInterval = 0
        if language == "en" {
            let article = ArticleHelper.getArticle(items[meIndex].name.en ?? "")
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(language)/nhanbiet/nhanbiet_door_\(article)",
                "\(language)/topics/\(folder)/\(items[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        } else {
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(language)/nhanbiet/nhanbiet_door",
                "\(language)/topics/\(folder)/\(items[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        }
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let folder = getFolder(), items.count > meIndex else { return }
            let language = getLanguage() ?? "vi"
            var delay: TimeInterval = 0
            if language == "en" {
                let article = ArticleHelper.getArticle(items[meIndex].name.en ?? "")
                delay += playSound(delay: delay, names: [
                    "\(language)/nhanbiet/nhanbiet_door_\(article)",
                    "\(language)/topics/\(folder)/\(items[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            } else {
                delay += playSound(delay: delay, names: [
                    "\(language)/nhanbiet/nhanbiet_door",
                    "\(language)/topics/\(folder)/\(items[meIndex].path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIControl) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == meIndex {
            animateCoinIfCorrect(view: coinView)
            playSound("effect/door_open")
            openDoor(index: index) { [weak self] in
                guard let self = self else { return }
                let delay = self.playSound(delay: 0, names: ["effect/answer_end", self.getCorrectHumanSound(), self.endGameSound()])
                self.scheduler.schedule(after: delay) { [weak self] in
                    self?.finishGame()
                }
            }
        } else {
            setGameWrong()
            playSound("effect/door_open")
            openDoor(index: index) { [weak self] in
                guard let self = self else { return }
                var delay = self.playSound("effect/answer_wrong")
                delay += 1.0
                self.scheduler.schedule(after: delay) { [weak self] in
                    guard let self = self else { return }
                    self.playSound("effect/door_close")
                    self.closeDoor(index: index) { [weak self] in
                        self?.resumeGame(startMusic: false)
                    }
                }
            }
        }
    }
    
    // MARK: - Helper Methods
    private func openDoor(index: Int, completion: @escaping () -> Void) {
        
        let doors = [door1, door2, door3]
        let doors2 = [door1_2, door2_2, door3_2]
        let textViews = [textView1, textView2, textView3]
        
        var frame = doors[index]!.frame
        doors[index]!.layer.anchorPoint = CGPoint(x: 0, y: 0.5)
        doors[index]!.frame = frame
        
        frame = doors2[index]!.frame
        doors2[index]!.layer.anchorPoint = CGPoint(x: 0, y: 0.5)
        doors2[index]!.frame = frame
        doors2[index]?.transform = CGAffineTransformMakeScale(0.01, 1)
        doors2[index]?.alpha = 1
        
        frame = textViews[index]!.frame
        textViews[index]!.layer.anchorPoint = CGPoint(x: 0, y: 0.5)
        textViews[index]!.frame = frame
        
        
        UIView.animate(withDuration: 0.5, delay: 0, options: .curveLinear, animations: {
            doors[index]!.transform = CGAffineTransform(scaleX: 0.01, y: 1)
            textViews[index]!.transform = CGAffineTransform(scaleX: 0.01, y: 1)
        }, completion: { _ in
            UIView.animate(withDuration: 0.3, delay: 0, options: .curveLinear, animations: {
                doors2[index]!.transform = CGAffineTransform(scaleX: -1, y: 1)
            }, completion: { _ in
                completion()
            })
        })
    }
    
    private func closeDoor(index: Int, completion: @escaping () -> Void) {
        let doors = [door1, door2, door3]
        let doors2 = [door1_2, door2_2, door3_2]
        let textViews = [textView1, textView2, textView3]
        
        UIView.animate(withDuration: 0.3, animations: {
            doors2[index]!.transform = CGAffineTransform(scaleX: 0.01, y: 1)
        }, completion: { _ in
            UIView.animate(withDuration: 0.5, animations: {
                doors[index]!.transform = .identity
                textViews[index]!.transform = .identity
            }, completion: { _ in
                completion()
            })
        })
    }
}
