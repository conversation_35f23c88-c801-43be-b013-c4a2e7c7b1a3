//
//  toancoban_list_taosomaxmin.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_taosomaxmin: NhanBietGameFragment {
    // MARK: - Properties
    private var textResult: UILabel!
    private var gridLayout: MyGridView!
    private var bottomGridLayout: MyGridView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var bottomViews: [UIView] = []
    private var snapViews: [UIView?] = [nil, nil]
    private var max: Bool = false
    private var orderNumbers: [Int] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        gridLayout = MyGridView()
        gridLayout.isUserInteractionEnabled = true
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        self.addGestureRecognizer(panGesture)
        
        bottomGridLayout = MyGridView()
        bottomGridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        addSubview(bottomGridLayout)
        bottomGridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        textResult = AutosizeLabel()
        textResult.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.height.equalTo(textResult.snp.width).multipliedBy(0.8) // HeightRatio 0.8
            make.width.equalTo(view).multipliedBy(0.1)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.97) // Bias 0.485
        }
        self.bringSubviewToFront(gridLayout)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        max = Bool.random()
        textResult.text = max ? "max" : "min"
        
        let _numbers = (1...9).shuffled().take(count: 3)
        var views: [UIView] = []
        for i in 0..<_numbers.count {
            let view = createItemNumber(number: _numbers[i])
            view.tag = _numbers[i]
            views.append(view)
        }
        
        gridLayout.columns = 3
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        bottomViews = []
        views = []
        for _ in 0..<2 {
            let view = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
            view.contentMode = .scaleAspectFit
            views.append(view)
            bottomViews.append(view)
        }
        bottomGridLayout.columns = 3
        bottomGridLayout.itemRatio = 1
        bottomGridLayout.itemSpacingRatio = 0.1
        bottomGridLayout.insetRatio = 0.1
        bottomGridLayout.reloadItemViews(views: views)
        
        orderNumbers = _numbers.sorted()
        
        let delay = playSound(openGameSound(), max ? "toan/toan_tao so max min" : "toan/toan_tao so max min_min")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(max ? "toan/toan_tao so max min" : "toan/toan_tao so max min_min")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: gridLayout)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                for i in 0..<snapViews.count {
                    if snapViews[i] == currentView {
                        snapViews[i] = nil
                    }
                }
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
                playSound("effect/cungchoi_pick\(random(1,2))")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let tran = gesture.translation(in: self)
                currentView.transform.tx += tran.x
                currentView.transform.ty += tran.y
                gesture.setTranslation(.zero, in: self)
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for view in bottomViews {
                    let distance = currentView.distanceFromCenterToCenter(to: view)
                    let d = hypot(distance.x, distance.y)
                    if d < minDistance {
                        minDistance = d
                        targetView = view
                    }
                }
                
                if let target = targetView, minDistance < currentView.frame.width / 2 {
                    playSound("effect/word puzzle drop")
                    let index = bottomViews.firstIndex(of: target)!
                    if let snapView = snapViews[index] {
                        UIView.animate(withDuration: 0.3) {
                            snapView.transform = .identity
                        }
                    }
                    snapViews[index] = currentView
                    currentView.moveToCenter(of: target, duration: 0.2) {
                        [weak self] _ in
                        guard let self = self else { return }
                        self.dropDone()
                    }
                } else {
                    UIView.animate(withDuration: 0.3) {
                        currentView.transform = .identity
                    }
                    playSound("effect/slide2")
                    self.currentView = nil
                }
            }
            
        default:
            break
        }
    }
    private func dropDone(){
        if let snapView0 = self.snapViews[0], let snapView1 = self.snapViews[1] {
            let value1 = snapView0.tag
            let value2 = snapView1.tag
            let value = value1 * 10 + value2
            let targetValue = self.max ?
                (self.orderNumbers[self.orderNumbers.count - 1] * 10 + self.orderNumbers[self.orderNumbers.count - 2]) :
                (self.orderNumbers[0] * 10 + self.orderNumbers[1])
            let correct = value == targetValue
            if correct {
                self.pauseGame()
                self.animateCoinIfCorrect(view: self.textResult)
                let delay = self.playSound(self.finishEndSounds())
                self.scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                self.pauseGame()
                self.setGameWrong()
                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                for i in 0..<self.gridLayout.subviews.count {
                    let view = self.gridLayout.subviews[i]
                    UIView.animate(withDuration: 0.3, delay: 0.1 + 0.1 * Double(i)) {
                        view.transform = .identity
                    }
                }
                self.snapViews = [nil, nil]
                self.scheduler.schedule(delay: delay) { [weak self] in
                    self?.resumeGame()
                }
            }
        }
    }
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            if x >= child.frame.minX && x <= child.frame.maxX && y >= child.frame.minY && y <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func createItemNumber(number: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(number)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = number
        return container
    }
}

