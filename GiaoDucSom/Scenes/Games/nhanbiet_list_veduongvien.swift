//
//  nhanbiet_list_veduongvien.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics
// import CoreAnimation


// MARK: - TracingGameFragment
class nhanbiet_list_veduongvien: NhanBietGameFragment {
    // MARK: - Properties
    private var svg: SVGKImage?
    private var shadowSvg: SVGKImage?
    private var svgShadowView: TracingView!
    private var svgView: SVGImageView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_cloud"))!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.makeViewCenterFillAndKeep(ratio: 2688.0/1236.0)
        
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        svgShadowView = TracingView()
        svgShadowView.stringTag = "svg_shadow_view"
        svgShadowView.setOnTracingListener { [weak self] in
            self?.onTracingDone()
        }
        itemContainer.addSubview(svgShadowView)
        svgShadowView.makeViewCenterAndKeep(ratio: 1)
        
        svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view"
        svgView.isHidden = true
        itemContainer.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let folder = getFolder(), let item = getItem(), let path = item.path, !path.isEmpty else { return }
        
        svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(path)").uiImage
        
        let language = getLanguage() ?? "vi"
        let soundPath = language == "vi" ? "\(language)/nhanbiet/nhanbiet_tracing" :
                                          "\(language)/nhanbiet/nhanbiet_tracing_\(ArticleHelper.getArticle(item.name.en ?? ""))"
        let delay = playSound(delay: 0, names: [openGameSound(), soundPath, itemSound()!])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let language = getLanguage() ?? "vi"
            let soundPath = language == "vi" ? "\(language)/nhanbiet/nhanbiet_tracing" :
            "\(language)/nhanbiet/nhanbiet_tracing_\(ArticleHelper.getArticle(getItem()!.name.en ?? ""))"
            let delay = playSound(delay: 0, names: [soundPath, itemSound()!])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        loadSVG()
    }
    
    // MARK: - Helper Methods
    private func loadSVG() {
        guard let folder = getFolder(), let item = getItem(), let path = item.path else { return }
        shadowSvg = Utilities.GetSVGKImage(named: "topics/\(folder)/shadow/\(path)")
        if let svg = shadowSvg, svg.caLayerTree.sublayers!.count > 0 {
            svgShadowView.setSvg(svg)
        } else if BuildConfig.DEBUG {
            shadowSvg = Utilities.GetSVGKImage(named: "topics/Foods/salad.svg")
            if let svg = shadowSvg {
                svgShadowView.setSvg(svg)
            }
        }
    }
    
    private func onTracingDone() {
        pauseGame(stopMusic: false)
        let delay = playSound(delay: 0, names: ["effect/answer_correct", getCorrectHumanSound(), itemSound()!, endGameSound()])
        svgView.isHidden = false
        UIView.animate(withDuration: 0.3) {
            self.svgView.alpha = 1
            self.svgShadowView.alpha = 0
        }
        animateCoinIfCorrect(view: svgView)
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
}


// MARK: - TracingView
class TracingView: UIView {
    // MARK: - Properties
    private var player: AVAudioPlayer?
    private var onTracingDone: (() -> Void)?
    private var dashedPaint = Paint()
    private var paint = Paint()
    private var paint2 = Paint()
    private var dashed2Paint = Paint()
    private var points: [CGPoint] = []
    private var leftPoints: [CGPoint] = []
    private var paths: [CGPath] = []
    private var newPath: CGMutablePath?
    private var newPathJustCreated: Bool = false
    private var startPoint: CGPoint?
    private var startIndex: Int = 0
    private var svg: SVGKImage?
    private var path: CGPath?
    private var path2: CGMutablePath?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
        //wantsLayer = true
        //dashedPaint.style = .stroke
        dashedPaint.strokeColor = UIColor.white.cgColor
        dashedPaint.lineCap = .round
        dashedPaint.lineJoin = .round
        
        //paint.style = .stroke
        paint.strokeColor = UIColor(hex: "#FFB924").cgColor
        paint.lineCap = .round
        paint.lineJoin = .round
        
        //dashed2Paint.style = .stroke
        dashed2Paint.strokeColor = UIColor.red.cgColor
        dashed2Paint.lineCap = .round
        dashed2Paint.lineJoin = .round
        
        //paint2.style = .stroke
        paint2.strokeColor = UIColor(hex: "#74B6FF").cgColor
        paint2.lineCap = .round
        paint2.lineJoin = .round
        
        playWritingSound()
    }
    
    // MARK: - Public Methods
    func setOnTracingListener(_ listener: @escaping () -> Void) {
        self.onTracingDone = listener
    }
    var scale = 1.0
    func setSvg(_ svg: SVGKImage) {
        self.svg = svg
        let tenDp = bounds.width / 50
        dashedPaint.lineDashPattern = [0.5 * tenDp, 2 * tenDp]
        dashed2Paint.lineDashPattern = [0.2 * tenDp, tenDp]
        scale = frame.width / svg.size.width
        if let svgPath = svg.caLayerTree.sublayers?[0] as? CAShapeLayer, let cgPath = svgPath.path {
            let point = svgPath.convert( CGRect.zero, to: svg.caLayerTree)
            let bounds = svgPath.shapeContentBounds
            path = cgPath
            
            points = []
            
            points = self.getPoints(path: UIBezierPath(cgPath: path!))
            points = points.map{CGPoint(x: (point.minX+$0.x) * self.scale, y: (point.minY+$0.y) * self.scale)}
            
            
            let distance = tenDp / 5
            var currentDistance: CGFloat = 0
            path2 = CGMutablePath()
            for i in 0..<points.count {
                if i == 0 {
                    path2?.move(to: points[i])
                } else {
                    path2?.addLine(to: points[i])
                }
            }
            leftPoints = points
            setNeedsDisplay()
        }
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        if let path = path2 {
            
            context.addPath(path)
            context.setStrokeColor(paint.strokeColor)
            context.setLineWidth(paint.lineWidth)
            context.setLineCap(paint.lineCap)
            context.setLineJoin(paint.lineJoin)
            context.strokePath()
            
            
            context.addPath(path)
            context.setStrokeColor(dashedPaint.strokeColor)
            context.setLineWidth(dashedPaint.lineWidth)
            context.setLineCap(dashedPaint.lineCap)
            context.setLineJoin(dashedPaint.lineJoin)
            context.setLineDash(phase: 0, lengths: dashedPaint.lineDashPattern ?? [])
            context.strokePath()
                    
            context.setLineDash(phase: 0, lengths: [])
            
            for path in paths {
                context.addPath(path)
                context.setStrokeColor(paint2.strokeColor)
                context.setLineWidth(paint2.lineWidth)
                context.setLineCap(paint2.lineCap)
                context.setLineJoin(paint2.lineJoin)
                context.strokePath()
            }
            
            if let newPath = newPath {
                context.addPath(newPath)
                context.setStrokeColor(paint2.strokeColor)
                context.setLineWidth(paint2.lineWidth)
                context.setLineCap(paint2.lineCap)
                context.setLineJoin(paint2.lineJoin)
                context.strokePath()
            }
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            newPath = CGMutablePath()
            newPathJustCreated = true
            startPoint = nearest
            startIndex = points.firstIndex(of: nearest) ?? 0
            newPath?.move(to: nearest)
            setNeedsDisplay()
            player?.currentTime = 0
            player?.play()
            Utils.vibrate()
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            if newPath == nil {
                newPath = CGMutablePath()
                newPathJustCreated = true
                startPoint = nearest
                startIndex = points.firstIndex(of: nearest) ?? 0
                newPath?.move(to: nearest)
            } else {
                lineTo2(nearest)
            }
            setNeedsDisplay()
            if !(player?.isPlaying ?? false) {
                player?.play()
            }
        } else {
            player?.pause()
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = nil
            setNeedsDisplay()
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let newPath = newPath, !newPathJustCreated {
            paths.append(newPath)
        }
        self.newPath = nil
        let longestContinueLeftPoints = findLongestContinueLeftPoints()
        if longestContinueLeftPoints < 20 {
            onTracingDone?()
        }
        player?.pause()
        setNeedsDisplay()
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func nearestPoint(to touch: CGPoint) -> CGPoint? {
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        for point in points {
            let distance = hypot(touch.x - point.x, touch.y - point.y)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        return nearest
    }
    
    private func lineTo2(_ point: CGPoint) {
        guard let endIndex = points.firstIndex(of: point) else { return }
        if endIndex == startIndex { return }
        
        let max = Swift.max(startIndex, endIndex)
        let min = Swift.min(startIndex, endIndex)
        let distance = Swift.min(max - min, min + points.count - max)
        
        if distance > 10 {
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = CGMutablePath()
            self.newPath?.move(to: point)
            startIndex = endIndex
            newPathJustCreated = true
            return
        }
        
        if distance == max - min {
            if startIndex < endIndex {
                for i in (startIndex + 1)...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (endIndex...startIndex - 1).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        } else {
            if startIndex < endIndex {
                if startIndex > 0 {
                    for i in (0...(startIndex - 1)).reversed() {
                        newPath?.addLine(to: points[i])
                        leftPoints.removeAll { $0 == points[i] }
                    }
                }
                for i in (endIndex...(points.count - 1)).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (startIndex + 1)..<points.count {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
                for i in 0...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        }
        
        startIndex = endIndex
        newPathJustCreated = false
    }
    
    private func findLongestContinueLeftPoints() -> Int {
        var max = 0
        var count = 0
        for point in points {
            if leftPoints.contains(point) {
                count += 1
            } else {
                max = Swift.max(max, count)
                count = 0
            }
        }
        return Swift.max(max, count)
    }
    
    private func playWritingSound() {
        if let url = Bundle.main.url(forResource: "writing", withExtension: "mp3", subdirectory: "Sounds/effect") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.numberOfLoops = -1
                player?.prepareToPlay()
            } catch {
                if BuildConfig.DEBUG {
                    print(error)
                }
            }
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        paint.lineWidth = bounds.width / 16
        dashedPaint.lineWidth = bounds.width / 60
        paint2.lineWidth = bounds.width / 16
    }
    
    // MARK: - Helper Structs
    struct Paint {
        //var style: CGPaintingStyle = .stroke
        var strokeColor: CGColor = UIColor.black.cgColor
        var lineWidth: CGFloat = 1
        var lineCap: CGLineCap = .round
        var lineJoin: CGLineJoin = .round
        var lineDashPattern: [CGFloat]?
    }
    
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        var points: [CGPoint] = []
        let count = 500
        return path.evenlySpacedPointsUsingDash(count: count)
    }
}
