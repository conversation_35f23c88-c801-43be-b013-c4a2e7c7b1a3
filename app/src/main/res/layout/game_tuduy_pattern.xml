<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#8AE6FF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:splitMotionEvents="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1000:155">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:adjustViewBounds="true"
            android:src="@drawable/tuduy_pattern_bg"
            app:layout_constraintBottom_toBottomOf="parent" />

        <View
            android:id="@+id/btn_green"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01ff0000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.565"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/btn_yellow"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01ff0000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.68"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/btn_red"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01ff0000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
            android:id="@+id/animation_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="@id/guideline2"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.18"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.3" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/guideline2"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.55" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintDimensionRatio="9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#4A4A85"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintWidth_percent="0.8" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/lights_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:id="@+id/imageView21"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toStartOf="@+id/imageView20"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/imageView20"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light_red"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toStartOf="@+id/imageView22"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView21"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/imageView22"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light_yellow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toStartOf="@+id/imageView23"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView20"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/imageView23"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light_green"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toStartOf="@+id/imageView24"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView22"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/imageView24"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light_red"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toStartOf="@+id/imageView25"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView23"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/imageView25"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_pattern_light"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.3"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/imageView24"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/light_rotation"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_pattern_dot"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.65"
            app:layout_constraintHorizontal_bias="0.85"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.97" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>