//
//  tuduy_list_quyluathinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_quyluathinh: NhanBietGameFragment {
    // MARK: - Properties
    private var paths: [String] = []
    private var positions: [Int] = []
    private var hiddenPositions: [Int] = []
    private var leftGridLayout: MyGridView!
    private var gridLayout: MyGridView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        let viewBackgroud = SVGImageView(frame: .zero)
        viewBackgroud.SVGName = "tuduy_quyluat_bg"
        view.addSubview(viewBackgroud)
        viewBackgroud.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let centerGuideline = UIView()
        view.addSubview(centerGuideline)
        centerGuideline.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.top.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        let rightContainer = UIView()
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        let rightSquareView = UIView()
        rightContainer.addSubview(rightSquareView)
        rightSquareView.makeViewCenterAndKeep(ratio: 1)
        
        let gridBackgroud = SVGImageView(frame: .zero)
        gridBackgroud.SVGName = "tuduy_quyluat3_bg"
        rightSquareView.addSubviewWithPercentInset(subview: gridBackgroud, percentInset: 5)
        
        gridLayout = MyGridView()
        rightSquareView.addSubviewWithPercentInset(subview: gridLayout, percentInset: 5)
                
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        let leftSquareView = UIView()
        leftContainer.addSubview(leftSquareView)
        leftSquareView.makeViewCenterAndKeep(ratio: 1)
        
        leftGridLayout = MyGridView()
        leftSquareView.addSubviewWithPercentInset(subview: leftGridLayout, percentInset: 5)
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        leftGridLayout.addGestureRecognizer(panGesture)
        
        coinView = UIView()
        view.addSubview(coinView)
        coinView.isUserInteractionEnabled = false
        coinView.snp.makeConstraints { make in
            make.width.height.equalTo(view).multipliedBy(0.3)
            make.width.equalTo(coinView.snp.height) // Ratio 1:1
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "tuduy/tuduy_quy luat 3")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        paths = []
        let folders = FlashcardsManager.shared.getPacks().shuffled()
        for folder in folders {
            let items = folder.items.shuffled()
            for item in items {
                if let foodLike = item.foodLike, foodLike.contains("topics/"),
                   let foodUnlike = item.foodUnlike, foodUnlike.contains("topics/") {
                    paths.append("topics/\(folder.folder)/\(item.path!)")
                    paths.append("\(foodLike)")
                    paths.append("\(foodUnlike)")
                    break
                }
            }
            if paths.count == 3 { break }
        }
        let svgList = paths.map { Utilities.GetSVGKImage(named: $0) }
        self.positions = self.makeList()
        while true {
            self.hiddenPositions = Utils.generatePermutation(3, size: self.positions.count)
            if self.positions[self.hiddenPositions[0]] == self.positions[self.hiddenPositions[1]] &&
               self.positions[self.hiddenPositions[1]] == self.positions[self.hiddenPositions[2]] {
                continue
            }
            break
        }
        
        var views: [UIView] = []
        for i in 0..<self.positions.count {
            let position = self.positions[i]
            let view = self.createItemQuyLuat3(svg: svgList[position], hidden: self.hiddenPositions.contains(i))
            if self.hiddenPositions.contains(i) {
                view.tag = 100 + position
            }
            views.append(view)
        }
        self.gridLayout.columns = 3
        self.gridLayout.itemRatio = 1
        self.gridLayout.itemSpacingRatio = 0.03
        self.gridLayout.insetRatio = 0.05
        self.gridLayout.reloadItemViews(views: views)
        
        let leftPositions = [[1, 4, 7], [3, 4, 5], [0, 4, 8], [2, 4, 6]].shuffled()[0].shuffled()
        views = []
        for i in 0..<self.positions.count {
            let show = leftPositions.contains(i)
            let view = self.createItemQuyLuat3(svg: show ? svgList[self.positions[self.hiddenPositions[leftPositions.firstIndex(of: i)!]]] : nil, hidden: false)
            if show {
                view.tag = 100 + self.positions[self.hiddenPositions[leftPositions.firstIndex(of: i)!]]
            } else {
                view.isHidden = true
            }
            views.append(view)
        }
        self.leftGridLayout.columns = 3
        self.leftGridLayout.itemRatio = 1
        self.leftGridLayout.itemSpacingRatio = 0.03
        self.leftGridLayout.insetRatio = 0.05
        self.leftGridLayout.reloadItemViews(views: views)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_quy luat 3")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    var originX = 0.0, originY = 0.0
    var zPosition = 5.0
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: leftGridLayout)
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
                playSound("effect/cungchoi_pick\(random(1,2))")
                originX = currentView.frame.minX
                originY = currentView.frame.minY
                zPosition += 1
                currentView.layer.zPosition = zPosition
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = location.x + dX
                let newY = location.y + dY
                currentView.frame.origin = CGPoint(x: newX, y: newY)
            }
            
        case .ended:
            if let currentView = currentView {
                let svgThumbnail = currentView.subviews.first(where: { $0 is UIImageView }) as? UIImageView
                svgThumbnail?.isHidden = false
                
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for child in gridLayout.subviews where child.tag != 0 {
                    let dvector = currentView.distanceFromCenterToCenter(to: child)
                    let distance = hypot(dvector.x, dvector.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = child
                    }
                }
                
                if minDistance < currentView.frame.width / 2, let targetView = targetView, targetView.tag == currentView.tag {
                    targetView.tag = 0
                    playSound("effect/word puzzle drop")
                    currentView.moveToCenter(of: targetView, duration: 0.2) {
                        [weak self] _ in
                        guard let self = self else { return }
                        targetView.isHidden = true
                    }
                    let win = gridLayout.subviews.allSatisfy { $0.tag == 0 }
                    if win {
                        animateCoinIfCorrect(view: self.coinView)
                        self.pauseGame()
                        let delay = self.playSound("effect/answer_end", self.getCorrectHumanSound(), self.endGameSound())
                        self.scheduler.schedule(delay: delay) { [weak self] in
                            self?.finishGame()
                        }
                    } else {
                        playSound("effect/answer_correct")
                    }
                    self.currentView = nil
                } else {
                    setGameWrong()
                    UIView.animate(withDuration: 0.8) {
                        self.currentView?.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                        self.currentView?.transform = .identity
                    }
                    playSound("effect/slide2")
                    self.currentView = nil
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: Float, y: Float) -> UIView? {
        for i in (0..<leftGridLayout.subviews.count).reversed() {
            let child = leftGridLayout.subviews[i]
            if x >= Float(child.frame.minX) && x <= Float(child.frame.maxX) &&
               y >= Float(child.frame.minY) && y <= Float(child.frame.maxY) &&
               child.tag != 0 {
                return child
            }
        }
        return nil
    }
    
    private func createItemQuyLuat3(svg: SVGKImage?, hidden: Bool) -> UIView {
        let view = UIView()
        let viewBackground = UIImageView()
        viewBackground.contentMode = .scaleAspectFit
        viewBackground.image = Utilities.SVGImage(named: hidden ? "option_bg_white_shadow2" : "option_bg_white_shadow")
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let svgThumbnail = UIImageView()
        svgThumbnail.contentMode = .scaleAspectFit
        if let svg = svg {
            svgThumbnail.image = svg.uiImage
            svgThumbnail.isHidden = hidden
        }
        view.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.7)
            make.height.equalTo(svgThumbnail.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return view
    }
    
    private func makeList() -> [Int] {
        let index = Int.random(in: 0..<4)
        switch index {
        case 0: return [0, 1, 2, 0, 1, 2, 0, 1, 2]
        case 1: return [0, 0, 0, 1, 1, 1, 2, 2, 2]
        case 2: return [0, 1, 2, 2, 0, 1, 1, 2, 0]
        case 3: return [0, 1, 2, 1, 2, 0, 2, 0, 1]
        default: return [0, 0, 0, 1, 1, 1, 2, 2, 2]
        }
    }
}

// MARK: - Supporting Structures

/*
struct R {
    struct drawable {
        static let tuduy_quyluat_bg = 1
        static let tuduy_quyluat3_bg = 2
        static let option_bg_white_shadow = 3
        static let option_bg_white_shadow2 = 4
        static let item_quyluat3 = 5
    }
}
 */
/*
extension Int {
    func toDrawableName() -> String? {
        switch self {
        case R.drawable.tuduy_quyluat_bg:
            return "tuduy_quyluat_bg"
        case R.drawable.tuduy_quyluat3_bg:
            return "tuduy_quyluat3_bg"
        case R.drawable.option_bg_white_shadow:
            return "option_bg_white_shadow"
        case R.drawable.option_bg_white_shadow2:
            return "option_bg_white_shadow2"
        case R.drawable.item_quyluat3:
            return "item_quyluat3"
        default:
            return nil
        }
    }
}
*/
