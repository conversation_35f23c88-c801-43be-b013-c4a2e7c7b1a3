//
//  nhanbiet_list_chinhgocquay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_chinhgocquay: NhanBietGameFragment {
    // MARK: - Properties
    private var svg: SVGKImage?
    private var svgViewLeft: UIImageView!
    private var svgViewRight: UIImageView!
    private var viewSlider: SVGKFastImageView!
    private var bgSlider: SVGKFastImageView!
    private var initRotation: CGFloat = 0
    private var lastRotation: CGFloat = 0
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    private var player: AVAudioPlayer?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#35373F")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let ratioView = UIView()
        view.addSubview(ratioView)
        ratioView.makeViewCenterAndKeep(ratio: 1.5)
        
        bgSlider = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_rotation"))!
        bgSlider.clipsToBounds = false
        bgSlider.isUserInteractionEnabled = true
        bgSlider.contentMode = .scaleAspectFill
        ratioView.addSubview(bgSlider)
        bgSlider.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(bgSlider.snp.height).multipliedBy(189.0 / 729.0) // Ratio 189:729
            make.centerX.equalTo(ratioView.snp.right).multipliedBy(0.55)
            make.centerY.equalTo(ratioView.snp.bottom).multipliedBy(0.5)
        }
        
        viewSlider = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_btn_binoculars"))!
        bgSlider.addSubview(viewSlider)
        viewSlider.makeViewCenterAndKeep(ratio: 1)
        
        let leftContainer = SVGKFastImageView(svgkImage:  Utilities.GetSVGKImage(named: "bg_white_circle"))!
        leftContainer.clipsToBounds = false
        ratioView.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalTo(leftContainer.snp.width) // Ratio 1:1
            make.centerX.equalTo(ratioView.snp.right).multipliedBy(0.25)
            make.centerY.equalToSuperview()
        }
        
        svgViewLeft = UIImageView()
        svgViewLeft.backgroundColor = .clear
        leftContainer.addSubview(svgViewLeft)
        svgViewLeft.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.76)
            make.height.equalTo(svgViewLeft.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        svgViewRight = UIImageView()
        svgViewRight.backgroundColor = .clear
        ratioView.addSubview(svgViewRight)
        svgViewRight.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.304)
            make.height.equalTo(svgViewRight.snp.width) // Ratio 1:1
            make.centerX.equalTo(ratioView.snp.right).multipliedBy(0.8)
            make.centerY.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        bgSlider.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        svg = Utilities.GetSVGKImage(named: "topics/\(getFolder() ?? "")/\(getItem()?.path ?? "")")
        svgViewLeft.image = svg?.uiImage
        svgViewRight.image = svg?.uiImage
        initRotation = CGFloat.random(in: 0..<360)
        lastRotation = initRotation
        svgViewRight.transform = CGAffineTransform(rotationAngle: initRotation.degreesToRadians)
        
        let delay = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_rotation"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    var initY = 0.0
    override func createGame() {
        super.createGame()
        initY = viewSlider.frame.minY
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_rotation")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                let newX = currentView.frame.minX + translation.x
                var newY = currentView.frame.minY + translation.y
                
                newY = max(0, min(newY, bgSlider.frame.height - currentView.frame.height))
                currentView.frame = CGRect(
                    x: currentView.frame.minX,
                    y: newY,
                    width: currentView.frame.width,
                    height: currentView.frame.height
                )
                
                let rotation = ((currentView.frame.minY - initY) / (bgSlider.frame.height - viewSlider.frame.height)) / 0.5 * 180.0
                svgViewRight.transform = CGAffineTransform(rotationAngle: (initRotation + rotation).degreesToRadians)
                
                if abs(rotation - lastRotation) > 10 {
                    playGearSound()
                    Utils.vibrate()
                    lastRotation = rotation
                }
                
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if currentView != nil {
                let rotation = svgViewRight.transform.rotationAngleDegrees()
                if abs(rotation) < 10 || abs(rotation - 360) < 10 || abs(rotation + 360) < 10 {
                    animateCoinIfCorrect(view: viewSlider)
                    let delay = playSound(delay: 0, names: [
                        "effect/answer_end",
                        getCorrectHumanSound(),
                        endGameSound()
                    ])
                    UIView.animate(withDuration: 0.8, animations: {
                        self.svgViewRight.transform = CGAffineTransform(rotationAngle: (round(rotation / 360) * 360).degreesToRadians)
                            .scaledBy(x: 1.2, y: 1.2)
                    }, completion: { _ in
                        UIView.animate(withDuration: 0.4) {
                            self.svgViewRight.transform = CGAffineTransform(rotationAngle: (round(rotation / 360) * 360).degreesToRadians)
                        }
                    })
                    pauseGame(stopMusic: false)
                    scheduler.schedule(after: delay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    setGameWrong()
                    playSound("effect/answer_wrong")
                }
                self.currentView = nil
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<bgSlider.subviews.count).reversed() {
            let child = bgSlider.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func playGearSound() {
        if player == nil {
            if let url = Utilities.url(soundPath: "effect/gear2") {
                do {
                    player = try AVAudioPlayer(contentsOf: url)
                    player?.prepareToPlay()
                } catch {
                    print("Error loading gear2 sound: \(error)")
                }
            }
        }
        if let player = player {
            player.currentTime = 0
            player.play()
        }
    }
}
