//
//  phonics_list_beginningsound.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_beginningsound: GameFragment {
    private var values : [String] = []
    private var values1 : [String] = []
    var answerLayout = MyGridView()
    var bottomView = SVGImageView(SVGName: "btn bg yellow").then {
        $0.isUserInteractionEnabled = true
        $0.alpha = 0
    }
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#99B6C1")
    }
    var svgView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    var question = ""
    var questions : [String] = []
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#F4FAFF")
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#FFF5CC")
        let leftBg = UIView()
        leftBg.backgroundColor = .color(hex: "#FFF5CC")
        leftView.addSubview(leftBg)
        leftBg.snp.makeConstraints{ make in
            make.top.left.bottom.equalTo(self)
            make.right.equalToSuperview()
        }
        view.addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        let leftInnerView = UIView()
        leftView.addSubview(leftInnerView)
        leftInnerView.makeViewCenterAndKeep(ratio: 1.5)
            
        bottomView.isUserInteractionEnabled = true
        leftInnerView.addSubview(textName)
        leftInnerView.addSubview(bottomView)
        bottomView.snp.makeConstraints{ make in
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.height.equalTo(bottomView.snp.width).multipliedBy(151.0/400.0)
        }
        bottomView.addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(1)
            make.height.equalToSuperview().multipliedBy(1)
            make.center.equalToSuperview()
        }
        textName.snp.makeConstraints{ make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.35)
        }
        let rightView = UIView()
        rightView.backgroundColor = .color(hex: "#849BFD")
        let rightBgx = UIView()
        rightBgx.backgroundColor = .color(hex: "#849BFD")
        rightView.addSubview(rightBgx)
        rightBgx.snp.makeConstraints{ make in
            make.top.right.bottom.equalTo(self)
            make.left.equalToSuperview()
        }
        view.addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(leftView.snp.right)
        }
        let rightBg = SVGImageView(SVGName: "option_bg_white_shadow")
        rightView.addSubview(rightBg)
        rightBg.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalTo(rightBg.snp.width)
            make.center.equalToSuperview()
        }
        rightView.addSubview(svgView)
        svgView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgView.snp.width)
        }
        svgView.alpha = 0
        textName.alpha = 0
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: game.questions!)
        values1 = (game.values1?.compactMap { $0.value as? String })!.sorted { $0.count < $1.count }
        loadNextStep()
    }
    var step = 0
    var answer = ""
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            delay += self.playSound(name: answer.replacingOccurrences(of: "_", with: ""), delay: delay)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
   
    func loadNextStep(){
        CoinAnimationUtils.shared.removeList()
        if step >= values.count {
            var delay = 0.5
            delay += playSound(name: "effects/end game", delay: delay)
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
        step += 1
        var delay = 0.2
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.showImage()
        })
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: answer.replacingOccurrences(of: "_", with: ""), delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
            self.showBottom()
        })
        
                
        svgView.SVGName = "flashcards/\(game.level!)/\(answer.replacingOccurrences(of: "_", with: "")).svg"
        for s in values1 {
            if answer.starts(with: "\(s)_") {
                question = s
                break
            }
        }
        while true {
            var list = values1.randomOrder().take(count: 3)
            if list.contains(question) {
                questions = list
                break
            }
        }
        
        let attributedString = NSMutableAttributedString(string: "_\(answer.dropFirst(question.count+1))")
        let colorRange = NSRange(location: 0, length: 1)
        attributedString.addAttribute(.foregroundColor, value: UIColor.color(hex: "#FF7761"), range: colorRange)
        textName.attributedText = attributedString
        
        var listViews : [UIView] = []
        for i in 0..<questions.count {
            let view = createItem(text: questions[i])
            view.tag = 100 + i
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 1
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.03
        answerLayout.columns = 3
        answerLayout.reloadItemViews(views: listViews)
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
                delay += self.playSound(name: answer.replacingOccurrences(of: "_", with: ""), delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg yellow")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#68C1FF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        if answer.starts(with: "\(label.text!)_") {
            pauseGame()
            //sender.animateCoin(answer: true)
            animateCoinIfCorrect(view: sender)
            label.textColor = .color(hex: "#73D048")
            textName.textColor = .color(hex: "#73D048")
            textName.text = answer.replacingOccurrences(of: "_", with: "")
            playSound(name: "effects/cheer\(Int.random(in: 1...4))", delay: 0.5)
            scheduler.schedule(delay: 2.3, execute: {
                [weak self] in
                guard let self = self else { return }
                self.hideAll()
            })
            scheduler.schedule(delay: 3, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
                self.textName.textColor = .color(hex: "#99B6C1")
                self.resumeGame()
                self.loadNextStep()
            })
        } else {
            incorrect += 1
            setGameWrong()
            label.textColor = .color(hex: "#FF7761")
            playSound(name: label.text!, delay: 0.3)
            scheduler.schedule(delay: 0.7, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
            })
        }
    }
    func showBottom(){
        let delta = bottomView.bounds.height * 1.5
        bottomView.transform = CGAffineTransformMakeTranslation(0, delta)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [delta, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue)
            self.bottomView.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func hideAll(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -svgView.bounds.width * 1.2]
        let animValues2: [Double] = [0, self.bounds.height * 0.6 + 40.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.svgView.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            self.svgView.alpha = 1 - value
            self.textName.alpha = 1 - value
            self.textName.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue2)
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showImage(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [svgView.bounds.width * 1.2, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.svgView.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            self.textName.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            self.svgView.alpha = value
            self.textName.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}

