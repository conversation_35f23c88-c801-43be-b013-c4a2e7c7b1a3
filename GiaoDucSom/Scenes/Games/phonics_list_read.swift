//
//  phonics_list_read.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import Speech
import Reachability

class phonics_list_read: GameFragment {
    private var values : [String] = []
    var xamlWave = XAMLAnimationView()
    var xamlLoad = XAMLAnimationView()
    var textCenter = AutosizeLabel().then{
        $0.textColor = .color(hex: "#1497E0")
    }
    private var speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: "en-US")) //1
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()
    var lang: String = "en-US"
    var waveData: XAMLModel.UserControl?
    var loadData: XAMLModel.UserControl?
    var listening = false
    var timertimeout = TimeoutTimer()
    var timerLongtimeout = TimeoutTimer()
    var texts : [String] = []
    var meIndex = 0
    //var score = 0.0
    var centerView = UIView()
    var topText = AutosizeLabel()
    let reachability = try! Reachability()
    var internetConnected = false
    let leftContainer = UIView()
    let rightContainer = UIView()
    let imageReplay = SVGImageView(SVGName: "bot_btn_replay")
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(centerView)
        centerView.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.6)
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalToSuperview().multipliedBy(0.4)
        }
        //centerView.backgroundColor = .red
        var recordingBg = SVGImageView(SVGName: "recording bg").then{
            $0.contentMode = .scaleAspectFit
        }
        centerView.addSubviewWithInset(subview: recordingBg, inset: 0)
        centerView.addSubview(xamlWave)
        centerView.addSubview(xamlLoad)
        xamlWave.makeViewCenterAndKeep(ratio: 1)
        xamlLoad.makeViewCenterAndKeep(ratio: 1)
        let textContainer = UIView()
        addSubview(textCenter)
        textCenter.snp.makeConstraints{ make in
            make.center.equalTo(centerView)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(centerView).multipliedBy(0.3)
        }
        textCenter.text = ""
        
        xamlLoad.isHidden = true
        do {
            waveData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "recording wave")!)
            loadData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "recording load")!)
            xamlWave.loadView(from: waveData!)
            xamlLoad.loadView(from: loadData!)
        } catch {}
        timertimeout.duration = 1
        timertimeout.onActived = {
            [weak self] in
            guard let self = self else { return }
            if !self.listening {
                return
            }
            self.xamlWave.isHidden = true
            self.xamlLoad.isHidden = false;
            self.listening = false
            self.toggleStartStop()
            self.scheduler.schedule(delay: 2, execute: {
                [weak self] in
                guard let self = self else { return }
                self.xamlLoad.isHidden = true
                self.processData(text: self.texts[self.texts.count-1])
            })
        }
        timerLongtimeout.duration = 5
        timerLongtimeout.onActived = {
            [weak self] in
            guard let self = self else { return }
            if !self.listening {
                return
            }
            self.texts.append("")
            self.timertimeout.schedule()
        }
        
        
        
        leftContainer.clipsToBounds = false
        leftContainer.transform = CGAffineTransform(translationX: -100, y: 0)
        self.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.height.equalTo(100)
            make.left.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
        
        let leftBg = UIImageView(image: Utilities.SVGImage(named: "bot_bg_left"))
        leftBg.contentMode = .scaleAspectFit
        leftContainer.addSubview(leftBg)
        leftBg.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.height.equalTo(leftBg.snp.width).multipliedBy(401.0 / 628.0) // Ratio 628:401
        }
        
        let btnReplay = KUButton()
        
        imageReplay.contentMode = .scaleAspectFit
        btnReplay.addSubviewWithInset(subview: imageReplay, inset: 0)
        btnReplay.contentMode = .scaleAspectFit
        btnReplay.isUserInteractionEnabled = true
        leftContainer.addSubview(btnReplay)
        btnReplay.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.height.equalTo(btnReplay.snp.width)
        }
        
        
        rightContainer.clipsToBounds = false
        rightContainer.transform = CGAffineTransform(translationX: 100, y: 0)
        self.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.height.equalTo(100)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().offset(-20)
        }
        
        let rightBg = UIImageView(image: Utilities.SVGImage(named: "bot_bg_right"))
        rightBg.contentMode = .scaleAspectFit
        rightContainer.addSubview(rightBg)
        rightBg.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.height.equalTo(rightBg.snp.width).multipliedBy(401.0 / 628.0) // Ratio 628:401
        }
        
        let btnNext = KUButton()
        let imageNext = SVGImageView(SVGName: "bot_btn_next")
        imageNext.contentMode = .scaleAspectFit
        btnNext.addSubviewWithInset(subview: imageNext, inset: 0)
        btnNext.contentMode = .scaleAspectFit
        btnNext.isUserInteractionEnabled = true
        rightContainer.addSubview(btnNext)
        btnNext.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.height.equalTo(btnNext.snp.width) // Ratio 1:1
        }
        
        btnReplay.addTarget(self, action: #selector(replay), for: .touchUpInside)
        btnNext.addTarget(self, action: #selector(loadNext), for: .touchUpInside)
        
        
        addSubview(topText)
        topText.textColor = .color(hex: "#1497E0")
        topText.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.centerY.equalToSuperview().multipliedBy(1.5)
            make.height.equalToSuperview().multipliedBy(0.15)
        }
        topText.text = ""
        reachability.whenReachable = { [weak self] reachability in
            guard let self = self else { return }
            if reachability.connection == .wifi {
                print("Reachable via WiFi")
                self.internetConnected = true
                self.reloadRecordButton()
            } else {
                print("Reachable via Cellular")
                self.internetConnected = true
                self.reloadRecordButton()
            }
        }
        reachability.whenUnreachable = {[weak self] _ in
            guard let self = self else { return }
            print("Not reachable")
            self.internetConnected = false
            self.reloadRecordButton()
        }

        do {
            try reachability.startNotifier()
        } catch {
            print("Unable to start notifier")
        }
    }
    
    func reloadRecordButton(){
        var enableSpeech = false
        if #available(iOS 13, *) {
            enableSpeech = speechRecognizer?.supportsOnDeviceRecognition ?? false || internetConnected
        } else {
            enableSpeech = internetConnected
        }
        //leftButton.alpha = enableSpeech ? 1 : 0.5
        imageReplay.SVGName = enableSpeech ? "bot btn replay" :"bot btn replay2"
    }
    func processData(text:String){
        score = Float(self.getScore(text: text.lowercased(), answer: values[meIndex]))
        var intScore : Int = Int(round(score * 100))
        var duration = 1.2
        var step = duration / Double(intScore)
        var delay = 0.0
        self.playSound(name: "effects/score", delay: 0)
        for s in 0...intScore {
            delay += step
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.textCenter.text = "\(s)%"
            })
        }
        delay += 0.1
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.1, animations: {
                self.textCenter.transform = CGAffineTransformMakeScale(1.5, 1.5)
            })
        })
        delay += 0.5
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.1, animations: {
                self.textCenter.transform = CGAffineTransformMakeScale(1, 1)
            })
            var rightAns = score > 0.6
            if rightAns {
                animateCoinIfCorrect(view: self.centerView)
            } else {
                setGameWrong()
            }
            
            if rightAns {
                self.playSound(name: "effects/cheer\(Int.random(in: 1...4))" , delay: 0.5)
            }
            self.showMenu()
        })
    }
    func getScore(text: String, answer: String)->Double{
        var text = text
        #if DEBUG
        topText.text = "\(text)->\(answer)"
        #endif
        var origin = ["and", "axe", "ask", "bon", "bad"]
        var newValue = ["ant", "ax", "ax", "barn", "bat"]

        origin += ["bad", "be", "bad", "been", "bug"]
        newValue += ["bed", "bee", "beg", "bin", "but"]

        origin += ["call", "chu", "con", "caught", "crap"]
        newValue += ["car", "chew", "cone", "cot", "crab"]

        origin += ["cup", "damn", "dawn", "that", "fake"]
        newValue += ["cub", "dam", "doll", "dot", "fig"]

        origin += ["finn", "fuck", "fuck", "hi", "how old"]
        newValue += ["fin", "fog", "fork", "hive", "hole"]

        origin += ["hook", "hot", "joc", "cat", "kids"]
        newValue += ["hug", "hut", "jog", "keg", "kit"]

        origin += ["live", "let", "ladder", "meet", "man"]
        newValue += ["leaf", "leg", "letter", "meat", "men"]

        origin += ["muir", "next", "gnu", "not", "other"]
        newValue += ["mule", "nest", "new", "nut", "otter"]

        origin += ["penn", "p", "penn", "pac", "hi"]
        newValue += ["pan", "pea", "pen", "park", "pie"]

        origin += ["pete", "poet", "\"", "''", "right"]
        newValue += ["pit", "point", "quilt", "quilt", "rat"]

        origin += ["read", "drop", "rap", "drug", "cell"]
        newValue += ["red", "rope", "rub", "rug", "sail"]

        origin += ["see", "set", "sup", "suck", "still"]
        newValue += ["sea", "sit", "soap", "sock", "stew"]

        origin += ["son", "tell", "10", "some", "thai"]
        newValue += ["sun", "tail", "ten", "thumb", "tie"]

        origin += ["teen", "tao", "thai", "three", "turn"]
        newValue += ["tin", "toe", "toy", "tree", "tub"]
        
        origin += ["turn", "empire", "one", "wait", "well"]
        newValue += ["tug", "umpire", "wall", "wed", "whale"]
        
        origin += ["we'll", "yet", "young", "yo-yo", ""]
        newValue += ["wheel", "yatch", "yarn", "yoyo", ""]
        
        origin += ["", "", "", "", ""]
        newValue += ["", "", "", "", ""]

        if origin.count != newValue.count {
            text = "ỹ"
        }

        if text != answer {
            var t = text
            for i in 0..<origin.count {
                if text == origin[i] {
                    t = newValue[i]
                    if t == answer {
                        return 1
                    }
                }
            }
        }
        #if DEBUG
        topText.text = "\(topText.text!)-> fix: \(text)"
        #endif
        var MIN = 0.1
        var bonus = 0.1 * Double.random(in: 0..<1)
        var missing = 0
        var extra = 0
        var correct = 0
        for i in 0..<text.count {
            if answer.contains(text[i]) {
                missing += 1
            }
        }
        for i in 0..<answer.count {
            if !text.contains(answer[i]) {
                extra += 1
            } else {
                correct += 1
            }
        }
        correct = min(correct, text.count)
        var score = Double(correct) / Double(max(max(text.count,answer.count),correct + missing + extra))
        score = max(score, getScore1(text: text, answer: answer))
        score = MIN + score * (1 - MIN) + bonus
        if score > 1 {
            score = 1
        }
        return score
    }
    func getScore1(text: String, answer: String)->Double{
        var count = min(text.count, answer.count)
        var correct = 0
        for i in 0..<count {
            if text[i] == answer[i] {
                correct += 1
            }
        }
        return Double(correct) / Double(max(text.count, answer.count))
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        meIndex = Int.random(in: 0..<values.count)
        leftContainer.alpha = 0
        rightContainer.alpha = 0
        hideMenu()
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.leftContainer.alpha = 1
            self.rightContainer.alpha = 1
        })
        requestAudioRecordingPermission {[weak self] granted in
            guard let self = self else { return }
            if granted {
                // User granted permission, you can now record audio
                // Place your code here to start recording audio
                initData()
            } else {
                // User denied access to the microphone
                // Handle this case appropriately, e.g., show an alert
                self.finishGame()
            }
        }
        
    }
    func initData(){
        speechRecognizer?.delegate = self as? SFSpeechRecognizerDelegate  //3
        speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: lang))
        SFSpeechRecognizer.requestAuthorization {[weak self] (authStatus) in  //4
            guard let self = self else { return }
            var isButtonEnabled = false
            
            switch authStatus {  //5
            case .authorized:
                isButtonEnabled = true
                
            case .denied:
                isButtonEnabled = false
                print("User denied access to speech recognition")
                
            case .restricted:
                isButtonEnabled = false
                print("Speech recognition restricted on this device")
                
            case .notDetermined:
                isButtonEnabled = false
                print("Speech recognition not yet authorized")
            }
            
            OperationQueue.main.addOperation() {
                [weak self] in
                guard let self = self else { return }
                if !isButtonEnabled {
                    self.finishGame()
                } else {
                    self.topText.text =  self.values[self.meIndex]
                    var delay = 0.0
                    delay += self.playSound(delay: delay, names: self.parseIntroText()!)
                    delay += 1
                    delay += self.playSound(name: self.values[self.meIndex], delay: delay)
                    delay += 0.1
                    self.scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.xamlWave.startAnimation()
                        self.xamlLoad.startAnimation()
                        listening = true
                        self.toggleStartStop()
                    })
                }
            }
        }
    }
    func requestAudioRecordingPermission(completion: @escaping (Bool) -> Void) {
        let audioSession = AVAudioSession.sharedInstance()
        
        do {
            audioSession.requestRecordPermission { granted in
                DispatchQueue.main.async {
                    completion(granted)
                }
            }
        } catch {
            completion(false)
        }
    }
   
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += 1
        delay += self.playSound(name: self.values[self.meIndex], delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func toggleStartStop() {
        
        speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: lang))
        
        if !listening {
            audioEngine.stop()
            recognitionRequest?.endAudio()
            let audioSession = AVAudioSession.sharedInstance()
            do {
                try audioSession.setCategory(AVAudioSession.Category.playback)
                try audioSession.setMode(AVAudioSession.Mode.default)
                try audioSession.setActive(true)
            } catch {
                print("audioSession properties weren't set because of an error.")
            }
            //startStopBtn.isEnabled = false
            //startStopBtn.setTitle("Start Recording", for: .normal)
            timerLongtimeout.cancel()
        } else {
            self.startRecording()
            timerLongtimeout.schedule()
            //self.playSound(name: "effects/score", delay: 0)
            scheduler.schedule(delay: 1, execute: {
                [weak self] in
                guard let self = self else { return }
                
            })
            
            //startStopBtn.setTitle("Stop Recording", for: .normal)
        }
    }
    
    
    func startRecording() {
        
        texts = []
        if recognitionTask != nil {
            recognitionTask?.cancel()
            recognitionTask = nil
        }
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(AVAudioSession.Category.record)
            try audioSession.setMode(AVAudioSession.Mode.measurement)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("audioSession properties weren't set because of an error.")
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        
        let inputNode = audioEngine.inputNode
        
        guard let recognitionRequest = recognitionRequest else {
            fatalError("Unable to create an SFSpeechAudioBufferRecognitionRequest object")
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest, resultHandler: { [weak self] (result, error) in        
            guard let self = self else { return }
            var isFinal = false
            
            if result != nil {
                var text = result?.bestTranscription.formattedString
                if text != nil && listening {
                    self.texts.append(text!)
                    self.timertimeout.schedule()
                }
                isFinal = (result?.isFinal)!
            }
            if error != nil || isFinal {
                self.audioEngine.stop()
                self.recognitionRequest?.endAudio()
                inputNode.removeTap(onBus: 0)
                
                self.recognitionRequest = nil
                self.recognitionTask = nil
                
                //self.startStopBtn.isEnabled = true
                if error != nil {
                    self.texts.append("")
                    self.timertimeout.schedule()
                }
            }
        })
        
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        audioEngine.inputNode.removeTap(onBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { (buffer, when) in
            self.recognitionRequest?.append(buffer)
        }
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
        } catch {
            print("audioEngine couldn't start because of an error.")
        }
        
        //textView.text = "Say something, I'm listening!"
        
    }
    @objc func loadNext(){
        hideMenu()
        finishGame()
    }
    @objc func replay(){
        hideMenu()
        textCenter.text = ""
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += 1
        delay += self.playSound(name: self.values[self.meIndex], delay: delay)
        delay += 0.1
        self.scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.xamlWave.isHidden = false
            self.xamlLoad.isHidden = true
            listening = true
            self.toggleStartStop()
        })
    }
    func hideMenu(){
        leftContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -leftContainer.bounds.width * 1.2 - 20.0]
        let animValues2: [Double] = [0, rightContainer.bounds.width * 1.2 + 20.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showMenu(){
        startGame()
        leftContainer.transform = CGAffineTransformMakeTranslation(-leftContainer.bounds.width, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(rightContainer.bounds.width, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [-leftContainer.bounds.width, 0]
        let animValues2: [Double] = [rightContainer.bounds.width, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    // Override the willMove(toSuperview:) method
    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        
        if newSuperview == nil {
            // The view is being removed from the view hierarchy, remove the tap from the input node.
            if listening {
                audioEngine.stop()
                recognitionRequest?.endAudio()
                audioEngine.inputNode.removeTap(onBus: 0)
                let audioSession = AVAudioSession.sharedInstance()
                do {
                    try audioSession.setCategory(AVAudioSession.Category.playback)
                    try audioSession.setMode(AVAudioSession.Mode.default)
                    try audioSession.setActive(true)
                } catch {
                    print("audioSession properties weren't set because of an error.")
                }
                timerLongtimeout.cancel()
                timertimeout.cancel()
            }
            reachability.stopNotifier()
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameSpeaking]
    }
    override func getScore()->Float{
        return score
    }
}

