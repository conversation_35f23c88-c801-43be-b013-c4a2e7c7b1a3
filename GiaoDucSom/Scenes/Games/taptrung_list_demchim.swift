//
//  taptrung_list_demchim.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit
import SVGKit
import SwiftSVG

class taptrung_list_demchim: NhanBietGameFragment {
    // MARK: - Properties
    private var svg: SVGKImage?
    private var moveContainer: UIView!
    private var bird: Int = 0
    private var swingIndex: Int = 0
    private var numpad: MathNumpad!
    private var answerLayout: UIView!
    private var coinView: UIView!
    private var answerText: HeightRatioTextView!
    private var moveAnimation: UIViewPropertyAnimator?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 53/255, green: 55/255, blue: 63/255, alpha: 1) // #35373F
        self.clipsToBounds = true
        let imageBoy = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_binoculars"))
        imageBoy.contentMode = .scaleAspectFit
        addSubview(imageBoy)
        imageBoy.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(isRunningOnIphone() ? 0.7 : 0.4)                    
            make.width.equalTo(imageBoy.snp.height).multipliedBy(412.7 / 753.0) // Ratio 412.7:753
            make.bottom.equalToSuperview()
            make.left.equalTo(safeAreaLayoutGuide.snp.left)
        }
        
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.left.equalTo(imageBoy.snp.right).offset(0)
        }
        
        let maskContainer = UIView()
        rightContainer.addSubview(maskContainer)
        maskContainer.makeViewCenterAndKeep(ratio: 1837.0 / 1068.0)
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "taptrung_demchim_bg"))
        bgImage.contentMode = .scaleAspectFill
        bgImage.clipsToBounds = true
        maskContainer.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        moveContainer = UIView()
        moveContainer.clipsToBounds = false
        maskContainer.addSubview(moveContainer)
        moveContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let maskView = UIImageView()
        maskView.image = Utilities.SVGImage(named: "nhanbiet_bg_binoculars2")
        maskView.contentMode = .scaleToFill
        maskContainer.addSubviewWithInset(subview: maskView, inset: 0)
        let maskView2 = UIView()
        maskView2.backgroundColor = backgroundColor
        maskContainer.addSubview(maskView2)
        maskView2.snp.makeConstraints { make in
            make.width.height.top.equalToSuperview()
            make.right.equalTo(maskContainer.snp.left)
        }
        bringSubviewToFront(imageBoy)
        
        answerLayout = UIView()
        answerLayout.backgroundColor = UIColor(red: 53/255, green: 55/255, blue: 63/255, alpha: 0.8) // #CC35373F
        answerLayout.isHidden = true
        addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        answerText = HeightRatioTextView()
        answerText.textColor = .white
        answerText.font = .Freude(size: 20)
        answerText.textAlignment = .center
        answerText.adjustsFontSizeToFitWidth = true
        answerText.minimumScaleFactor = 0.1
        answerText.setHeightRatio(0.4)
        answerLayout.addSubview(answerText)
        answerText.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.66)
        }
        
        let rightView = UIView()
        answerLayout.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalTo(safeAreaLayoutGuide.snp.right)
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        bird = random(5, 6, 7, 8, 9, 10)
        let delay = playSound(openGameSound(), "taptrung/dem chim")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startFly()
            self?.swing()
        }
    }
    
    override func createGame() {
        super.createGame()
        
        let stepHeight = moveContainer.frame.height / 2 / CGFloat(2 + bird) * 0.6
        let stepWidth = moveContainer.frame.height / 4
        var sign: CGFloat = CGFloat(random(1, -1))
        
        for i in 0..<bird {
            svg = Utilities.GetSVGKImage(named: "animations/animation_swan.svg")
            let view = SVGKFastImageView(svgkImage: svg)
            view?.alpha = 0
            guard let view = view else { continue }
            moveContainer.addSubview(view)
            view.snp.makeConstraints { make in
                make.height.equalTo(moveContainer).multipliedBy(0.3)
                make.width.equalTo(view.snp.height) // Ratio 1:1
                make.centerY.equalToSuperview()
            }
            let SIGN = sign
            let I = i
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                view.center = CGPointMake(view.center.x - CGFloat(I+1) * stepWidth,
                                          view.center.y+CGFloat(I) * SIGN * stepHeight)
            }
            view.tag = Int.random(in: 0..<(svg!.caLayerTree.sublayers?.count ?? 1))
            let scale = 1 + 0.2 / CGFloat(self.bird) * CGFloat(i)
            view.transform = CGAffineTransform(scaleX: sign == -1 ? 1/scale : scale, y: sign == -1 ? 1/scale : scale)
            sign *= -1
        }
    }
    
    private func swing() {
        if gameState == .paused {
            swingIndex += 1
            for i in 0..<moveContainer.subviews.count {
                guard let view = moveContainer.subviews[i] as? SVGKFastImageView else { continue }
                guard  let svg = view.image else { continue }
                let index = (view.tag + swingIndex) % svg.caLayerTree.sublayers!.count
                view.displayLayer(atIndex: index)
                view.alpha = 1
            }
        }
        scheduler.schedule(after: 0.1) { [weak self] in
            self?.swing()
        }
    }
    
    private func startFly() {
        pauseGame()
        let stepWidth = moveContainer.frame.height / 4
        let distance = moveContainer.frame.width * 1.0 + CGFloat(bird) * stepWidth
        let duration = TimeInterval(distance / moveContainer.frame.height * 3)
        
        moveContainer.alpha = 1
        moveAnimation = UIViewPropertyAnimator(duration: duration, curve: .linear) {
            self.moveContainer.transform = CGAffineTransform(translationX: distance, y: 0)
        }
        moveAnimation?.startAnimation()
        
        scheduler.schedule(delay: duration) { [weak self] in
            guard let self = self else { return }
            self.answerLayout.alpha = 0
            self.answerLayout.isHidden = false
            UIView.animate(withDuration: 0.5) {
                self.answerLayout.alpha = 1
            }
            self.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        pauseGame()
        moveAnimation?.stopAnimation(true)
        moveContainer.alpha = 0
        scheduler.clearAll()
        QueueSoundPlayer.shared.clearAll()        
        //SoundManager.getManager().stopAllScheduled()
        let delay = playSound(openGameSound(), "taptrung/dem chim")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startFly()
            self?.swing()
        }
        answerLayout.isHidden = true
    }
}

// MARK: - MathNumpadListener
extension taptrung_list_demchim: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        answerText.text = String(value)
        answerText.textColor = .white
    }
    
    func onDelClick(value: Int) {
        answerText.text = String(value)
        answerText.textColor = .white
    }
    
    func onCheckClick(value: Int) {
        answerText.text = String(value)
        pauseGame()
        let correct = value == bird
        if correct {
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), "topics/Numbers/\(value)", endGameSound())
            animateCoinIfCorrect(view: coinView)
            answerText.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            setGameWrong()
            answerText.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            numpad.reset()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - Supporting Structures
// Giả lập MaskableFrameLayout nếu không có trong context
class MaskableFrameLayout: UIView {
    var maskImage: UIImage?
    
    override func layoutSubviews() {
        super.layoutSubviews()
        
        if let maskImage = maskImage {
            // Tạo mask layer
            let maskLayer = CALayer()
            maskLayer.contents = maskImage.cgImage
            maskLayer.frame = bounds
            
            // Để đạt được hiệu ứng tương tự DST_OUT
            // Chúng ta cần đảo ngược mask và sử dụng nó
            maskLayer.contentsGravity = .resizeAspect
            
            // Gán mask cho layer chính
            layer.mask = maskLayer
            
            // Đảm bảo layer chính không hiển thị ngoài vùng mask
            layer.masksToBounds = true
        }
    }
    
    // Phương thức tiện ích để set mask
    func setMaskImage(_ image: UIImage?) {
        maskImage = image
        setNeedsLayout()
    }
}
import SVGKit

extension SVGKFastImageView {
    func displayLayer(atIndex k: Int) {
        guard let svgImage = self.image else { return }
        
        // Lấy tất cả các sublayer từ caLayerTree
        guard let sublayers = svgImage.caLayerTree?.sublayers else {
            print("No sublayers found in caLayerTree")
            return
        }
        
        // Kiểm tra index hợp lệ
        guard k >= 0 && k < sublayers.count else {
            print("Index \(k) out of bounds. Total layers: \(sublayers.count)")
            return
        }
        
        // Ẩn tất cả các layer khác, chỉ hiển thị layer thứ k
        for (index, layer) in sublayers.enumerated() {
            layer.isHidden = (index != k)
        }
        
        // Cập nhật lại view (không cần gán lại image vì layer đã được thay đổi trực tiếp)
        self.setNeedsDisplay()
    }
}
