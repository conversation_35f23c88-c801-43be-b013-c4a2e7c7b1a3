//
//  toancoban_list_xemgiochan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate
import SVGKit


// MARK: - toancoban_list_xemgiochan
class toancoban_list_xemgiochan: NhanBietGameFragment {
    // MARK: - Properties
    private var hour = 0
    private var sliderView: MySliderView!
    private var imageHour: UIView!
    private var clockDigital: ClockDigital!

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")

        let itemContainer = UIView()
        itemContainer.stringTag = "item_container"
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
            make.width.equalTo(itemContainer.snp.height)
        }

        let clockContainer = UIView()
        clockContainer.stringTag = "clock_container"
        itemContainer.addSubview(clockContainer)        
        clockContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(clockContainer.snp.height)
            make.center.equalToSuperview()
        }

        clockDigital = ClockDigital()
        clockDigital.stringTag = "clock_digital"
        clockContainer.addSubview(clockDigital)
        clockDigital.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        let bottomView = UIView()
        bottomView.stringTag = "bottom_view"
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(itemContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        sliderView = MySliderView()
        sliderView.horizontalPadding = 1.5
        sliderView.stringTag = "slider_view"
        bottomView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(sliderView.snp.width).dividedBy(6)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }
        
        sliderView.setListener { [weak self] value in
            guard let self = self else { return }
            let correct = value == self.hour
            self.pauseGame()
            self.sliderView.setRight(correct)
            if correct {
                self.animateCoinIfCorrect(view: self.sliderView)
                let delay = self.playSound(self.finishEndSounds())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                self.setGameWrong()
                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.startGame()
                }
            }
        }
        sliderView.setScrollToListener { [weak self] value, position in
            guard let self = self else { return }
            self.sliderView.setTextAtPosition("\(value):00", at: position)
        }
        sliderView.setScrollEndListener { [weak self] value, position in
            guard let self = self else { return }
            let correct = value == self.hour
            self.sliderView.setRight(correct)
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        hour = Int.random(in: 1...12)
        clockDigital.setTime(hour: hour, minute: 0)
        var minValue = hour - Int.random(in: 0..<5)
        if minValue < 1 { minValue = 1 }
        if minValue > 7 { minValue = 7 }
        let maxValue = minValue + 5
        sliderView.setMinValue(minValue)
        sliderView.setMaxValue(maxValue)
        sliderView.setStep(1)
        while true {
            let tick = Int.random(in: minValue...maxValue)
            if tick != hour {
                sliderView.setSelectedTick(tick - minValue)
                sliderView.setTextAtPosition("\(tick):00", at: tick - minValue)
                break
            }
        }
    }

    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_xem gio chan")
        scheduler.schedule(delay: Double(delay)) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_xem gio chan")
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}

// MARK: - Extensions
extension UIView {
    var rotationDegrees: CGFloat {
        get { return atan2(transform.b, transform.a).degrees }
        set { transform = CGAffineTransform(rotationAngle: newValue.radians) }
    }
}

extension CGFloat {
    var degrees: CGFloat { return self * 180 / .pi }
    var radians: CGFloat { return self * .pi / 180 }
}


// MARK: - ClockBase
class ClockBase: UIView {
    override init(frame: CGRect) {
        super.init(frame: frame)
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
    }

    func setTime(hour: Int, minute: Int) {}
}

// MARK: - ClockDigital
class ClockDigital: ClockBase {
    // MARK: - Properties
    private var timeChangeListener: ((Int, Int) -> Void)?
    private var canEdit: Bool = false
    private var snapTo5Minutes: Bool = false
    private var svg: SVGKImage?
    private let svgView = SVGKFastImageView(svgkImage: nil)!
    private let imageMinute = UIImageView(image: Utilities.SVGImage(named: "math_clock1_minute"))
    private let imageHour = UIImageView(image: Utilities.SVGImage(named: "math_clock1_hour"))
    private let clockContainer = UIView()
    private var totalMinutes: CGFloat = 0
    private var oldMinuteAngle: CGFloat = 0
    private var oldHourAngle: CGFloat = 0

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }

    private func initView() {
        clockContainer.stringTag = "clock_container"
        addSubview(clockContainer)
        clockContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(clockContainer.snp.height)
        }

        svgView.stringTag = "svg_view"
        svgView.contentMode = .scaleAspectFit
        clockContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        imageMinute.stringTag = "image_minute"
        clockContainer.addSubview(imageMinute)
        imageMinute.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        imageHour.stringTag = "image_hour"
        clockContainer.addSubview(imageHour)
        imageHour.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        setTime(hour: 10, minute: 15)
        setupTouchListener()

        self.svg = Utilities.GetSVGKImage(named: "math_clock1_bg")
        for i in 1...12 {
            svg?.caLayerTree.sublayers?[i].sublayers?.first?.opacity = 0
        }
        self.svgView.image = svg
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            loaded = true
        }
    }

    var loaded = false
    
    // MARK: - Public Methods
    @discardableResult
    func setTimeChangeListener(_ listener: @escaping (Int, Int) -> Void) -> ClockDigital {
        self.timeChangeListener = listener
        return self
    }

    func setSnapTo5Minutes(_ snap: Bool) -> ClockDigital {
        self.snapTo5Minutes = snap
        return self
    }

    func setCanEdit(_ edit: Bool) -> ClockDigital {
        self.canEdit = edit
        return self
    }

    override func setTime(hour: Int, minute: Int) {
        totalMinutes = CGFloat(hour * 60 + minute)
        oldMinuteAngle = CGFloat(minute) / 60 * 360
        oldHourAngle = CGFloat(hour % 12) * 30 + CGFloat(minute) / 60 * 30
        updateClockHands()
    }

    // MARK: - Private Methods
    let scheduler = Scheduler()
    private func highlight(hour: Int, minute: Int) {
        guard let svg = svg else {
            scheduler.schedule(delay: 0.1) { [weak self] in
                self?.highlight(hour: hour, minute: minute)
            }
            return
        }
        let numberHour = hour
        let numberMinute = Int(round(CGFloat(minute) / 5))
        for i in 1...12 {
            let color: UIColor
            if i % 12 == (numberMinute % 12 + 12) % 12 {
                color = UIColor(hex: "#E73C2B")
            } else if i % 12 == (numberMinute % 12 + 12) % 12 {
                color = UIColor(hex: "#006EB8")
            } else {
                color = UIColor(hex: "#9DB7C4")
            }
            (svg.caLayerTree.sublayers?[i].sublayers?[1] as? CAShapeLayer)?.fillColor = color.cgColor
        }
        svgView.setNeedsDisplay()
    }

    private func setupTouchListener() {
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        clockContainer.addGestureRecognizer(panGesture)
    }

    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        guard canEdit, let view = gesture.view else { return }
        let location = gesture.location(in: view)
        let centerX = view.bounds.width / 2
        let centerY = view.bounds.height / 2
        let dx = location.x - centerX
        let dy = location.y - centerY
        var angle = atan2(dy, dx).degrees + 90
        if angle < 0 { angle += 360 }
        let distance = sqrt(dx * dx + dy * dy)
        let radius = view.bounds.width / 2
        var holdHour = distance < radius * 0.5

        switch gesture.state {
        case .began, .changed:
            let oldTotalMinutes = totalMinutes
            holdHour = false
            if !holdHour {
                var diff = angle - oldMinuteAngle
                if diff > 180 { totalMinutes -= 60 }
                else if diff < -180 { totalMinutes += 60 }
                oldMinuteAngle = angle
                let minuteFloat = angle / 6
                let oldHourFloat = floor(totalMinutes / 60)
                totalMinutes = oldHourFloat * 60 + minuteFloat
                let delta = ((totalMinutes - oldTotalMinutes).truncatingRemainder(dividingBy: 60) + 60).truncatingRemainder(dividingBy: 60)
                let adjustedDelta = delta > 30 ? delta - 60 : delta
                if abs(adjustedDelta) > 10 {
                    totalMinutes = oldTotalMinutes
                    return
                }
                while totalMinutes - oldTotalMinutes > 30 { totalMinutes -= 60 }
                while totalMinutes - oldTotalMinutes < -30 { totalMinutes += 60 }
                highlight(hour: -1, minute: Int(round(totalMinutes / 5) * 5))
            } else {
                var diff = angle - oldHourAngle
                if diff > 180 { totalMinutes -= 720 }
                else if diff < -180 { totalMinutes += 720 }
                oldHourAngle = angle
                let hourFloat = angle / 30
                let oldMinuteFloat = totalMinutes.truncatingRemainder(dividingBy: 60)
                let oldTotalMinutes = totalMinutes
                totalMinutes = hourFloat * 60
                let delta = ((totalMinutes - oldTotalMinutes).truncatingRemainder(dividingBy: 720) + 720).truncatingRemainder(dividingBy: 720)
                let adjustedDelta = delta > 360 ? delta - 720 : delta
                if abs(adjustedDelta) > 15 {
                    totalMinutes = oldTotalMinutes
                    return
                }
                highlight(hour: -1, minute: Int(round(totalMinutes / 5) * 5))
            }
            updateClockHands()
        case .ended:
            if snapTo5Minutes {
                totalMinutes = round(totalMinutes / 5) * 5
                let hour = Int((totalMinutes / 60 - 1).truncatingRemainder(dividingBy: 12) + 1)
                let minute = Int(totalMinutes.truncatingRemainder(dividingBy: 60))
                timeChangeListener?(hour, minute)
                updateClockHands(animation: true)
                highlight(hour: hour, minute: minute)
            }
        default:
            break
        }
    }

    private func updateClockHands(animation: Bool = false) {
        var minute = totalMinutes.truncatingRemainder(dividingBy: 60)
        if minute < 0 { minute += 60 }
        let rotationMinute = minute * 6
        var hourFloat = totalMinutes / 60
        if hourFloat < 0 { hourFloat += 12 }
        let rotationHour = hourFloat * 30
        let oldRotation = imageMinute.rotationDegrees
        if animation {
            var adjustedMinute = imageMinute.rotationDegrees
            if imageMinute.rotationDegrees - rotationMinute > 180 {
                adjustedMinute -= 360
            } else if imageMinute.rotationDegrees - rotationMinute < -180 {
                adjustedMinute += 360
            }
            imageMinute.rotationDegrees = adjustedMinute
            UIView.animate(withDuration: 0.1) {
                self.imageMinute.rotationDegrees = rotationMinute
                self.imageHour.rotationDegrees = rotationHour
            }
        } else {
            imageMinute.rotationDegrees = rotationMinute
            imageHour.rotationDegrees = rotationHour
        }
        let newRotation = imageMinute.rotationDegrees
        let oldX = Int(oldRotation / 30)
        let newX = Int(newRotation / 30)
        if oldX != newX {
            if loaded {
                playGearSound()
                Utilities.vibrate()
            }
        }
    }
    private var player: AVAudioPlayer?
    private func playGearSound() {
        if player == nil {
            if let url = Utilities.url(soundPath: "effect/gear2") {
                do {
                    player = try AVAudioPlayer(contentsOf: url)
                    player?.prepareToPlay()
                } catch {
                    print("Error loading gear2 sound: \(error)")
                }
            }
        }
        if let player = player {
            player.currentTime = 0
            player.play()
        }
    }
}
