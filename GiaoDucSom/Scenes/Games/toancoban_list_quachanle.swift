//
//  toancoban_list_quachanle.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_quachanle: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var gridLayout: MyGridView!
    private var number: Int = 0
    private var answer: String = ""
    private let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        itemContainer = UIView()
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
            make.width.equalTo(itemContainer.snp.height) // Ratio 1:1
            make.left.equalToSuperview().inset(view.frame.height * 0.05) // Margin 5% height
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    override func createGame() {
        super.createGame()
        let file = StorageManager.manager.list(path: "topics/Fruits/numbers")
            .filter { $0.hasSuffix(".svg") }
            .shuffled()
            .first ?? ""
        number = Int(file.prefix(while: { $0 != "_" }).trimmingCharacters(in: .whitespaces)) ?? 0
        answer = number % 2 == 0 ? "chẵn" : "lẻ"
        
        let svgNumber = Utilities.GetSVGKImage(named: "topics/Fruits/numbers/\(file)")
        let svgItem = Utilities.GetSVGKImage(named: "topics/Fruits/\(String(file.dropFirst(2)))")
        
        for i in 0..<svgNumber.caLayerTree.sublayers!.count {
            let bounds = svgNumber.caLayerTree.sublayers![i].shapeContentBounds!
            let width = svgNumber.size.width
            let height = svgNumber.size.height
            let item = UIImageView(image: svgItem.uiImage)
            item.contentMode = .scaleAspectFit
            itemContainer.addSubview(item)
            item.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(bounds.width / width)
                make.height.equalToSuperview().multipliedBy(bounds.height / height)
                make.right.equalToSuperview().multipliedBy(bounds.maxX / width)
                make.bottom.equalToSuperview().multipliedBy(bounds.maxY / height)
            }
        }
        
        buildGrid(grid: gridLayout)
        
        var delay = playSound(openGameSound(), "toan/toan_qua chan le")
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_qua chan le")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView) {
        var views: [UIView] = []
        let numbers = ["chẵn", "lẻ"].shuffled()
        for i in 0..<numbers.count {
            let value = numbers[i]
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: String) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = value
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value == "chẵn" ? 0 : 1
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag == 0 ? "chẵn" : "lẻ"
        let correct = value == answer
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: itemContainer)
            delay += playSound(delay: delay, names: [answerCorrect1EffectSound(), "toan/\(value)", getCorrectHumanSound(), endGameSound()])
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), "toan/\(value)", getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
