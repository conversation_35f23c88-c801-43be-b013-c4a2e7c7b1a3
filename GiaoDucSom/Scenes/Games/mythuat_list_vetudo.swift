//
//  mythuat_list_vetudo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/6/25.
//

import UIKit
import SnapKit

class mythuat_list_vetudo: NhanBietGameFragment {
    // MARK: - Properties
    private var drawView: SmoothStrokeView!
    private var imageUndo: UIImageView!
    private var imageRedo: UIImageView!
    private var imageStroke: RoundedView!
    private var imageColors: UIImageView!
    var filename: String?
    
    
    // MARK: - Public Methods
    func getFilename() -> String? {
        return filename
    }
    
    @discardableResult
    func setFilename(_ filename: String?) -> mythuat_list_vetudo {
        self.filename = filename
        return self
    }
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.backgroundColor = .white // #FFF
        
        let buttonContainer = UIView()
        buttonContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #1f00
        view.addSubview(buttonContainer)
        buttonContainer.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.8)
            make.width.equalTo(buttonContainer.snp.height).multipliedBy(0.25) // Ratio 0.25
            make.left.bottom.equalToSuperview()
        }
        
        imageUndo = UIImageView(image: Utilities.SVGImage(named: "mythuat_vetudo_undo"))
        imageUndo.isUserInteractionEnabled = true
        buttonContainer.addSubview(imageUndo)
        imageUndo.snp.makeConstraints { make in
            make.width.equalTo(buttonContainer).multipliedBy(0.8) // Margin 10%
            make.height.equalTo(imageUndo.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(0.04) // Bias 0.02
        }
        imageUndo.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            imageUndo.snapToVerticalBias(verticalBias: 0.02)
        }
        
        imageRedo = UIImageView(image: Utilities.SVGImage(named: "mythuat_vetudo_redo"))
        imageRedo.isUserInteractionEnabled = true
        buttonContainer.addSubview(imageRedo)
        imageRedo.snp.makeConstraints { make in
            make.width.equalTo(buttonContainer).multipliedBy(0.8) // Margin 10%
            make.height.equalTo(imageRedo.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(0.68) // Bias 0.34
        }
        imageRedo.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            imageRedo.snapToVerticalBias(verticalBias: 0.34)
        }
        
        
        imageStroke = RoundedView()
        imageStroke.backgroundColor = .green
        imageStroke.isUserInteractionEnabled = true
        buttonContainer.addSubview(imageStroke)
        imageStroke.snp.makeConstraints { make in
            make.width.equalTo(buttonContainer).multipliedBy(0.8) // Margin 10%
            make.height.equalTo(imageStroke.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.32) // Bias 0.66
        }
        imageStroke.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            imageStroke.snapToVerticalBias(verticalBias: 0.66)
        }
        
        imageColors = UIImageView(image: Utilities.SVGImage(named: "mythuat_vetudo_hopmau"))
        imageColors.isUserInteractionEnabled = true
        buttonContainer.addSubview(imageColors)
        imageColors.snp.makeConstraints { make in
            make.width.equalTo(buttonContainer).multipliedBy(0.8) // Margin 10%
            make.height.equalTo(imageColors.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.96) // Bias 0.98
        }
        imageColors.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            imageColors.snapToVerticalBias(verticalBias: 0.98)
        }
        
        let itemContainer = UIView()
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.left.equalTo(buttonContainer.snp.right)
            make.top.right.bottom.equalToSuperview()
        }
        
        drawView = SmoothStrokeView()
        drawView.clipsToBounds = false
        drawView.isUserInteractionEnabled = true
        itemContainer.addSubview(drawView)
        drawView.makeViewCenterAndKeep(ratio: 2)
                
        // Set tap gestures
        imageUndo.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(undoTapped)))
        imageRedo.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(redoTapped)))
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        if let filename = filename {
            drawView.load(filename: filename)
        }
    }
    
    override func finishGame() {
        super.finishGame()
    }
    
    override func removeFromSuperview() {
        if let filename = filename {
            drawView.save(filename: filename, override: true)
        }
        super.removeFromSuperview()
        
    }
    
    // MARK: - Actions
    @objc private func undoTapped() {
        drawView.undo()
    }
    
    @objc private func redoTapped() {
        drawView.redo()
    }
}

// MARK: - SmoothStrokeView
class SmoothStrokeView: UIView {
    // MARK: - Properties
    private var nextStrokes: [Stroke] = []
    private var strokes: [Stroke] = []
    private var currentPath: UIBezierPath?
    private var currentLayer: CAShapeLayer?
    private var currentColor: UIColor = .black
    private var smooth: Bool = true
    private var lastPoint: CGPoint = .zero
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initialize()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initialize()
    }
    
    private func initialize() {
        backgroundColor = .clear
        layer.masksToBounds = false
    }
    
    // MARK: - Drawing Methods
    func undo() -> Bool {
        if let lastStroke = strokes.popLast() {
            nextStrokes.append(lastStroke)
            layer.sublayers = strokes.map { $0.layer }
            return !strokes.isEmpty
        }
        return false
    }
    
    func redo() -> Bool {
        if let lastStroke = nextStrokes.popLast() {
            strokes.append(lastStroke)
            layer.sublayers = strokes.map { $0.layer }
            return !nextStrokes.isEmpty
        }
        return false
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard isUserInteractionEnabled, let touch = touches.first else { return }
        let point = touch.location(in: self)
        
        currentColor = generateRandomColor()
        currentPath = UIBezierPath()
        currentPath?.move(to: point)
        
        let layer = CAShapeLayer()
        layer.path = currentPath?.cgPath
        layer.strokeColor = currentColor.cgColor
        layer.lineWidth = 8
        layer.lineCap = .round
        layer.lineJoin = .round
        layer.fillColor = nil
        
        currentLayer = layer
        strokes.append(Stroke(path: currentPath!, layer: layer))
        self.layer.sublayers = strokes.map { $0.layer }
        
        lastPoint = point
        nextStrokes.removeAll()
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, let currentPath = currentPath, let currentLayer = currentLayer else { return }
        let point = touch.location(in: self)
        
        if smooth {
            let midPoint = CGPoint(x: (lastPoint.x + point.x) / 2, y: (lastPoint.y + point.y) / 2)
            currentPath.addQuadCurve(to: midPoint, controlPoint: lastPoint)
        } else {
            currentPath.addLine(to: point)
        }
        
        currentLayer.path = currentPath.cgPath
        lastPoint = point
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, let currentPath = currentPath else { return }
        let point = touch.location(in: self)
        currentPath.addLine(to: point)
        
        currentLayer?.path = currentPath.cgPath
        self.currentPath = nil
        currentLayer = nil
    }
    
    private func generateRandomColor() -> UIColor {
        let red = CGFloat.random(in: 0...1)
        let green = CGFloat.random(in: 0...1)
        let blue = CGFloat.random(in: 0...1)
        return UIColor(red: red, green: green, blue: blue, alpha: 1)
    }
    
    // MARK: - Save and Load
    func save(filename: String, override: Bool) {
        guard let fileURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.appendingPathComponent(filename) else { return }
        
        if FileManager.default.fileExists(atPath: fileURL.path) && !override {
            print("File already exists and override is disabled.")
            return
        }
        
        do {
            let strokeArray = try strokes.map { stroke -> [String: Any] in
                let color = stroke.layer.strokeColor?.components ?? [0, 0, 0, 1]
                let points = stroke.path.points.map { ["x": $0.x, "y": $0.y] }
                return [
                    "color": [color[0], color[1], color[2], color[3]],
                    "strokeWidth": stroke.layer.lineWidth,
                    "width": bounds.width,
                    "height": bounds.height,
                    "path": points
                ]
            }
            let jsonData = try JSONSerialization.data(withJSONObject: strokeArray, options: .prettyPrinted)
            try jsonData.write(to: fileURL)
            print("File saved to: \(fileURL.path)")
        } catch {
            print("Error saving file: \(error)")
        }
    }
    
    func load(filename: String) {
        guard let fileURL = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask).first?.appendingPathComponent(filename),
              FileManager.default.fileExists(atPath: fileURL.path) else {
            print("File not found: \(filename)")
            return
        }
        
        do {
            let jsonData = try Data(contentsOf: fileURL)
            let strokeArray = try JSONSerialization.jsonObject(with: jsonData) as? [[String: Any]] ?? []
            strokes.removeAll()
            
            let currentWidth = bounds.width
            let currentHeight = bounds.height
            
            for strokeObj in strokeArray {
                let originalWidth = strokeObj["width"] as? CGFloat ?? currentWidth
                let originalHeight = strokeObj["height"] as? CGFloat ?? currentHeight
                let scaleX = currentWidth / originalWidth
                let scaleY = currentHeight / originalHeight
                
                let color = strokeObj["color"] as? [CGFloat] ?? [0, 0, 0, 1]
                let strokeWidth = (strokeObj["strokeWidth"] as? CGFloat ?? 8) * min(scaleX, scaleY)
                let points = (strokeObj["path"] as? [[String: CGFloat]])?.map { CGPoint(x: ($0["x"] ?? 0) * scaleX, y: ($0["y"] ?? 0) * scaleY) } ?? []
                
                let path = UIBezierPath()
                for (i, point) in points.enumerated() {
                    if i == 0 {
                        path.move(to: point)
                    } else {
                        path.addLine(to: point)
                    }
                }
                
                let layer = CAShapeLayer()
                layer.path = path.cgPath
                layer.strokeColor = UIColor(red: color[0], green: color[1], blue: color[2], alpha: color[3]).cgColor
                layer.lineWidth = strokeWidth
                layer.lineCap = .round
                layer.lineJoin = .round
                layer.fillColor = nil
                
                strokes.append(Stroke(path: path, layer: layer))
            }
            
            self.layer.sublayers = strokes.map { $0.layer }
            print("File loaded from: \(fileURL.path)")
        } catch {
            print("Error loading file: \(error)")
        }
    }
    
    // MARK: - Stroke Struct
    private struct Stroke {
        let path: UIBezierPath
        let layer: CAShapeLayer
    }
}

// MARK: - UIBezierPath Extension
extension UIBezierPath {
    var points: [CGPoint] {
        var points: [CGPoint] = []
        cgPath.applyWithBlock { element in
            switch element.pointee.type {
            case .moveToPoint, .addLineToPoint:
                points.append(element.pointee.points[0])
            case .addQuadCurveToPoint:
                points.append(element.pointee.points[1])
            default:
                break
            }
        }
        return points
    }
}
