//
//  mythuat_list_phanbietmaucoban.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/5/25.
//

import Foundation

class mythuat_list_phanbietmaucoban : nhanbiet_list_phanbiet {
    private let colors: [String] = ["<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "<PERSON>", "Gray", "Pink"].map{$0.lowercased()}
    
    required init(frame: CGRect) {
        super.init(frame: frame)
        setupData()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupData()
    }
    
    func setupData() {
        for pack in FlashcardsManager.shared.getPacks() {
            if pack.folder == "Colors" {
                self.setFolder(pack.folder)
                let selectedItems = pack.items
                    .filter { colors.contains($0.name.en!.lowercased()) }
                    .shuffled()
                    .prefix(3)
                setListItems(Array(selectedItems))
            }
        }
    }
}
