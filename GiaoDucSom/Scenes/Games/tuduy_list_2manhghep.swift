//
//  tuduy_list_2manhghep.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class tuduy_list_2manhghep: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var topGridLayout: MyGridView!
    private var meIndex: Int = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        topGridLayout = MyGridView()
        addSubview(topGridLayout)
        topGridLayout.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(view.snp.centerY)
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        coinView = UIView()
        addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.3)
            make.height.equalTo(view).multipliedBy(0.3)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let piece1 = [[1, 0], [2, 1], [1, 0]]
        let piece2 = [[1, 0], [1, 0], [1, 2]]
        let piece3 = [[1, 0], [1, 2], [0, 1]]
        let piece4 = [[1, 0], [1, 2], [1, 0]]
        let piece5 = [[1, 0], [1, 0], [2, 1]]
        let piece6 = [[1, 0], [1, 1], [0, 2]]
        let piece7 = [[2, 0], [1, 1], [1, 0]]
        let piece8 = [[1, 0], [2, 0], [1, 1]]
        let piece9 = [[1, 0], [1, 1], [0, 1]]
        let piece10 = [[1, 2], [1, 1]]
        let piece11 = [[2, 1, 1, 1]]
        let piece12 = [[2, 2], [1, 1]]
        let piece13 = [[1, 2, 1, 1]]
        
        let pieces = [piece1, piece2, piece3, piece4, piece5, piece6, piece7, piece8, piece9, piece10, piece11, piece12, piece13]
        let indexes = (0..<pieces.count).shuffled().prefix(2).map { $0 }
        var pieceList: [[[Int]]] = []
        let count = 3
        meIndex = Int.random(in: 0..<count)
        
        for i in 0..<count {
            var _combinePieces = combinePieces(piece1: pieces[indexes[0]], piece2: pieces[indexes[1]])
            if i != meIndex {
                while true {
                    let randomIndex1 = Int.random(in: 0..<pieces.count)
                    let randomIndex2 = Int.random(in: 0..<pieces.count)
                    _combinePieces = combinePieces(piece1: pieces[randomIndex1], piece2: pieces[randomIndex2])
                    if !canFormPiece(piece1: pieces[indexes[0]], piece2: pieces[indexes[1]], piece3: _combinePieces) {
                        break
                    }
                }
            }
            if !containPiece(pieceList: pieceList, piece: _combinePieces) {
                pieceList.append(_combinePieces)
            }
        }
        
        var maxRow = 0
        var maxCol = 0
        for piece in pieceList {
            maxRow = max(maxRow, piece.count)
            maxCol = max(maxCol, piece[0].count)
        }
        
        var views: [UIView] = []
        for i in 0..<pieceList.count {
            let view = PieceView()
            view.setPiece(expandPiece(piece: pieceList[i], m3: maxRow, n3: maxCol))
            view.tag = i
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onPieceTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        gridLayout.columns = views.count
        gridLayout.itemRatio = Float(maxCol) / Float(maxRow)
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
        
        views = []
        for i in 0..<indexes.count {
            let view = PieceView()
            view.setPiece(expandPiece(piece: pieces[indexes[i]], m3: maxRow, n3: maxCol))
            views.append(view)
        }
        topGridLayout.columns = 3
        topGridLayout.itemRatio = Float(maxCol) / Float(maxRow)
        topGridLayout.itemSpacingRatio = 0.02
        topGridLayout.insetRatio = 0.05
        topGridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_2 manh ghep")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.topGridLayout.transform = .identity
        }
        scheduler.schedule(delay: delay + 0.5) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.gridLayout.alpha = 1
            }
        }
        let gridDelay = delay + 1.0 + gridLayout.showItems(startDelay: delay + 1.0)
        scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_2 manh ghep")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        topGridLayout.transform = CGAffineTransform(translationX: 0, y: 0)
        gridLayout.alpha = 0
    }
    
    // MARK: - Touch Handling
    @objc private func onPieceTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int else { return }
        
        if index == meIndex {
            UIView.animate(withDuration: 0) {
                self.coinView.frame = view.frame
            }
            animateCoinIfCorrect(view: coinView)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            pauseGame()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            pauseGame()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func rotate90(piece: [[Int]]) -> [[Int]] {
        let m = piece.count
        let n = piece[0].count
        var rotated = Array(repeating: Array(repeating: 0, count: m), count: n)
        for r in 0..<m {
            for c in 0..<n {
                rotated[c][m - 1 - r] = piece[r][c]
            }
        }
        return rotated
    }
    
    private func canFormPiece(piece1: [[Int]], piece2: [[Int]], piece3: [[Int]]) -> Bool {
        let p1Variants = [piece1, rotate90(piece: piece1), rotate90(piece: rotate90(piece: piece1)), rotate90(piece: rotate90(piece: rotate90(piece: piece1)))]
        let p2Variants = [piece2, rotate90(piece: piece2), rotate90(piece: rotate90(piece: piece2)), rotate90(piece: rotate90(piece: rotate90(piece: piece2)))]
        
        let m3 = piece3.count
        let n3 = piece3[0].count
        
        for p1Var in p1Variants {
            for p2Var in p2Variants {
                for rowOffset in -m3...m3 {
                    for colOffset in -n3...n3 {
                        if let combined = combineTwoAndTrim(piece1: p1Var, piece2: p2Var, rowOffset: rowOffset, colOffset: colOffset),
                           areEqual(a: combined, b: piece3) {
                            return true
                        }
                    }
                }
            }
        }
        return false
    }
    
    private func areEqual(a: [[Int]], b: [[Int]]) -> Bool {
        guard a.count == b.count && a[0].count == b[0].count else { return false }
        for i in 0..<a.count {
            for j in 0..<a[0].count {
                if a[i][j] != b[i][j] {
                    return false
                }
            }
        }
        return true
    }
    
    private func combineTwoIntoMaxSize(piece1: [[Int]], piece2: [[Int]], m3: Int, n3: Int, rowOffset: Int, colOffset: Int) -> [[Int]]? {
        let m1 = piece1.count
        let n1 = piece1[0].count
        let m2 = piece2.count
        let n2 = piece2[0].count
        
        var result = Array(repeating: Array(repeating: 0, count: n3), count: m3)
        
        for r in 0..<m1 {
            for c in 0..<n1 {
                let rr = r
                let cc = c
                if rr < 0 || rr >= m3 || cc < 0 || cc >= n3 {
                    return nil
                }
                result[rr][cc] = piece1[r][c]
            }
        }
        
        for r in 0..<m2 {
            for c in 0..<n2 {
                let val = piece2[r][c]
                if val != 0 {
                    let rr = r + rowOffset
                    let cc = c + colOffset
                    if rr < 0 || rr >= m3 || cc < 0 || cc >= n3 || result[rr][cc] != 0 {
                        return nil
                    }
                    result[rr][cc] = val
                }
            }
        }
        return result
    }
    
    private func combineTwoAndTrim(piece1: [[Int]], piece2: [[Int]], rowOffset: Int, colOffset: Int) -> [[Int]]? {
        let m1 = piece1.count
        let n1 = piece1[0].count
        let m2 = piece2.count
        let n2 = piece2[0].count
        
        let minRow = min(0, rowOffset)
        let maxRow = max(m1, m2 + rowOffset)
        let minCol = min(0, colOffset)
        let maxCol = max(n1, n2 + colOffset)
        
        let combinedHeight = maxRow - minRow
        let combinedWidth = maxCol - minCol
        var combined = Array(repeating: Array(repeating: 0, count: combinedWidth), count: combinedHeight)
        
        let baseRow = -minRow
        let baseCol = -minCol
        
        for r in 0..<m1 {
            for c in 0..<n1 {
                if piece1[r][c] != 0 {
                    combined[r + baseRow][c + baseCol] = piece1[r][c]
                }
            }
        }
        
        for r in 0..<m2 {
            for c in 0..<n2 {
                if piece2[r][c] != 0 {
                    let rr = r + rowOffset + baseRow
                    let cc = c + colOffset + baseCol
                    if combined[rr][cc] != 0 {
                        return nil
                    }
                    combined[rr][cc] = piece2[r][c]
                }
            }
        }
        
        var trimMinRow = combinedHeight, trimMaxRow = -1
        var trimMinCol = combinedWidth, trimMaxCol = -1
        for r in 0..<combinedHeight {
            for c in 0..<combinedWidth {
                if combined[r][c] != 0 {
                    trimMinRow = min(trimMinRow, r)
                    trimMaxRow = max(trimMaxRow, r)
                    trimMinCol = min(trimMinCol, c)
                    trimMaxCol = max(trimMaxCol, c)
                }
            }
        }
        
        if trimMaxRow == -1 {
            return [[0]]
        }
        
        let finalHeight = trimMaxRow - trimMinRow + 1
        let finalWidth = trimMaxCol - trimMinCol + 1
        var trimmed = Array(repeating: Array(repeating: 0, count: finalWidth), count: finalHeight)
        for r in 0..<finalHeight {
            for c in 0..<finalWidth {
                trimmed[r][c] = combined[trimMinRow + r][trimMinCol + c]
            }
        }
        
        return trimmed
    }
    
    private func isConnected(piece: [[Int]]) -> Bool {
        let m = piece.count
        let n = piece[0].count
        var startR = -1, startC = -1
        var totalNonZero = 0
        
        for r in 0..<m {
            for c in 0..<n {
                if piece[r][c] != 0 {
                    totalNonZero += 1
                    if startR == -1 {
                        startR = r
                        startC = c
                    }
                }
            }
        }
        
        if totalNonZero == 0 { return true }
        
        var visited = Array(repeating: Array(repeating: false, count: n), count: m)
        var visitedCount = 0
        var queue: [[Int]] = [[startR, startC]]
        visited[startR][startC] = true
        
        let directions = [[1, 0], [-1, 0], [0, 1], [0, -1]]
        
        while !queue.isEmpty {
            let cell = queue.removeFirst()
            let cr = cell[0], cc = cell[1]
            visitedCount += 1
            
            for dir in directions {
                let nr = cr + dir[0]
                let nc = cc + dir[1]
                if nr >= 0 && nr < m && nc >= 0 && nc < n && !visited[nr][nc] && piece[nr][nc] != 0 {
                    visited[nr][nc] = true
                    queue.append([nr, nc])
                }
            }
        }
        
        return visitedCount == totalNonZero
    }
    
    private func expandPiece(piece: [[Int]], m3: Int, n3: Int) -> [[Int]] {
        let m = piece.count
        let n = piece[0].count
        guard m3 >= m && n3 >= n else { fatalError("Kích thước mới quá nhỏ so với piece gốc!") }
        
        var expanded = Array(repeating: Array(repeating: 0, count: n3), count: m3)
        let rowOffset = (m3 - m) / 2
        let colOffset = (n3 - n) / 2
        
        for r in 0..<m {
            for c in 0..<n {
                expanded[rowOffset + r][colOffset + c] = piece[r][c]
            }
        }
        return expanded
    }
    
    private func combinePieces(piece1: [[Int]], piece2: [[Int]]) -> [[Int]] {
        let p1Variants = [piece1, rotate90(piece: piece1), rotate90(piece: rotate90(piece: piece1)), rotate90(piece: rotate90(piece: rotate90(piece: piece1)))]
        let p2Variants = [piece2, rotate90(piece: piece2), rotate90(piece: rotate90(piece: piece2)), rotate90(piece: rotate90(piece: rotate90(piece: piece2)))]
        
        let p1Var = p1Variants.randomElement()!
        let p2Var = p2Variants.randomElement()!
        
        let m3 = piece1.count + piece2.count
        let n3 = piece1[0].count + piece2[0].count
        var tries = 0
        while tries <= 100 {
            tries += 1
            let rowOffset = -m3 + Int.random(in: 0...(2 * m3))
            let colOffset = -n3 + Int.random(in: 0...(2 * n3))
            if let combined = combineTwoAndTrim(piece1: p1Var, piece2: p2Var, rowOffset: rowOffset, colOffset: colOffset),
               isConnected(piece: combined) {
                return combined
            }
        }
        return []
    }
    
    private func containPiece(pieceList: [[[Int]]], piece: [[Int]]) -> Bool {
        for p in pieceList {
            if areEqual(a: p, b: piece) {
                return true
            }
        }
        return false
    }
}

// MARK: - PieceView
class PieceView: UIView {
    private var piece: [[Int]]?
    private var cellSize: CGFloat = 0
    
    private var color1 = UIColor.white
    private let color2 = UIColor(red: 173/255, green: 231/255, blue: 244/255, alpha: 1) // #ADE7F4
    private let borderColor1 = UIColor(red: 197/255, green: 214/255, blue: 232/255, alpha: 1) // #C5D6E8
    private let borderColor2 = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    var edgeOn = false // thụt vào để hiện được nét ở các mép
    
    // Dictionary để lưu trữ các layer cho từng ô
    private var cellLayers: [String: CAShapeLayer] = [:]
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupView()
    }
    
    private func setupView() {
        backgroundColor = .clear
        clipsToBounds = false // Cho phép vẽ ngoài bounds
    }
    
    func updateColor1(color1: UIColor) {
        self.color1 = color1
        updateLayers()
    }
    
    func setPiece(_ piece: [[Int]]) {
        self.piece = piece
        clipsToBounds = false
        updateLayers()
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        guard let piece = piece else {
            frame.size = CGSize(width: 100, height: 100)
            return
        }
        
        let rows = CGFloat(piece.count)
        let cols = CGFloat(piece[0].count)
        let availableWidth = bounds.width
        let availableHeight = bounds.height
        cellSize = min(availableWidth / cols, availableHeight / rows)
        if cellSize <= 0 { cellSize = 1 }
        
        if edgeOn {
            let strokeWidth = cellSize / 15
            let totalWidth = cols * cellSize + strokeWidth
            let totalHeight = rows * cellSize + strokeWidth
            frame.size = CGSize(width: max(totalWidth, bounds.width), height: max(totalHeight, bounds.height))
        }
        
        updateLayers()
    }
    
    private func updateLayers() {
        // Xóa các layer cũ
        cellLayers.values.forEach { $0.removeFromSuperlayer() }
        cellLayers.removeAll()
        
        guard let piece = piece else { return }
        
        let rows = piece.count
        let cols = piece[0].count
        let strokeWidth = cellSize / 15
        let startX = edgeOn ? strokeWidth / 2 : 0
        let startY = edgeOn ? strokeWidth / 2 : 0
        
        // Vòng lặp đầu tiên: Tạo layer cho các ô có giá trị 1
        for r in 0..<rows {
            for c in 0..<cols {
                let val = piece[r][c]
                if val == 1 {
                    let x = startX + CGFloat(c) * cellSize
                    let y = startY + CGFloat(r) * cellSize
                    let cellRect = CGRect(x: x, y: y, width: cellSize, height: cellSize)
                    
                    let shapeLayer = CAShapeLayer()
                    shapeLayer.path = UIBezierPath(rect: cellRect).cgPath
                    shapeLayer.fillColor = color1.cgColor
                    shapeLayer.strokeColor = borderColor1.cgColor
                    shapeLayer.lineWidth = strokeWidth
                    
                    layer.addSublayer(shapeLayer)
                    cellLayers["\(r)_\(c)"] = shapeLayer
                }
            }
        }
        
        // Vòng lặp thứ hai: Tạo layer cho các ô có giá trị 2 và 3
        for r in 0..<rows {
            for c in 0..<cols {
                let val = piece[r][c]
                if val == 2 || val == 3 {
                    let x = startX + CGFloat(c) * cellSize
                    let y = startY + CGFloat(r) * cellSize
                    let cellRect = CGRect(x: x, y: y, width: cellSize, height: cellSize)
                    
                    let shapeLayer = CAShapeLayer()
                    shapeLayer.path = UIBezierPath(rect: cellRect).cgPath
                    shapeLayer.lineWidth = strokeWidth
                    
                    if val == 2 {
                        shapeLayer.fillColor = color2.cgColor
                        shapeLayer.strokeColor = borderColor2.cgColor
                    } else { // val == 3
                        shapeLayer.fillColor = borderColor2.cgColor
                        shapeLayer.strokeColor = borderColor2.cgColor
                    }
                    
                    layer.addSublayer(shapeLayer)
                    cellLayers["\(r)_\(c)"] = shapeLayer
                }
            }
        }
    }
}
