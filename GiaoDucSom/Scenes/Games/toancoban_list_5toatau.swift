//
//  toancoban_list_5toatau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 31/3/25.
//


import UIKit
import SnapKit

class toancoban_list_5toatau: NhanBietGameFragment {
    // MARK: - Properties
    private var gridBottomLayout: MyGridView!
    private var gridBottom2Layout: MyGridView!
    private var listviews2: [UIView] = []
    private var listViews: [UIView] = []
    private var move = 0
    private var zPosition = 10.0
    private var text1: UILabel!
    
    // MARK: - Lifecycle
    
    // MARK: - Configure Layout
    private func configureLayout() {
        backgroundColor = UIColor(red: 220/255, green: 255/255, blue: 183/255, alpha: 1) // #DCFFB7
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_door"))
        bgImage.contentMode = .scaleAspectFill
        addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let lineBottom = UIView() // Guideline
        //lineBottom.backgroundColor = .green
        addSubview(lineBottom)
        lineBottom.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(0)
            make.bottom.equalToSuperview().multipliedBy(0.9) // 0.9 percent
        }
        
        let container = UIView()
        container.clipsToBounds = false
        addSubview(container)
        container.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.width.equalTo(container.snp.height).multipliedBy(1000.0 / 400.0) // Ratio 1000:400
            make.bottom.equalTo(lineBottom)
        }
        
        let viewBg = UIImageView(image:  Utilities.SVGImage(named: "math_5toatau_1"))
        viewBg.contentMode = .scaleToFill
        container.addSubviewWithInset(subview: viewBg, inset: 0)
        
        text1 = AutosizeLabel()
        text1.text = "1"
        text1.textColor = .white
        text1.textAlignment = .center
        text1.font = UIFont(name: "SVN-Freude", size: 20) // Giả định font đã thêm
        text1.adjustsFontSizeToFitWidth = true
        container.addSubview(text1)
        text1.snp.makeConstraints { make in
            make.width.equalTo(text1.snp.height) // Ratio 1:1
            make.height.equalTo(container).multipliedBy(0.3)
            //make.centerX.equalToSuperview().multipliedBy(0.4) // bias 0.2
            //make.centerY.equalToSuperview().multipliedBy(1.6) // bias 0.8
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.text1.snapToHorizontalBias(horizontalBias: 0.2)
            self.text1.snapToVerticalBias(verticalBias: 0.8)
        }
        
        gridBottomLayout = MyGridView()
        gridBottomLayout.alpha = 0.01
        gridBottomLayout.clipsToBounds = false
        container.addSubview(gridBottomLayout)
        gridBottomLayout.snp.makeConstraints { make in
            make.width.equalTo(gridBottomLayout.snp.height).multipliedBy(648.0 / 137.0) // Ratio 648:137
            make.height.equalTo(container).multipliedBy(0.34)
            //make.centerX.equalToSuperview().multipliedBy(1.76) // bias 0.88
            //make.centerY.equalToSuperview().multipliedBy(1.92) // bias 0.96
        }
        
        gridBottom2Layout = MyGridView()
        //gridBottom2Layout.backgroundColor = .green // #0f00
        container.addSubview(gridBottom2Layout)
        gridBottom2Layout.snp.makeConstraints { make in
            make.width.equalTo(gridBottom2Layout.snp.height).multipliedBy(648.0 / 137.0) // Ratio 648:137
            make.height.equalTo(container).multipliedBy(0.34)
            //make.centerX.equalToSuperview().multipliedBy(1.76) // bias 0.88
            //make.centerY.equalToSuperview().multipliedBy(1.92) // bias 0.96
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.gridBottomLayout.snapToHorizontalBias(horizontalBias: 0.88)
            self.gridBottomLayout.snapToVerticalBias(verticalBias: 0.96)
            self.gridBottom2Layout.snapToHorizontalBias(horizontalBias: 0.88)
            self.gridBottom2Layout.snapToVerticalBias(verticalBias: 0.96)
        }
    }
    
    override func configureLayout(_ view: UIView) {
        configureLayout()
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "toan/toan_5 toa tau")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_5 toa tau")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        var listViews: [UIView] = []
        let answer = "2345"
        for char in answer {
            let view = createItemView(text: String(char))
            listViews.append(view)
            view.tag = Int(String(char)) ?? 0
        }
        gridBottomLayout.columns = answer.count
        gridBottomLayout.itemRatio = 162.0 / 137.0
        gridBottomLayout.itemSpacingRatio = 0
        gridBottomLayout.insetRatio = 0
        gridBottomLayout.reloadItemViews(views: listViews)
        self.listViews = listViews
        
        var randomIndexes: [Int]
        repeat {
            randomIndexes = Utils.generatePermutation(answer.count)
            let foundAnswer = zip(answer, randomIndexes).contains { $0 != answer[$1] }
            if foundAnswer { break }
        } while true
        
        listviews2.removeAll()
        for i in 0..<answer.count {
            let text = String(answer[randomIndexes[i]])
            let view = createItemView(text: text)
            listviews2.append(view)
            view.tag = Int(text) ?? 0
            //AnimationUtils.setTouchEffect(view: view)
                       
            view.isUserInteractionEnabled = true
        }
        gridBottom2Layout.columns = answer.count
        gridBottom2Layout.itemRatio = 162.0 / 137.0
        gridBottom2Layout.itemSpacingRatio = 0
        gridBottom2Layout.insetRatio = 0
        gridBottom2Layout.reloadItemViews(views: listviews2)
        
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
    }
    
    // MARK: - Item View Creation
    let imageToaTau = Utilities.SVGImage(named: "math_5toatau_2")
    private func createItemView(text: String) -> UIView {
        let container = UIButton()
        //container.backgroundColor = .red.withAlphaComponent(1)
        
        let bgView = UIImageView(image: imageToaTau)
        bgView.contentMode = .scaleToFill
        container.addSubviewWithInset(subview: bgView, inset: 0)
        
        let textView = AutosizeLabel()
        textView.text = text
        textView.textColor = .white
        textView.textAlignment = .center
        textView.font = .Freude(size: 20)
        container.addSubview(textView)
        
        textView.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.74)
            make.left.right.top.equalToSuperview()
        }
        return container
    }
    var selectedView : UIView?
    var busy = false
    // MARK: - Gesture Handling
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            for view in gridBottom2Layout.subviews {
                if sender.placeInView(view: view){
                    selectedView = view
                    zPosition += 1
                    view.layer.zPosition = zPosition
                    UIView.animate(withDuration: 0.3) {
                        view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                    }
                    //let background = view.viewWithTag(2) as! SVGImageView
                    //background.SVGName = "drag bg3"
                    return;
                }
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if !busy {
            guard let selectedView = selectedView else { return }
            var oldIndex = listviews2.firstIndex(of: selectedView)!
            for i in 0..<listviews2.count {
                let view2 = listviews2[i]
                if selectedView == view2 {
                    continue
                }
                if sender.placeInView(view: view2) {
                    playSound(name: "effect/bubble" + String(Int.random(in: 1...4)), delay: 0)
                    busy = true
                    scheduler.schedule(delay: 0.3, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.busy = false
                    })
                    var index = i
                    var delta = index > oldIndex ? 1 : -1
                    var start = oldIndex
                    while start != index {
                        var viewTmp = listviews2[start]
                        listviews2[start] = listviews2[start + delta]
                        listviews2[start + delta] = viewTmp
                        start += delta
                    }
                    for j in 0..<listviews2.count {
                        if listviews2[j] != selectedView {
                            let dest = listViews[j]
                            let source = listviews2[j]
                            var p = dest.convert( CGPointMake(dest.bounds.width / 2, dest.bounds.height / 2), to: source)
                            p.x -= source.bounds.width / 2
                            p.y -= source.bounds.height / 2
                            UIView.animate(withDuration: 0.2) {
                                var tran = source.transform
                                tran.tx += p.x
                                tran.ty += p.y
                                source.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
                            } completion: { [weak self] done in
                                guard let self = self else { return }
                                
                            }
                        }
                    }
                    break
                }
            }
        }
        if state == .ended || state == .cancelled || state == .failed {
            playSound("effect/word puzzle drop")
            for j in 0..<listviews2.count {
                if listviews2[j] == selectedView {
                    let source = listviews2[j]
                    let dest = listViews[j]
                    var p = dest.convert( CGPointMake(dest.bounds.width / 2, dest.bounds.height / 2), to: source)
                    p.x -= source.bounds.width / 2
                    p.y -= source.bounds.height / 2
                    UIView.animate(withDuration: 0.2) {
                        var tran = source.transform
                        tran.tx += p.x
                        tran.ty += p.y
                        source.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
                    } completion: { [weak self] done in
                        guard let self = self else { return }
                        
                    }
                }
            }
            for j in 0..<listviews2.count {
                let textview = listviews2[j].subviews[1] as! AutosizeLabel
                let textview2 = listViews[j].subviews[1] as! AutosizeLabel
                let rightOrder = textview.text == textview2.text
                textview.textColor = .color(hex: rightOrder ? "#73D048" : "#68C1FF")
            }
            let index = listviews2.firstIndex(of: selectedView!)!
            scheduler.schedule(delay: 0.3, execute: {
                [weak self] in
                guard let self = self else { return }
                for view in self.listviews2 {
                    view.transform = .identity
                }
                self.gridBottom2Layout.reloadItemViews(views: self.listviews2)
            })
            move += 1
            checkFinish()
        }
    }
    
    // MARK: - Check Finish
    private func checkFinish() {
        let finish = listviews2.enumerated().allSatisfy { i, view in
            let tag1 = (view.subviews[1] as? UILabel)?.text
            let tag2 = (listViews[i].subviews[1] as? UILabel)?.text
            return tag1 == tag2
        }
        
        if finish {
            pauseGame()
            animateCoinIfCorrect(view: text1)
            var delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound())
            for i in 0..<5 {
                let number = i + 1
                scheduler.schedule(delay: delay) { [weak self] in
                    guard let self = self else { return }
                    self.playSound("topics/Numbers/\(number)")
                    if number > 1 {
                        let itemView = self.gridBottom2Layout.subviews[number - 2]
                        itemView.transform = .identity
                        UIView.animate(withDuration: 0.5) {
                            itemView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                        } completion: { _ in
                            itemView.transform = .identity
                        }
                    } else {
                        UIView.animate(withDuration: 0.5) {
                            self.text1.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                        } completion: { _ in
                            self.text1.transform = .identity
                        }
                    }
                }
                delay += 1000 / 1000 // 1000ms = 1s
            }
            delay += playSound(delay: delay, names: [endGameSound()])
            scheduler.schedule(delay: delay + 1000 / 1000) { [weak self] in
                self?.finishGame()
            }
        }
    }
}

// MARK: - Supporting Classes
