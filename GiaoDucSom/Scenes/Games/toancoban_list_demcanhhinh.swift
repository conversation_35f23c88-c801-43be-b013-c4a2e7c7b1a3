//
//  toancoban_list_demcanhhinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_demcanhhinh: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: UIImageView!
    private var gridLayout: MyGridView!
    private let shapes = ["hexagon", "kite", "parallelogram", "pentagon", "rectangle", "rhombus", "right triangle", "square", "trapezoid", "triangle"]
    private let edges = [6, 4, 4, 5, 4, 4, 3, 4, 4, 3]
    private var meIndex: Int = 0
    private var count: Int = 0
    let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
            make.width.equalTo(svgView.snp.height) // Ratio 1:1
            make.left.equalToSuperview().inset(view.frame.height * 0.05) // Margin 5% height
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        meIndex = Int.random(in: 0..<shapes.count)
        count = edges[meIndex]
        let svg = Utilities.GetSVGKImage(named: "topics/2D Shapes/\(shapes[meIndex]).svg")
        svgView.image = svg.uiImage
        buildGrid(grid: gridLayout, count: count)
        svgView.alpha = 0
    }
    
    override func createGame() {
        super.createGame()
        svgView.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.svgView.alpha = 1
        }
        gridLayout.alpha = 0
        
        var delay = playSound(openGameSound(), "toan/toan_dem canh1", "topics/2D Shapes/\(shapes[meIndex])", "toan/toan_dem canh2")
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.svgView.transform = .identity // translationX(0)
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 1
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem canh1", "topics/2D Shapes/\(shapes[meIndex])", "toan/toan_dem canh2")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == count
        var delay = playSound("topics/Numbers/\(value)")
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            delay += playSound(delay: delay, names: finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
