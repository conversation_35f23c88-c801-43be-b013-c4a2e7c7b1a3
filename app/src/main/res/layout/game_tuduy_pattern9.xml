<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FFFFFF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.5">

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/left_grid_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="1"
            android:background="#74B6FF"
            app:columnCount="2"
            app:horizontalInsetRatio="0.05"
            app:itemRatio="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.1"
            app:spacingRatio="0.05"
            app:verticalInsetRatio="0.05" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#74B6FF"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.5">

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/grid_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="1"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:columnCount="2"
            app:horizontalInsetRatio="0.05"
            app:itemRatio="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.1"
            app:spacingRatio="0.05"
            app:verticalInsetRatio="0.05" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>