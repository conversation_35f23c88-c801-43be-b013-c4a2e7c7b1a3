//
//  tuduy_list_robothutbui.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 11/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_robothutbui: NhanBietGameFragment {
    // MARK: - Properties
    private var imageVacuumZoom: UIImageView!
    private var gridLayoutMap: MyGridView!
    private let ROW = 5
    private let COL = 5
    private var data: [[Int]] = Array(repeating: Array(repeating: 0, count: 5), count: 5)
    private var moves: [[Int]] = []
    private var viewCar: UIView!
    private var imageCar: UIImageView!
    private var currentView: UIImageView?
    private var viewsDone: [UIView] = []
    private var viewsGo: [UIView] = []
    private var timeoutTimer: TimeoutTimer!
    private var playIndex: Int = -1
    private var xCar: Int = 0
    private var yCar: Int = 0
    private var playing: Bool = false
    private var TREES: [[Int]] = []
    private var CARS: [[Int]] = []
    private var ROAD: [[Int]] = []
    private var count: Int = 0
    private var batterySVG: SVGKImage?
    private var imagePin: UIImageView!
    private var vacuumAnimationView: XamlAnimationView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 98/255, green: 106/255, blue: 122/255, alpha: 1) // #626A7A
        let view02 = UIView()
        view.addSubview(view02)
        view02.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview()
        }
        let viewSquare = UIView()
        viewSquare.backgroundColor = UIColor(red: 34/255, green: 36/255, blue: 45/255, alpha: 1) // #22242D
        view02.addSubview(viewSquare)
        viewSquare.makeViewCenterAndKeep(ratio: 1)
        
        
        gridLayoutMap = MyGridView()
        gridLayoutMap.backgroundColor = UIColor(red: 58/255, green: 60/255, blue: 71/255, alpha: 1) // #3A3C47
        gridLayoutMap.columns = COL
        gridLayoutMap.insetRatio = 0.05
        viewSquare.addSubview(gridLayoutMap)
        gridLayoutMap.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(viewSquare.frame.width * 0.015) // 0.97 height percent
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayoutMap.addGestureRecognizer(panGesture)
        
        imageVacuumZoom = UIImageView(image: Utilities.SVGImage(named: "tuduy_vacuum_end2"))
        imageVacuumZoom.contentMode = .scaleAspectFit
        //imageVacuumZoom.isHidden = true
        imageVacuumZoom.alpha = 0
        viewSquare.addSubview(imageVacuumZoom)
        imageVacuumZoom.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(viewSquare.frame.width * 0.015) // 0.97 height percent
        }
        
        viewCar = UIView()
        viewCar.isUserInteractionEnabled = false
        viewSquare.addSubview(viewCar)
        viewCar.snp.makeConstraints { make in
            make.width.equalTo(viewSquare).multipliedBy(0.2)
            make.height.equalTo(viewCar.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        imageCar = UIImageView(image: Utilities.SVGImage(named: "tuduy_car"))
        imageCar.contentMode = .scaleAspectFit
        imageCar.isHidden = true
        viewCar.addSubview(imageCar)
        imageCar.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        vacuumAnimationView = XamlAnimationView()
        vacuumAnimationView.loadFile(named: "images/animations/xaml/vacuum.xaml")
        viewCar.addSubview(vacuumAnimationView)
        vacuumAnimationView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let pinContainer = UIView()
        //pinContainer.backgroundColor = .red
        view.addSubview(pinContainer)
        pinContainer.snp.makeConstraints { make in
            make.top.right.bottom.equalToSuperview()
            make.left.equalTo(viewSquare.snp.right)
        }
        imagePin = UIImageView()
        imagePin.contentMode = .scaleAspectFit
        pinContainer.addSubview(imagePin)
        imagePin.makeViewCenterAndKeep(ratio: 192.0 / 1039.0)
        addActionOnLayoutSubviews {
            self.imagePin.snapToHorizontalBias(horizontalBias: 0.3)
            self.imagePin.snapToVerticalBias(verticalBias: 0.5)
        }
        timeoutTimer = TimeoutTimer()
        timeoutTimer.duration = 0.3
        timeoutTimer.onActived = {
            [weak self] in
            guard let self = self else { return }
            if self.playing == false {
                self.play()
            }
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        generateData()
        buildMap()
        batterySVG = Utilities.GetSVGKImage(named: "tuduy_vacuum_pin")
        updateBattery(level: ROAD.count)
        let delay = playSound(openGameSound(), "tuduy/robot hut bui")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
            self?.vacuumAnimationView.startAnimation()
            self?.playSound("effect/vacuum_start")
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/robot hut bui")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: gridLayoutMap)
        switch gesture.state {
        case .began, .changed:
            let currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
            if currentView != self.currentView {
                processView(view: currentView)
            }
        case .ended:
            break
        default:
            break
        }
    }
    
    // MARK: - Game Logic
    private func updateBattery(level: Int) {
        guard let batterySVG = batterySVG else {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.updateBattery(level: level)
            }
            return
        }
        let color = level > 3 ? UIColor(red: 98/255, green: 249/255, blue: 102/255, alpha: 1) : UIColor(red: 255/255, green: 120/255, blue: 127/255, alpha: 1) // #62F966, #FF787F
        let colorGray = UIColor(red: 98/255, green: 106/255, blue: 122/255, alpha: 1) // #626A7A
        for i in 2..<batterySVG.caLayerTree.sublayers!.count {
            (batterySVG.caLayerTree?.sublayers?[i] as! CAShapeLayer).fillColor = level > i - 2 ? color.cgColor : colorGray.cgColor
        }
        imagePin.image = batterySVG.uiImage
    }
    
    private func processView(view: UIView?) {
        guard let view = view as? UIImageView else { return }
        let oldItemIndex = gridLayoutMap.subviews.firstIndex(of: currentView ?? UIView()) ?? 0
        let oldX = oldItemIndex / COL
        let oldY = oldItemIndex % COL
        let itemIndex = gridLayoutMap.subviews.firstIndex(of: view) ?? 0
        let x = itemIndex / COL
        let y = itemIndex % COL
        
        if abs(x - oldX) + abs(y - oldY) != 1 { return }
        if viewsDone.count >= ROAD.count { return }
        
        if data[x][y] == 0 || data[x][y] == 2 || data[x][y] == 3 {
            view.image = Utilities.SVGImage(named: "tuduy_vacuum_bg3")
            if checkHighlight(view: currentView) {
                currentView?.image = Utilities.SVGImage(named: "tuduy_vacuum_bg1")
            }
            currentView = view
            viewsDone.append(view)
            move(x: y - oldY, y: x - oldX)
            timeoutTimer.schedule()
        }
    }
    
    private func findViewUnder(x: Float, y: Float) -> UIView? {
        for i in (0..<gridLayoutMap.subviews.count).reversed() {
            let view = gridLayoutMap.subviews[i]
            if x >= Float(view.frame.minX) && x <= Float(view.frame.maxX) && y >= Float(view.frame.minY) && y <= Float(view.frame.maxY) {
                if isPixelVisible(view: view, x: Int(x), y: Int(y)) {
                    return view
                }
            }
        }
        return nil
    }
    
    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard view.isHidden == false else { return false }
        return view.isPixelVisible(x: x, y: y)
    }
    
    private func play() {
        if playIndex == -1 {
            let car = CARS[0]
            xCar = car[0]
            yCar = car[1]
        }
        playIndex += 1
        if playIndex == moves.count {
            if xCar == CARS[1][0] && yCar == CARS[1][1] {
                viewCar.alpha = 0
                animateCoinIfCorrect(view: viewCar)
                let delay = playSound("effect/vacuum_end", getCorrectHumanSound(), endGameSound())
                imageVacuumZoom.alpha = 1
                scheduler.schedule(after: delay) {
                    UIView.animate(withDuration: 0.6) {
                        self.imageVacuumZoom.transform = .identity
                        self.imageVacuumZoom.alpha = 1
                    }
                    self.scheduler.schedule(after: 3.0) { [weak self] in
                        self?.finishGame()
                    }
                }
            } else if ROAD.count == moves.count {
                setGameWrong()
                animateCoinIfCorrect(view: viewCar)
                self.vacuumAnimationView.stopAnimation(name: "sb")
                let delay = playSound("effect/vacuum_end", "effect/end game2")
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            }
            playing = false
            playIndex -= 1
            return
        }
        
        playing = true
        let move = moves[playIndex]
        let x = move[0]
        let y = move[1]
        xCar += x
        yCar += y
        
        let rotation = vacuumAnimationView.transform.rotationAngle()
        let newRotation = y == 0 ? (x == 1 ? .pi / 2.0 : 3.0 * .pi / 2.0) : (y == 1 ? 0.0 : .pi)
        let delta = abs(newRotation - rotation)
        let delay = TimeInterval(delta < .pi ? delta : 2 * .pi - delta) * 2 * 180 / .pi / 1000
        
        if rotation != newRotation {
            UIView.animate(withDuration: delay) {
                self.vacuumAnimationView.transform = CGAffineTransform(rotationAngle: newRotation)
            } completion: { _ in
                self.vacuumAnimationView.transform = CGAffineTransform(rotationAngle: (self.vacuumAnimationView.transform.rotationAngle() + 2 * .pi).truncatingRemainder(dividingBy: 2 * .pi))
            }
        }
        
        let nextView = gridLayoutMap.subviews[xCar * COL + yCar] as! UIImageView
        playSound(name: "effect/vacuum_step", delay: delay)
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.viewCar.moveToCenter(of: nextView, duration: 1.0) {
                [weak self] _ in
                guard let self = self else { return }
                if self.xCar < 0 || self.xCar >= self.ROW || self.yCar < 0 || self.yCar >= self.COL || self.data[self.xCar][self.yCar] == 1 {
                    self.imageCar.image = Utilities.SVGImage(named: "tuduy_car2")
                    nextView.alpha = 0.5
                    return
                }
                self.viewsGo.append(nextView)
                updateBattery(level: ROAD.count - playIndex - 1)
                let check = self.viewsDone[self.viewsGo.count..<self.viewsDone.count].contains(nextView)
                if !check {
                    self.scheduler.schedule(after: 0.1) { [weak self] in
                        guard self != nil else { return }
                        nextView.image = Utilities.SVGImage(named: "tuduy_vacuum_bg2")
                    }
                }
                self.play()
            }
        }
    }
    
    private func checkHighlight(view: UIView?) -> Bool {
        updateBattery(level: ROAD.count - playIndex - 1)
        return viewsDone[viewsGo.count..<viewsDone.count].contains { $0 == view }
    }
    
    private func move(x: Int, y: Int) {
        if moves.count == ROAD.count { return }
        moves.append([y, x])
    }
    
    private func generateData() {
        while true {
            data = Array(repeating: Array(repeating: 0, count: COL), count: ROW)
            let TREE = 5 + Int.random(in: 0..<3)
            let CAR = 2
            TREES = []
            CARS = []
            
            for i in 0..<(TREE + CAR) {
                while true {
                    let x = Int.random(in: 0..<ROW)
                    let y = Int.random(in: 0..<COL)
                    if data[x][y] == 0 {
                        data[x][y] = i < TREE ? 1 : 2
                        if data[x][y] == 1 {
                            TREES.append([x, y])
                        } else {
                            CARS.append([x, y])
                        }
                        break
                    }
                }
            }
            
            count = 0
            if checkDirectConnect(a: CARS[0], b: CARS[1]) { continue }
            ROAD = findRoadFromCar0ToCar1() ?? []
            if ROAD.count < 5 { continue }
            
            for i in 1..<ROAD.count {
                let road = ROAD[i]
                data[road[0]][road[1]] = 3
            }
            return
        }
    }
    
    private func buildMap() {
        moves = []
        var views: [UIView] = []
        for i in 0..<ROW {
            for j in 0..<COL {
                let view = UIImageView()
                view.isUserInteractionEnabled = true
                if data[i][j] == 1 {
                    view.backgroundColor = .clear
                }
                if data[i][j] == 0 || data[i][j] == 2 || data[i][j] == 3 {
                    view.image = Utilities.SVGImage(named: "tuduy_vacuum_bg2")
                }
                if data[i][j] == 2 {
                    if CARS[0][0] == i && CARS[0][1] == j {
                        currentView = view
                        scheduler.schedule(after: 0.1) {
                            self.viewCar.moveToCenter(of: view, duration: 0.1)
                        }
                    } else {
                        view.image = Utilities.SVGImage(named: "tuduy_vacuum_end1")
                        scheduler.schedule(after: 0.1) {
                            self.imageVacuumZoom.frame = view.frame
                            self.imageVacuumZoom.alpha = 0
                        }
                    }
                }
                view.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
                views.append(view)
            }
        }
        gridLayoutMap.reloadItemViews(views: views)
    }
    
    private func checkDirectConnect(a: [Int], b: [Int]) -> Bool {
        count += 1
        if count > 100 { count += 1 }
        
        if a[0] == b[0] || a[1] == b[1] {
            if a[0] == b[0] {
                let minY = min(a[1], b[1])
                let maxY = max(a[1], b[1])
                for i in (minY + 1)..<maxY {
                    if data[a[0]][i] != 0 { return false }
                }
                return true
            } else {
                let minX = min(a[0], b[0])
                let maxX = max(a[0], b[0])
                for i in (minX + 1)..<maxX {
                    if data[i][a[1]] != 0 { return false }
                }
                return true
            }
        }
        
        if data[a[0]][b[1]] == 0 {
            if checkDirectConnect(a: [a[0], b[1]], b: a) && checkDirectConnect(a: [a[0], b[1]], b: b) {
                return true
            }
        }
        if data[b[0]][a[1]] == 0 {
            if checkDirectConnect(a: [b[0], a[1]], b: a) && checkDirectConnect(a: [b[0], a[1]], b: b) {
                return true
            }
        }
        return false
    }
    
    private func findRoadFromCar0ToCar1() -> [[Int]]? {
        var distance = Array(repeating: Array(repeating: -1, count: COL), count: ROW)
        distance[CARS[0][0]][CARS[0][1]] = 0
        var queue: [[Int]] = [CARS[0]]
        
        while !queue.isEmpty {
            let u = queue.removeFirst()
            let directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
            for d in directions {
                let x = u[0] + d[0]
                let y = u[1] + d[1]
                if x >= 0 && y >= 0 && x < ROW && y < COL && (data[x][y] == 0 || data[x][y] == 2) && distance[x][y] == -1 {
                    distance[x][y] = distance[u[0]][u[1]] + 1
                    queue.append([x, y])
                }
            }
        }
        
        if distance[CARS[1][0]][CARS[1][1]] == -1 { return nil }
        
        var result: [[Int]] = []
        var x = CARS[1][0]
        var y = CARS[1][1]
        while x != CARS[0][0] || y != CARS[0][1] {
            result.append([x, y])
            let directions = [[-1, 0], [1, 0], [0, -1], [0, 1]]
            for d in directions {
                let u = x - d[0]
                let v = y - d[1]
                if u >= 0 && v >= 0 && u < ROW && v < COL && (data[u][v] == 0 || data[u][v] == 2) && distance[u][v] == distance[x][y] - 1 {
                    x = u
                    y = v
                    break
                }
            }
        }
        return result
    }
}



class XamlAnimationView: UIView {
    var rotation: CGFloat = 0 {
        didSet { transform = CGAffineTransform(rotationAngle: rotation * .pi / 180) }
    }
    
    func loadFile(named: String) {
        backgroundColor = .red
        // Giả lập, cần thay bằng implement thực tế
    }
    
    func startAnimation() {
        // Giả lập, cần thay bằng implement thực tế
    }
    
    func startAnimation(name: String) {
        // Giả lập, cần thay bằng implement thực tế
    }
    
    func stopAnimation(name: String) {
        // Giả lập, cần thay bằng implement thực tế
    }
}

extension UIView {
    func snapshot() -> UIImage? {
        UIGraphicsBeginImageContextWithOptions(bounds.size, false, 0)
        defer { UIGraphicsEndImageContext() }
        drawHierarchy(in: bounds, afterScreenUpdates: true)
        return UIGraphicsGetImageFromCurrentImageContext()
    }
}

extension UIImage {
    func getPixelColor(at point: CGPoint) -> UIColor {
        guard let cgImage = cgImage, let dataProvider = cgImage.dataProvider else { return .clear }
        let pixelData = dataProvider.data
        let data: UnsafePointer<UInt8> = CFDataGetBytePtr(pixelData)
        
        let pixelInfo: Int = ((Int(size.width) * Int(point.y)) + Int(point.x)) * 4
        let r = CGFloat(data[pixelInfo]) / 255.0
        let g = CGFloat(data[pixelInfo + 1]) / 255.0
        let b = CGFloat(data[pixelInfo + 2]) / 255.0
        let a = CGFloat(data[pixelInfo + 3]) / 255.0
        
        return UIColor(red: r, green: g, blue: b, alpha: a)
    }
}
/*
struct R {
    struct drawable {
        static let tuduy_car = 1
        static let tuduy_car2 = 2
        static let tuduy_vacuum_bg1 = 3
        static let tuduy_vacuum_bg2 = 4
        static let tuduy_vacuum_bg3 = 5
        static let tuduy_vacuum_end1 = 6
        static let tuduy_vacuum_end2 = 7
        static let tuduy_car_fuel = 8
        static let tuduy_car_arrow2 = 9
    }
    struct raw {
        static let tuduy_vacuum_pin = 1
    }
}
*/
