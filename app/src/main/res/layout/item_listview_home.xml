<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.AnimatedConstraintView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootview"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="#0f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1.2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kidsup.giaoducsom.view.RatioImageView
            android:id="@+id/imageView"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/btn_subject_thu_thach_ngay"
            app:layout_constraintDimensionRatio="494.8:444"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <TextView
            android:id="@+id/textview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:lines="1"
            android:text=""
            android:textColor="#849BFE"
            app:autoSizeMaxTextSize="1500dp"
            app:autoSizeMinTextSize="5dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.95"
            app:layout_constraintWidth_percent="0.4" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_bonus_level"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="x5"
            android:textColor="#fff"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintHeight_percent="0.15"
            app:layout_constraintHorizontal_bias="0.32"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.08" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</com.kidsup.giaoducsom.view.AnimatedConstraintView>
