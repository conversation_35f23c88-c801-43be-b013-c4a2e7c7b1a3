<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F8EFDE"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#EEE3D0"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/grid_layout_2"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/toan_khoigachmau_orange1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintHorizontal_bias="0.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/toan_khoigachmau_orange1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>