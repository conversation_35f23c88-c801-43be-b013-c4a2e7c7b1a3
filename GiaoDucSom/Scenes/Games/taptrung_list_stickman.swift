//
//  taptrung_list_stickman.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_stickman: NhanBietGameFragment {
    // MARK: - Properties
    private let stroke: CGFloat = 50
    private var touchView: UIView!
    private var contentLayout: UIView!
    private var containerLayout: UIView!
    private var body: BodyParser.Body?
    private var body0: BodyParser.Body?
    private var scale: CGFloat = 0
    private var bones0: [UIView] = []
    private var bones: [UIView] = []
    private var viewToHighlight: [UIView: UIView] = [:]
    private var zoomLevel: CGFloat = 1
    private var mainView0: UIView!
    private var mainView: UIView!
    private var coinView: UIView!
    let zoomView = UIScrollView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.white        
        
        containerLayout = UIView()
        containerLayout.clipsToBounds = false
        view.addSubview(containerLayout)
        containerLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
                
        zoomView.clipsToBounds = false
        zoomView.minimumZoomScale = 1.0
        zoomView.maximumZoomScale = 3.0
        zoomView.delegate = self
        containerLayout.addSubview(zoomView)
        zoomView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentLayout = UIView()
        contentLayout.clipsToBounds = false
        //contentLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        zoomView.addSubview(contentLayout)
        contentLayout.makeViewCenterAndKeep(ratio: 1)
        
        touchView = UIView()
        containerLayout.addSubview(touchView)
        touchView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        coinView.clipsToBounds = false
        containerLayout.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
        
    
    override func layoutSubviews() {
        super.layoutSubviews()
        zoomView.contentSize = CGSize(width: 1200, height: 1200) // Đảm bảo contentSize khớp với contentLayout
        scale = contentLayout.frame.height / 1200.0
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        do {
            body0 = try BodyParser().parse(assetPath: "SVG/stickman/\(Int.random(in: 1...60)).svg")
            body = try BodyParser().parse(assetPath: "SVG/stickman/0.svg")
        } catch {
            print("Error parsing SVG: \(error)")
        }
    }
    
    override func createGame() {
        super.createGame()
        
        guard let body = body, let body0 = body0 else { return }
        scale = contentLayout.frame.height / 1200.0
        
        let mBody = body.findPart(id: "body")!
        let mBody0 = body0.findPart(id: "body")!
        let width = stroke * scale
        
        mainView0 = UIView()
        mainView0.accessibilityIdentifier = "mainView0"
        mainView0.clipsToBounds = false
        contentLayout.addSubview(mainView0)
        mainView0.snp.makeConstraints { make in
            make.width.height.equalTo(width)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(CGFloat(mBody0.y1) * scale)
        }
        let body0View = createBone(parent: mainView0, bodyPart: mBody0)
        let head0 = createBone(parent: mainView0, bodyPart: body0.findPart(id: "head")!)
        let arm110 = createBone(parent: mainView0, bodyPart: body0.findPart(id: "arm11")!)
        let arm210 = createBone(parent: mainView0, bodyPart: body0.findPart(id: "arm21")!)
        let arm120 = createBone(parent: arm110, bodyPart: body0.findPart(id: "arm12")!)
        let arm220 = createBone(parent: arm210, bodyPart: body0.findPart(id: "arm22")!)
        let leg110 = createBone(parent: body0View, bodyPart: body0.findPart(id: "leg11")!)
        let leg210 = createBone(parent: body0View, bodyPart: body0.findPart(id: "leg21")!)
        let leg120 = createBone(parent: leg110, bodyPart: body0.findPart(id: "leg12")!)
        let leg220 = createBone(parent: leg210, bodyPart: body0.findPart(id: "leg22")!)
        let leg130 = createBone(parent: leg120, bodyPart: body0.findPart(id: "leg13")!)
        let leg230 = createBone(parent: leg220, bodyPart: body0.findPart(id: "leg23")!)
        
        bones0 = bones
        bones = []
        
        let alphaView = UIView()
        alphaView.backgroundColor = .white
        alphaView.alpha = 0.6
        contentLayout.addSubview(alphaView)
        alphaView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let mainViewContainer = UIView()
        mainViewContainer.clipsToBounds = false
        contentLayout.addSubview(mainViewContainer)
        mainViewContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        mainView = UIView()
        mainView.accessibilityIdentifier = "mainView"
        mainView.clipsToBounds = false
        mainViewContainer.addSubview(mainView)
        mainView.snp.makeConstraints { make in
            make.width.height.equalTo(width)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(CGFloat(mBody.y1) * scale)
        }
        
        let bodyView = createBone(parent: mainView, bodyPart: mBody, blackColor: true)
        let head = createBone(parent: mainView, bodyPart: body.findPart(id: "head")!, blackColor: true)
        let arm11 = createBone(parent: mainView, bodyPart: body.findPart(id: "arm11")!, blackColor: true)
        let arm21 = createBone(parent: mainView, bodyPart: body.findPart(id: "arm21")!, blackColor: true)
        let arm12 = createBone(parent: arm11, bodyPart: body.findPart(id: "arm12")!, blackColor: true)
        let arm22 = createBone(parent: arm21, bodyPart: body.findPart(id: "arm22")!, blackColor: true)
        let leg11 = createBone(parent: bodyView, bodyPart: body.findPart(id: "leg11")!, blackColor: true)
        let leg21 = createBone(parent: bodyView, bodyPart: body.findPart(id: "leg21")!, blackColor: true)
        let leg12 = createBone(parent: leg11, bodyPart: body.findPart(id: "leg12")!, blackColor: true)
        let leg22 = createBone(parent: leg21, bodyPart: body.findPart(id: "leg22")!, blackColor: true)
        let leg13 = createBone(parent: leg12, bodyPart: body.findPart(id: "leg13")!, blackColor: true)
        let leg23 = createBone(parent: leg22, bodyPart: body.findPart(id: "leg23")!, blackColor: true)
        
        mainViewContainer.alpha = 0.01
        alphaView.alpha = 0
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/con roi"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
        
        scheduler.schedule(delay: 0.5) {
            [weak self] in
            guard let self = self else { return }
            //var rotations: [CGFloat] = []
            for i in 0..<bones0.count {
                let constraintLayout = bones0[i]
                let rotation = constraintLayout.transform.rotationAngleDegrees()
                //rotations.append(rotation)
                constraintLayout.transform = CGAffineTransformMakeRotation(bones[i].transform.rotationAngleDegrees().degreesToRadians)
                let closestC = self.findClosestC(a: bones[i].transform.rotationAngleDegrees(), b: rotation)
                scheduler.schedule(delay: 2.0) {
                    UIView.animate(withDuration: 2.0) {
                        constraintLayout.transform = CGAffineTransformMakeRotation(closestC.degreesToRadians)
                    }
                }
            }
        }
                
        scheduler.schedule(delay: 5.0) {
            UIView.animate(withDuration: 5.0) {
                mainViewContainer.alpha = 1
            }
        }
        scheduler.schedule(delay: 4.5) {
            UIView.animate(withDuration: 0.6) {
                alphaView.alpha = 0.6
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/con roi")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    var zPosition = 10.0
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        let gesture = touches.first!
        let view = touchView
        let location = gesture.location(in: view)
        targetView = nil
        var targetView: UIView?
        var topLeft: UIView?
        var topRight: UIView?
        
        for bone in bones {
            var points: [CGPoint] = []
            for i in 0..<4 {
                let child = bone.subviews[i]
                let touchViewLocation = touchView.convert(touchView.bounds.origin, to: nil)
                let childLocation = child.convert(child.bounds.origin, to: nil)
                points.append(CGPoint(x: childLocation.x - touchViewLocation.x, y: childLocation.y - touchViewLocation.y))
            }
            
            if isPointInConvexQuadrilateral(a: points[0], b: points[1], c: points[2], d: points[3], e: location) {
                targetView = bone
                topLeft = bone.subviews[0]
                topRight = bone.subviews[1]
                let tag = tags[bone] as! BodyParser.BodyPart
                print("Inside: \(tag.id)")
                var vgs: [UIView] = [bone]
                var vg: UIView? = bone.superview
                while vg != mainView && vg != nil {
                    vgs.insert(vg!, at: 0)
                    vg = vg!.superview
                }
                for viewGroup in vgs {
                    viewGroup.layer.zPosition = zPosition
                    zPosition += 1
                }
                if let highlightView = viewToHighlight[bone] {
                    highlightView.backgroundColor = .white
                    highlightView.layer.setNeedsDisplay()
                    playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                }
                break
            }
        }
        
        if targetView == nil { return }
        self.targetView = targetView
        self.topLeft = topLeft
        self.topRight = topRight
        previousAngle = calculateAngle(x: location.x, y: location.y)
        originRotation = targetView!.transform.rotationAngleDegrees()
        Utils.vibrate()
    }
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        let gesture = touches.first!
        let view = touchView
        let location = gesture.location(in: view)
        
        guard let targetView = targetView else { return }
        let currentAngle = calculateAngle(x: location.x, y: location.y)
        let rotation = currentAngle - previousAngle
        let newRotation = originRotation + rotation
        print("newRotation \(newRotation)")
        targetView.transform = CGAffineTransformMakeRotation(newRotation.degreesToRadians)
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        let gesture = touches.first!
        let view = touchView
        let location = gesture.location(in: view)
        
        guard let targetView = self.targetView else { return }
        if let highlightView = viewToHighlight[targetView] {
            highlightView.backgroundColor = UIColor.color(hex: "#F3FF2B")
            highlightView.layer.setNeedsDisplay()
        }
        
        if let index = bones.firstIndex(of: targetView) {
            let originTargetView = bones0[index]
            let delta = abs(targetView.transform.rotationAngleDegrees() - originTargetView.transform.rotationAngleDegrees())
            let normalizedDelta = delta.truncatingRemainder(dividingBy: 360)
            let finalDelta = normalizedDelta > 180 ? 360 - normalizedDelta : normalizedDelta
            if finalDelta < 20 {
                let r = findClosestC(a: targetView.transform.rotationAngleDegrees(), b: originTargetView.transform.rotationAngleDegrees())
                UIView.animate(withDuration: 0.2, animations: {
                    targetView.transform = CGAffineTransformMakeRotation(r.degreesToRadians)
                }, completion: { _ in
                    self.copyColor(targetView: targetView, originTargetView: originTargetView)
                    var finish = true
                    for i in 0..<self.bones.count {
                        let r1 = self.bones[i].transform.rotationAngleDegrees()
                        let r2 = self.bones0[i].transform.rotationAngleDegrees()
                        let r3 = self.findClosestC(a: r1, b: r2)
                        if abs(r1 - r3) > 3 {
                            finish = false
                            break
                        }
                    }
                    if finish {
                        self.pauseGame(stopMusic: false)
                        self.animateCoinIfCorrect(view: self.coinView)
                        for i in 0..<self.bones.count {
                            self.copyColor(targetView: self.bones[i], originTargetView: self.bones0[i])
                            let r1 = self.bones[i].transform.rotationAngleDegrees()
                            let r2 = self.bones0[i].transform.rotationAngleDegrees()
                            let r3 = self.findClosestC(a: r1, b: r2)
                            UIView.animate(withDuration: 0.2) {
                                self.bones[i].transform = CGAffineTransformMakeRotation(r3.degreesToRadians)
                                self.bones[i].bringSubviewToFront(self.bones[i])
                            }
                        }
                        let delay = self.playSound(delay: 0, names: [
                            "effect/answer_end",
                            self.getCorrectHumanSound(),
                            self.endGameSound()
                        ])
                        self.scheduler.schedule(after: delay) { [weak self] in
                            self?.finishGame()
                        }
                    }
                })
                playSound("effect/word puzzle drop")
            }
        }
    }
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        self.touchesEnded(touches, with: event)
    }
    
    
    // MARK: - Helper Methods
    private var targetView: UIView?
    private var topLeft: UIView?
    private var topRight: UIView?
    private var previousAngle: CGFloat = 0
    private var originRotation: CGFloat = 0
    
    private func isPointInConvexQuadrilateral(a: CGPoint, b: CGPoint, c: CGPoint, d: CGPoint, e: CGPoint) -> Bool {
        let cross1 = crossProduct(p1: a, p2: b, p: e)
        let cross2 = crossProduct(p1: b, p2: c, p: e)
        let cross3 = crossProduct(p1: c, p2: d, p: e)
        let cross4 = crossProduct(p1: d, p2: a, p: e)
        
        let hasNeg = cross1 < 0 || cross2 < 0 || cross3 < 0 || cross4 < 0
        let hasPos = cross1 > 0 || cross2 > 0 || cross3 > 0 || cross4 > 0
        
        return !(hasNeg && hasPos)
    }
    
    private func crossProduct(p1: CGPoint, p2: CGPoint, p: CGPoint) -> CGFloat {
        return (p2.x - p1.x) * (p.y - p1.y) - (p2.y - p1.y) * (p.x - p1.x)
    }
    
    private func calculateAngle(x: CGFloat, y: CGFloat) -> CGFloat {
        let touchViewLocation = touchView.convert(touchView.bounds.origin, to: nil)
        let topLeftLocation = topLeft!.convert(topLeft!.bounds.origin, to: nil)
        let topRightLocation = topRight!.convert(topRight!.bounds.origin, to: nil)
        let centerX = (topLeftLocation.x + topRightLocation.x) / 2 - touchViewLocation.x
        let centerY = (topLeftLocation.y + topRightLocation.y) / 2 - touchViewLocation.y
        return atan2(y - centerY, x - centerX) * 180 / .pi
    }
    
    private func calculateSignedAngle(a: CGPoint, b: CGPoint, c: CGPoint) -> CGFloat {
        let bax = a.x - b.x
        let bay = a.y - b.y
        let bcx = c.x - b.x
        let bcy = c.y - b.y
        let crossProduct = bax * bcy - bay * bcx
        let dotProduct = bax * bcx + bay * bcy
        let angleRad = atan2(crossProduct, dotProduct)
        return angleRad * 180 / .pi
    }
    
    private func findClosestC(a: CGFloat, b: CGFloat) -> CGFloat {
        let remainder = b - 360 * floor(b / 360)
        let c1 = a - ((a - remainder).truncatingRemainder(dividingBy: 360))
        let values: [CGFloat] = [c1 - 720, c1 - 360, c1, c1 + 360, c1 + 720]
        var min = abs(c1 - a)
        var rs = c1
        for value in values {
            let d = abs(value - a)
            if min > d {
                min = d
                rs = value
            }
        }
        return rs
    }
    
    private func copyColor(targetView: UIView, originTargetView: UIView) {
        targetView.transform = CGAffineTransformMakeRotation( originTargetView.transform.rotationAngleDegrees().degreesToRadians)
        let tag = tags[targetView] as! BodyParser.BodyPart
        targetView.backgroundColor = UIColor.color(hex: tag.strokeColor)
        if let topView = targetView.viewWithStringTag("-1"), let originTopView = originTargetView.viewWithStringTag("-1") {
            topView.backgroundColor = originTopView.backgroundColor
        }
        if let bottomView = targetView.viewWithStringTag("1"), let originBottomView = originTargetView.viewWithStringTag("1") {
            bottomView.backgroundColor = originBottomView.backgroundColor
        }
        if tag.id == "head", let headView = targetView.viewWithStringTag("2"), let originHeadView = originTargetView.viewWithStringTag("2") {
            headView.backgroundColor = originHeadView.backgroundColor
        }
    }
    var tags : [UIView: BodyParser.BodyPart?] = [:]
    private func createBone(parent: UIView, bodyPart: BodyParser.BodyPart, blackColor: Bool = false) -> UIView {
        let width = stroke * scale
        let partView = UIView()
        tags[partView] = bodyPart
        let id = bodyPart.id
        partView.accessibilityIdentifier = "bone-\(bodyPart.id)"
        partView.clipsToBounds = false
        parent.addSubview(partView)
        partView.snp.makeConstraints { make in
            make.width.equalTo(width)
            make.height.equalTo(CGFloat(bodyPart.getLength()) * scale)
            make.centerX.equalToSuperview()
            if let parentTag = tags[parent] as? BodyParser.BodyPart {
                make.top.equalToSuperview().offset(CGFloat(parentTag.getLength()) * scale)
            } else {
                make.top.equalToSuperview().offset(width / 2)
            }
        }
        scheduler.schedule(delay: 0.2) {
            [weak self] in
            guard let self = self else { return }
            let frame = partView.frame
            partView.layer.anchorPoint = CGPoint(x: 0.5, y: 0)
            partView.frame = frame
            var degrees = atan2(bodyPart.y2 - bodyPart.y1, bodyPart.x2 - bodyPart.x1) * 180 / .pi - 90
            if let parentTag = tags[parent] as? BodyParser.BodyPart {
                degrees = Float(180 + calculateSignedAngle(
                    a: CGPoint(x: CGFloat(parentTag.x1), y: CGFloat(parentTag.y1)),
                    b: CGPoint(x: CGFloat(parentTag.x2), y: CGFloat(parentTag.y2)),
                    c: CGPoint(x: CGFloat(bodyPart.x2), y: CGFloat(bodyPart.y2))
                ))
            }
            partView.transform = CGAffineTransformMakeRotation( CGFloat(degrees.degreesToRadians))
        }
        
        partView.backgroundColor = blackColor ? .black : UIColor.color(hex: bodyPart.strokeColor)
        //partView.tag = bodyPart
        bones.append(partView)
        
        let topLeft = UIView()
        topLeft.accessibilityIdentifier = "bone-top-left-\(bodyPart.id)"
        topLeft.backgroundColor = .red
        partView.addSubview(topLeft)
        topLeft.snp.makeConstraints { make in
            make.width.height.equalTo(1)
            make.top.left.equalToSuperview()
        }
        
        let topRight = UIView()
        topRight.accessibilityIdentifier = "bone-top-right-\(bodyPart.id)"
        topRight.backgroundColor = .red
        partView.addSubview(topRight)
        topRight.snp.makeConstraints { make in
            make.width.height.equalTo(1)
            make.top.right.equalToSuperview()
        }
        
        let bottomRight = UIView()
        bottomRight.accessibilityIdentifier = "bone-bottom-right-\(bodyPart.id)"
        bottomRight.backgroundColor = .red
        partView.addSubview(bottomRight)
        bottomRight.snp.makeConstraints { make in
            make.width.height.equalTo(1)
            make.bottom.right.equalToSuperview()
        }
        
        let bottomLeft = UIView()
        bottomLeft.accessibilityIdentifier = "bone-bottom-left-\(bodyPart.id)"
        bottomLeft.backgroundColor = .red
        partView.addSubview(bottomLeft)
        bottomLeft.snp.makeConstraints { make in
            make.width.height.equalTo(1)
            make.bottom.left.equalToSuperview()
        }
        
        
        let viewTop = RoundedView()
        viewTop.accessibilityIdentifier = "view-top-\(bodyPart.id)"
        viewTop.backgroundColor = blackColor ? .black : UIColor.color(hex: bodyPart.strokeColor)
        partView.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.width.height.equalTo(width)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(-width / 2)
        }
        viewTop.stringTag = "-1"
        
        let viewBottom = RoundedView()
        viewBottom.accessibilityIdentifier = "view-bottom-\(bodyPart.id)"
        viewBottom.backgroundColor = blackColor ? .black : UIColor.color(hex: bodyPart.strokeColor)
        partView.addSubview(viewBottom)
        viewBottom.snp.makeConstraints { make in
            make.width.height.equalTo(width)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(width / 2)
        }
        viewBottom.stringTag = "1"
        
        if bodyPart.id == "head" {
            let headWidth = width * 4
            let viewHead = RoundedView()
            viewHead.backgroundColor = blackColor ? .black : UIColor.color(hex: "#F4C3A2")
            partView.addSubview(viewHead)
            viewHead.snp.makeConstraints { make in
                make.width.height.equalTo(headWidth)
                make.centerX.equalToSuperview()
                make.bottom.equalToSuperview().offset(headWidth / 2)
            }
            viewHead.stringTag = "2"
        }
        
        let viewHighlight = RoundedView()
        viewHighlight.accessibilityIdentifier = "view-highlight-\(bodyPart.id)"
        viewHighlight.backgroundColor =  UIColor.color(hex: "#F3FF2B")
        viewTop.addSubview(viewHighlight)
        viewHighlight.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        viewToHighlight[partView] = viewHighlight
        
        return partView
    }
    
    // MARK: - ZoomPanViewGroup
   
}

extension taptrung_list_stickman: UIScrollViewDelegate {
    func viewForZooming(in scrollView: UIScrollView) -> UIView? {
        return contentLayout
    }
    func scrollViewDidEndZooming(_ scrollView: UIScrollView, with view: UIView?, atScale scale: CGFloat) {
        print("scrollViewDidEndZooming scale: \(scale)")
    }
}

// MARK: - BodyParser
class BodyParser {
    func parse(assetPath: String) throws -> Body {
        guard let inputStream = StorageManager.manager.getStream(path: assetPath) else {
            throw NSError(domain: "BodyParser", code: -1, userInfo: ["message": "Cannot open stream for \(assetPath)"])
        }
        return try parse(inputStream: inputStream)
    }
    
    func parse(inputStream: InputStream) throws -> Body {
        let parser = XMLParser(stream: inputStream)
        let delegate = BodyParserDelegate()
        parser.delegate = delegate
        if !parser.parse() {
            throw NSError(domain: "BodyParser", code: -1, userInfo: ["message": "XML parsing failed"])
        }
        return delegate.body
    }
    
    class BodyParserDelegate: NSObject, XMLParserDelegate {
        var body = Body()
        var currentElement: String?
        
        func parser(_ parser: XMLParser, didStartElement elementName: String, namespaceURI: String?, qualifiedName qName: String?, attributes: [String: String]) {
            currentElement = elementName
            if elementName == "line" {
                guard let id = attributes["id"],
                      let x1 = Float(attributes["x1"] ?? ""),
                      let y1 = Float(attributes["y1"] ?? ""),
                      let x2 = Float(attributes["x2"] ?? ""),
                      let y2 = Float(attributes["y2"] ?? ""),
                      let strokeColor = attributes["stroke"] else { return }
                let part = BodyPart(id: id, x1: x1, y1: y1, x2: x2, y2: y2, strokeColor: strokeColor)
                body.parts.append(part)
            }
        }
    }
    
    class Body {
        var parts: [BodyPart] = []
        
        func findPart(id: String) -> BodyPart? {
            return parts.first { $0.id == id }
        }
    }
    
    class BodyPart {
        let id: String
        let x1: Float
        let y1: Float
        let x2: Float
        let y2: Float
        let strokeColor: String
        
        init(id: String, x1: Float, y1: Float, x2: Float, y2: Float, strokeColor: String) {
            self.id = id
            self.x1 = x1
            self.y1 = y1
            self.x2 = x2
            self.y2 = y2
            self.strokeColor = strokeColor
        }
        
        func getLength() -> Float {
            return sqrt(pow(x2 - x1, 2) + pow(y2 - y1, 2))
        }
    }
}

import Foundation

extension FloatingPoint {
    var degreesToRadians: Self {
        return self * .pi / 180
    }
    
    var radiansToDegrees: Self {
        return self * 180 / .pi
    }
}

// Sử dụng cho các kiểu số cụ thể nếu cần
extension Double {
    var degreesToRadians: Double {
        return self * .pi / 180
    }
    
    var radiansToDegrees: Double {
        return self * 180 / .pi
    }
}

extension Float {
    var degreesToRadians: Float {
        return self * .pi / 180
    }
    
    var radiansToDegrees: Float {
        return self * 180 / .pi
    }
}
