//
//  nhanbiet_list_xephinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_xephinh4mieng: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var centerView: UIView!
    //private var viewLeft: UIView!
    //private var viewRight: UIView!
    private var placeHolder1: UIView!
    private var placeHolder2: UIView!
    private var placeHolder3: UIView!
    private var placeHolder4: UIView!
    private var viewTopLeft: KUButton!
    private var viewTopRight: KUButton!
    private var viewBottomLeft: KUButton!
    private var viewBottomRight: KUButton!
    private var doneViews: [UIView] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    private var svg: SVGKImage?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_puzzle2"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        itemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.0006) // #0f00
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: isRunningOnIpad() ? 1.45 : 2.0)
        
        
        centerView = UIView()
        centerView.clipsToBounds = false
        itemContainer.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(isRunningOnIpad() ? 0.5 : 0.7)
            make.height.equalTo(centerView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        placeHolder1 = UIView()
        placeHolder1.backgroundColor = UIColor.blue.withAlphaComponent(0.0006) // #00f0
        itemContainer.addSubview(placeHolder1)
        placeHolder1.snp.makeConstraints { make in
            make.width.equalTo(placeHolder1.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(isRunningOnIpad() ? 0.25 : 0.35)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.placeHolder1.snapToHorizontalBias(horizontalBias: 0.09)
            self.placeHolder1.snapToVerticalBias(verticalBias: 0.15)
        }
        
        placeHolder2 = UIView()
        placeHolder2.backgroundColor = UIColor.blue.withAlphaComponent(0.0006) // #00f0
        itemContainer.addSubview(placeHolder2)
        placeHolder2.snp.makeConstraints { make in
            make.width.equalTo(placeHolder2.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(isRunningOnIpad() ? 0.25 : 0.35)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.placeHolder2.snapToHorizontalBias(horizontalBias: 0.91)
            self.placeHolder2.snapToVerticalBias(verticalBias: 0.15)
        }
        
        placeHolder3 = UIView()
        placeHolder3.backgroundColor = UIColor.blue.withAlphaComponent(0.0006) // #00f0
        itemContainer.addSubview(placeHolder3)
        placeHolder3.snp.makeConstraints { make in
            make.width.equalTo(placeHolder3.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(isRunningOnIpad() ? 0.25 : 0.35)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.placeHolder3.snapToHorizontalBias(horizontalBias: 0.09)
            self.placeHolder3.snapToVerticalBias(verticalBias: 0.85)
        }
        
        placeHolder4 = UIView()
        placeHolder4.backgroundColor = UIColor.blue.withAlphaComponent(0.0006) // #00f0
        itemContainer.addSubview(placeHolder4)
        placeHolder4.snp.makeConstraints { make in
            make.width.equalTo(placeHolder4.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(isRunningOnIpad() ? 0.25 : 0.35)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.placeHolder4.snapToHorizontalBias(horizontalBias: 0.91)
            self.placeHolder4.snapToVerticalBias(verticalBias: 0.85)
        }
        
        let centerImage = SVGImageView(frame: .zero)
        centerImage.SVGName = "nhanbiet_btn_puzzle40"
        itemContainer.addSubview(centerImage)
        centerImage.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(isRunningOnIpad() ? 0.5 : 0.7)
            make.height.equalTo(centerImage.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        viewTopLeft = createPuzzlePiece(id: "view_topleft", mask: "nhanbiet_btn_puzzle41")
        viewTopLeft.snp.makeConstraints { make in
            make.width.equalTo(centerView).multipliedBy(0.5)
            make.height.equalTo(centerView).multipliedBy(0.5)
            make.left.top.equalToSuperview()
        }
        
        viewTopRight = createPuzzlePiece(id: "view_topright", mask: "nhanbiet_btn_puzzle42")
        viewTopRight.snp.makeConstraints { make in
            make.width.equalTo(centerView).multipliedBy(0.5)
            make.height.equalTo(centerView).multipliedBy(0.5)
            make.right.top.equalToSuperview()
        }
        
        viewBottomLeft = createPuzzlePiece(id: "view_bottomleft", mask: "nhanbiet_btn_puzzle44")
        viewBottomLeft.snp.makeConstraints { make in
            make.width.equalTo(centerView).multipliedBy(0.5)
            make.height.equalTo(centerView).multipliedBy(0.5)
            make.left.bottom.equalToSuperview()
        }
        
        viewBottomRight = createPuzzlePiece(id: "view_bottomright", mask: "nhanbiet_btn_puzzle43")
        viewBottomRight.snp.makeConstraints { make in
            make.width.equalTo(centerView).multipliedBy(0.5)
            make.height.equalTo(centerView).multipliedBy(0.5)
            make.right.bottom.equalToSuperview()
        }
                
        itemContainer.bringSubviewToFront(centerView)
        view.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        if let folder = getFolder(), let itemPath = getItem()?.path {
            svg = Utilities.GetSVGKImage(named: "topics/\(folder)/\(itemPath)")
            let imageViews = self.centerView.findSubviews(ofType: SVGImageView.self)
            for imageView in imageViews {
                imageView.image = svg?.uiImage
            }
        }
        
        let delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_puzzle4"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        let views = [viewTopLeft, viewTopRight, viewBottomLeft, viewBottomRight]
        let placeholders = [placeHolder1, placeHolder2, placeHolder3, placeHolder4].shuffled()
        for i in 0..<4 {
            guard let view = views[i], let placeholder = placeholders[i] else { continue }
            scheduler.schedule(after: 1) {
                view.moveToCenter(of: placeholder, duration: 0.5)
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("\(getLanguage())/nhanbiet/nhanbiet_puzzle4")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    var zposition = 100.0
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        zposition += 1
        view?.layer.zPosition = zposition
        let location = gesture.location(in: view)
        latestPoint = location
        deltaX = centerView.frame.minX
        deltaY = centerView.frame.minY
        currentView = gesture.placeInView(view: viewTopLeft) ? viewTopLeft :
                    gesture.placeInView(view: viewTopRight) ? viewTopRight :
                    gesture.placeInView(view: viewBottomLeft) ? viewBottomLeft :
                    gesture.placeInView(view: viewBottomRight) ? viewBottomRight : nil
        if let currentView = currentView {
            dX = currentView.frame.minX - location.x
            dY = currentView.frame.minY - location.y
            currentView.layer.zPosition = CGFloat.greatestFiniteMagnitude
            doneViews.removeAll { $0 == currentView }
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            Utils.vibrate()
        }
    }
    var latestPoint = CGPoint.zero
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        let location = gesture.location(in: view)
        if let currentView = currentView {
            let newX = currentView.center.x + location.x - latestPoint.x
            let newY = currentView.center.y + location.y - latestPoint.y
            currentView.center = CGPoint(x: newX, y: newY)
            latestPoint = location
        }
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        if let currentView = currentView {
            Utils.vibrate()
            playSound("effect/word puzzle drop")
            let vector = currentView.subviews[0].distanceFromCenterToCenter(to: centerView)
            let distance = hypot(vector.x, vector.y)
            if distance < currentView.frame.height / 5 {
                UIView.animate(withDuration: 0.2) {
                    [weak self] in
                    guard let self = self else { return }
                    currentView.subviews[0].moveToCenter(of: self.centerView)
                }
                doneViews.append(currentView)
                if doneViews.count == 4 {
                    animateCoinIfCorrect(view: centerView)
                    var delay: TimeInterval = 0.5
                    if let folder = self.getFolder(), let itemPath = self.getItem()?.path {
                        delay += self.playSound(delay: delay, names: [
                            "\(self.getLanguage())/topics/\(folder)/\(itemPath.replacingOccurrences(of: ".svg", with: ""))",
                            "effect/answer_end",
                            self.getCorrectHumanSound(),
                            self.endGameSound()
                        ])
                    }
                    self.pauseGame(stopMusic: false)
                    self.scheduler.schedule(after: delay) { [weak self] in
                        self?.finishGame()
                    }
                }
            } else {
                setGameWrong()
                let delay = playSound(delay: 0.5, names: ["effect/answer_wrong"])
                scheduler.schedule(after: delay) { [weak self] in
                    self?.resumeGame(startMusic: false)
                }
            }
        }
        currentView = nil
    }
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    
    // MARK: - Helper Methods
    private func createPuzzlePiece(id: String, mask: String) -> KUButton {
        let button = KUButton()
        //button.clipsToBounds = true
        centerView.addSubview(button)
        button.stringTag = id
        button.backgroundColor = .clear
        
        let maskView = MaskableView()
        maskView.setMask(mask: Utilities.SVGImage(named: mask))
        maskView.porterDuffMode = .destinationIn
        button.addSubview(maskView)
        maskView.snp.makeConstraints { make in
            make.edges.equalTo(centerView)            
        }
        
        let imageView = SVGImageView(frame: .zero)
        imageView.backgroundColor = UIColor.white
        maskView.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return button
    }
}

class MaskableView: UIView {
    private var _mask: UIImage?
    func setMask(mask: UIImage){
        self._mask = mask
        updateMask()
    }
    
    var porterDuffMode: PorterDuffMode = .destinationIn {
        didSet {
            updateMask()
        }
    }
    
    private func updateMask() {
        if let maskImage = _mask {
            let maskLayer = CALayer()
            maskLayer.contents = maskImage.cgImage
            maskLayer.frame = bounds
            layer.mask = maskLayer
            // TODO: Implement PorterDuff mode (DST_IN) if needed
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        updateMask()
    }
}

enum PorterDuffMode {
    case destinationIn
}
