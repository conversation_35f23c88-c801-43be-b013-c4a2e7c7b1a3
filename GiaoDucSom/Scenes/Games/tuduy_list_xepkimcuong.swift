//
//  tuduy_list_xepkimcuong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 10/4/25.
//


import UIKit
import SnapKit
import SVGKit
/*
class tuduy_list_xepkimcuong: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: UIImageView!
    private var itemContainer: UIView!
    private var contentLayout: UIView!
    private var containerLayout: UIView!
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    private var centerView: UIView!
    private var doneViews: [UIView] = []
    private var viewToSVGShape: [UIView: SVGKImage] = [:]
    private var svgs: [SVGKImage] = []
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    private var stepX: CGFloat = 0
    private var stepY: CGFloat = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 49/255, green: 38/255, blue: 114/255, alpha: 1) // #312672
        
        containerLayout = UIView()
        containerLayout.clipsToBounds = false
        addSubviewWithPercentInset(subview: containerLayout, percentInset: 5)
        
        centerView = UIView()
        centerView.clipsToBounds = false
        containerLayout.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.33)
            make.center.equalToSuperview()
        }
        
        contentLayout = UIView()
        contentLayout.clipsToBounds = false
        centerView.addSubview(contentLayout)
        contentLayout.makeViewCenterAndKeep(ratio: 0.5)
        
        svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        contentLayout.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        contentLayout.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        containerLayout.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "tuduy/kimcuong")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/kimcuong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        centerView.isHidden = true
        loadFullPieces()
    }
    
    private func loadFullPieces() {
        let pieces = StorageManager.manager.list(path: "diamond/full piece").filter({ $0.hasSuffix(".svg") })
        let fullPiecePaths = pieces.map { "diamond/full piece/\($0)" }
        self.svgs = fullPiecePaths.map { Utilities.GetSVGKImage(named: $0)}
        self.loadGame()
    }
    
    private func loadGame() {
        let svgPath = "diamond/\(1 + Int.random(in: 0..<41)).svg"
        let svg = Utilities.GetSVGKImage(named: svgPath)
        let pathCount = svg.caLayerTree.sublayers?.count ?? 0
        var fillColors: [UIColor] = []
        for i in 0..<pathCount {
            if let path = svg.caLayerTree.sublayers?[i] as? CAShapeLayer, let fillColor = path.fillColor, !fillColors.contains(UIColor(cgColor: fillColor)) {
                fillColors.append(UIColor(cgColor: fillColor))
            }
        }
        
        let bounds = svg.getPathBounds(index: 0)
        stepX = bounds.width / 2.0
        stepY = bounds.height
        
        let svgShadow = svg.copy() as! SVGKImage
        svgShadow.caLayerTree.sublayers?.forEach { ($0 as? CAShapeLayer)?.fillColor = UIColor(red: 19/255, green: 13/255, blue: 51/255, alpha: 1).cgColor } // #130D33
        svgView.image = svgShadow.uiImage
        
        contentLayout.snp.remakeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(contentLayout.snp.height).multipliedBy(svg.size.width / svg.size.height)
        }
        
        for i in 0..<fillColors.count {
            var indexes: [Int] = []
            for j in 0..<pathCount {
                if let path = svg.caLayerTree.sublayers?[j] as? CAShapeLayer, UIColor(cgColor: path.fillColor ?? .clear) == fillColors[i] {
                    indexes.append(j)
                }
            }
            
            let partSvg = svg.clone(withIndexes: indexes)
            let partBounds = partSvg.getBounds()
            let x = Int(round(partBounds.minX / stepX))
            let y = Int(round(partBounds.minY / stepY))
            
            let partView = UIImageView()
            let trimSVG = partSvg.trimmed()
            partView.image = trimSVG.uiImage
            itemContainer.addSubview(partView)
            partView.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(partBounds.height / svg.size.height)
                make.width.equalToSuperview().multipliedBy(partBounds.width / svg.size.width)
                make.left.top.equalToSuperview()
            }
            partView.transform = CGAffineTransform(translationX: (x + y) % 2 == 0 ? 0 : stepX, y: 0)
            partView.tag = (x + y) % 2
            
            let colorSVG = getColorSVG(svg: trimSVG)
            viewToSVGShape[partView] = trimSVG
            partView.image = colorSVG.uiImage
        }
        
        scheduler.schedule(after: 0.1) { [weak self] in
            self?.shuffleViews()
        }
    }
    
    private func getColorSVG(svg: SVGKImage) -> SVGKImage {
        let pathCount = svg.caLayerTree.sublayers?.count ?? 0
        let candidates = svgs.filter { $0.caLayerTree.sublayers?.count == pathCount && $0.size == svg.size }
        let width = candidates[0].caLayerTree.sublayers?[0].bounds.width ?? 0
        let centers1 = getTriangles(svg: svg)
        for candidate in candidates {
            let centers2 = getTriangleCenters(svg: candidate)
            if checkSameCenters(centers1: centers1, centers2: centers2, edge: width) {
                return candidate
            }
        }
        return candidates[0]
    }
    
    private func setLocation(view: UIImageView, index: Int) {
        var adjustedIndex = index
        switch index {
        case 0: adjustedIndex = 1
        case 1: adjustedIndex = 2
        case 2: adjustedIndex = 5
        case 3: adjustedIndex = 6
        case 4: adjustedIndex = 3
        case 5: adjustedIndex = 4
        case 6: adjustedIndex = 7
        case 7: adjustedIndex = 0
        default: break
        }
        
        let column = adjustedIndex / 4
        let row = adjustedIndex % 4
        let left = row % 2 == 0
        let bottom = row >= 2
        
        let containerLocation = containerLayout.convert(containerLayout.bounds.origin, to: nil)
        let viewLocation = view.convert(view.bounds.origin, to: nil)
        deltaX = viewLocation.x - containerLocation.x
        deltaY = viewLocation.y - containerLocation.y
        
        var tranX = -containerLayout.frame.width / 3.0
        var tranY = -deltaY
        if !left {
            tranX += containerLayout.frame.width / 3.0 - view.frame.width
            tranY += containerLayout.frame.height / 2.0 - view.frame.height
        }
        if bottom {
            tranY += containerLayout.frame.height / 2.0
        }
        if column == 1 {
            tranX += containerLayout.frame.width * 0.666667
        }
        view.transform = CGAffineTransform(translationX: tranX, y: tranY)
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: containerLayout)
        switch gesture.state {
        case .began:
            let containerLocation = containerLayout.convert(containerLayout.bounds.origin, to: nil)
            let itemLocation = itemContainer.convert(itemContainer.bounds.origin, to: nil)
            deltaX = itemLocation.x - containerLocation.x
            deltaY = itemLocation.y - containerLocation.y
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                if currentView.transform != .identity {
                    let currentFrame = currentView.frame
                    currentView.transform = .identity
                    currentView.frame = currentFrame
                }
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                let originX = currentView.frame.minX
                let originY = currentView.frame.minY
                currentView.bringSubviewToFront(self)
                currentView.transform = .init(scaleX: 1.1, y: 1.1)
                doneViews.removeAll { $0 == currentView }
                containerLayout.alpha = 1
                playSound("effect/word puzzle drop")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = max(-currentView.frame.width / 2 - deltaX, min(location.x + dX, containerLayout.frame.width - currentView.frame.width / 2 - deltaX))
                let newY = max(-currentView.frame.height / 2 - deltaY, min(location.y + dY, containerLayout.frame.height - currentView.frame.height / 2 - deltaY))
                currentView.frame = CGRect(x: newX, y: newY, width: currentView.frame.width, height: currentView.frame.height)
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                currentView.transform = .identity
                let scale = svgView.frame.height / svg.size.height
                let STEPX = stepX * scale
                let STEPY = stepY * scale
                let tag = currentView.tag
                let row = Int(round(currentView.frame.minY / STEPY))
                let valueX = (100 + tag + row) % 2 == 0 ? round(currentView.frame.minX / (STEPX * 2.0)) * STEPX * 2.0 : round((currentView.frame.minX - STEPX) / (STEPX * 2)) * STEPX * 2 + STEPX
                let valueY = CGFloat(row) * STEPY
                
                UIView.animate(withDuration: 0.2) {
                    currentView.frame = CGRect(x: valueX, y: valueY, width: currentView.frame.width, height: currentView.frame.height)
                }
                
                let checkView = currentView
                playSound("effect/diamond_drop")
                scheduler.schedule(after: 0.2) { [weak self] in
                    guard let self = self else { return }
                    var checkLocationOK = true
                    let triangles = self.getTriangles(view: checkView as! UIImageView)
                    for subview in self.itemContainer.subviews {
                        if subview != checkView, let view = subview as? UIImageView {
                            let centerTriangles = self.getTriangles(view: view)
                            if self.checkIfIntersect(triangles1: triangles, triangles2: centerTriangles) {
                                checkView.moveToCenter(of: checkView, duration: 0.2) // Quay về origin
                                checkLocationOK = false
                                self.setGameWrong()
                                break
                            }
                        }
                    }
                    if checkLocationOK {
                        let triangleCenter = self.getTriangles(svg: self.svgView.image)
                        if !self.checkIfInside(triangles1: triangles, triangles2: triangleCenter) {
                            if self.checkIfIntersect(triangles1: triangles, triangles2: triangleCenter) {
                                checkView.moveToCenter(of: checkView, duration: 0.2)
                                self.setGameWrong()
                            }
                            checkLocationOK = false
                        }
                    }
                    if checkLocationOK {
                        if !self.doneViews.contains(checkView) {
                            self.doneViews.append(checkView)
                        }
                        let checkFinish = self.itemContainer.subviews.allSatisfy { self.doneViews.contains($0) }
                        if checkFinish {
                            self.pauseGame()
                            self.animateCoinIfCorrect(view: checkView)
                            let delay = self.playSound(self.answerCorrect1EffectSound(), self.getCorrectHumanSound(), self.endGameSound())
                            self.scheduler.schedule(delay: delay) { [weak self] in
                                self?.finishGame()
                            }
                        }
                    } else {
                        self.setGameWrong()
                    }
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func shuffleViews() {
        var views = itemContainer.subviews.compactMap { $0 as? UIImageView }
        views.shuffle()
        for (i, view) in views.enumerated() {
            setLocation(view: view, index: i)
        }
        centerView.isHidden = false
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        let adjustedX = x - deltaX
        let adjustedY = y - deltaY
        for i in (0..<itemContainer.subviews.count).reversed() {
            let view = itemContainer.subviews[i]
            if adjustedX >= view.frame.minX && adjustedX <= view.frame.maxX && adjustedY >= view.frame.minY && adjustedY <= view.frame.maxY {
                if isPixelVisible(view: view, x: Int(adjustedX), y: Int(adjustedY)) {
                    return view
                }
            }
        }
        return nil
    }
    
    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard !view.isHidden else { return false }
        return view.isPixelVisible(x: x, y: y)
    }
    
    private func getTriangles(view: UIImageView) -> [Triangle] {
        let containerLocation = containerLayout.convert(containerLayout.bounds.origin, to: nil)
        let viewLocation = view.convert(view.bounds.origin, to: nil)
        deltaX = viewLocation.x - containerLocation.x
        deltaY = viewLocation.y - containerLocation.y
        
        var triangles: [Triangle] = []
        let svg = viewToSVGShape[view] ?? (view.image as? SVGKImage) ?? SVGKImage()
        guard let paths = svg.caLayerTree.sublayers as? [CAShapeLayer] else { return triangles }
        
        for path in paths {
            let pathMeasure = PathMeasure(path: path.path!)
            let length = pathMeasure.length
            var points: [Point] = []
            for j in 0..<3 {
                let position = pathMeasure.getPosition(at: length / 3 * CGFloat(j))
                let point = Point(x: deltaX + position.x, y: deltaY + position.y)
                points.append(point)
            }
            let triangle = Triangle(points: points)
            triangles.append(triangle)
        }
        return triangles
    }
    
    private func getTriangles(svg: SVGKImage) -> [Triangle] {
        var triangles: [Triangle] = []
        guard let paths = svg.caLayerTree.sublayers as? [CAShapeLayer] else { return triangles }
        
        for path in paths {
            let pathMeasure = PathMeasure(path: path.path!)
            let length = pathMeasure.length
            var points: [Point] = []
            for j in 0..<3 {
                let position = pathMeasure.getPosition(at: length / 3 * CGFloat(j))
                let point = Point(x: deltaX + position.x, y: deltaY + position.y)
                points.append(point)
            }
            let triangle = Triangle(points: points)
            triangles.append(triangle)
        }
        return triangles
    }
    
    private func getTriangleCenters(svg: SVGKImage) -> [Point] {
        var centers: [Point] = []
        guard let groups = svg.caLayerTree.sublayers as? [CALayer] else { return centers }
        
        for group in groups {
            guard let paths = group.sublayers as? [CAShapeLayer] else { continue }
            var points: [Point] = []
            for path in paths {
                let pathMeasure = PathMeasure(path: path.path!)
                let length = pathMeasure.length
                for k in 0..<3 {
                    let position = pathMeasure.getPosition(at: length / 3 * CGFloat(k))
                    let point = Point(x: deltaX + position.x, y: deltaY + position.y)
                    points.append(point)
                }
            }
            let center = Point(x: points.reduce(0) { $0 + $1.x } / CGFloat(points.count), y: points.reduce(0) { $0 + $1.y } / CGFloat(points.count))
            centers.append(center)
        }
        return centers
    }
    
    private func checkIfIntersect(triangles1: [Triangle], triangles2: [Triangle]) -> Bool {
        for triangle1 in triangles1 {
            for triangle2 in triangles2 {
                if triangle1 == triangle2 {
                    return true
                }
            }
        }
        return false
    }
    
    private func checkIfInside(triangles1: [Triangle], triangles2: [Triangle]) -> Bool {
        for triangle1 in triangles1 {
            if !triangles2.contains(where: { $0 == triangle1 }) {
                return false
            }
        }
        return true
    }
    
    private func checkSameCenters(centers1: [Point], centers2: [Point], edge: CGFloat) -> Bool {
        guard centers1.count == centers2.count else { return false }
        for center1 in centers1 {
            if !centers2.contains(where: { sqrt(pow(center1.x - $0.x, 2) + pow(center1.y - $0.y, 2)) < edge / 10 }) {
                return false
            }
        }
        for center2 in centers2 {
            if !centers1.contains(where: { sqrt(pow($0.x - center2.x, 2) + pow($0.y - center2.y, 2)) < edge / 10 }) {
                return false
            }
        }
        return true
    }
    
    // MARK: - Supporting Structures
    struct Point: Equatable {
        var x: CGFloat
        var y: CGFloat
        
        static func == (lhs: Point, rhs: Point) -> Bool {
            return lhs.x == rhs.x && lhs.y == rhs.y
        }
    }
    
    struct Triangle: Equatable {
        var points: [Point]
        
        func center() -> Point {
            let sumX = points.reduce(0) { $0 + $1.x }
            let sumY = points.reduce(0) { $0 + $1.y }
            return Point(x: sumX / CGFloat(points.count), y: sumY / CGFloat(points.count))
        }
        
        static func == (lhs: Triangle, rhs: Triangle) -> Bool {
            let center1 = lhs.center()
            let center2 = rhs.center()
            let edge = sqrt(pow(lhs.points[0].x - lhs.points[1].x, 2) + pow(lhs.points[0].y - lhs.points[1].y, 2))
            return sqrt(pow(center1.x - center2.x, 2) + pow(center1.y - center2.y, 2)) < edge / 3
        }
    }
    
    class PathMeasure {
        let path: CGPath
        let length: CGFloat
        
        init(path: CGPath) {
            self.path = path
            self.length = path.length
        }
        
        func getPosition(at distance: CGFloat) -> CGPoint {
            var point = CGPoint.zero
            let pathCopy = path.copy()
            let pathLength = length
            let fraction = min(max(distance / pathLength, 0), 1)
            pathCopy.getPoint(&point, at: fraction)
            return point
        }
    }
}

// MARK: - Extensions
extension CGPath {
    /*
    var length: CGFloat {
        var totalLength: CGFloat = 0
        var previousPoint: CGPoint?
        self.apply { element in
            switch element.type {
            case .moveToPoint, .addLineToPoint:
                let point = element.points[0]
                if let prev = previousPoint {
                    totalLength += sqrt(pow(point.x - prev.x, 2) + pow(point.y - prev.y, 2))
                }
                previousPoint = point
            case .addQuadCurveToPoint, .addCurveToPoint, .closeSubpath:
                break // Simplified, real length calculation would need curve approximation
            default:
                break
            }
        }
        return totalLength
    }*/
    
    func getPoint(_ point: inout CGPoint, at fraction: CGFloat) {
        var totalLength: CGFloat = 0
        var previousPoint: CGPoint?
        let targetLength = length * fraction
        
        self.apply { element in
            switch element.type {
            case .moveToPoint, .addLineToPoint:
                let currentPoint = element.points[0]
                if let prev = previousPoint {
                    let segmentLength = sqrt(pow(currentPoint.x - prev.x, 2) + pow(currentPoint.y - prev.y, 2))
                    totalLength += segmentLength
                    if totalLength >= targetLength {
                        let overshoot = totalLength - targetLength
                        let ratio = 1 - (overshoot / segmentLength)
                        point.x = prev.x + (currentPoint.x - prev.x) * ratio
                        point.y = prev.y + (currentPoint.y - prev.y) * ratio
                    }
                }
                previousPoint = currentPoint
            default:
                break
            }
        }
    }
}

*/
