//
//  tuduy_list_catgiay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit

class tuduy_list_catgiay: NhanBietGameFragment {
    // MARK: - Properties
    private let MAXHEIGHT = 4
    private let MAXWIDTH = 4
    private var paper1View: PaperView!
    private var paper2View: PaperView!
    private var paper3View: PaperView!
    private var paper1Flipview: PaperView!
    private var paper2Flipview: PaperView!
    private var paper3Flipview: PaperView!
    private var paperCutView: PaperView!
    private var rightIndex = Int.random(in: 0..<3)
    private var button1: UIImageView!
    private var button2: UIImageView!
    private var button3: UIImageView!
    private var indexes: [Int] = []
    private var points: [[Int]] = []
    private var viewAnimation: XAMLAnimationView!
    private var paperMatrix = Array(repeating: Array(repeating: false, count: 4), count: 4)
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 204/255, green: 255/255, blue: 209/255, alpha: 1) // #CCFFD1
        
        let container = UIView()
        container.clipsToBounds = false
        addSubview(container)
        container.makeViewCenterAndKeep(ratio: 2.0)
        
        let topContainer = UIView()
        topContainer.alpha = 0.001
        container.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(container).multipliedBy(0.4)
        }
        
        let paper1Image = UIImageView(image: Utilities.SVGImage(named: "tuduy_paper1"))
        paper1Image.contentMode = .scaleAspectFit
        topContainer.addSubview(paper1Image)
        paper1Image.snp.makeConstraints { make in
            make.height.equalTo(topContainer).multipliedBy(0.7)
            make.width.equalTo(paper1Image.snp.height).multipliedBy(688.0 / 384.0) // Ratio 688:384
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            paper1Image.snapToHorizontalBias(horizontalBias: 0.3)
        }
        
        let paper2Container = UIImageView()
        paper2Container.image = Utilities.SVGImage(named: "tuduy_paper2")
        topContainer.addSubview(paper2Container)
        paper2Container.snp.makeConstraints { make in
            make.height.equalTo(topContainer).multipliedBy(0.74)
            make.width.equalTo(paper2Container.snp.height).multipliedBy(308.0 / 408.0) // Ratio 308:408
            make.bottom.equalTo(paper1Image)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            paper2Container.snapToHorizontalBias(horizontalBias: 0.75)
            UIView.animate(withDuration: 0.2) {
                topContainer.alpha = 1
                self.viewAnimation.alpha = 1
            }
        }
        
        paperCutView = PaperView()
        paperCutView.showGrid = false
        paper2Container.addSubview(paperCutView)
        paperCutView.snp.makeConstraints { make in
            make.height.equalTo(paper2Container).multipliedBy(0.935)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        container.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(container).multipliedBy(0.32)
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        button1 = UIImageView()
        button1.image = Utilities.SVGImage(named: "tuduy_paper_option_bg")
        bottomContainer.addSubview(button1)
        button1.snp.makeConstraints { make in
            make.height.equalTo(bottomContainer)
            make.width.equalTo(button1.snp.height).multipliedBy(7.0 / 4.0) // Ratio 7:4
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.button1.snapToHorizontalBias(horizontalBias: 0.05)
        }
        
        let guideLine1 = UIView()
        button1.addSubview(guideLine1)
        guideLine1.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.height.equalTo(button1)
            make.centerX.equalToSuperview()
        }
        
        paper1View = PaperView()
        button1.addSubview(paper1View)
        paper1View.snp.makeConstraints { make in
            make.height.equalTo(button1)
            make.width.equalTo(paper1View.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.left.equalTo(guideLine1)
            make.centerY.equalToSuperview()
        }
        
        paper1Flipview = PaperView()
        paper1Flipview.transform = CGAffineTransform(scaleX: -1, y: 1)
        button1.addSubview(paper1Flipview)
        paper1Flipview.snp.makeConstraints { make in
            make.height.equalTo(button1)
            make.width.equalTo(paper1Flipview.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.right.equalTo(guideLine1)
            make.centerY.equalToSuperview()
        }
        
        button2 = UIImageView()
        button2.image = Utilities.SVGImage(named: "tuduy_paper_option_bg")
        bottomContainer.addSubview(button2)
        button2.snp.makeConstraints { make in
            make.height.equalTo(bottomContainer)
            make.width.equalTo(button2.snp.height).multipliedBy(7.0 / 4.0) // Ratio 7:4
            make.center.equalToSuperview()
        }
        
        let guideLine2 = UIView()
        button2.addSubview(guideLine2)
        guideLine2.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.height.equalTo(button2)
            make.centerX.equalToSuperview()
        }
        
        paper2View = PaperView()
        button2.addSubview(paper2View)
        paper2View.snp.makeConstraints { make in
            make.height.equalTo(button2)
            make.width.equalTo(paper2View.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.left.equalTo(guideLine2)
            make.centerY.equalToSuperview()
        }
        
        paper2Flipview = PaperView()
        paper2Flipview.transform = CGAffineTransform(scaleX: -1, y: 1)
        button2.addSubview(paper2Flipview)
        paper2Flipview.snp.makeConstraints { make in
            make.height.equalTo(button2)
            make.width.equalTo(paper2Flipview.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.right.equalTo(guideLine2)
            make.centerY.equalToSuperview()
        }
        
        button3 = UIImageView()
        button3.image = Utilities.SVGImage(named: "tuduy_paper_option_bg")
        bottomContainer.addSubview(button3)
        button3.snp.makeConstraints { make in
            make.height.equalTo(bottomContainer)
            make.width.equalTo(button3.snp.height).multipliedBy(7.0 / 4.0) // Ratio 7:4
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.button3.snapToHorizontalBias(horizontalBias: 0.95)
        }
        
        let guideLine3 = UIView()
        button3.addSubview(guideLine3)
        guideLine3.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.height.equalTo(button3)
            make.centerX.equalToSuperview()
        }
        
        paper3View = PaperView()
        button3.addSubview(paper3View)
        paper3View.snp.makeConstraints { make in
            make.height.equalTo(button3)
            make.width.equalTo(paper3View.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.left.equalTo(guideLine3)
            make.centerY.equalToSuperview()
        }
        
        paper3Flipview = PaperView()
        paper3Flipview.transform = CGAffineTransform(scaleX: -1, y: 1)
        button3.addSubview(paper3Flipview)
        paper3Flipview.snp.makeConstraints { make in
            make.height.equalTo(button3)
            make.width.equalTo(paper3Flipview.snp.height).multipliedBy(5.0 / 6.0) // Ratio 5:6
            make.right.equalTo(guideLine3)
            make.centerY.equalToSuperview()
        }
        
        viewAnimation = XAMLAnimationView()
        viewAnimation.alpha = 0.001
        container.addSubview(viewAnimation)
        viewAnimation.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.3)
            make.width.equalTo(viewAnimation.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview().multipliedBy(0.2) // Bias 0.1
        }
        addActionOnLayoutSubviews {
            self.viewAnimation.snapToHorizontalBias(horizontalBias: 0.7)
        }
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "scissors")!)
            viewAnimation.loadView(from: xamlData)
        } catch {}
        scheduler.schedule(after: 0.2) { [weak self] in
            self?.viewAnimation.startAnimation()
        }
        
        //AnimationUtils.setTouchEffect(views: [button1, button2, button3])
        
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        button1.addGestureRecognizer(tap1)
        button1.isUserInteractionEnabled = true
        
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        button2.addGestureRecognizer(tap2)
        button2.isUserInteractionEnabled = true
        
        let tap3 = UITapGestureRecognizer(target: self, action: #selector(handleButtonTap(_:)))
        button3.addGestureRecognizer(tap3)
        button3.isUserInteractionEnabled = true
         
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            createPaperMatrix()
            if checkValidOneAreaAndNoInsideHole(paperMatrix: paperMatrix) {
                let lastPoint1 = points.last!
                let lastPoint2 = points[points.count - 2]
                paperMatrix[lastPoint1[0]][lastPoint1[1]] = false
                if checkValidOneAreaAndNoInsideHole(paperMatrix: paperMatrix) {
                    let paperViews = [paper1View, paper2View, paper3View]
                    let paperFlipViews = [paper1Flipview, paper2Flipview, paper3Flipview]
                    indexes = Utils.generatePermutation(3)
                    paperMatrix[lastPoint2[0]][lastPoint2[1]] = false
                    if checkValidOneAreaAndNoInsideHole(paperMatrix: paperMatrix) {
                        paperViews[indexes[0]]?.updateData(paperMatrix: paperMatrix)
                        paperFlipViews[indexes[0]]?.updateData(paperMatrix: paperMatrix)
                        if rightIndex == 0 {
                            paperCutView.updateData(paperMatrix: paperMatrix)
                        }
                        paperMatrix[lastPoint2[0]][lastPoint2[1]] = true
                        paperViews[indexes[1]]?.updateData(paperMatrix: paperMatrix)
                        paperFlipViews[indexes[1]]?.updateData(paperMatrix: paperMatrix)
                        if rightIndex == 1 {
                            paperCutView.updateData(paperMatrix: paperMatrix)
                        }
                        paperMatrix[lastPoint1[0]][lastPoint1[1]] = true
                        paperViews[indexes[2]]?.updateData(paperMatrix: paperMatrix)
                        paperFlipViews[indexes[2]]?.updateData(paperMatrix: paperMatrix)
                        if rightIndex == 2 {
                            paperCutView.updateData(paperMatrix: paperMatrix)
                        }
                        break
                    }
                }
            }
        }
        
        var delay = playSound(openGameSound(), "tuduy/cat giay")
        
        button1.transform = CGAffineTransform(scaleX: 0, y: 0)
        button1.alpha = 0
        button2.transform = CGAffineTransform(scaleX: 0, y: 0)
        button2.alpha = 0
        button3.transform = CGAffineTransform(scaleX: 0, y: 0)
        button3.alpha = 0
        
        delay += 0.5
        UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
            self.button1.transform = .identity
            self.button1.alpha = 1
        })
        UIView.animate(withDuration: 0.6, delay: delay + 0.5, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
            self.button2.transform = .identity
            self.button2.alpha = 1
        })
        UIView.animate(withDuration: 0.6, delay: delay + 1.0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
            self.button3.transform = .identity
            self.button3.alpha = 1
        })
        playSound(name: "effect/bubble1", delay: delay)
        playSound(name: "effect/bubble2", delay: delay + 0.5)
        playSound(name: "effect/bubble3", delay: delay + 1.0)
        delay += 1.0
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/cat giay")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleButtonTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view == button1 ? 0 : view == button2 ? 1 : 2
        if indexes[rightIndex] == index {
            pauseGame()
            animateCoinIfCorrect(view: view)
            let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            pauseGame()
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func checkValidOneAreaAndNoInsideHole(paperMatrix: [[Bool]]) -> Bool {
        var data = Array(repeating: Array(repeating: 0, count: MAXWIDTH + 1), count: MAXHEIGHT + 2)
        var filledCell = 0
        for i in 0..<MAXHEIGHT {
            for j in 0..<MAXWIDTH {
                if paperMatrix[i][j] {
                    data[i + 1][j] = 1
                    filledCell += 1
                }
            }
        }
        
        var x = 0
        var y = 0
        while true {
            x = Int.random(in: 0..<data.count)
            y = Int.random(in: 0..<data[0].count)
            if data[x][y] == 0 {
                data[x][y] = 2
                filledCell += 1
                break
            }
        }
        
        let leftCell = data.count * data[0].count - filledCell
        for _ in 0..<leftCell {
            var c = 0
            while true {
                c += 1
                if c > 1000 { return false }
                let i = Int.random(in: 0..<data.count)
                let j = Int.random(in: 0..<data[0].count)
                if data[i][j] == 0 && ((i > 0 && data[i - 1][j] == 2) || (j > 0 && data[i][j - 1] == 2) || (i < data.count - 1 && data[i + 1][j] == 2) || (j < data[0].count - 1 && data[i][j + 1] == 2)) {
                    data[i][j] = 2
                    break
                }
            }
        }
        return true
    }
    
    private func createPaperMatrix() {
        for i in 0..<MAXHEIGHT {
            for j in 0..<MAXWIDTH {
                paperMatrix[i][j] = false
            }
        }
        points.removeAll()
        let randomRow = Int.random(in: 0..<MAXHEIGHT)
        paperMatrix[randomRow][0] = true
        points.append([randomRow, 0])
        let cellCount = 10 + Int.random(in: 0..<5)
        for _ in 1..<cellCount {
            while true {
                let x = Int.random(in: 0..<MAXHEIGHT)
                let y = Int.random(in: 0..<MAXWIDTH)
                if !paperMatrix[x][y] && ((x > 0 && paperMatrix[x - 1][y]) || (y > 0 && paperMatrix[x][y - 1]) || (x < MAXHEIGHT - 1 && paperMatrix[x + 1][y]) || (y < MAXWIDTH - 1 && paperMatrix[x][y + 1])) {
                    paperMatrix[x][y] = true
                    points.append([x, y])
                    break
                }
            }
        }
    }
}

// MARK: - PaperView

// MARK: - PaperView
class PaperView: UIView {
    private let MAX_WIDTH = 4
    private let MAX_HEIGHT = 4
    private var _showGrid: Bool = true
    private var strokeWidth: CGFloat = 0
    private var paperMatrix = Array(repeating: Array(repeating: false, count: 5), count: 6)
    private var paint = CGPaint()
    private var paintFill = CGPaint()
    private var paintBold = CGPaint()
    private var lines: [[Int]] = []
    private var boldLines: [[Int]] = []
    private var strokePath = UIBezierPath()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }
    
    private func initView() {
        backgroundColor = .clear
        paint.style = .stroke
        paint.color = UIColor(red: 179/255, green: 184/255, blue: 202/255, alpha: 1) // #B3B8CA
        paintBold.style = .stroke
        paintBold.color = UIColor(red: 130/255, green: 135/255, blue: 155/255, alpha: 1) // #82879B
        paintBold.strokeCap = .round
        paintBold.strokeJoin = .round
        paintFill.style = .fill
        paintFill.color = .white
    }
    
    var showGrid: Bool {
        get { _showGrid }
        set {
            _showGrid = newValue
            setNeedsLayout()
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        strokeWidth = frame.width / CGFloat(MAX_WIDTH + 1) / 20
        paint.strokeWidth = strokeWidth
        paintBold.strokeWidth = strokeWidth * 2
        if !showGrid {
            paintBold.color = UIColor(red: 56/255, green: 63/255, blue: 86/255, alpha: 1) // #383F56
            paintBold.pathEffect = DashPathEffect(intervals: [strokeWidth * 10, strokeWidth * 10], phase: 0)
        }
        
        let cellWidth = frame.width / CGFloat(MAX_WIDTH + 1)
        let cellHeight = frame.height / CGFloat(MAX_HEIGHT + 2)
        
        // Tạo đường gấp khúc bao quanh các ô được chọn
        strokePath = UIBezierPath()
        var visited = Array(repeating: Array(repeating: false, count: MAX_WIDTH + 1), count: MAX_HEIGHT + 2)
        
        // Hàm kiểm tra ô hợp lệ
        func isValid(_ i: Int, _ j: Int) -> Bool {
            return i >= 0 && i < MAX_HEIGHT + 2 && j >= 0 && j < MAX_WIDTH + 1
        }
        
        // Duyệt từng ô để tìm các cạnh biên
        for i in 0..<MAX_HEIGHT + 2 {
            for j in 0..<MAX_WIDTH + 1 {
                if paperMatrix[i][j] && !visited[i][j] {
                    // Tìm các cạnh biên của ô hiện tại
                    let top = isValid(i - 1, j) ? paperMatrix[i - 1][j] : false
                    let bottom = isValid(i + 1, j) ? paperMatrix[i + 1][j] : false
                    let left = isValid(i, j - 1) ? paperMatrix[i][j - 1] : false
                    let right = isValid(i, j + 1) ? paperMatrix[i][j + 1] : false
                    
                    let x = CGFloat(j) * cellWidth
                    let y = CGFloat(i) * cellHeight
                    
                    // Thêm các đoạn thẳng cho các cạnh biên
                    if !top {
                        strokePath.move(to: CGPoint(x: x, y: y))
                        strokePath.addLine(to: CGPoint(x: x + cellWidth, y: y))
                    }
                    if !right {
                        strokePath.move(to: CGPoint(x: x + cellWidth, y: y))
                        strokePath.addLine(to: CGPoint(x: x + cellWidth, y: y + cellHeight))
                    }
                    if !bottom {
                        strokePath.move(to: CGPoint(x: x + cellWidth, y: y + cellHeight))
                        strokePath.addLine(to: CGPoint(x: x, y: y + cellHeight))
                    }
                    if !left && j > 0 { // Tránh vẽ cạnh trái của cột đầu tiên
                        strokePath.move(to: CGPoint(x: x, y: y + cellHeight))
                        strokePath.addLine(to: CGPoint(x: x, y: y))
                    }
                    
                    visited[i][j] = true
                }
            }
        }
        
        setNeedsDisplay()
    }

    override func draw(_ rect: CGRect) {
        super.draw(rect)
        let cellWidth = frame.width / CGFloat(MAX_WIDTH + 1)
        let cellHeight = frame.height / CGFloat(MAX_HEIGHT + 2)
        if showGrid {
            for i in 0..<MAX_HEIGHT + 2 {
                for j in 0..<MAX_WIDTH + 1 {
                    if paperMatrix[i][j] {
                        let rect = CGRect(x: CGFloat(j) * cellWidth, y: CGFloat(i) * cellHeight, width: cellWidth, height: cellHeight)
                        paintFill.draw(rect: rect)
                        paint.draw(rect: rect)
                    }
                }
            }
        }
        paintBold.draw(path: strokePath)
    }
    
    func updateData(paperMatrix: [[Bool]]) {
        lines.removeAll()
        boldLines.removeAll()
        for i in 0..<self.paperMatrix.count {
            for j in 0..<self.paperMatrix[0].count {
                self.paperMatrix[i][j] = false
            }
        }
        for i in 0..<MAX_HEIGHT {
            for j in 0..<MAX_WIDTH {
                self.paperMatrix[i + 1][j] = paperMatrix[i][j]
            }
        }
        setNeedsDisplay()
    }    
}

// MARK: - Paint (Mock for Canvas Drawing)
struct CGPaint {
    var style: Style = .fill
    var color: UIColor = .black
    var strokeWidth: CGFloat = 1
    var strokeCap: CGLineCap = .butt
    var strokeJoin: CGLineJoin = .miter
    var pathEffect: PathEffect?
    
    enum Style {
        case fill
        case stroke
    }
    
    func draw(rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        context.setStrokeColor(color.cgColor)
        context.setFillColor(color.cgColor)
        context.setLineWidth(strokeWidth)
        context.setLineCap(strokeCap)
        context.setLineJoin(strokeJoin)
        if let effect = pathEffect as? DashPathEffect {
            context.setLineDash(phase: effect.phase, lengths: effect.intervals)
        }
        if style == .fill {
            context.fill(rect)
        } else {
            context.stroke(rect)
        }
    }
    
    func draw(path: UIBezierPath) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        context.setStrokeColor(color.cgColor)
        context.setLineWidth(strokeWidth)
        context.setLineCap(strokeCap)
        context.setLineJoin(strokeJoin)
        if let effect = pathEffect as? DashPathEffect {
            context.setLineDash(phase: effect.phase, lengths: effect.intervals)
        }
        path.stroke()
    }
}

struct DashPathEffect: PathEffect {
    let intervals: [CGFloat]
    let phase: CGFloat
    
    init(intervals: [CGFloat], phase: CGFloat) {
        self.intervals = intervals
        self.phase = phase
    }
}

protocol PathEffect {}
