<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#CCFFD1"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_paper1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="688:384"
                app:layout_constraintHeight_percent="0.7"
                app:layout_constraintHorizontal_bias="0.3"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_paper2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="308:408"
                app:layout_constraintHeight_percent="0.74"
                app:layout_constraintHorizontal_bias="0.75"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.42">

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_cut_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHeight_percent="0.935"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1"></com.kidsup.giaoducsom.view.PaperView>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:splitMotionEvents="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.32"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.85">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/button_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_paper_option_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="7:4"
                app:layout_constraintHorizontal_bias="0.05"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_line_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_1_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintLeft_toLeftOf="@id/guide_line_1"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_1_flipview"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleX="-1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintRight_toRightOf="@id/guide_line_1"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/button_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_paper_option_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="7:4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_line_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_2_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintLeft_toLeftOf="@id/guide_line_2"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_2_flipview"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleX="-1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintRight_toRightOf="@id/guide_line_2"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/button_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_paper_option_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="7:4"
                app:layout_constraintHorizontal_bias="0.95"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guide_line_3"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="vertical"
                    app:layout_constraintGuide_percent="0.5" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_3_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintLeft_toLeftOf="@id/guide_line_3"
                    app:layout_constraintTop_toTopOf="parent" />

                <com.kidsup.giaoducsom.view.PaperView
                    android:id="@+id/paper_3_flipview"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:scaleX="-1"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="5:6"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintRight_toRightOf="@id/guide_line_3"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
            android:id="@+id/view_animation"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintHorizontal_bias="0.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>