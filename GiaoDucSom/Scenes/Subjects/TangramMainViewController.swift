//
//  TangramMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/6/25.
//


import UIKit
import SnapKit
import SVGKit

// MARK: - TangramMainViewController
class TangramMainViewController: BaseViewController {
    // MARK: - Properties  
    private var musics: [Lyric] = []
    private var selectedIndex: Int = -1
    private var column: Int = 3
    private var tangrams: Set<String> = []
    private var playedTangrams: Set<String> = []

    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        return UICollectionView(frame: .zero, collectionViewLayout: layout)
    }()
    private let btnClose = KUButton()
    private let gradientView = FadeGradientView()
    private let txtTitle = AutosizeLabel()
    
    var files : [String] = []
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#FDEAC9")
        // hideStatusBar equivalent: set in Info.plist or use prefersStatusBarHidden
        tangrams = loadUnlockedTangram()
        playedTangrams = loadPlayedTangram()
        files = StorageManager.manager.list(path: "tangram").filter { $0.hasSuffix(".svg") }.sorted { o1, o2 in
            let paddedO1 = padText(o1)
            let paddedO2 = padText(o2)
            return paddedO1 < paddedO2
        }.map { "tangram/\($0)" }
        setupUI()
    }
    
    private func padText(_ text: String) -> String {
        if text.count == 5 {
            return "0" + text
        }
        return text
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if selectedIndex != -1 {
            playedTangrams = loadPlayedTangram()
            collectionView.reloadData()
        }
    }

    
    private func setupUI() {
        view.removeAllSubviews()
        view.backgroundColor = UIColor(hex: "#DD9FF8")
        // btnClose
        btnClose.setImage(Utilities.SVGImage(named: "btn_back_mythuat_vetungnet"), for: .normal)
        btnClose.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.width.equalTo(btnClose.snp.height)
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
        }
        btnClose.snpLeftTop(ratio: 1)
        // txtTitle
        txtTitle.text = "Ghép mặt nạ"
        txtTitle.font = .Freude(size: 20)
        txtTitle.textColor = .white
        txtTitle.textAlignment = .left
        txtTitle.overrideTextAlignment = false
        txtTitle.numberOfLines = 1
        view.addSubview(txtTitle)
        txtTitle.snp.makeConstraints { make in
            make.centerY.equalTo(btnClose)
            make.width.equalTo(view.snp.width).multipliedBy(0.45)
            make.height.equalTo(view.snp.height).multipliedBy(0.06)
        }
        txtTitle.snpLeftTop(ratio: 3.0)
        
        
                
        // collectionView
        collectionView.backgroundColor = .clear
        collectionView.clipsToBounds = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(TangramCell.self, forCellWithReuseIdentifier: "TangramCell")
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            let column = Utilities.isIPad ? 4 : 3
            let dpToPx = view.bounds.width / CGFloat(column) * 0.92
            layout.itemSize = CGSize(width: dpToPx, height: dpToPx * 616 / 560)
            layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        }
        view.addSubview(collectionView)
        
        
        // gradientView
        gradientView.setupGradient(color: .color(hex: "#DD9FF8"))
        view.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(gradientView.snp.width).dividedBy(30)
            make.top.equalTo(btnClose.snp.bottom)
        }
        
        let viewTop = UIView()
        viewTop.backgroundColor = .color(hex: "#DD9FF8")
        view.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(gradientView.snp.top)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(gradientView.snp.bottom)
            make.left.equalTo(btnClose)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let coinView = CoinView()
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.top.bottom.equalTo(btnClose)
        }
        coinView.snpRightTop(ratio: 1)
        view.bringSubviewToFront(btnClose)
        view.bringSubviewToFront(txtTitle)
    }
    // MARK: - Actions
    @objc private func closeTapped() {
        navigationController?.popViewController(animated: true)
    }

    // MARK: - Tangram Management
    private func loadUnlockedTangram() -> Set<String> {
        let preferences = UserDefaults.standard
        let tangrams = Set(preferences.stringArray(forKey: "tangram_unlocked_\(DataManager.shared.currentProfile?.id ?? "default")") ?? [])
        return tangrams
    }

    private func unlockTangram(_ tangram: String) {
        let preferences = UserDefaults.standard
        var tangrams = Set(preferences.stringArray(forKey: "tangram_unlocked_\(DataManager.shared.currentProfile?.id ?? "default")") ?? [])
        tangrams.insert(tangram)
        preferences.set(Array(tangrams), forKey: "tangram_unlocked_\(DataManager.shared.currentProfile?.id ?? "default")")
        self.tangrams = tangrams
    }

    private func loadPlayedTangram() -> Set<String> {
        let preferences = UserDefaults.standard
        let tangrams = Set(preferences.stringArray(forKey: "tangram_played_\(DataManager.shared.currentProfile?.id ?? "default")") ?? [])
        return tangrams
    }

    private func playTangram(_ tangram: String) {
        let preferences = UserDefaults.standard
        var tangrams = Set(preferences.stringArray(forKey: "tangram_played_\(DataManager.shared.currentProfile?.id ?? "default")") ?? [])
        tangrams.insert(tangram)
        preferences.set(Array(tangrams), forKey: "tangram_played_\(DataManager.shared.currentProfile?.id ?? "default")")
        self.playedTangrams = tangrams
    }
}

// MARK: - UICollectionViewDelegateFlowLayout
extension TangramMainViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return files.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TangramCell", for: indexPath) as! TangramCell
        let item = files[indexPath.item]
        cell.configure(with: item, isUnlocked: self.tangrams.contains(item), isPlayed: self.playedTangrams.contains(item), price: CoinManager.PRICE_FILL)
        cell.onTap = { [weak self] in
            guard let self = self else { return }
            let unlocked = self.tangrams.contains(item)
            if !unlocked {
                return
            }
            self.selectedIndex = indexPath.item
            let gameVC = SingleGameActivity()
            gameVC.game = tuduy_list_tangram.className
            gameVC.data = item//.replacingOccurrences(of: "Animal Heads/", with: "").replacingOccurrences(of: ".svg", with: "")
            self.navigationController?.pushViewController(gameVC, animated: true)
        }
        cell.onBuy = { [weak self] in
            guard let self = self else { return }
            self.playSound("vi/popup_becodongydung", "vi/topics/Numbers/\(CoinManager.PRICE_TANGRAM)", "vi/popup_xu_hinh")
            let dialog = MessageDialogView()
                .setTitle("Mua tangram mới")
                .setMessage("Bé có đồng ý dùng \(CoinManager.PRICE_TANGRAM) xu để mua hình này không?")
                .setImageResId("icon_buy")
                .setButtonLaterText("Để sau")
                .setButtonOkText("Mua")
                .setShowLaterButton(true)
                .setShowOkButton(true)
                .setListener {
                    [weak self] buttonIndex in
                    guard let self = self else { return }
                    if buttonIndex == .ok {
                        self.scheduler.clearAll()
                        if CoinManager.shared.useCoin(CoinManager.PRICE_TANGRAM) {
                            self.playSound("effect/unlock item")
                            self.unlockTangram(item)
                            self.collectionView.reloadData()
                            self.scheduler.schedule(delay: 1) { [weak self] in
                                guard self != nil else { return }
                                // AnimateCoin.animateRemoveCoin equivalent
                            }
                        } else {
                            self.playSound("vi/popup_khongdu_hinh")
                            let noCoinDialog = MessageDialogView()
                                .setTitle("Không đủ xu")
                                .setMessage("Bé không đủ xu để mua hình này")
                                .setImageResId("icon_no_coin")
                            noCoinDialog.showIn(self.view)
                        }
                    }                    
                }
            dialog.showIn(self.view)            
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let spanCount = Utilities.isIPad ? 4 : 3
        let totalSpacing = CGFloat(spanCount - 1) * 10 + 20 // 10 là minimumInteritemSpacing, 20 là padding trái + phải từ sectionInset
        let width = (collectionView.bounds.width - totalSpacing) / CGFloat(spanCount)
        return CGSize(width: width, height: width / 0.8)
    }
}

// MARK: - TangramCell

class TangramCell: UICollectionViewCell {
    private let viewBackground = UIImageView()
    private let textName = HeightRatioTextView()
    private let svgThumbnail = SVGKFastImageView(svgkImage: nil)!
    private let textQuestion = HeightRatioTextView()
    private let btnBuy = KUButton()
    private let textItemCoin = HeightRatioTextView()
    
    var onTap: (() -> Void)?
    var onBuy: (() -> Void)?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupView()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupView() {
        let ratioView = UIView()
        contentView.addSubview(ratioView)
        ratioView.makeViewCenterAndKeep(ratio: 0.8)
        let bgView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "mythuat_tranhtomau_list_bg"))!
        ratioView.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.width.equalTo(bgView.snp.height).multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.9)
        }
        
        textName.text = "Name"
        textName.textColor = UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1) // #f39a71
        textName.font = .Freude(size: 16)
        textName.textAlignment = .center
        textName.setHeightRatio(0.7)
        bgView.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.15)
            make.left.right.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.7) // Bias 0.85
        }
        
        svgThumbnail.contentMode = .scaleAspectFit
        bgView.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.5)
            make.width.equalTo(svgThumbnail.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        svgThumbnail.waitForLayout {
            self.svgThumbnail.snapToVerticalBias(verticalBias: 0.25)
        }
        
        textQuestion.text = "?"
        textQuestion.textColor = UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1) // #f39a71
        textQuestion.font = .Freude(size: 30)
        textQuestion.textAlignment = .center
        textQuestion.setHeightRatio(0.8)
        bgView.addSubview(textQuestion)
        textQuestion.snp.makeConstraints { make in
            make.height.equalTo(bgView).multipliedBy(0.5)
            make.width.equalTo(textQuestion.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        textQuestion.waitForLayout {
            self.textQuestion.snapToVerticalBias(verticalBias: 0.25)
        }
        
        btnBuy.setImage(Utilities.SVGImage(named: "mythuat_hopmau_btn_buy"), for: .normal)
        btnBuy.isUserInteractionEnabled = true
        bgView.addSubview(btnBuy)
        btnBuy.snp.makeConstraints { make in
            make.width.equalTo(bgView).multipliedBy(0.8)
            make.height.equalTo(btnBuy.snp.width).multipliedBy(18.0 / 40.0) // Ratio 40:18
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
        }
        btnBuy.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            btnBuy.snapToVerticalBias(verticalBias: 0.9)
        }
        
        textItemCoin.text = "0"
        textItemCoin.textColor = UIColor(red: 255/255, green: 247/255, blue: 0, alpha: 1) // #fff700
        textItemCoin.font = .Freude(size: 14)
        textItemCoin.textAlignment = .center
        textItemCoin.setHeightRatio(0.7)
        btnBuy.addSubview(textItemCoin)
        textItemCoin.snp.makeConstraints { make in
            make.width.equalTo(btnBuy).multipliedBy(0.5)
            make.height.equalTo(btnBuy).multipliedBy(0.8)
            //make.centerY.equalToSuperview().multipliedBy(0.6) // Bias 0.3
        }
        textItemCoin.waitForLayout {
            self.textItemCoin.snapToVerticalBias(verticalBias: 0.3)
            self.textItemCoin.snapToHorizontalBias(horizontalBias: 0.8)
        }
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(cellTapped))
        contentView.addGestureRecognizer(tapGesture)
        
        let buyGesture = UITapGestureRecognizer(target: self, action: #selector(buyTapped))
        btnBuy.addGestureRecognizer(buyGesture)
    }
    
    func configure(with data: String, isUnlocked: Bool, isPlayed: Bool, price: Int) {
        let name = data.replacingOccurrences(of: "tangram/", with: "").replacingOccurrences(of: ".svg", with: "")
        textName.text = name
        btnBuy.isHidden = isUnlocked
        textQuestion.isHidden = isUnlocked
        svgThumbnail.isHidden = !isUnlocked
        textItemCoin.text = "\(price)"
        
        if isUnlocked {
            let image = Utilities.GetSVGKImage(named: "\(data)")
            if !isPlayed {
                let color = UIColor.color(hex: "#f39a71")
                image.caLayerTree.sublayers?.forEach { path in
                    path.setFillColor(color: color)
                }
            }
            svgThumbnail.image = image
            if image.size.width > image.size.height {
                svgThumbnail.transform = CGAffineTransform(scaleX: 1, y: image.size.height / image.size.width)
            } else {
                svgThumbnail.transform = CGAffineTransform(scaleX: image.size.width / image.size.height, y: 1)
            }
            //svgThumbnail.setColor(isPlayed ? 0 : UIColor(red: 243/255, green: 154/255, blue: 113/255, alpha: 1).rgbValue)
        }
        
    }
    
    @objc private func cellTapped() {
        onTap?()
    }
    
    @objc private func buyTapped() {
        onBuy?()
    }
}

// MARK: - Helper Extensions
extension UIView {
    func addTouchEffect() {
        let tap = UITapGestureRecognizer(target: self, action: #selector(handleTouch))
        addGestureRecognizer(tap)
        isUserInteractionEnabled = true
    }

    @objc private func handleTouch() {
        UIView.animate(withDuration: 0.1, animations: {
            self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }, completion: { _ in
            UIView.animate(withDuration: 0.1) {
                self.transform = .identity
            }
        })
    }
}



// MARK: - TangramItem
class TangramItem: Hashable {
    let data: String
    let id = UUID()

    init(data: String) {
        self.data = data
    }

    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }

    static func == (lhs: TangramItem, rhs: TangramItem) -> Bool {
        lhs.id == rhs.id
    }
}
