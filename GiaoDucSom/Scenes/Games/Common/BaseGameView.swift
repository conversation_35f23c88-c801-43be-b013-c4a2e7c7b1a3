//
//  BaseGameView.swift
//  KidsUPTiengViet
//
//  Created by <PERSON> on 17/01/2023.
//

import UIKit
import AnyCodable
import SnapKit

class BaseGameView: UIView {

    // MARK: - Components
    
    // MARK: - Properties
    
    let game: Animation
    private var gameCreated = false
    var gameStatus: GameStatus = .waiting
    var onCreatedGame: (() -> Void)?
    var onStartedGame: (() -> Void)?
    var onPauseGame: (() -> Void)?
    var onResumeGame: (() -> Void)?
    var onFinishGame: (() -> Void)?
    var scheduler = Scheduler()
    let soundPlayer = SoundHelper.shared
    
    var voiceSoundDirectory: String {
        "hanoi"
    }
    
    private var gameVoiceSoundDirectory: String {
        "\(voiceSoundDirectory)/games"
    }
    
    private var gameEffectSoundDirectory: String {
        "effects/games"
    }
        
    var isNeedShowHintButton: Bool {
        true
    }
    
    var enableBackgroundMusic: Bool {
        false
    }
    
    var backgroundMusicFile: String? {
        nil
    }
    
    // MARK: - Life Cycle
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override init(frame: CGRect) {
        fatalError("init(frame:) has not been implemented")
    }
    
    init(game: Animation) {
        self.game = game
        super.init(frame: .zero)
        isUserInteractionEnabled = false
        setupUI()
    }
    
    // MARK: - SetupUI
    
    func setupUI() {
        setupHierarchy()
        setupConstraints()
    }
    
    func setupHierarchy() {
        
    }
    
    func setupConstraints() {
        
    }
    
    // MARK: - Game Play
    
    func createGame(){
        onCreatedGame?()
        if enableStartGameSound {
            //playsound(file: "effects/start game")
        }
        #if DEBUG
        scheduler.schedule(delay: 3, execute: {
            [weak self] in
            guard let self = self else { return }
            if !(self is GameReading2View){
                //self.finishGame()
            }
        })
        #endif
    }
    var enableStartGameSound: Bool {
        true
    }
    func updateLayout(){
        
    }
    
    func startGame() {
        isUserInteractionEnabled = true
        gameStatus = .playing
        onStartedGame?()
    }
    
    func pauseGame() {
        isUserInteractionEnabled = false
        gameStatus = .paused
        onPauseGame?()
    }
    
    func resumeGame() {
        isUserInteractionEnabled = true
        gameStatus = .playing
        onResumeGame?()
    }
    
    func finishGame() {
        isUserInteractionEnabled = false
        gameStatus = .finished
        playComplimentSound()
        onFinishGame?()
        CoinAnimationUtils.shared.removeList()
    }
    
    // MARK: - Hint
    
    func hintButtonTapped() {
        
    }
    
    // MARK: - Sounds
    
    func playFailSound() {
        playsound(file: "effects/fail")
    }
    
    func playCorrectSound() {
        playsound(file: "true")
    }
    
    func playEffectSound(_ sound: String) {
        playsound(file: "\(gameEffectSoundDirectory)/\(sound)")
    }

    func playComplimentSound() {
        let index = Int.random(in: 1...5)
        playsound(file: "\(voiceSoundDirectory)/true\(index)")
    }
            
    func playSound(_ sound: String) {
        soundPlayer.playSound(
            soundFile: sound,
            subDirectory: "hanoi"
        )
    }
    
    deinit {
        scheduler.clearAll()
        QueueSoundPlayer.shared.clearAll()
    }
    
    @discardableResult
    func playsound(file: String) -> Double{
        return playsound(file: file, delay: 0)
    }
    
    func playsound( file: String, delay: Double) -> Double {
        scheduler.schedule(delay: delay) { [weak self] in
            guard self != nil else { return }
            QueueSoundPlayer.shared.play(sound: file, delay: 0)
        }
        return QueueSoundPlayer.shared.calcDuration(sound: file)
    }
    func playsound( files:[String], delay: Double)->Double{
        return playsound(files: files, delay: delay, delta: 0)
    }
    func playsound( files:[String], delay: Double, delta: Double)->Double{
        var d = delay
        for file in files {
            NSLog(file)
            let duration = playsound(file: file, delay: d)
            d += duration
            d += delta
        }
        return d - delay
    }
    private var latestWidth = 0.0
    private var latestHeight = 0.0
    override func layoutSubviews() {
        super.layoutSubviews()
        if (bounds.width != latestWidth || bounds.height != latestHeight) && bounds.width > 0 && bounds.height > 0 {
            latestWidth = bounds.width
            latestHeight = bounds.height
            if !gameCreated {
                gameCreated = true
                createGame()
            } else {
                updateLayout()
            }
        }
    }
    func isSupportUpperCaseGame()->Bool{
        let values = (game.values?.compactMap { $0.value as? String }) ?? []
        guard values.count > 2 else { return false }
        guard values[0].caseInsensitiveCompare(values[1]) == .orderedSame else { return false }
        guard let questions = game.questions else {
            return false
        }
        guard questions == 1 else {
            return false
        }
        return true
    }
    var progressConstraint: ConstraintMakerEditable?
    lazy var progress = GradientBackgroundView()
    var progressContainer: UIView?
    func setupProgressBar(){
        let Height = Utilities.isIPad ? 12.0 : 12.0
        progressContainer = UIView()
        if let progressContainer = progressContainer {
            progressContainer.backgroundColor = .color(hex: "AAEEF9")
            progressContainer.layer.cornerRadius = Height / 2
            addSubview(progressContainer)
            progressContainer.snp.makeConstraints { make in
                make.left.top.right.equalToSuperview().inset(Utilities.isIPad ? 30 : 20)
                make.height.equalTo(Height)
            }
            
            progress.layer.cornerRadius = Height / 2
            progressContainer.addSubview(progress)
            progress.snp.makeConstraints { make in
                make.left.top.bottom.equalToSuperview()
                progressConstraint = make.width.equalToSuperview().inset(10)
            }
            self.setGameProgress(progress: 0)            
        }
    }
    func setGameProgress(progress: Double){
        UIView.animate(withDuration: 0.5) {
            let inset = progress == 0 ? 1000 : (1.0-progress)*(self.progressContainer?.bounds.width ?? 1000)/2
            self.progressConstraint?.constraint.update(inset: inset)
            self.layoutIfNeeded()
        }
    }
    func Skills()->[GameSkill]{
        return []
    }
    var incorrect = 0
    func Score()->Double{
        return 1.0
    }
    func parseIntroText()->[String]{
        if game.text == nil {
            return []
        }
        return game.text!.lowercased().split(separator: "#").map{$0.string}
    }
}
enum GameStatus : String, Codable{
    case waiting,playing,paused,finished
}
enum GameSkill : String, Codable{
    case GameReading
    case GameListening
    case GameWriting
    case GameSpeaking
}
