//
//  toancoban_list_chauhoa.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/3/25.
//



import UIKit
import SnapKit

class toancoban_list_chauhoa: NhanBietGameFragment {
    // MARK: - Properties
    private let ids: [UIImage] = [
        Utilities.SVGImage(named: "math_1hoa_1"),
        Utilities.SVGImage(named: "math_1hoa_2"),
        Utilities.SVGImage(named: "math_1hoa_3")
    ]
    private var meIndex: Int = 0
    private var imageViews: [UIImageView] = [UIImageView(), UIImageView(), UIImageView()]
    private var itemContainer: UIView?
    private var values: [Int]?
    private var backgroundImageView: UIImageView?


    // MARK: - Setup Layout from XML
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 220/255, green: 255/255, blue: 183/255, alpha: 1.0) // #DCFFB7
        
        // Background ImageView
        backgroundImageView = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_garden"))
        backgroundImageView?.contentMode = .scaleAspectFill // Thay centerCrop
        backgroundImageView?.clipsToBounds = false
        addSubview(backgroundImageView!)
        backgroundImageView?.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Item Container
        itemContainer = UIView()
        itemContainer?.isUserInteractionEnabled = true
        addSubview(itemContainer!)
        itemContainer?.makeViewCenterAndKeep(ratio: 1.7)
        // ImageViews trong item_container
        for (index, imageView) in imageViews.enumerated() {
            imageView.isUserInteractionEnabled = true
            //imageView.contentMode = .scaleAspectFit
            itemContainer?.addSubview(imageView)
            
            imageView.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.7) // Height 70%
                make.width.equalTo(imageView.snp.height).multipliedBy(404.4 / 937.6) // Tỷ lệ 404.4:937.6
                make.centerY.equalToSuperview()
                
                // Horizontal bias
                switch index {
                case 0:
                    make.centerX.equalToSuperview().multipliedBy(0.15 * 2) // Bias 0.15
                case 1:
                    make.centerX.equalToSuperview() // Bias mặc định (0.5)
                case 2:
                    make.centerX.equalToSuperview().multipliedBy(0.85 * 2) // Bias 0.85
                default:
                    break
                }
            }
        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self) // Giả định top menu được thêm trong NhanBietGameFragment
        topMenuContainer?.isHidden = true // visibility="invisible"
    }

    // MARK: - GameFragment Methods
    override open func createGame() {
        super.createGame()
        // Khởi tạo game nếu cần
    }

    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        setupLayout()        
    }

    override open func updateData() {
        super.updateData()
        values = Utils.generatePermutation(3)
        meIndex = Int.random(in: 0..<values!.count)
        
        let delay = playSound(openGameSound(), getLanguage() + "/toan/toan_1_hoa\(values![meIndex] + 1)")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }

        for (index, imageView) in imageViews.enumerated() {
            imageView.image = ids[values![index]]
            imageView.isExclusiveTouch = true // Đảm bảo chỉ một gesture nhận sự kiện
            imageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(imageTapped(_:))))
        }
    }

    // MARK: - Event Handling
    @objc private func imageTapped(_ sender: UITapGestureRecognizer) {
        guard let view = sender.view, let index = imageViews.firstIndex(of: view as! UIImageView) else { return }
        
        if index == meIndex {
            animateCoinIfCorrect(view: view)
            let delay = playSound(finishCorrect1Sounds())
            pauseGame()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(chooseWrongSounds())
            pauseGame()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }

    // MARK: - Override Methods
    override open func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound( getLanguage() + "/toan/toan_1_hoa\(values![meIndex] + 1)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// Giả lập các class cần thiết
class Utils {
    static func generatePermutation(_ n: Int) -> [Int] {
        var result = Array(0..<n) // Convert Range<Int> to [Int]
        result.shuffle()          // Shuffle the array
        return result
    }
    // Giả lập hàm này để trả về một mảng n số ngẫu nhiên từ 0 đến size-1 ( n<= size )
    static func generatePermutation(_ n: Int, size: Int) -> [Int] {
        if size < n {
            return []
        }
        var result = Array(0..<size) // Convert Range<Int> to [Int]
        result.shuffle()          // Shuffle the array
        return result.take(count: n)
    }
    static func getAppModuleName() -> String? {
        return Bundle.main.infoDictionary?["CFBundleName"] as? String
    }
    static func isAbstract(_ type: String) -> Bool{
        return false
    }
}
