<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_weight="1"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_calendar"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/report_bg_month"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="400:314"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:pcl_layout_marginPercentPerWidth="0.05">

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_month"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="1"
            app:fontFamily="@font/svn_freude"
            app:heightRatio="0.7"
            android:textColor="#000"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />
        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:text="tháng"
            app:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:textColor="#fff"
            app:layout_constraintHeight_percent="0.45"
            app:heightRatio="0.6"
            app:layout_constraintVertical_bias="0"
            android:layout_height="0dp"/>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toTopOf="@id/view_calendar"
        app:layout_constraintHorizontal_bias="0.1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.445"
        app:pcl_layout_marginBottomPercentPerWidth="0.05"
        app:pcl_layout_marginTopPercentPerWidth="0.4">

        <View
            android:id="@+id/view_column_time"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#1AA7D3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_time"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="12"
            android:textColor="#1AA7D3"
            app:fontFamily="@font/svn_freude"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toTopOf="@id/view_column_time"
            app:layout_constraintDimensionRatio="1.1"
            app:layout_constraintLeft_toLeftOf="@id/view_column_time"
            app:layout_constraintRight_toRightOf="@id/view_column_time" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toTopOf="@id/view_calendar"
        app:layout_constraintHorizontal_bias="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.445"
        app:pcl_layout_marginBottomPercentPerWidth="0.05"
        app:pcl_layout_marginTopPercentPerWidth="0.4">

        <View
            android:id="@+id/view_column_score"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="0.01"
            android:background="#4f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_score"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text="100"
            android:textColor="#58CC02"
            app:fontFamily="@font/svn_freude"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toTopOf="@id/view_column_score"
            app:layout_constraintDimensionRatio="1.1"
            app:layout_constraintLeft_toLeftOf="@id/view_column_score"
            app:layout_constraintRight_toRightOf="@id/view_column_score" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.kidsup.giaoducsom.view.PercentConstraintLayout>