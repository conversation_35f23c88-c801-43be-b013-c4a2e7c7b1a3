<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/text_name"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:lines="1"
        android:text="123456"
        android:textColor="#6B0800"
        app:autoSizeMaxTextSize="500dp"
        app:autoSizeMinTextSize="5dp"
        app:autoSizeTextType="uniform"></TextView>
</androidx.constraintlayout.widget.ConstraintLayout>