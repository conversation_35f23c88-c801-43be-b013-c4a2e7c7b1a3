//
//  toancoban_list_demcuaso.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 31/3/25.
//


import UIKit
import SnapKit

class toancoban_list_demcuaso: NhanBietGameFragment {

    // MARK: - Properties
    private var imageLeft: UIImageView!
    private var imageRight: UIImageView!
    private var meIndex: Int = 0
    private var coinView: UIView!
    private var windows: MyList<Int> = MyList<Int>()
    private var backgroundImageView: UIImageView!


    // MARK: - Setup Layout
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 235/255, green: 251/255, blue: 252/255, alpha: 1.0) // #EBFBFC

        // Background Image View
        backgroundImageView = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_garden"))
        backgroundImageView.contentMode = .scaleAspectFill
        addSubview(backgroundImageView)

        let mainContainer = UIView()
        mainContainer.backgroundColor = .green.withAlphaComponent(0.0003)
        addSubview(mainContainer)

        let imageContainer = UIView()
        imageContainer.backgroundColor = .red.withAlphaComponent(0.0003)
        mainContainer.addSubview(imageContainer)

        imageLeft = UIImageView()
        imageLeft.contentMode = .scaleAspectFit
        imageContainer.addSubview(imageLeft)

        imageRight = UIImageView()
        imageRight.contentMode = .scaleAspectFit
        imageContainer.addSubview(imageRight)

        coinView = UIView()  // Placeholder
        addSubview(coinView)

        // Setup tap gestures
        imageLeft.isUserInteractionEnabled = true
        imageRight.isUserInteractionEnabled = true

        let tapGestureLeft = UITapGestureRecognizer(target: self, action: #selector(imageTapped(_:)))
        imageLeft.isExclusiveTouch = true
        imageLeft.addGestureRecognizer(tapGestureLeft)
        imageLeft.tag = 0

        let tapGestureRight = UITapGestureRecognizer(target: self, action: #selector(imageTapped(_:)))
        imageRight.isExclusiveTouch = true
        imageRight.addGestureRecognizer(tapGestureRight)
        imageRight.tag = 1

        // SnapKit Constraints
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        mainContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.8)
        }

        imageContainer.makeViewCenterAndKeep(ratio: 2)
        
        addActionOnLayoutSubviews {
            imageContainer.snapToVerticalBias(verticalBias: 0.8)
        }

        imageLeft.snp.makeConstraints { make in
            make.top.bottom.leading.equalToSuperview()
            make.width.equalTo(imageContainer.snp.width).multipliedBy(0.5)
        }

        imageRight.snp.makeConstraints { make in
            make.top.bottom.trailing.equalToSuperview()
            make.width.equalTo(imageContainer.snp.width).multipliedBy(0.5)
        }

        coinView.snp.makeConstraints { make in
            make.center.equalToSuperview() // Example - Adjust as needed
            make.width.height.equalTo(50) // Example - Adjust as needed
        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true
        coinView.isUserInteractionEnabled = false
    }

    // MARK: - GameFragment Methods
    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        // Setup layout programmatically
        setupLayout()
    }

    override open func updateData() {
        super.updateData()

        windows = MyList(Utils.generatePermutation(4).shuffled().prefix(2).map { $0 + 1 })

        meIndex = Int.random(in: 0..<2)

        imageLeft.image = getImageForWindow(number: windows[0])
        imageRight.image = getImageForWindow(number: windows[1])

        let delay = playSound(openGameSound(), "vi/toan/toan_4 cua so", "vi/toan/toan_4 cua so\(windows[meIndex])")

        scheduler.schedule(delay: delay) {
            self.startGame()
        }
    }

    // Helper function to get image based on window number
    func getImageForWindow(number: Int) -> UIImage? {
        switch number {
        case 1: return Utilities.SVGImage(named: "math_4cuaso_1")
        case 2: return Utilities.SVGImage(named: "math_4cuaso_2")
        case 3: return Utilities.SVGImage(named: "math_4cuaso_3")
        case 4: return Utilities.SVGImage(named: "math_4cuaso_4")
        default: return Utilities.SVGImage(named: "math_4cuaso_1") // Provide a default image
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()

        if gameState == .playing {
            pauseGame()

            let delay = playSound("vi/toan/toan_4 cua so", "vi/toan/toan_4 cua so\(windows[meIndex])")

            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }

    // MARK: - Event Handling
    @objc func imageTapped(_ sender: UITapGestureRecognizer) {
        guard let imageView = sender.view as? UIImageView else { return }
        let index = imageView.tag

        if index == meIndex {
            // Assuming VisualTree.moveElement, scale, and duration are defined elsewhere
            // Replace with appropriate animation code
            animateCoinIfCorrect(view: coinView)

            let delay = playSound(finishCorrect1Sounds())

            pauseGame()
            scheduler.schedule(delay: delay) {
                self.finishGame()
            }
        } else {
            setGameWrong()

            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())

            pauseGame()
            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }
}
