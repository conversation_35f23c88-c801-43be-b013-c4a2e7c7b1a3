<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#2f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/nhanbiet_bg_option_white"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/taptrung_hinhchieu2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="374.3:239.7"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.8">

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/taptrung_hinhchieu2_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="374.3:41.7"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.151" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/taptrung_hinhchieu2_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="374.3:41.7"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.363" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/taptrung_hinhchieu2_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="374.3:41.7"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.574" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/taptrung_hinhchieu2_4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="374.3:41.7"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.786" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>