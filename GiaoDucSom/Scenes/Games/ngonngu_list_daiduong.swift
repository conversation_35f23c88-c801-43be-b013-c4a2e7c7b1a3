//
//  ngonngu_list_daiduong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreImage

class ngonngu_list_daiduong: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView1: SVGImageView!
    private var svgView2: SVGImageView!
    private var svgView3: SVGImageView!
    private var svgViewPrev: SVGImageView!
    private var svgViewCurrent: SVGImageView!
    private var svgViewNext: SVGImageView!
    private var containerLayout: UIView!
    private var svgImage: SVGImageView!
    private var svgImage2: SVGImageView!
    private var word1: KUButton!
    private var word2: KUButton!
    private var word3: KUButton!
    private var text1: AutosizeLabel!
    private var text2: AutosizeLabel!
    private var text3: AutosizeLabel!
    private var wordContainer: UIView!
    private var pack: Folder?
    private var mapData: MapData?
    private var itemMapItemMap: [Item: MapItem] = [:]
    private var itemStickerItemMap: [Item: StickerItem] = [:]
    private var mapItemItemMap: [MapItem: Item] = [:]
    private var items: [Item] = []
    private var itemIndex: Int = 0
    private var chooseItems: [Item] = []
    private var paths: [CGPoint] = []
    private var point: CGPoint?
    private var player: AVAudioPlayer?
    private var blurFilter: CIFilter!
    private var blurContext: CIContext!
    
    // MARK: - Models
    struct MapData: Codable {
        let map: String
        let h: Double
        let w: Double
        let items: [MapItem]
        
        enum CodingKeys: String, CodingKey {
            case map = "Map"
            case h = "H"
            case w = "W"
            case items = "Items"
        }
    }
    
    struct MapItem: Codable, Hashable {
        let id: String
        let h: Double
        let w: Double
        
        enum CodingKeys: String, CodingKey {
            case id = "Id"
            case h = "H"
            case w = "W"
        }
        
        func hash(into hasher: inout Hasher) {
            hasher.combine(id)
            hasher.combine(h)
            hasher.combine(w)
        }
    }
    
    struct StickerItem: Codable, Hashable{
        let id: String
        let h: Double
        let w: Double
        
        enum CodingKeys: String, CodingKey {
            case id = "Id"
            case h = "H"
            case w = "W"
        }
        func hash(into hasher: inout Hasher) {
            hasher.combine(id)
            hasher.combine(h)
            hasher.combine(w)
        }
    }
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        containerLayout = view
        view.clipsToBounds = true
        backgroundColor = UIColor.black
        
        svgImage = SVGImageView(frame: .zero)
        svgImage.contentMode = .scaleAspectFill
        svgImage.clipsToBounds = true
        svgImage.transform = CGAffineTransform(scaleX: 2, y: 2)
        view.addSubview(svgImage)
        svgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        svgImage2 = SVGImageView(frame: .zero)
        svgImage2.contentMode = .scaleAspectFill
        svgImage2.clipsToBounds = true
        svgImage2.transform = CGAffineTransform(scaleX: 2, y: 2)
        view.addSubview(svgImage2)
        svgImage2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1)
        
        
        let container1 = SVGImageView(frame: .zero)
        container1.accessibilityIdentifier = "svg_view_1"
        itemContainer.addSubview(container1)
        container1.snp.makeConstraints { make in
            make.width.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        let container2 = SVGImageView(frame: .zero)
        container2.accessibilityIdentifier = "svg_view_2"
        itemContainer.addSubview(container2)
        container2.snp.makeConstraints { make in
            make.width.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        let container3 = SVGImageView(frame: .zero)
        container3.accessibilityIdentifier = "svg_view_3"
        itemContainer.addSubview(container3)
        container3.snp.makeConstraints { make in
            make.width.height.equalToSuperview()
            make.center.equalToSuperview()
        }
        
        svgView1 = SVGImageView(frame: .zero)
        container1.addSubviewWithInset(subview: svgView1, inset: 0)
        svgView2 = SVGImageView(frame: .zero)
        container2.addSubviewWithInset(subview: svgView2, inset: 0)
        svgView3 = SVGImageView(frame: .zero)
        container3.addSubviewWithInset(subview: svgView3, inset: 0)
        svgViewPrev = container1
        svgViewCurrent = container2
        svgViewNext = container3
        
        wordContainer = UIView()
        wordContainer.clipsToBounds = false
        view.addSubview(wordContainer)
        wordContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.11)
            make.centerX.equalToSuperview()
            make.centerY.equalTo(containerLayout.snp.bottom).multipliedBy(0.5)
        }
        
        word1 = KUButton()
        word1.isHidden = true
        wordContainer.addSubview(word1)
        word1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let innerWord1 = UIImageView(image: Utilities.SVGImage(named: "btn_flashcards_imagetext"))
        word1.addSubview(innerWord1)
        innerWord1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        text1 = AutosizeLabel()
        text1.textAlignment = .center
        text1.textColor = UIColor.color(hex: "#74B6FF")
        text1.font = .Freude(size: 20)
        innerWord1.addSubview(text1)
        text1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.center.equalToSuperview()
        }
        
        word2 = KUButton()
        word2.isHidden = true
        wordContainer.addSubview(word2)
        word2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let innerWord2 = UIImageView(image: Utilities.SVGImage(named: "btn_flashcards_imagetext"))
        word2.addSubview(innerWord2)
        innerWord2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        text2 = AutosizeLabel()
        text2.textAlignment = .center
        text2.textColor = UIColor.color(hex: "#74B6FF")
        text2.font = .Freude(size: 20)
        innerWord2.addSubview(text2)
        text2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.center.equalToSuperview()
        }
        
        word3 = KUButton()
        word3.isHidden = true
        wordContainer.addSubview(word3)
        word3.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let innerWord3 = UIImageView(image: Utilities.SVGImage(named: "btn_flashcards_imagetext"))
        word3.addSubview(innerWord3)
        innerWord3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        text3 = AutosizeLabel()
        text3.textAlignment = .center
        text3.textColor = UIColor.color(hex: "#74B6FF")
        text3.font = .Freude(size: 20)
        innerWord3.addSubview(text3)
        text3.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.center.equalToSuperview()
        }
        
        let tapGesture1 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        word1.addGestureRecognizer(tapGesture1)
        let tapGesture2 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        word2.addGestureRecognizer(tapGesture2)
        let tapGesture3 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        word3.addGestureRecognizer(tapGesture3)
        
        // Khởi tạo CIFilter và CIContext cho blur
        blurFilter = CIFilter(name: "CIGaussianBlur")!
        blurContext = CIContext(options: nil)
        
        word1.isExclusiveTouch = true
        word2.isExclusiveTouch = true
        word3.isExclusiveTouch = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let stickerSizeUrl = Bundle.main.url(forResource: "sticker_size", withExtension: "json"),
              let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
              let stickerSizes = try? JSONDecoder().decode([MapData].self, from: stickerSizeData) else {
            print("Error loading sticker_size.json")
            return
        }
        
        guard let shadowUrl = Bundle.main.url(forResource: "shadow", withExtension: "json"),
              let shadowData = try? Data(contentsOf: shadowUrl),
              let stickerItems = try? JSONDecoder().decode([StickerItem].self, from: shadowData) else {
            print("Error loading shadow.json")
            return
        }
        
        let packs = FlashcardsManager.shared.getPacks()
        while true {
            guard let selectedPack = packs.randomElement() else { continue }
            if !selectedPack.name.en!.lowercased().contains(topic().lowercased()) {
                continue
            }
            var found = false
            if !selectedPack.items.isEmpty {
                for stickerSize in stickerSizes {
                    if stickerSize.map == selectedPack.folder.replacingOccurrences(of: " ", with: "") + ".svg" {
                        mapData = stickerSize
                        found = true
                        break
                    }
                }
            }
            if found {
                pack = selectedPack
                break
            }
        }
        
        guard let pack = pack, let mapData = mapData else {
            print("No valid pack or map data found")
            return
        }
        
        for item in pack.items {
            var found = false
            for mapItem in mapData.items {
                if item.name.en!.lowercased().replacingOccurrences(of: " ", with: "_") == mapItem.id.lowercased() {
                    itemMapItemMap[item] = mapItem
                    mapItemItemMap[mapItem] = item
                    found = true
                    break
                }
            }
            for stickerItem in stickerItems {
                if (pack.folder + "\\" + item.name.en! + ".svg") == stickerItem.id {
                    itemStickerItemMap[item] = stickerItem
                    break
                }
            }
            if !found {
                itemMapItemMap[item] = nil
                print("Not found: \(pack.folder): \(item.name.en!)")
            }
        }
        
        items = Array(itemMapItemMap.keys).shuffled()
        while items.count < 10000 {
            items.append(contentsOf: items.shuffled())
        }
        
        let svg = Utilities.GetSVGKImage(named: "gallery/\(pack.folder.replacingOccurrences(of: " ", with: ""))_1.svg")
        svgImage.image = svg.uiImage
    }
    
    override func createGame() {
        super.createGame()
        
        applyBlur(radius: 20)
        paths = [CGPoint(x: 0, y: 0)]
        var valueX: CGFloat = 0
        var valueY: CGFloat = 0
        for _ in 1..<items.count {
            while true {
                let nextStep = getNextStep()
                valueX += nextStep.x
                valueY += nextStep.y
                if valueX >= 4 || valueY >= 4 || valueX <= -4 || valueY <= -4 {
                    valueX -= nextStep.x
                    valueY -= nextStep.y
                    continue
                }
                paths.append(nextStep)
                break
            }
        }
        
        loadNextItem(itemIndex: 0)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing, let point = point {
            pauseGame(stopMusic: false)
            var delay: TimeInterval = 0
            let direction = point.x == 1 ? "right" : point.x == -1 ? "left" : point.y == 1 ? "bot" : "top"
            delay += playSound(delay: delay, names: ["ngonngu/ban nao day nhi/\(direction)"])
            UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self.word1.alpha = 1
                self.word1.transform = .identity
            })
            delay += playSound(delay: delay, names: ["effect/bubble"])
            delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[0].path!.replacingOccurrences(of: ".svg", with: ""))"])
            UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self.word2.alpha = 1
                self.word2.transform = .identity
            })
            delay += playSound(delay: delay, names: ["effect/bubble"])
            delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[1].path!.replacingOccurrences(of: ".svg", with: ""))"])
            UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self.word3.alpha = 1
                self.word3.transform = .identity
            })
            delay += playSound(delay: delay, names: ["effect/bubble"])
            delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[2].path!.replacingOccurrences(of: ".svg", with: ""))"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        pauseGame(stopMusic: false)
        word1.isUserInteractionEnabled = false
        word2.isUserInteractionEnabled = false
        word3.isUserInteractionEnabled = false
        
        if view == word1 {
            if items[itemIndex + 1] == chooseItems[0] {
                animateCoinIfCorrect(view: view)
                clickRight()
            } else {
                clickWrong()
            }
        } else if view == word2 {
            if items[itemIndex + 1] == chooseItems[1] {
                animateCoinIfCorrect(view: view)
                clickRight()
            } else {
                clickWrong()
            }
        } else if view == word3 {
            if items[itemIndex + 1] == chooseItems[2] {
                animateCoinIfCorrect(view: view)
                clickRight()
            } else {
                clickWrong()
            }
        }
        
        scheduler.schedule(after: 0.5) { [weak self] in
            self?.resumeGame(startMusic: false)
            self?.word1.isUserInteractionEnabled = true
            self?.word2.isUserInteractionEnabled = true
            self?.word3.isUserInteractionEnabled = true
        }
    }
    
    // MARK: - Helper Methods
    func topic() -> String {
        return "Aquatic Animals"
    }
    
    private func getScale(item: Item) -> CGFloat {
        guard let mapItem = itemMapItemMap[item] else { return 0.5 }
        if let stickerItem = itemStickerItemMap[item] {
            return CGFloat(mapItem.h / mapData!.h) / CGFloat(stickerItem.h / 400.0)
        }
        return CGFloat(mapItem.h / mapData!.h)
    }
    
    
    
    private func clickRight() {
        pauseGame(stopMusic: false)
        playSound("effect/answer_correct")
        playSound(delay: 0.2, names: ["answer_correct\(Int.random(in: 1...3))"])
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.3, delay: 0.1, options: [.curveLinear, .beginFromCurrentState, .allowUserInteraction], animations: {
                self.word1.alpha = 0.01
                self.word1.transform = .init(scaleX: 0.1, y: 0.1)
            })
            UIView.animate(withDuration: 0.3, delay: 0.3, options: [.curveLinear, .beginFromCurrentState, .allowUserInteraction], animations: {
                self.word2.alpha = 0.01
                self.word2.transform = .init(scaleX: 0.1, y: 0.1)
            })
            UIView.animate(withDuration: 0.3, delay: 0.5, options: [.curveLinear, .beginFromCurrentState, .allowUserInteraction], animations: {
                self.word3.alpha = 0.01
                self.word3.transform = .init(scaleX: 0.1, y: 0.1)
            })
            
            scheduler.schedule(after: 1.5) { [weak self] in
                guard let self = self else { return }
                let temp = self.svgViewPrev
                self.svgViewPrev = self.svgViewCurrent
                self.svgViewCurrent = self.svgViewNext
                self.svgViewNext = temp
                self.loadNextItem(itemIndex: self.itemIndex + 1)
            }
        }
    }
    
    private func clickWrong() {
        playSound(delay: 0, names: [answerWrongEffectSound(),getWrongHumanSound()])
        setGameWrong()
    }
    
    private func getNextStep() -> CGPoint {
        return Bool.random() ? CGPoint(x: random(-1, 1), y: 0) : CGPoint(x: 0, y: random(-1, 1))
    }
    
    private func loadNextItem(itemIndex: Int) {
        pauseGame(stopMusic: false)
        self.itemIndex = itemIndex
        
        let currentItem = items[itemIndex]
        (svgViewCurrent.subviews[0] as! UIImageView).image = Utilities.GetSVGKImage(named: "topics/\(pack!.folder)/\(currentItem.path!)").uiImage
        let scale = getScale(item: currentItem)
        svgViewCurrent.subviews[0].transform = CGAffineTransform(scaleX: scale, y: scale)
        svgViewCurrent.stringTag = "\(itemIndex)"
        
        let nextItem = items[itemIndex + 1]
        (svgViewNext.subviews[0] as! UIImageView).image = Utilities.GetSVGKImage(named: "topics/\(pack!.folder)/\(nextItem.path!)").uiImage
        let nextScale = getScale(item: nextItem)
        svgViewNext.subviews[0].transform = CGAffineTransform(scaleX: nextScale, y: nextScale)
        svgViewNext.stringTag = "\(itemIndex + 1)"
        
        let nextNextItem = items[itemIndex + 2]
        scheduler.schedule(after: 0.6) { [weak self] in
            guard let self = self else { return }
            (self.svgViewPrev.subviews[0] as! UIImageView).image = Utilities.GetSVGKImage(named: "topics/\(self.pack!.folder)/\(nextNextItem.path!)").uiImage
            let scale = self.getScale(item: nextNextItem)
            self.svgViewPrev.subviews[0].transform = CGAffineTransform(scaleX: scale, y: scale)
            self.svgViewPrev.stringTag = "\(itemIndex + 2)"
        }
        
        if itemIndex == 0 {
            svgViewCurrent.transform = .identity
            svgViewNext.transform = svgViewNext.transform.translatedBy(x: paths[itemIndex + 1].x * containerLayout.frame.width, y: paths[itemIndex + 1].y * containerLayout.frame.height)
            svgViewPrev.alpha = 0.01
            
            let delay = playSound(delay: 0, names: [
                "ngonngu/ban nao day nhi/hello",
                "topics/\(pack!.folder)/\(items[0].path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
            scheduler.schedule(after: delay + 1) { [weak self] in
                self?.showNextItem(itemIndex: itemIndex, nextIndex: itemIndex + 1)
            }
        } else {
            let nextIndex = itemIndex + 1
            UIView.animate(withDuration: 0.6) {
                self.svgViewPrev.transform = CGAffineTransformMakeTranslation(-self.paths[itemIndex].x * self.containerLayout.frame.width, -self.paths[itemIndex].y * self.containerLayout.frame.height)
                self.svgViewPrev.alpha = 0
            }
            UIView.animate(withDuration: 0.6) {
                self.svgViewCurrent.transform = .identity
                self.svgViewCurrent.alpha = 1
            }
            UIView.animate(withDuration: 0.6) {
                self.svgImage2.transform = CGAffineTransformMakeTranslation(-self.paths[itemIndex].x * self.svgImage2.frame.height / 10, -self.paths[itemIndex].y * self.svgImage2.frame.height / 10).scaledBy(x: 2, y: 2)
            }
            
            let item = items[itemIndex]
            var delay = playSound(delay: 0, names: [
                "ngonngu/ban nao day nhi/hello",
                "topics/\(pack!.folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
            
            if nextIndex == 40 {
                pauseGame(stopMusic: false)
                delay += playSound(delay: delay, names: [
                    "effect/answer_end",
                    getCorrectHumanSound(),
                    endGameSound()
                ])
                scheduler.schedule(after: delay) { [weak self] in
                    self?.finishGame()
                }
                return
            }
            
            scheduler.schedule(after: delay + 1) { [weak self] in
                self?.showNextItem(itemIndex: itemIndex, nextIndex: nextIndex)
            }
        }
    }
    
    private func showNextItem(itemIndex: Int, nextIndex: Int) {
        let point = paths[nextIndex]
        self.point = point
        
        var delay: TimeInterval = 0
        let direction = point.x == 1 ? "right" : point.x == -1 ? "left" : point.y == 1 ? "bot" : "top"
        delay += playSound(delay: delay, names: ["ngonngu/ban nao day nhi/\(direction)"])
        
        
        
        self.svgViewNext.transform = CGAffineTransformMakeTranslation((self.paths[nextIndex].x * self.containerLayout.frame.width) / 1, (self.paths[nextIndex].y * self.containerLayout.frame.height) / 1)
        self.svgViewNext.alpha = 0
        
        
        
        UIView.animate(withDuration: 0.6) {
            self.svgViewNext.transform = CGAffineTransformMakeTranslation((self.paths[nextIndex].x * self.containerLayout.frame.width) / 2, (self.paths[nextIndex].y * self.containerLayout.frame.height) / 2)
            self.svgViewNext.alpha = 1
        }
        UIView.animate(withDuration: 0.6) {
            self.svgViewCurrent.transform = CGAffineTransformMakeTranslation(-self.paths[nextIndex].x * self.containerLayout.frame.width / 4, -self.paths[nextIndex].y * self.containerLayout.frame.height / 4)
            self.svgViewCurrent.alpha = 1
        }
        UIView.animate(withDuration: 0.6) {
            self.svgImage2.transform = CGAffineTransformMakeTranslation( -self.paths[nextIndex].x * self.svgImage2.frame.height / 20,  -self.paths[nextIndex].y * self.svgImage2.frame.height / 20).scaledBy(x: 2, y: 2)
        }
        
        let item = items[itemIndex]
        let nextItem = items[nextIndex]
        while true {
            chooseItems = items.shuffled().prefix(2).map { $0 }
            if !chooseItems.contains(nextItem) && !chooseItems.contains(item) && chooseItems[0] != chooseItems[1] {
                break
            }
        }
        chooseItems.append(nextItem)
        chooseItems = chooseItems.shuffled()
        
        text1.text = chooseItems[0].name.vi
        text2.text = chooseItems[1].name.vi
        text3.text = chooseItems[2].name.vi
        
        word1.alpha = 0
        word2.alpha = 0
        word3.alpha = 0
        word1.isHidden = false
        word2.isHidden = false
        word3.isHidden = false
        
        wordContainer.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.15)
            make.centerX.equalToSuperview()
            make.centerY.equalTo(containerLayout.snp.bottom).multipliedBy(self.paths[nextIndex].y == -1 ? 0.85 : 0.15)
        }
        self.word1.alpha = 0.01
        self.word1.transform = CGAffineTransformMakeScale(0.01, 0.01)
        self.word2.alpha = 0.01
        self.word2.transform = CGAffineTransformMakeScale(0.01, 0.01)
        self.word3.alpha = 0.01
        self.word3.transform = CGAffineTransformMakeScale(0.01, 0.01)
        UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
            self.word1.alpha = 1
            self.word1.transform = .identity
        })
        delay += playSound(delay: delay, names: ["effect/bubble"])
        delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[0].path!.replacingOccurrences(of: ".svg", with: ""))"])
        UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
            self.word2.alpha = 1
            self.word2.transform = .identity
        })
        delay += playSound(delay: delay, names: ["effect/bubble"])
        delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[1].path!.replacingOccurrences(of: ".svg", with: ""))"])
        UIView.animate(withDuration: 0.6, delay: delay, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
            self.word3.alpha = 1
            self.word3.transform = .identity
        })
        delay += playSound(delay: delay, names: ["effect/bubble"])
        delay += playSound(delay: delay, names: ["topics/\(self.pack!.folder)/\(self.chooseItems[2].path!.replacingOccurrences(of: ".svg", with: ""))"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    private func applyBlur(radius: CGFloat) {
        guard let inputImage = svgImage.image?.ciImage ?? CIImage(image: svgImage.image!) else { return }
        
        blurFilter.setValue(inputImage, forKey: kCIInputImageKey)
        blurFilter.setValue(radius, forKey: kCIInputRadiusKey)
        
        guard let outputImage = blurFilter.outputImage else { return }
        if let cgImage = blurContext.createCGImage(outputImage, from: inputImage.extent) {
            let blurredImage = UIImage(cgImage: cgImage)
            svgImage2.image = blurredImage
        }
    }
}
