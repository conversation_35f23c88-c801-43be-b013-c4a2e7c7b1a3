//
//  TapTrungMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class TapTrungMainViewController: SubjectMainViewController {
    override var backgroundMusicFile: String? {
        "bg_list5"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Tập Trung")
        titleColor = UIColor(hex: "#00C8CE")
        backgroundColor = UIColor(hex: "#BEE8E6")
    }
    
    override func setupData() {
        // Section 1: Tập thổi
        let tapThoiSection = SectionData(
            header: "Tập thổi",
            items: [
                TroChoiNgonNguItem(data: "taptrung_list_thoichongchong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_thoibongbay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_thoibongxaphong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_thoithuyengiay", isTapDocGame: false)
            ]
        )
        
        // Section 2: Quan sát
        let quanSatSection = SectionData(
            header: "Quan sát",
            items: [
                TroChoiNgonNguItem(data: "taptrung_list_chonnhanpizza", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_lamkemocque", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_timbongdongvat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_timcaphinhgiongnhau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_chiecorucro", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_thapmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_kimtuthap", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_doitat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_doigiay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_bimtocmau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_caphinhdenhau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_demchim", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_hinhchieuhop", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_duongchamdiem", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_xephinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_kinhlup", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_upcoc", isTapDocGame: false)
            ]
        )
        
        // Section 3: Vận động tinh
        let vanDongTinhSection = SectionData(
            header: "Vận động tinh",
            items: [
                TroChoiNgonNguItem(data: "taptrung_list_vedoixung", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_vehaitay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_xephop", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_veduongdi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_mecung", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_xephinhgo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_noidaychun", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_stickman", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_lanbi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_ongnhom", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_xoayhinh", isTapDocGame: false)
            ]
        )
        
        // Section 4: Ghi nhớ
        let ghiNhoSection = SectionData(
            header: "Ghi nhớ",
            items: [
                TroChoiNgonNguItem(data: "taptrung_list_latthehinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "taptrung_list_nhochuoihinh", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            tapThoiSection,
            quanSatSection,
            vanDongTinhSection,
            ghiNhoSection
        ]
    }
    /*
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            // Mở SingleGameViewController với game tương ứng
            let singleGameVC = SingleGameViewController()
            singleGameVC.game = getGameByThumb(troChoiItem.data)
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = checkFreeGameByThumbnail(troChoiItem.data)
            let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
            if playable {
                navigationController?.pushViewController(singleGameVC, animated: true)
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this game")
            }
        }
    }
     */
    
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự TapTrungGameNameHelper.getGameByThumb
        return thumbnail
    }
}
