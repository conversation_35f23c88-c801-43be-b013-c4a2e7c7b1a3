<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#D6FAFF"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.33">

        <com.kidsup.giaoducsom.view.MathNumpad
            android:id="@+id/numpad"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.05" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.67">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/grid_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_margin="20dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="2.2"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/left_item_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/right_item_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="4.4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:pcl_layout_marginPercent="0.05">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout59"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_result_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/constraintLayout58"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:id="@+id/text_a"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="#74B6FF"
                        app:heightRatio="0.7"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout58"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="0.6"
                    app:layout_constraintEnd_toStartOf="@+id/constraintLayout60"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/constraintLayout59"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="+"
                        android:textColor="#74B6FF"
                        app:heightRatio="0.7"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout60"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_result_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/constraintLayout61"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/constraintLayout58"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:id="@+id/text_b"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="#74B6FF"
                        app:heightRatio="0.7"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout61"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="0.6"
                    app:layout_constraintEnd_toStartOf="@+id/constraintLayout63"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/constraintLayout60"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="="
                        android:textColor="#74B6FF"
                        app:heightRatio="0.7"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/constraintLayout63"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_result_bg"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHeight_percent="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/constraintLayout61"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.view.HeightRatioTextView
                        android:id="@+id/text_result"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="1"
                        android:textColor="#74B6FF"
                        app:heightRatio="0.7"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.kidsup.giaoducsom.view.PercentConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>