//
//  tuduy_list_rotnuoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_rotnuoc: NhanBietGameFragment {
    // MARK: - Properties
    private var btnMoveBack: KUButton!
    private var gridLayout: MyGridView!
    private var countOfMoves: Int = 0
    private var numberOfTubes: Int = 10
    private var numberOfFreeTubes: Int = 2
    private var tubes: [[Int]] = []
    private var stop: Bool = false
    private var selectedViews: [UIView] = []
    private var views: [UIView] = []
    private let TIME_TUBE_MOVE: TimeInterval = 0.5
    private let TIME_TUBE_DROP: TimeInterval = 0.5
    private let TIME_TUBE_MOVE_BACK: TimeInterval = 0.5
    private var timeoutTimerGameOver: TimeoutTimer!
    private var viewNoMove: UIImageView!
    private var doneCount: Int = 0
    //var viewNapWater : UIView! = nil
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 177/255, green: 201/255, blue: 249/255, alpha: 1) // #B1C9F9
        
        viewNoMove = UIImageView(image: Utilities.SVGImage(named: "empty"))
        viewNoMove.backgroundColor = .red.withAlphaComponent(0.7)
        viewNoMove.isHidden = true
        addSubview(viewNoMove)
        bringSubviewToFront(view)
        viewNoMove.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let contentLayout = UIView()
        contentLayout.clipsToBounds = false
        view.addSubview(contentLayout)
        contentLayout.makeViewCenterAndKeep(ratio: 2.0)
        contentLayout.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        contentLayout.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(contentLayout).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.4) // Bias 0.7
            make.width.equalTo(gridLayout.snp.height).multipliedBy(2.0) // Ratio 2:1
        }
        
        btnMoveBack = KUButton()
        btnMoveBack.setImage(Utilities.SVGImage(named: "btn_back"), for: .normal)
        btnMoveBack.isHidden = true
        view.addSubview(btnMoveBack)
        btnMoveBack.snp.makeConstraints { make in
            make.width.height.equalTo(50)
            make.top.right.equalToSuperview()
        }
        btnMoveBack.addTarget(self, action: #selector(handleMoveBackTap(_:)), for: .touchUpInside)
        timeoutTimerGameOver = TimeoutTimer()
        timeoutTimerGameOver.duration = 10.0
        timeoutTimerGameOver.onActived = { [weak self] in
            guard let self = self, !self.canMove() else { return }
            MessageDialogView().setTitle("Hết nước").setMessage("Hết cách di chuyển").show()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "tuduy/rot nuoc")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/rot nuoc")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        numberOfTubes = 6
        numberOfFreeTubes = 1
        countOfMoves = 15
        createData()
        
        var errorCount = 0
        while true {
            var isOk = true
            tubes = []
            for i in 0..<numberOfTubes {
                var tube: [Int] = []
                if i < numberOfTubes - numberOfFreeTubes {
                    for _ in 0..<4 {
                        tube.append(i)
                    }
                }
                tubes.append(tube)
            }
            
            for _ in 0..<countOfMoves {
                if !moveBack() {
                    errorCount += 1
                    isOk = false
                    break
                }
            }
            if isOk { break }
        }
        
        for i in 0..<views.count {
            if let fourColorView = views[i].findSubview(ofType: FourColorView.self) {
                fourColorView.updateColor(colors: tubes[i])
            }
        }
    }
    
    private func createData() {
        tubes = []
        for i in 0..<numberOfTubes {
            var tube: [Int] = []
            if i < numberOfTubes - numberOfFreeTubes {
                for _ in 0..<4 {
                    tube.append(i)
                }
            }
            tubes.append(tube)
        }
        
        views = []
        for i in 0..<numberOfTubes {
            let view = createItemBinh()
            if let fourColorView = view.subviews.first(where: { $0 is FourColorView }) as? FourColorView {
                fourColorView.updateColor(colors: tubes[i])
            }
            view.tag = i
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTubeTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            views.append(view)
        }
        
        gridLayout.columns = numberOfTubes
        gridLayout.itemRatio = 0.36
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.reloadItemViews(views: views)
    }
    
    // MARK: - Touch Handling
    @objc private func handleMoveBackTap(_ sender: UIButton) {
        if moveBack() {
            for i in 0..<views.count {
                if let fourColorView = views[i].subviews.first(where: { $0 is FourColorView }) as? FourColorView {
                    fourColorView.updateColor(colors: tubes[i])
                }
            }
        }
    }
    
    @objc private func handleTubeTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        guard let viewNapWater = view.viewWithStringTag("view_nap_water") else { return }
        if !viewNapWater.isHidden { return }
        if selectedViews.count >= 2 { return }
        
        if selectedViews.isEmpty {
            playSound("effect/slide1")
            selectedViews.append(view)
            UIView.animate(withDuration: 0.2) {
                view.transform = CGAffineTransform(translationX: 0, y: -view.frame.height / 5)
            }
        } else if selectedViews.count == 1 {
            selectedViews.append(view)
            let from = views.firstIndex(of: selectedViews[0])!
            let to = views.firstIndex(of: selectedViews[1])!
            
            if from != to {
                let toSize = tubes[to].count
                let fromSize = tubes[from].count
                
                if fromSize > 0 && toSize < 4 {
                    let value = tubes[from][fromSize - 1]
                    let toValue = toSize > 0 ? tubes[to][toSize - 1] : value
                    
                    if value == toValue {
                        var count = 0
                        var tempValue = value
                        while tempValue == toValue {
                            count += 1
                            tubes[from].removeLast()
                            tubes[to].append(tempValue)
                            if tubes[from].isEmpty || tubes[to].count == 4 { break }
                            tempValue = tubes[from].last ?? -1
                        }
                        
                        // Đẩy lại để chuẩn bị animation
                        for _ in 0..<count {
                            tubes[from].append(toValue)
                            tubes[to].removeLast()
                        }
                        
                        let tubeFromLeft = tubes[from].count - count
                        waterDrop(fromView: selectedViews[0], toView: selectedViews[1], tubeFromLeft: tubeFromLeft, count: count, value: toValue, from: from, to: to, viewNapWater: viewNapWater)
                        return
                    }
                }
            }
            
            playSound("effect/slide1")
            UIView.animate(withDuration: 0.2) {
                self.selectedViews.forEach { $0.transform = .identity }
            }
            selectedViews = []
        }
    }
        
    // MARK: - Helper Methods
    private func createItemBinh() -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "tuduy_rotnuoc_binh")
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height).multipliedBy(0.36) // Ratio 0.36
        }
        let viewNapWater = UIImageView(image: Utilities.SVGImage(named: "tuduy_rotnuoc_nap"))
        viewNapWater.stringTag = "view_nap_water"
        viewNapWater.isHidden = true
        container.addSubview(viewNapWater)
        viewNapWater.snp.makeConstraints { make in
            make.width.equalTo(container)
            make.height.equalTo(viewNapWater.snp.width).multipliedBy(133.0 / 175.9) // Ratio 175.9:133
            make.centerX.equalToSuperview()
            make.bottom.equalTo(container.snp.top)
        }
        
        let viewDropWater = UIView()
        viewDropWater.stringTag = "view_drop_water"
        viewDropWater.isHidden = true
        container.addSubview(viewDropWater)
        viewDropWater.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.05)
            make.height.equalTo(container).multipliedBy(1.2)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let innerContainer = UIView()
        innerContainer.clipsToBounds = true
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let fourColorView = FourColorView()
        //fourColorView.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        innerContainer.addSubview(fourColorView)
        fourColorView.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.65)
            make.width.equalTo(fourColorView.snp.height).multipliedBy(0.25) // Ratio 0.25
            make.center.equalToSuperview()
        }
        
        let newWaterContainer = UIView()
        innerContainer.addSubview(newWaterContainer)
        newWaterContainer.snp.makeConstraints { make in
            make.edges.equalTo(fourColorView)
        }
        
        let viewNewWater = UIView()
        viewNewWater.stringTag = "view_new_water"
        newWaterContainer.addSubview(viewNewWater)
        viewNewWater.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let binhOverlay = UIImageView()
        binhOverlay.image = Utilities.SVGImage(named: "tuduy_rotnuoc_binh_2")
        container.addSubview(binhOverlay)
        binhOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return view
    }
    
    private func waterDrop(fromView: UIView, toView: UIView, tubeFromLeft: Int, count: Int, value: Int, from: Int, to: Int, viewNapWater: UIView) {
        let moveInfo = fromView.distanceFromCenterToCenter(to: toView)
        let toInRight = to >= numberOfTubes / 2
        let rotation = tubeFromLeft == 0 ? 90 : tubeFromLeft == 1 ? 82 : tubeFromLeft == 2 ? 75 : 62
        
        guard let fourColorView = fromView.findSubview(ofType: FourColorView.self),
              let viewNewWater = toView.viewWithStringTag("view_new_water"),
              let viewDropWater = toView.viewWithStringTag("view_drop_water") else { return }
        
        fromView.bringSubviewToFront(self)
        let segment = fromView.distanceFromCenterToCenter(to: toView)
        UIView.animate(withDuration: TIME_TUBE_MOVE) {
            var transform = fromView.transform
            transform.tx += -segment.x - (toInRight ? fromView.frame.width/2 : -fromView.frame.width/2)
            fromView.transform = transform
        } completion: { _ in
            self.playSound("effect/tuduy_rotnuoc_nuocchay")
            let frame = fromView.subviews[0].frame
            fromView.subviews[0].layer.anchorPoint = CGPoint(x: toInRight ? 0.9 : 0.1, y: 0)
            fromView.subviews[0].frame = frame
            
            let animator = UIViewPropertyAnimator(duration: self.TIME_TUBE_DROP, curve: .linear) {
                fromView.subviews[0].transform = CGAffineTransformMakeRotation(CGFloat(toInRight ? rotation : -rotation) * .pi / 180)
                fourColorView.updateRotation(angle: Float(toInRight ? rotation : -rotation))
            }
            
            viewDropWater.backgroundColor = FourColorView.colorsList[value]
            viewDropWater.isHidden = false            
            self.scheduler.schedule(delay: self.TIME_TUBE_DROP) { [weak self] in
                guard self != nil else { return }
                viewDropWater.isHidden = true
            }
            
            animator.addCompletion { _ in
                let backAnimator = UIViewPropertyAnimator(duration: self.TIME_TUBE_MOVE_BACK, curve: .linear) {
                    fromView.subviews[0].transform = .identity
                    fourColorView.updateRotation(angle: 0)
                }
                backAnimator.startAnimation()
                
                UIView.animate(withDuration: self.TIME_TUBE_MOVE_BACK) {
                    fromView.transform = .identity
                }
                
                let oldSize = self.tubes[to].count
                let newSize = oldSize + count
                let margin = (4 - newSize) * Int(fourColorView.frame.height) / 4
                viewNewWater.snp.updateConstraints { make in
                    make.top.equalToSuperview().offset(margin)
                    make.bottom.equalToSuperview().offset(-oldSize * Int(fourColorView.frame.height) / 4)
                }
                
                viewNewWater.backgroundColor = FourColorView.colorsList[value]
                let frame = viewNewWater.frame
                viewNewWater.layer.anchorPoint = CGPoint(x: 0.5, y: CGFloat(count) / 4)
                viewNewWater.transform = CGAffineTransform(scaleX: 1, y: 0)
                viewNewWater.frame = frame
                
                UIView.animate(withDuration: self.TIME_TUBE_DROP, delay: self.TIME_TUBE_MOVE) {
                    viewNewWater.transform = .identity
                } completion: { _ in
                    viewNewWater.backgroundColor = .clear
                    //viewDropWater.isHidden = true
                }
                
                
                UIView.animate(withDuration: self.TIME_TUBE_MOVE + self.TIME_TUBE_DROP * 0.2) {
                    viewDropWater.transform = .identity
                } completion: { _ in
                    //viewDropWater.isHidden = false
                }
                
                UIView.animate(withDuration: self.TIME_TUBE_MOVE + self.TIME_TUBE_DROP) {
                    viewDropWater.transform = .identity
                } completion: { _ in
                    for _ in 0..<count {
                        self.tubes[from].removeLast()
                        self.tubes[to].append(value)
                    }
                    
                    if let select1 = self.selectedViews[0].findSubview(ofType: FourColorView.self) {
                        select1.updateColor(colors: self.tubes[from])
                    }
                    if let select2 = self.selectedViews[1].findSubview(ofType: FourColorView.self) {
                        select2.updateColor(colors: self.tubes[to])
                    }
                    
                    if self.tubes[to].count == 4 && self.tubes[to].allSatisfy({ $0 == self.tubes[to][0] }) {
                        self.scheduler.schedule(after: 0.5) {
                            [weak self] in
                            guard let self = self else { return }
                            self.playSound("effect/answer_correct1")
                            viewNapWater.transform = CGAffineTransform(translationX: 0, y: -self.frame.height)
                            viewNapWater.alpha = 0.01
                            viewNapWater.isHidden = false
                            UIView.animate(withDuration: 0.2) {
                                [weak self] in
                                guard let self = self else { return }
                                viewNapWater.transform = .identity
                                viewNapWater.alpha = 1
                            }
                            
                            self.doneCount += 1
                            if self.doneCount == self.numberOfTubes - self.numberOfFreeTubes {
                                self.pauseGame()
                                self.animateCoinIfCorrect(view: self.gridLayout)
                                let delay = self.playSound(self.getCorrectHumanSound(), self.endGameSound()) + 0.5
                                self.scheduler.schedule(delay: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            }
                        }
                    }
                    
                    self.selectedViews = []
                    let canMove = self.canMove()
                    self.backgroundColor = canMove ? UIColor(red: 177/255, green: 201/255, blue: 249/255, alpha: 1) : UIColor(red: 178/255, green: 185/255, blue: 247/255, alpha: 1)
                    self.viewNoMove.isHidden = canMove
                    if !canMove {
                        self.timeoutTimerGameOver.schedule()
                    }
                }
            }
            animator.startAnimation()
        }
    }
    
    private func moveBack() -> Bool {
        var count = 0
        while count < 1000 {
            count += 1
            let moveFrom = Int.random(in: 0..<numberOfTubes)
            let moveTo = Int.random(in: 0..<numberOfTubes)
            
            if moveTo == moveFrom { continue }
            
            var tubeTo = tubes[moveTo]
            let moveToSize = tubeTo.count
            var tubeFrom = tubes[moveFrom]
            let moveFromSize = tubeFrom.count
            
            if moveFromSize > 0 && moveToSize < 4 {
                let value = tubeFrom[moveFromSize - 1]
                if moveToSize > 0 {
                    let toValue = tubeTo[moveToSize - 1]
                    if value == toValue && moveFromSize < 4 { continue }
                }
                
                if moveFromSize != 1 && (moveFromSize >= 2 && tubeFrom[moveFromSize - 2] != value) { continue }
                
                var maxChoose = min(4 - moveToSize, moveFromSize)
                for i in (0..<tubeFrom.count).reversed() {
                    if tubeFrom[i] != value {
                        let canTakeOff = tubeFrom.count - i - 2
                        maxChoose = min(maxChoose, canTakeOff)
                        break
                    }
                }
                
                let choose = Int.random(in: 1...maxChoose)
                for _ in 0..<choose {
                    tubeTo.append(value)
                    tubeFrom.removeLast()
                }
                tubes[moveTo] = tubeTo
                tubes[moveFrom] = tubeFrom
                return true
            }
        }
        return false
    }
    
    private func canMove() -> Bool {
        let solver = ColorBallSort()
        return solver.solve(tubes: tubes) != nil
    }
}

// MARK: - Supporting Structures

class FourColorView: UIView {
    // MARK: - Properties
    static let colors: [UIColor] = [
        UIColor(red: 255/255, green: 110/255, blue: 106/255, alpha: 1), // #FF6E6A
        UIColor(red: 79/255, green: 238/255, blue: 26/255, alpha: 1),  // #4FEE1A
        UIColor(red: 254/255, green: 202/255, blue: 51/255, alpha: 1), // #FECA33
        UIColor(red: 119/255, green: 72/255, blue: 41/255, alpha: 1),  // #774829
        UIColor(red: 252/255, green: 103/255, blue: 209/255, alpha: 1), // #FC67D1
        UIColor(red: 24/255, green: 169/255, blue: 251/255, alpha: 1),  // #18A9FB
        UIColor(red: 134/255, green: 95/255, blue: 255/255, alpha: 1)   // #865FFF
    ]
    
    static var colorsList: [UIColor] = colors.shuffled()
    
    private var paint1: UIColor = colorsList[0]
    private var paint2: UIColor = colorsList[1]
    private var paint3: UIColor = colorsList[2]
    private var paint4: UIColor = colorsList[3]
    private var tube: [Int] = []
    private var rotation: CGFloat = 0
    private var right: Bool = true
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
    }
    
    // MARK: - Public Methods
    func updateColor(colors: [Int]) {
        self.tube = colors
        paint4 = tube.count > 0 ? FourColorView.colorsList[tube[0]] : .clear
        paint3 = tube.count > 1 ? FourColorView.colorsList[tube[1]] : .clear
        paint2 = tube.count > 2 ? FourColorView.colorsList[tube[2]] : .clear
        paint1 = tube.count > 3 ? FourColorView.colorsList[tube[3]] : .clear
        setNeedsDisplay()
    }
    
    func updateRotation(angle: Float) {
        rotation = CGFloat(angle)
        right = true
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext() else { return }
        
        let width = bounds.width
        let height = bounds.height
        let partHeight = height / 4
        
        context.saveGState()
        // Không xoay toàn bộ canvas ở đây để xử lý riêng từng hình bình hành
        
        if tube.count >= 4 {
            drawParallelogram(context: context, left: 0, top: 0, right: width, bottom: partHeight, color: paint1, angle: rotation)
        }
        if tube.count >= 3 {
            drawParallelogram(context: context, left: 0, top: partHeight, right: width, bottom: 2 * partHeight, color: paint2, angle: rotation)
        }
        if tube.count >= 2 {
            drawParallelogram(context: context, left: 0, top: 2 * partHeight, right: width, bottom: 3 * partHeight, color: paint3, angle: rotation)
        }
        if tube.count >= 1 {
            drawParallelogram(context: context, left: 0, top: 3 * partHeight, right: width, bottom: height, color: paint4, angle: rotation)
        }
        
        context.restoreGState()
    }
    
    private func drawParallelogram(context: CGContext, left: CGFloat, top: CGFloat, right: CGFloat, bottom: CGFloat, color: UIColor, angle: CGFloat) {
        guard self.right else { return }
        
        let path = CGMutablePath()
        let offset = (right - left) / 2 * tan(angle * .pi / 180)
        var adjustedTop = top - abs(offset)
        if adjustedTop < 0 {
            if color == paint2 && tube.count == 4 { self.right = false }
            if color == paint3 && tube.count == 3 { self.right = false }
            if color == paint4 && tube.count == 2 { self.right = false }
            adjustedTop = 0
        }
        
        path.move(to: CGPoint(x: left, y: top + offset))
        path.addLine(to: CGPoint(x: right, y: top - offset))
        path.addLine(to: CGPoint(x: right, y: 2 * bounds.height))
        path.addLine(to: CGPoint(x: left, y: 2 * bounds.height))
        path.closeSubpath()
        
        context.addPath(path)
        context.setFillColor(color.cgColor)
        context.fillPath()
    }
}
class ColorBallSort {
    func solve(tubes: [[Int]]) -> [[Int]]? {
        var node = Node()
        node.state = tubes
        node.parent = nil
        node.from = 0
        node.to = 0
        var stack: [Node] = [node]
        var visited: Set<[[Int]]> = []
        
        while !stack.isEmpty {
            let current = stack.removeLast()
            if isGoalState(tubes: current.state) {
                return buildPath(node: current)
            }
            if visited.contains(current.state) { continue }
            visited.insert(current.state)
            
            for i in 0..<current.state.count {
                for j in 0..<current.state.count where i != j && isValidMove(tubes: current.state, from: i, to: j) {
                    let newState = moveBall(tubes: current.state, from: i, to: j)
                    if newState[j].count <= 4 {
                        var node = Node()
                        node.state = newState
                        node.parent = current
                        node.from = i
                        node.to = j
                        stack.append(Node())
                    }
                }
            }
        }
        return nil
    }
    
    private func isValidMove(tubes: [[Int]], from: Int, to: Int) -> Bool {
        if tubes[from].isEmpty { return false }
        if tubes[to].isEmpty { return true }
        return tubes[from].last == tubes[to].last
    }
    
    private func moveBall(tubes: [[Int]], from: Int, to: Int) -> [[Int]] {
        var newState = tubes.map { $0 }
        let ball = newState[from].removeLast()
        newState[to].append(ball)
        return newState
    }
    
    private func isGoalState(tubes: [[Int]]) -> Bool {
        return tubes.allSatisfy { tube in
            tube.count <= 1 || tube.allSatisfy { $0 == tube[0] }
        }
    }
    
    private func buildPath(node: Node?) -> [[Int]] {
        var path: [[Int]] = []
        var current = node
        while let node = current {
            path.append([node.from, node.to])
            current = node.parent
        }
        return path.reversed()
    }
    
    
}
class Node: Hashable {
    var state: [[Int]] = []
    weak var parent: Node?
    var from: Int = 0
    var to: Int = 0
    
    static func ==(lhs: Node, rhs: Node) -> Bool {
        return lhs.state == rhs.state
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(state)
    }
}
/*
struct R {
    struct drawable {
        static let tuduy_rotnuoc_no_move_bg = 1
        static let tuduy_rotnuoc_binh = 2
        static let tuduy_rotnuoc_nap = 3
        static let tuduy_rotnuoc_binh_2 = 4
        static let btn_back = 5
        static let icon_flashcards_stop = 6
        static let item_binh = 7
    }
}

extension Int {
    func toDrawableName() -> String? {
        switch self {
        case R.drawable.tuduy_rotnuoc_no_move_bg:
            return "tuduy_rotnuoc_no_move_bg"
        case R.drawable.tuduy_rotnuoc_binh:
            return "tuduy_rotnuoc_binh"
        case R.drawable.tuduy_rotnuoc_nap:
            return "tuduy_rotnuoc_nap"
        case R.drawable.tuduy_rotnuoc_binh_2:
            return "tuduy_rotnuoc_binh_2"
        case R.drawable.btn_back:
            return "btn_back"
        case R.drawable.icon_flashcards_stop:
            return "icon_flashcards_stop"
        case R.drawable.item_binh:
            return "item_binh"
        default:
            return nil
        }
    }
}
*/

