//
//  tuduy_list_chuechxanh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 10/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_chuechxanh: NhanBietGameFragment {
    // MARK: - Properties
    private var viewAnimation: XAMLAnimationView!
    private var gridLayout: MyGridView!
    private var frogContainer: UIView!
    private let ROW = 4
    private let COL = 5
    private var data: [[Int]] = Array(repeating: Array(repeating: 0, count: 5), count: 4)
    private let generator = PipeNetworkGenerator()
    private var viewRock: UIImageView!
    private var viewEnd: UIImageView!
    private let colorList = [
        UIColor(red: 0/255, green: 128/255, blue: 92/255, alpha: 1), // #00805C
        UIColor(red: 70/255, green: 141/255, blue: 35/255, alpha: 1), // #468D23
        UIColor(red: 127/255, green: 194/255, blue: 7/255, alpha: 1) // #7FC207
    ]
    private var randomIndexes: [Int] = [0, 1, 2, 0, 1, 2].shuffled().prefix(3).map { $0 }
    private var x: Int = -1
    private var y: Int = 0
    private var rightAnswer: [Int] = []
    private var gameIndex: Int = 0
    private var check1: UIImageView!
    private var check2: UIImageView!
    private var check3: UIImageView!
    private var destContainer: UIImageView!
    private var chuonchuonView: UIImageView!
    let image1 = Utilities.GetSVGKImage(named: "tuduy_trinhtu_sign_color1")
    let image2 = Utilities.GetSVGKImage(named: "tuduy_trinhtu_sign_color2")
    let image3 = Utilities.GetSVGKImage(named: "tuduy_trinhtu_sign_color3")
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 128/255, green: 222/255, blue: 255/255, alpha: 1) // #80DEFF
        
        let bg1 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_bg1"))
        bg1.contentMode = .scaleAspectFill
        view.addSubview(bg1)
        bg1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        addSubviewWithPercentInset(subview: itemContainer, percentInset: 10)
        itemContainer.makeViewCenterAndKeep(ratio: 9.0 / 4.0)
        
        destContainer = UIImageView()
        destContainer.image = Utilities.SVGImage(named: "tuduy_trinhtu_dich2")
        itemContainer.addSubview(destContainer)
        destContainer.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.25)
            make.width.equalTo(destContainer.snp.height).multipliedBy(369.0 / 225.0) // Ratio 369:225
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.destContainer.snapToHorizontalBias(horizontalBias: 1.0)
        }
        
        chuonchuonView = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_dich1"))
        chuonchuonView.contentMode = .scaleAspectFit
        destContainer.addSubview(chuonchuonView)
        chuonchuonView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let subContainer = UIView()
        subContainer.clipsToBounds = false
        itemContainer.addSubview(subContainer)
        subContainer.makeViewCenterAndKeep(ratio: 7.0 / 4.0)
        
        viewRock = UIImageView()
        viewRock.image = Utilities.SVGImage(named: "tuduy_trinhtu_la1")
        subContainer.addSubview(viewRock)
        viewRock.snp.makeConstraints { make in
            make.height.equalTo(subContainer).multipliedBy(0.235)
            make.width.equalTo(viewRock.snp.height) // Ratio 1:1
            make.left.equalToSuperview()
        }
        
        frogContainer = UIView()
        viewRock.addSubview(frogContainer)
        frogContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewAnimation = XAMLAnimationView()
        viewAnimation.transform = CGAffineTransformMakeRotation(.pi/2)
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "frog")!)
            viewAnimation.loadView(from: xamlData)
            let scheduler = Scheduler()
            scheduler.schedule(delay: 0.5) {
                [weak self] in
                guard let self = self else { return }
                // self.viewAnimation.startAnimation()
            }
        } catch {}
        frogContainer.addSubview(viewAnimation)
        viewAnimation.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewEnd = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_la1"))
        viewEnd.contentMode = .scaleAspectFit
        subContainer.addSubview(viewEnd)
        viewEnd.snp.makeConstraints { make in
            make.height.equalTo(subContainer).multipliedBy(0.235)
            make.width.equalTo(viewEnd.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview().multipliedBy(1.8) // Bias 1.0
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        subContainer.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 5.0 / 4.0)
        
        let bg2 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_bg2"))
        bg2.contentMode = .scaleAspectFill
        view.addSubview(bg2)
        bg2.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.width.equalTo(bg2.snp.height).multipliedBy(2688.0 / 285.0) // Ratio 2688:285
        }
        
        let bg3 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_bg3"))
        bg3.contentMode = .scaleAspectFill
        view.addSubview(bg3)
        bg3.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.width.equalTo(bg3.snp.height).multipliedBy(2688.0 / 285.0) // Ratio 2688:285
        }
        
        let signContainer = UIImageView()
        signContainer.image = Utilities.SVGImage(named: "tuduy_trinhtu_sign_bg")
        view.addSubview(signContainer)
        signContainer.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.2)
            make.width.equalTo(signContainer.snp.height).multipliedBy(402.0 / 141.0) // Ratio 402:141
            make.bottom.equalToSuperview().multipliedBy(0.98)
        }
        addActionOnLayoutSubviews {
            signContainer.snapToHorizontalBias(horizontalBias: 0.01)
        }
        
        (image1.caLayerTree.sublayers?[0].sublayers?[0] as? CAShapeLayer)?.fillColor = colorList[randomIndexes[2]].cgColor
        let viewSign1 = SVGKFastImageView(svgkImage: image1)!
        viewSign1.contentMode = .scaleAspectFit
        viewSign1.tintColor = colorList[randomIndexes[2]]
        signContainer.addSubview(viewSign1)
        viewSign1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        (image2.caLayerTree.sublayers?[0].sublayers?[0] as? CAShapeLayer)?.fillColor = colorList[randomIndexes[0]].cgColor
        let viewSign2 = SVGKFastImageView(svgkImage: image2)!
        viewSign2.contentMode = .scaleAspectFit
        viewSign2.tintColor = colorList[randomIndexes[0]]
        signContainer.addSubview(viewSign2)
        viewSign2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        (image3.caLayerTree.sublayers?[0].sublayers?[0] as? CAShapeLayer)?.fillColor = colorList[randomIndexes[1]].cgColor
        let viewSign3 = SVGKFastImageView(svgkImage: image3)!
        viewSign3.contentMode = .scaleAspectFit
        viewSign3.tintColor = colorList[randomIndexes[1]]
        signContainer.addSubview(viewSign3)
        viewSign3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        check3 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_sign_check"))
        check3.contentMode = .scaleAspectFit
        signContainer.addSubview(check3)
        check3.snp.makeConstraints { make in
            make.height.equalTo(signContainer).multipliedBy(0.8)
            make.width.equalTo(check3.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.check3.snapToHorizontalBias(horizontalBias: 0.05)
        }
        
        check1 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_sign_check"))
        check1.contentMode = .scaleAspectFit
        check1.isHidden = true
        signContainer.addSubview(check1)
        check1.snp.makeConstraints { make in
            make.height.equalTo(signContainer).multipliedBy(0.8)
            make.width.equalTo(check1.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.check1.snapToHorizontalBias(horizontalBias: 0.41)
        }
        
        check2 = UIImageView(image: Utilities.SVGImage(named: "tuduy_trinhtu_sign_check"))
        check2.contentMode = .scaleAspectFit
        check2.isHidden = true
        signContainer.addSubview(check2)
        check2.snp.makeConstraints { make in
            make.height.equalTo(signContainer).multipliedBy(0.8)
            make.width.equalTo(check2.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.check2.snapToHorizontalBias(horizontalBias: 0.78)
        }
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleEndTap(_:)))
        viewEnd.addGestureRecognizer(tapGesture)
        viewEnd.isUserInteractionEnabled = true
        
        addActionOnLayoutSubviews {
            self.viewRock.snp.makeConstraints { make in
                make.bottom.equalToSuperview().multipliedBy(CGFloat(self.generator.startPoint[1]) * 0.25 + 0.25)
            }
            self.viewEnd.snp.makeConstraints { make in
                make.bottom.equalToSuperview().multipliedBy(CGFloat(self.generator.endPoint[1]) * 0.25 + 0.25)
            }
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        createData()
        let delay = playSound(openGameSound(), "tuduy/trinhtu")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/trinhtu")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func createData() {
        while true {
            generator.generate(cols: COL, rows: ROW, steps: 7 + Int.random(in: 0..<5))
            if generator.foundAnswer && generator.pipes.count % 3 == 0 {
                break
            }
        }
        for i in 0..<ROW {
            for j in 0..<COL {
                data[i][j] = -1
            }
        }
        let pipes = generator.pipes
        var value = 0
        for pipe in pipes {
            data[pipe[1]][pipe[0]] = randomIndexes[value]
            rightAnswer.append(randomIndexes[value])
            value = (value + 1) % 3
        }
        data[generator.endPoint[1]][generator.endPoint[0]] = randomIndexes[value]
        rightAnswer.append(randomIndexes[value])
        for i in 0..<ROW {
            for j in 0..<COL {
                if data[i][j] == -1 {
                    data[i][j] = random(0, 1, 2)
                }
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        var cells: [UIView] = []
        for i in 0..<ROW {
            for j in 0..<COL {
                let cell = UIImageView()
                let colorIndex = data[i][j]
                cell.image = Utilities.SVGImage(named: colorIndex == 0 ? "tuduy_trinhtu_la1" : colorIndex == 1 ? "tuduy_trinhtu_la2" : "tuduy_trinhtu_la3")
                cell.contentMode = .scaleAspectFit
                cells.append(cell)
                
                let cellY = i
                let cellX = j
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleCellTap(_:)))
                cell.addGestureRecognizer(tapGesture)
                cell.isUserInteractionEnabled = true
                cell.tag = cellY * COL + cellX // Encode vị trí vào tag để sử dụng trong tap
            }
        }
        
        gridLayout.columns = COL
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.reloadItemViews(views: cells)
        
        x = -1
        y = generator.startPoint[1]
        destContainer.alpha = 0
        scheduler.schedule(after: 0.1) {
            self.destContainer.frame = self.viewEnd.frame
            self.destContainer.transform = .identity
            self.destContainer.alpha = 1
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleCellTap(_ gesture: UITapGestureRecognizer) {
        guard let cell = gesture.view as? UIImageView else { return }
        let tag = cell.tag
        let cellY = tag / COL
        let cellX = tag % COL
        
        if abs(cellX - x) + abs(cellY - y) == 1 {
            let rotation = cellX > x ? 90 : cellX < x ? -90 : cellY > y ? 180 : 0
            x = cellX
            y = cellY
            viewRock.superview?.bringSubviewToFront(viewRock)
            UIView.animate(withDuration: 0.1) {
                self.viewAnimation.transform = CGAffineTransform(rotationAngle: CGFloat(rotation) * .pi / 180)
            }
            //frogContainer.frame = cell.frame
            frogContainer.moveToCenter(of: cell, duration: 0.5)
            viewAnimation.startAnimation()
            
            let index = rightAnswer[gameIndex % 3]
            check1.isHidden = gameIndex % 3 != 0
            check2.isHidden = gameIndex % 3 != 1
            check3.isHidden = gameIndex % 3 != 2
            
            if data[cellY][cellX] == index {
                playSound("effect/cungchoi_frog_jump")
                gameIndex += 1
            } else {
                wrongMove(cell: cell)
            }
        }
    }
    
    @objc private func handleEndTap(_ gesture: UITapGestureRecognizer) {
        let endPoint = generator.endPoint
        if abs(endPoint[0] - x) + abs(endPoint[1] - y) == 0 {
            pauseGame()
            UIView.animate(withDuration: 0.1) {
                self.viewAnimation.transform = CGAffineTransform(rotationAngle: 90 * .pi / 180)
            }
            frogContainer.frame = viewEnd.frame
            let delay = playSound("effect/cungchoi_frog_jump")
            scheduler.schedule(after: delay) {
                self.animateCoinIfCorrect(view: self.destContainer)
                self.chuonchuonView.isHidden = true
            }
            let totalDelay = delay + playSound(delay: delay, names: ["effect/cungchoi_frog_jump_trung\(self.random(1,2))", self.getCorrectHumanSound(), self.endGameSound()])
            scheduler.schedule(delay: totalDelay) { [weak self] in
                self?.finishGame()
            }
            check1.isHidden = gameIndex % 3 != 0
            check2.isHidden = gameIndex % 3 != 1
            check3.isHidden = gameIndex % 3 != 2
        }
    }
    
    // MARK: - Helper Methods
    private func wrongMove(cell: UIView) {
        pauseGame()
        setGameWrong()
        animateCoinIfCorrect(view: cell)
        scheduler.schedule(after: 0.1) {
            let delay = self.playSound("effect/cungchoi_frog_jump_out", "effect/end game2")
            UIView.animate(withDuration: 0.5) {
                self.frogContainer.transform = CGAffineTransform(scaleX: 0, y: 0)
                self.frogContainer.alpha = 0
                cell.transform = CGAffineTransform(scaleX: 0, y: 0)
                cell.alpha = 0
            }
            self.scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func resetGame() {
        gameIndex = 0
        x = -1
        y = generator.startPoint[1]
        viewRock.transform = .identity
    }
}

