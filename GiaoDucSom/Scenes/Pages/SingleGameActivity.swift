//
//  SingleGameActivity.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/3/25.
//

import UIKit

class SingleGameActivity: TwoClickExitActivity, OnGameFragmentListener {
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        self.navigationController?.popViewController(animated: true)
    }

    func onUpdateGameProgress(_ progress: Float) {

    }

    var game: String?
    var data: String?
    var tapdoc: Bool = false
    var phonics: Bool = false

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        checkTimeoutAndRun(
            {
                [weak self] in
                guard let self = self else { return }
                self.initGame()
            },
            cancelAction: { [weak self] in
                guard let self = self else { return }
                self.navigationController?.popViewController(animated: true)
                self.dismiss(animated: true, completion: nil)
            }
        )
    }

    // MARK: - Initialization
    private func initGame() {
        if phonics {
            if game == "phonics_list_reading2" {
                game = "phonics_list_reading" // <PERSON><PERSON>y dữ liệu của game reading
            }
        }
        guard let gameClassName = game as? String else {
            print("No game class provided")
            return
        }

        do {
            /*
             let classType = NSClassFromString(moduleName! + ".toancoban_list_capbophan") as! BaseFragmentView.Type
             //let x = classType.create(frame: .zero)
             */
            // Tạo instance của game fragment dựa trên tên class
            guard
                let gameClass = NSClassFromString(
                    getAppModuleName()! + "." + gameClassName)
                    as? NhanBietGameFragment.Type
            else {
                throw NSError(
                    domain: "GameError", code: -1,
                    userInfo: [
                        NSLocalizedDescriptionKey:
                            "Game class not found: \(gameClassName)"
                    ])
            }

            gameFragment = gameClass.init()
            gameFragment?.isUserInteractionEnabled = true

            // Gán listener cho game fragment
            gameFragment?.onGameFragmentListener = self

            // Xử lý dữ liệu đặc biệt cho từng loại game fragment
            configureGameFragment()

            // Thêm gameFragment vào gameView
            if let gameView = gameView {
                //gameView.transform = CGAffineTransformMakeScale(0.5, 0.5)
                gameView.removeAllSubviews()
                gameView.addSubviewWithPercentInset(subview: gameFragment!, percentInset: 0.0)
            }
        } catch {
            print("Error initializing game: \(error.localizedDescription)")
        }
    }
    deinit {
        scheduler.clearAll()
    }

    private func configureGameFragment() {
        let data = data as? String
        //let tapdoc = extras["tapdoc"] as? Bool ?? false
        //let phonics = extras["phonics"] as? Bool ?? false
        /*
        // Xử lý dữ liệu cho các game fragment đặc biệt
        if let tangram = gameFragment as? TangramGameFragment {
            tangram.filename = data
        } else if let music = gameFragment as? MusicPlayerGameFragment {
            music.songName = data
        } else if let veTungNet = gameFragment as? VeTungNetGameFragment {
            veTungNet.data = data
        } else if let veTuDo = gameFragment as? VeTuDoGameFragment {
            veTuDo.filename = data
        } else if let toMau = gameFragment as? ToMauTheoSoGameFragment {
            toMau.data = data
        } else if let ghepMatNa = gameFragment as? GhepMatNaGameFragment {
            ghepMatNa.data = data
        } else if let coloring = gameFragment as? ColoringGameFragment {
            coloring.filename = data
        } else if let halfDrawing = gameFragment as? HalfDrawingGameFragment {
            halfDrawing.data = data
            halfDrawing.halfIndex = extras["halfIndex"] as? Int ?? 0
        }
        */
        if let ghepMatNa = gameFragment as? mythuat_list_ghepmatna {
            ghepMatNa.data = data
        }
        if let veTuDo = gameFragment as? mythuat_list_vetudo {
            veTuDo.filename = data
        }
        if let veTungNet = gameFragment as? mythuat_list_vetungnet {
            veTungNet.filename = data
        }
        if let tranhToMau = gameFragment as? mythuat_list_tranhtomau {
            tranhToMau.filename = data
        }
        if let veDoiXung = gameFragment as? taptrung_list_vedoixung {
            veDoiXung.filename = "half drawing/\(data!).svg"
        }
        if let tomau = gameFragment as? toancoban_list_tomautheoso {
            tomau.setData(data!)
        }
        if let baihat = gameFragment as? amnhac_list_baihatyeuthich {
            baihat.songName = data!
        }
        // Xử lý tapdoc
        if tapdoc, let game = gameFragment as? GameFragment {
            configureTapDoc(game)
        }
        
        // Xử lý phonics
        if phonics, let game = gameFragment as? GameFragment {
            configurePhonics(game)
        }
    }
    
    private func configureTapDoc(_ gameFragment: GameFragment) {
        let alphabets = [["a", "ă", "â"], ["b", "c"], ["d", "đ"], ["e", "ê"], ["g", "h"], ["i", "k"], ["l", "m", "n"], ["o", "ô", "ơ"], ["p", "q"], ["r", "s", "t"], ["u", "ư"], ["v", "x", "y"]]
        while true {
            let randomAlphabetIndex = Int.random(in: 0..<alphabets.count)
            var alphabet = alphabets[randomAlphabetIndex]
            let index = Int.random(in: 0...alphabet.count)
            if index < alphabet.count {
                alphabet = [alphabet[index]]
            }

            var data = MyList<Animation>()
            if alphabet.count == 1 {
                let data1 = TapDocViewController.createSoundGame(letters: alphabet)
                let data2 = TapDocViewController.createLetterGame(letters: alphabet)
                data = data.addAndReturn(data1)
                data = data.addAndReturn(data2)
            } else {
                data = data.addAndReturn(TapDocViewController.createPracticeGame(letters: alphabet))
            }

            data = data.randomOrder()
            let gameId = getGameId(gameFragment: gameFragment)
            if gameId == nil {
                var message = "chưa định nghĩa map trong get Game Id"
            }
            let filteredData = data.filter { $0.game?.rawValue == gameId }
            if !filteredData.isEmpty {
                gameFragment.data = filteredData[0]
                gameFragment.setTapdoc(true)
                print("Game data: \(filteredData[0])") // Giả lập GsonUtils.toJson
                break
            }
        }
    }
    
    private func configurePhonics(_ gameFragment: GameFragment) {
        let gameId = getGameId(gameFragment: gameFragment)
        if gameId == nil {
            fatalError("Không có dữ liệu phonics")
        }
        PhonicsManager.shared.initialize()
        var games: [Animation] = []

        let chapters = PhonicsManager.shared.getChapters()
        for chapter in chapters {
            for lesson in chapter.lessons {
                for section in lesson.intro {
                    for animation in section.animations! {
                        if animation.game?.rawValue == gameId {
                            games.append(animation)
                        }
                    }
                }
            }
        }

        games.shuffle()
        if !games.isEmpty {
            gameFragment.data = games[0]
            gameFragment.setLanguage("en")
        } else {
            fatalError("Không có dữ liệu phonics")
        }
    }
     
    
    // MARK: - Game ID Methods
    func getGameId(gameFragment: NhanBietGameFragment) -> String? {
        switch gameFragment {
           
        case is phonics_list_balloon: return "balloon"
        case is phonics_list_bee: return "bee"
        case is phonics_list_crab: return "crab"
        case is phonics_list_stickers: return "stickers"
        case is phonics_list_spelltheword: return "spell the word"
        case is phonics_list_chantbeat1: return "chant beat1"
        case is phonics_list_wordpuzzle: return "word puzzle"
        case is phonics_list_unscramble: return "unscramble"
        case is phonics_list_readtheword: return "read the word"
        case is phonics_list_memory: return "memory"
        case is phonics_list_listenandcheck: return "listen and check"
        case is phonics_list_chant: return "chant"
        case is phonics_list_readandcheck: return "read and check"
        case is phonics_list_read: return "read"
        case is phonics_list_whatletteristhis: return "what letter is this"
        case is phonics_list_basketball: return "basketball"
        case is phonics_list_letterandsound: return "letter and sound"
        case is phonics_list_samebeginningsound: return "same beginning sound"
        case is phonics_list_beginningsound: return "beginning sound"
        case is phonics_list_whatsoundisthis: return "what sound is this"
        case is phonics_list_missingletter: return "missing letter"
        case is phonics_list_canyouhear: return "can you hear"
        case is phonics_list_listenfor1: return "listen for1"
        case is phonics_list_listenfor2: return "listen for2"
        case is phonics_list_listening: return "listening"
        case is phonics_list_reading2: return "reading2"
        case is phonics_list_rhyme: return "rhyme"
        case is phonics_list_samerhyme: return "same rhyme"
        case is phonics_list_writing: return "writing"
        case is phonics_list_dragtheletter: return "drag the letter"
        case is phonics_list_flashcards: return "flashcards"
        case is phonics_list_flashcards2: return "flashcards2"
        case is phonics_list_letterintro: return "letter intro"
        case is phonics_list_mergedsound: return "merged sounds"
        case is phonics_list_photo: return "photo"
        case is phonics_list_reading: return "reading"
        case is phonics_list_sound: return "sound"
        case is phonics_list_sound2: return "sound2"
        case is phonics_list_tapviet: return "tap viet"
        case is phonics_list_tomau: return "tomau"
            
           
        default: return nil
        }
    }
   
}
