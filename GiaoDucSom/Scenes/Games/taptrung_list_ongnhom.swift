//
//  taptrung_list_ongnhom.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreImage

class taptrung_list_ongnhom: nhanbiet_list_ongnhom {
    // MARK: - Game Logic
    override func updateData() {
        let folder = FlashcardsManager.shared.getPacks()
            .filter { $0.recognize == true }
            .shuffled()
            .first!
        let items = folder.items
            .shuffled()
            .prefix(3)
            .map { $0 }
        setFolder(folder.folder)
        setItem(items.randomElement()!)
        setListItems(items)
        super.updateData()
    }
}
