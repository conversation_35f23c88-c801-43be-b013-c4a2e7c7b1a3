//
//  amnhac_list_choidantudo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 21/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class amnhac_list_choidantudo: MusicGameFragment {
    // MARK: - Properties
    private var svgPhimDanView: SVGKFastImageView!
    private var btnC1: UIView!
    private var btnD1: UIView!
    private var btnE1: UIView!
    private var btnF1: UIView!
    private var btnG1: UIView!
    private var btnA1: UIView!
    private var btnB1: UIView!
    private var btnC2: UIView!
    private var gridLayout: UIView!
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    private var viewToAnimators: [UIView: UIViewPropertyAnimator] = [:]
    private var notes: [String] = ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "c2"]
    private var musicStreamIdMap: [String: Bool] = [:]
    private var textColorMap: [String: UIColor] = [:]
    private var bgColorMap: [String: UIColor] = [:]
    private var animationValue: CGFloat = 0
    private var noteToView: [String: UIView] = [:]
    private var animator: UIViewPropertyAnimator?
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F73535"),
        UIColor.color(hex: "#FF8700"),
        UIColor.color(hex: "#FFD400"),
        UIColor.color(hex: "#05BD34"),
        UIColor.color(hex: "#00CFFF"),
        UIColor.color(hex: "#1B7CCC"),
        UIColor.color(hex: "#BF04BB"),
        UIColor.color(hex: "#F73535")
    ]
    var loadingSounds = false
    let innerContainer = UIImageView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.isUserInteractionEnabled = false
        backgroundColor = .white // #FFF
        self.isMultipleTouchEnabled = true
        let itemContainer = SVGImageView(frame: .zero)
        itemContainer.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
                
        innerContainer.isUserInteractionEnabled = true
        innerContainer.clipsToBounds = false
        itemContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 1840.0 / 1241.0)
        
        svgPhimDanView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "music_phimdan2"))
        svgPhimDanView.contentMode = .scaleToFill
        svgPhimDanView.stringTag = "svg_phimdan_view"
        innerContainer.addSubview(svgPhimDanView)
        svgPhimDanView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let buttons: [(tag: String, bias: CGFloat, width: CGFloat)] = [
            (tag: "c1", bias: 0.041, width: 0.10),
            (tag: "d1", bias: 0.173, width: 0.10),
            (tag: "e1", bias: 0.305, width: 0.10),
            (tag: "f1", bias: 0.435, width: 0.10),
            (tag: "g1", bias: 0.567, width: 0.10),
            (tag: "a1", bias: 0.698, width: 0.10),
            (tag: "b1", bias: 0.83, width: 0.10),
            (tag: "c2", bias: 0.96, width: 0.10)
        ]

        for button in buttons {
            let view = UIView()
            view.stringTag = button.tag
            view.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
            view.alpha = 0.01
            innerContainer.addSubview(view)
            view.snp.makeConstraints { make in
                make.width.equalTo(innerContainer).multipliedBy(button.width)
                make.height.equalTo(innerContainer).multipliedBy(0.41)
                make.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                view.snapToHorizontalBias(horizontalBias: button.bias)
            }
            view.isUserInteractionEnabled = true
        }
        gridLayout = UIView()
        //gridLayout.backgroundColor = .blue
        //gridLayout.clipsToBounds = false
        innerContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.97)
            make.height.equalTo(innerContainer).multipliedBy(0.56)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.58) // verticalBias=0.08
        }
        let overlayView = PassthroughImageView()
        overlayView.image = Utilities.SVGImage(named: "music_phimdan2_mask")
        innerContainer.addSubviewWithInset(subview: overlayView, inset: 0)
        innerContainer.clipsToBounds = true
        // Comment mask as per your style
        /*
        let mask = PassthroughImageView()
        mask.image = Utilities.SVGImage(named: "music_phimdan2_mask")
        innerContainer.addSubview(mask)
        mask.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        */
        
        loadPiano()
    }
    
    func loadPiano() {
        loadingSounds = true
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in notes {
                if let url = Utilities.url(soundPath: "effect/music/piano_8\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
            loadingSounds = false
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            let translationX = animationValue - (child.stringTag as? CGFloat ?? 0)
            if translationX < -gridLayout.frame.width * 2 {
                child.removeFromSuperview()
            } else {
                child.transform = CGAffineTransform(translationX: translationX, y: 0)
            }
        }
        startGame()
    }
    
    override func didMoveToSuperview() {
        super.didMoveToSuperview()
        if superview == nil {
            animator?.pauseAnimation()
        } else {
            animator?.startAnimation()
        }
    }
    var selectedNote = ""
    var touchToNote: [UITouch: String] = [:]
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        print("chạm")
        for touch in touches {
            for i in 0..<innerContainer.subviews.count {
                let view = innerContainer.subviews[i]
                if touch.placeInView(view: view) {
                    if let note = view.stringTag as? String, note != "svg_phimdan_view" {
                        touchToNote[touch] = note
                        handleButtonTouch(note: note, isDown: true)
                    }
                }
            }
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        for touch in touches {
            let note = touchToNote[touch] ?? ""
            if note != "" {
                handleButtonTouch(note: note, isDown: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    private func handleButtonTouch(note: String, isDown: Bool) {
        if isDown {
            selectedNote = note
            if let player = noteToMusicId[note] {
                player.currentTime = 0
                player.play()
                musicStreamIdMap[note] = true
            }
            
            // Placeholder for SVG path manipulation
            textColorMap[note] = .color(hex: "#B8C8D3")
            bgColorMap[note] = .color(hex: "#DCE4EA")
            svgPhimDanView.setNeedsDisplay()
            
            let view = UIView()
            gridLayout.addSubview(view)
            view.snp.makeConstraints { make in
                make.left.equalTo(gridLayout.snp.right)
                make.top.bottom.equalToSuperview()
                make.width.equalTo(gridLayout.frame.height * 2)
            }
            let bgView = UIView()
            bgView.backgroundColor = colors[notes.firstIndex(of: note) ?? 0].withAlphaComponent(0.3)
            view.addSubview(bgView)
            bgView.snp.makeConstraints { make in
                make.left.centerY.equalToSuperview()
                make.height.equalToSuperview().multipliedBy(1.05)
                make.width.equalTo(gridLayout.frame.height * 4)
            }
            // Tạo một UIViewPropertyAnimator
            let animator = UIViewPropertyAnimator(duration: 5, curve: .linear) {
                view.transform = CGAffineTransform(translationX: -self.gridLayout.frame.height * 4, y: 0)
            }

            // Xử lý completion
            animator.addCompletion { _ in
                view.removeFromSuperview()
            }

            // Bắt đầu animation
            animator.startAnimation()
            viewToAnimators[view] = animator
            
            let viewRect = UIView()
            viewRect.backgroundColor = colors[notes.firstIndex(of: note) ?? 0]
            view.addSubview(viewRect)
            viewRect.snp.makeConstraints { make in
                make.left.equalToSuperview()
                make.width.equalTo(gridLayout.frame.height * 4)
                make.height.equalToSuperview().multipliedBy(0.16)
                make.bottom.equalToSuperview().multipliedBy(
                    getNoteBias(note: note)
                )
            }
            
            let viewSquare = UIView()
            viewSquare.backgroundColor = colors[notes.firstIndex(of: note) ?? 0]
            view.addSubview(viewSquare)
            viewSquare.snp.makeConstraints { make in
                make.left.equalToSuperview()
                make.height.equalToSuperview().multipliedBy(0.16)
                make.width.equalTo(viewSquare.snp.height) // Ratio 1:1
                make.bottom.equalToSuperview().multipliedBy(
                    getNoteBias(note: note)
                )
            }
            
            let textView = HeightRatioTextView()
            textView.setHeightRatio(0.7)
            textView.text = note.prefix(1).uppercased()
            textView.font = .Freude(size: gridLayout.frame.height / 10)
            textView.textColor = .white
            textView.textAlignment = .center
            textView.backgroundColor = .clear
            viewSquare.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            noteToView[note] = view
             
        } else {
            if let player = noteToMusicId[note] {
                player.pause()
            }
            if let view = noteToView[note] {
                let bgView = view.subviews[0]
                let viewRect = view.subviews[1]
                let animator = viewToAnimators[view]!
                //animator.pauseAnimation()
                // Cập nhật width
                let tranx = -view.layer.presentation()?.transform.m41
                bgView.snp.remakeConstraints { make in
                    make.left.centerY.equalToSuperview()
                    make.height.equalToSuperview().multipliedBy(1.05)
                    make.width.equalTo(tranx!)
                }
                
                viewRect.snp.remakeConstraints { make in
                    make.left.equalToSuperview()
                    make.width.equalTo(tranx!)
                    make.height.equalToSuperview().multipliedBy(0.16)
                    make.bottom.equalToSuperview().multipliedBy(
                        getNoteBias(note: note)
                    )
                }
                // Tiếp tục animation
                //animator.startAnimation()
            }
            if musicStreamIdMap[note] == true {
                // Placeholder for volume fade-out (not implemented)
                musicStreamIdMap[note] = false
                
                // Placeholder for SVG path manipulation
                textColorMap.removeValue(forKey: note)
                bgColorMap.removeValue(forKey: note)
                svgPhimDanView.setNeedsDisplay()
                
                if let view = noteToView[note], let tag = view.stringTag as? String, let tagValue = Float(tag) {
                    let delta = abs(animationValue - CGFloat(tagValue))
                    /*
                    if delta < gridLayout.frame.width * 10 {
                        view.snp.updateConstraints { make in
                            make.width.equalTo(delta)
                        }
                    }*/
                }
            }
        }
    }
    func getNoteBias(note: String)->Double {
        return (note == "c1" ? 1.0 :
        note == "d1" ? 0.9 :
        note == "e1" ? 0.8 :
        note == "f1" ? 0.7 :
        note == "g1" ? 0.6 :
        note == "a1" ? 0.5 :
            note == "b1" ? 0.4 : 0.3) * 0.8 + 0.22
    }
    deinit {
        stopAnims()
    }
    
    func stopAnims(){
        for item in viewToAnimators.enumerated() {
            item.element.value.stopAnimation(true)
        }
        viewToAnimators.removeAll()
    }
    
    override func removeFromSuperview() {
        super.removeFromSuperview()
        stopAnims()
        //animator?.finishAnimation(at: .current)
    }
}
