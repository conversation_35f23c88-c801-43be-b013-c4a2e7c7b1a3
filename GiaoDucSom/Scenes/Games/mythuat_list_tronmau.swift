//
//  mythuat_list_tronmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/5/25.
//


import UIKit
import SnapKit
import SVGKit

class mythuat_list_tronmau: NhanBietGameFragment {
    // MARK: - Properties
    static var playedColors: [String] = []
    var list: [[String]] = [
        ["Red", "Yellow", "Orange"],
        ["<PERSON>", "Yellow", "Green"],
        ["<PERSON>", "Blue", "Purple"],
        ["<PERSON>", "Black", "Gray"],
        ["<PERSON>", "<PERSON>", "<PERSON>"],
        ["Pink", "<PERSON>", "Magenta"],
        ["Yellow", "White", "<PERSON>"],
        // Thêm các tổ hợp màu khác n<PERSON>u cần
    ]
    private let colorNames = ["Red", "Yellow", "Orange", "Blue", "Green", "Purple", "White", "Black", "Gray", "Pink", "<PERSON>e", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Turquoise", "<PERSON>", "<PERSON><PERSON>", "<PERSON>"]
    private let colors: [UIColor] = [
        UIColor(red: 1, green: 0, blue: 0, alpha: 1), // #FF0000
        UIColor(red: 1, green: 1, blue: 0, alpha: 1), // #FFFF00
        UIColor(red: 1, green: 165/255, blue: 0, alpha: 1), // #FFA500
        UIColor(red: 0, green: 0, blue: 1, alpha: 1), // #0000FF
        UIColor(red: 0, green: 128/255, blue: 0, alpha: 1), // #008000
        UIColor(red: 128/255, green: 0, blue: 128/255, alpha: 1), // #800080
        UIColor.white, // #FFFFFF
        UIColor.black, // #000000
        UIColor(red: 128/255, green: 128/255, blue: 128/255, alpha: 1), // #808080
        UIColor(red: 1, green: 192/255, blue: 203/255, alpha: 1), // #FFC0CB
        UIColor(red: 0, green: 1, blue: 0, alpha: 1), // #00FF00
        UIColor(red: 1, green: 0, blue: 1, alpha: 1), // #FF00FF
        UIColor(red: 128/255, green: 0, blue: 0, alpha: 1), // #800000
        UIColor(red: 128/255, green: 128/255, blue: 0, alpha: 1), // #808000
        UIColor(red: 192/255, green: 192/255, blue: 192/255, alpha: 1), // #C0C0C0
        UIColor(red: 0, green: 128/255, blue: 128/255, alpha: 1), // #008080
        UIColor(red: 64/255, green: 224/255, blue: 208/255, alpha: 1), // #40E0D0
        UIColor(red: 165/255, green: 42/255, blue: 42/255, alpha: 1), // #A52A2A
        UIColor(red: 0, green: 1, blue: 1, alpha: 1), // #00FFFF
        UIColor(red: 1, green: 215/255, blue: 0, alpha: 1) // #FFD700
    ]
    private var view1: UIView!
    private var view2: UIView!
    private var view3: UIView!
    private var gridLayout: MyGridView!
    private var chooseColors: [String]?
    let leftContainer = UIView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.4)
            make.top.bottom.right.equalToSuperview()
        }
        
        
        //leftContainer.backgroundColor = UIColor.red.withAlphaComponent(0.06)
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(leftContainer.snp.height).multipliedBy(1.96) // Ratio 1.96
            make.centerX.equalTo(view.snp.right).multipliedBy(0.3)
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.55)
        }
        
        view1 = UIView()
        view1.backgroundColor = .red // Sẽ được set trong updateData
        leftContainer.addSubview(view1)
        view1.snp.makeConstraints { make in
            make.height.equalTo(leftContainer).multipliedBy(0.8)
            make.width.equalTo(view1.snp.height) // Ratio 1:1
            make.left.bottom.equalToSuperview()
        }
        
        view2 = UIView()
        view2.backgroundColor = .red // Sẽ được set trong updateData
        leftContainer.addSubview(view2)
        view2.snp.makeConstraints { make in
            make.height.equalTo(leftContainer).multipliedBy(0.8)
            make.width.equalTo(view2.snp.height) // Ratio 1:1
            make.right.top.equalToSuperview()
        }        
        
        view3 = UIView()
        view3.backgroundColor = .green // Sẽ được set trong updateData
        leftContainer.addSubview(view3)
        view3.snp.makeConstraints { make in
            make.left.equalTo(view2).priority(.high)
            make.right.equalTo(view1).priority(.high)
            make.top.equalTo(view1).priority(.high)
            make.bottom.equalTo(view2).priority(.high)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        var count = 0
        while true {
            count += 1
            chooseColors = list.randomElement()
            guard let chooseColors = chooseColors else { break }
            let color = "\(chooseColors[0])+\(chooseColors[1])=\(chooseColors[2])"
            if !Self.playedColors.contains(color) {
                Self.playedColors.append(color)
                break
            }
            if count > 100 {
                Self.playedColors.removeAll()
                break
            }
        }
        
        guard let chooseColors = chooseColors else { return }
        view1.backgroundColor = nameToColor(name: chooseColors[0])
        view2.backgroundColor = nameToColor(name: chooseColors[1])
        view3.backgroundColor = nameToColor(name: chooseColors[2])
        
        let delay = playSound(openGameSound(), "mythuat/mythuat_hoa tron mau1", "topics/Colors/\(chooseColors[0].lowercased())", "mythuat/mythuat_hoa tron mau2", "topics/Colors/\(chooseColors[1].lowercased())", "mythuat/mythuat_hoa tron mau3")
        
        var answers: [[String]] = []
        while true {
            answers = list.shuffled().prefix(4).map { $0 }
            if answers.filter({ $0[2].lowercased() == chooseColors[2].lowercased() }).count == 1 {
                break
            }
        }
        
        var views: [UIView] = []
        for answer in answers {
            let view = createAnswerItem(color: answer[2])
            view.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.accessibilityIdentifier = answer[2] // Lưu màu để kiểm tra
            views.append(view)
            
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            let delay2 = self.gridLayout.showItems(startDelay: 0)
            self.scheduler.schedule(delay: delay2) { [weak self] in
                self?.startGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let chooseColors = chooseColors else { return }
            let delay = playSound("mythuat/mythuat_hoa tron mau1", "topics/Colors/\(chooseColors[0].lowercased())", "mythuat/mythuat_hoa tron mau2", "topics/Colors/\(chooseColors[1].lowercased())", "mythuat/mythuat_hoa tron mau3")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let selectedColor = view.accessibilityIdentifier,
              let chooseColors = chooseColors,
              let svgThumbnail = view.subviews.first?.subviews.first as? SVGKImageView else { return }
        
        pauseGame(stopMusic: false)
        if selectedColor.lowercased() == chooseColors[2].lowercased() {
            animateCoinIfCorrect(view: svgThumbnail)
            let delay = playSound("effect/answer_correct", getCorrectHumanSound(), "topics/Colors/\(selectedColor.lowercased())", endGameSound())
                
            self.view1.snp.remakeConstraints { make in
                make.height.equalTo(self.leftContainer).multipliedBy(0.8)
                make.width.equalTo(self.view1.snp.height) // Ratio 1:1
                make.bottom.equalToSuperview()
            }
            self.view2.snp.remakeConstraints { make in
                make.height.equalTo(self.leftContainer).multipliedBy(0.8)
                make.width.equalTo(self.view2.snp.height) // Ratio 1:1
                make.top.equalToSuperview()
            }
            self.view1.snapToHorizontalBias(horizontalBias: 0.155)
            self.view2.snapToHorizontalBias(horizontalBias: 0.845)
            UIView.animate(withDuration: 1.0, delay: 0, options: .curveLinear, animations: {
                self.layoutIfNeeded()
            }) {
                [weak self] _ in
                guard let self = self else { return }
                self.view1.snapToHorizontalBias(horizontalBias: 0.33)
                self.view2.snapToHorizontalBias(horizontalBias: 0.67)
                UIView.animate(withDuration: 1.0, delay: 0, options: .curveLinear){
                    self.layoutIfNeeded()
                }
            }
            
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", "answer_wrong\(random(1,2))", "topics/Colors/\(selectedColor.lowercased())")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func nameToColor(name: String) -> UIColor {
        for (i, colorName) in colorNames.enumerated() {
            if colorName.lowercased() == name.lowercased() {
                return colors[i]
            }
        }
        return .black
    }
    
    private func createAnswerItem(color: String) -> UIView {
        let container = UIView()
        
        let innerContainer = UIImageView()
        innerContainer.isUserInteractionEnabled = true
        innerContainer.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(innerContainer.snp.height) // Ratio 1:1
        }
        
        let svgThumbnail = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "topics/Colors/\(color.lowercased()).svg"))!
        innerContainer.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.85)
            make.height.equalTo(svgThumbnail.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return container
    }
}

