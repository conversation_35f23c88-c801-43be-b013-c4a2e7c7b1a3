//
//  tuduy_list_phanbuchunhat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 8/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_phanbuchunhat: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: UIImageView! // Gi<PERSON> lập SVGAutosizeView
    private var gridLayout: MyGridView!
    private var data: [Int] = []
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(view.snp.centerY)
        }
        svgView.transform = CGAffineTransformMakeScale(0.8, 0.8)
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        coinView = UIView()
        addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.3)
            make.height.equalTo(view).multipliedBy(0.3)
            make.center.equalToSuperview()
        }
    }
    var answers: [[Int]] = []
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        data = Array(repeating: 0, count: 12)
        while true {
            var sum = 0
            for i in 0..<12 {
                data[i] = Int.random(in: 0...1)
                sum += data[i]
            }
            if sum > 3 && sum < 8 {
                break
            }
        }
        
        var svg = Utilities.GetSVGKImage(named: "tuduy_phanbu")
        for i in 0..<self.data.count {
            if let group = svg.caLayerTree.sublayers?[i + 1] as? CALayer {
                if let paths = group.sublayers as? [CAShapeLayer] {
                    paths[0].opacity = Float(self.data[i])
                    paths[1].opacity = Float(self.data[i])
                }
            }
        }
        self.svgView.image = svg.uiImage
        
        
        var answer = self.data.map { 1 - $0 }
        answers.append(answer)
        
        for _ in 0..<3 {
            while true {
                let nearArray = self.createNearArray(a: answer)
                if !self.containArray(list: answers, array: nearArray) {
                    answers.append(nearArray)
                    break
                }
            }
        }
        
        var views: [UIView] = []
        for i in 0..<answers.count {
            let item = answers[i]
            //let clonedSVG = SVGKImage(caLayerTree: svg.caLayerTree.copy() as! CALayer)
            //svg = Utilities.GetSVGKImage(named: "tuduy_phanbu")
            for j in 0..<item.count {
                if let group = svg.caLayerTree.sublayers?[j + 1] as? CALayer {
                    if let paths = group.sublayers as? [CAShapeLayer] {
                        paths[0].opacity = Float(item[j])
                        paths[1].opacity = Float(item[j])
                    }
                }
            }
            
            let view = UIImageView(image: svg.uiImage)
            view.contentMode = .scaleAspectFit
            view.tag = i
            view.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            //AnimationUtils.setTouchEffect(view: view)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        self.gridLayout.columns = views.count
        self.gridLayout.itemRatio = 4 / 3
        self.gridLayout.itemSpacingRatio = 0.05
        self.gridLayout.insetRatio = 0.1
        self.gridLayout.reloadItemViews(views: views.shuffled())
        
        let delay = self.playSound(self.openGameSound(), "tuduy/tuduy_phan bu")
        scheduler.schedule(after: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.svgView.transform = CGAffineTransformMakeScale(0.8, 0.8)
            }
        }
        scheduler.schedule(after: delay + 0.5) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.gridLayout.alpha = 1
            }
        }
        
        let gridDelay = delay + 1.0 + self.gridLayout.showItems(startDelay: delay + 1.0)
        self.scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_phan bu")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        svgView.moveToCenter(of: self)
        gridLayout.alpha = 0
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int else { return }
        let tag = answers[index]
        let answer = data.map { 1 - $0 }
        
        pauseGame()
        if sameArray(a: tag, b: answer) {
            UIView.animate(withDuration: 0) {
                self.coinView.frame = view.frame
            }
            animateCoinIfCorrect(view: coinView)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func containArray(list: [[Int]], array: [Int]) -> Bool {
        list.contains { sameArray(a: $0, b: array) }
    }
    
    private func sameArray(a: [Int], b: [Int]) -> Bool {
        guard a.count == b.count else { return false }
        return a.elementsEqual(b)
    }
    
    private func createNearArray(a: [Int]) -> [Int] {
        var b = a
        let count = random(1, 2)
        for _ in 0..<count {
            let i = Int.random(in: 0..<a.count)
            b[i] = 1 - b[i]
        }
        return b
    }
}

