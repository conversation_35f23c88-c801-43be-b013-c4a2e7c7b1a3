//
//  taptrung_list_doitat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/3/25.
//


//
//  XepTatGameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/03/2024.
//

import UIKit
import SnapKit
import AVFAudio
let colorNames = ["Red", "Yellow", "Orange", "Blue", "Green", "Purple", "White", "Black", "Gray", "Pink"]
let colorHexs = ["#FF0000", "#FFFF00", "#FFA500", "#0000FF", "#008000", "#800080", "#FFFFFF", "#000000", "#808080", "#FFC0CB"]


class taptrung_list_timcaphinhgiongnhau: NhanBietGameFragment {

    // MARK: - Properties
    private var itemContainer: UIView! // Changed to non-optional and force unwrapped
    private var svgAnimationView: SVGKFastImageView! // Changed to non-optional and force unwrapped
    private var coinView: UIView! // Changed to non-optional and force unwrapped
    private var chooseSocks: [String] = []
    private var animationSmoke2: SVG?
    var animationSmoke2Frames: [UIImage] = []
    private var dones: [UIView] = []
    private var selectedSocks: [UIView] = []
    private var finish: Bool = false
    private var socksSVGList: [SVG] = []
    private var boomAudioPlayer: AVAudioPlayer?
    
    // MARK: - Lifecycle

    // MARK: - Setup Layout
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 215/255, green: 251/255, blue: 255/255, alpha: 1.0) // #D7FBFF

        // Item Container
        itemContainer = UIView()
        addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        itemContainer.addGestureRecognizer(tapGesture)
        itemContainer.isUserInteractionEnabled = true

        // SVG Animation View
        svgAnimationView = SVGKFastImageView(svgkImage: nil)!
        addSubview(svgAnimationView)
        svgAnimationView.snp.makeConstraints { make in
            make.centerX.centerY.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(svgAnimationView.snp.height)
        }

        // Coin View -  Assumed you have a separate coin view. Replace with actual view creation
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.size.equalTo(50) // Example size
        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self) // Assuming top menu is added in NhanBietGameFragment
        topMenuContainer?.isHidden = true // visibility="invisible"
    }
    
    @objc private func handleTap(_ gesture: UIGestureRecognizer) {
        let touchPoint = gesture.location(in: itemContainer)
        
        for subview in itemContainer.subviews.reversed() {
            guard subview.alpha >= 1, subview.isHidden == false else {
                continue
            }

            if isPixelVisible(view: subview, x: Int(touchPoint.x), y: Int(touchPoint.y)) {
                selectView(subview)
                break
            }
        }
    }

    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard view.isHidden == false else { return false }
        return view.isPixelVisible(x: x, y: y)
    }
    
    // MARK: - GameFragment Methods
    override open func createGame() {
        super.createGame()
        let svgPaths = chooseSocks.map { "topics/2D Shapes/\($0)" }
        for svgPath in svgPaths {
            socksSVGList.append(SVG(image: Utilities.GetSVGKImage(named: svgPath)))
        }
        // Use SVGManager here
        let points = PointsHelper.getPoints(size: self.chooseSocks.count * 2, container: self.itemContainer)
        
        for i in 0..<self.chooseSocks.count {
            let colorIndex = Int.random(in: 0..<colorNames.count)
            // Gán tag kiểu String bằng accessibilityIdentifier
            let tagString = "\(chooseSocks[i])_\(colorNames[colorIndex])"
            let sock = self.chooseSocks[i]
            let image = socksSVGList[i]
            changeFirstPathColor(svgImage: image.image, color: .color(hex: colorHexs[colorIndex]))
            let view1 = self.createItemView()//x: points[i * 2].x, y: points[i * 2].y)
            if let svgAutosizeView = view1.subviews.first as? UIImageView, i < socksSVGList.count {
                svgAutosizeView.image = image.uiImage
            }
            view1.stringTag = tagString
            self.itemContainer.addSubview(view1)
            view1.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.3)
                make.width.equalTo(view1.snp.height)
                make.center.equalToSuperview()
                //make.centerX.equalTo(points[i*2].x)
                //make.centerY.equalTo(points[i*2].y)
            }
            view1.transform = CGAffineTransformMakeTranslation(CGFloat(points[i*2].x) - itemContainer.frame.width/2, CGFloat(points[i*2].y) - itemContainer.frame.height/2)
            
            // Create and add second item view
            let view2 = self.createItemView()
            if let svgAutosizeView = view2.subviews.first as? UIImageView, i < socksSVGList.count {
                svgAutosizeView.image = image.uiImage
            }
            view2.stringTag = tagString
            self.itemContainer.addSubview(view2)
            view2.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.3)
                make.width.equalTo(view2.snp.height)
                make.center.equalToSuperview()
                //make.centerX.equalTo(points[i*2+1].x)
                //make.centerY.equalTo(points[i*2+1].y)
            }
            view2.transform = CGAffineTransformMakeTranslation(CGFloat(points[i*2+1].x) - itemContainer.frame.width/2, CGFloat(points[i*2+1].y) - itemContainer.frame.height/2)
        }
    }

    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        // Setup layout programmatically
        setupLayout()
    }

    override open func updateData() {
        super.updateData()
        do {
            boomAudioPlayer = try AVAudioPlayer(contentsOf: Utilities.url(soundPath: "effect/boom")!)
            boomAudioPlayer?.prepareToPlay()
        } catch {
            print("Lỗi: \(error.localizedDescription)")
        }
        var socks = StorageManager.manager.list(path: "topics/2D Shapes/")
        socks.shuffle() // randomorder()
        socks = Array(socks.prefix(8)) // take(8)

        chooseSocks = socks
        chooseSocks.append(contentsOf: socks)

        chooseSocks.shuffle() // randomorder()
        chooseSocks = Array(chooseSocks.prefix(7 + Int.random(in: 0..<3))) // take(7 + random.nextInt(3))

        let svg = SVG(image: Utilities.GetSVGKImage(named: "animations/animation_smoke2.svg"))
        self.animationSmoke2 = svg
        for i in 0..<svg.layers.count {
            animationSmoke2Frames.append(svg.clone(pathIndex: i).uiImage)
        }
                
        let delay = playSound(openGameSound(), getLanguage() + "/taptrung/cap hinh")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }

    // MARK: - Event Handling

    private func selectView(_ view: UIView) {
        guard !dones.contains(view) else { return }

        if selectedSocks.contains(view) {
            selectedSocks.removeAll { $0 == view }
            UIView.animate(withDuration: 0.2) {
                // Lấy translation hiện tại từ transform
                let translationX = view.transform.tx
                let translationY = view.transform.ty

                // Tạo transform mới với scale = 1 và giữ nguyên translation
                view.transform = CGAffineTransform(translationX: translationX, y: translationY)
            }
            return
        }

        view.superview?.bringSubviewToFront(view)
        UIView.animate(withDuration: 0.2) {
            view.transform = CGAffineTransformConcat(CGAffineTransform(scaleX: 1.5, y: 1.5), view.transform)
        }
        selectedSocks.append(view)

        if selectedSocks.count == 2 {
            if selectedSocks[0].stringTag == selectedSocks[1].stringTag {
                dones.append(contentsOf: selectedSocks)
                pauseGame(stopMusic: false)
                playSound("effect/word puzzle drop")
                let tag = selectedSocks[0].stringTag!
                let items = tag.split(separator: "_")
                let shapeName = String(items[0]).replacingOccurrences(of: ".svg", with: "").lowercased()
                let colorName = String(items[1]).lowercased()
                let delay = playSound("effect/answer_correct",
                                      "topics/2D Shapes/\(shapeName)",
                                      "topics/Colors/\(colorName)")
                let leftSock = selectedSocks[0].frame.origin.x < selectedSocks[1].frame.origin.x ? selectedSocks[0] : selectedSocks[1]
                let rightSock = selectedSocks[0].frame.origin.x >= selectedSocks[1].frame.origin.x ? selectedSocks[0] : selectedSocks[1]

                scheduler.schedule(delay: 0.2) {
                    [weak self] in
                    guard let self = self else { return }
                    //leftSock.moveToCenter(of: self.itemContainer, duration: 0.5)
                    //rightSock.moveToCenter(of: self.itemContainer, duration: 0.5)
                    UIView.animate(withDuration: 0.5) {
                        leftSock.transform = CGAffineTransform(translationX: -self.itemContainer.bounds.height / 7, y: 0)
                        rightSock.transform = CGAffineTransform(translationX: self.itemContainer.bounds.height / 7, y: 0)
                        (leftSock.subviews.first)?.transform = .identity
                        (rightSock.subviews.first)?.transform = .identity
                        
                    }
                    self.scheduler.schedule(delay: 0.5) {
                        [weak self] in
                        guard let self = self else { return }
                        self.playSound("effect/slide1")
                        UIView.animate(withDuration: 0.5, animations: {
                            leftSock.transform = .identity
                            leftSock.alpha = 0
                        }, completion: { _ in
                            leftSock.isHidden = true
                        })
                        UIView.animate(withDuration: 0.5, animations: {
                            rightSock.transform = .identity
                            rightSock.alpha = 0
                        }, completion: {
                            [weak self] _ in
                            guard let self = self else { return }
                            rightSock.isHidden = true
                            boomAudioPlayer?.play()
                            startAnimation()
                        })
                        self.scheduler.schedule(delay: 1) { [weak self] in
                            self?.checkFinish()
                        }
                        self.resumeGame(startMusic: false)
                    }
                }
                selectedSocks.removeAll()
            } else {
                setGameWrong()
                playSound("effect/slide2")

                let view1 = selectedSocks[0]
                let view2 = selectedSocks[1]
                scheduler.schedule(delay: 0.3) {
                    UIView.animate(withDuration: 0.2) {
                        view1.transform = CGAffineTransform(translationX: view1.transform.tx, y: view1.transform.ty)
                        view2.transform = CGAffineTransform(translationX: view2.transform.tx, y: view2.transform.ty)
                    }
                }
                selectedSocks.removeAll()
                pauseGame(stopMusic: false)
                scheduler.schedule(delay: 0.5) { [weak self] in
                    self?.resumeGame(startMusic: false)
                }
            }
        } else {
            playSound("effect/cungchoi_pick\(random(1, 2))")
        }
    }
    var displayLink: CADisplayLink?
        var frameIndex = 0
        let frameDuration = 0.05 // 20 FPS
    func startAnimation() {
        frameIndex = 0    
        // Dừng displayLink cũ nếu có
        displayLink?.invalidate()
        displayLink = CADisplayLink(target: self, selector: #selector(updateFrame))
        displayLink?.preferredFramesPerSecond = Int(1 / frameDuration)
        displayLink?.add(to: .main, forMode: .common)
    }

    @objc func updateFrame() {
        if frameIndex >= animationSmoke2Frames.count {
            displayLink?.invalidate()
            displayLink = nil
            UIView.animate(withDuration: frameDuration) {
                self.svgAnimationView.alpha = 0
            }
            return
        }

        // Cập nhật frame trên main thread
        DispatchQueue.main.async {
            [weak self] in
            guard let self = self else { return }
            if(self.frameIndex < self.animationSmoke2Frames.count){
                let frame = animationSmoke2?.image.caLayerTree.sublayers?[self.frameIndex]
                animationSmoke2?.image.caLayerTree.sublayers?.forEach { layer in
                    layer.isHidden = layer != frame
                }
                self.svgAnimationView.image = animationSmoke2?.image
                self.svgAnimationView.alpha = 1
            }
        }
        frameIndex += 1
    }
    private func checkFinish() {
        guard !finish else { return }

        // All done
        for view in itemContainer.subviews {
            if !view.isHidden && view.alpha > 0.8 {
                return
            }
        }

        finish = true
        pauseGame()
        animateCoinIfCorrect(view: coinView)

        var delay: TimeInterval = 2.0
        delay += playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())

        scheduler.schedule(delay: delay) { [weak self] in
            self?.finishGame()
        }
    }

    // MARK: - Item View Creation
    private func createItemView() -> UIView {
        let view = UIView()
        view.translatesAutoresizingMaskIntoConstraints = false

        let imageView = UIImageView()
        imageView.translatesAutoresizingMaskIntoConstraints = false
        imageView.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: -(.pi)...(.pi))) //random.nextInt(360) - 180

        view.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.snp.makeConstraints { make in
            make.height.equalTo(view.snp.width)
        }

        return view
    }

    // MARK: - Override Methods
    override open func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(getLanguage() + "/taptrung/cap hinh")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// Tạo key để lưu trữ associated object
private var tagKey: UInt8 = 2

extension UIView {
    // Getter và Setter cho tag kiểu String
    var stringTag: String? {
        get {
            return objc_getAssociatedObject(self, &tagKey) as? String
        }
        set {
            objc_setAssociatedObject(self, &tagKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    // Tìm view đầu tiên có stringTag khớp
    func viewWithStringTag(_ tag: String) -> UIView? {
        for subview in subviews {
            if subview.stringTag == tag {
                return subview
            }
            // Đệ quy tìm trong các subview
            if let foundView = subview.viewWithStringTag(tag) {
                return foundView
            }
        }
        return nil
    }
}


import SVGKit

func changeFirstPathColor(svgImage: SVGKImage, color: UIColor) {
    // Lấy root layer của SVG
    guard let layer = svgImage.caLayerTree else {
        print("Không thể lấy layer từ SVGKImage")
        return
    }
    
    // Hàm đệ quy để tìm CAShapeLayer đầu tiên
    func findFirstShapeLayer(in layer: CALayer) -> CAShapeLayer? {
        if let shapeLayer = layer as? CAShapeLayer {
            return shapeLayer
        }
        
        for sublayer in layer.sublayers ?? [] {
            if let shapeLayer = findFirstShapeLayer(in: sublayer) {
                return shapeLayer
            }
        }
        return nil
    }
    
    // Tìm path đầu tiên (CAShapeLayer)
    if let firstShapeLayer = findFirstShapeLayer(in: layer) {
        // Thay đổi màu fill
        firstShapeLayer.fillColor = color.cgColor
        // Nếu muốn thay đổi màu viền (stroke), dùng:
        // firstShapeLayer.strokeColor = color.cgColor
    } else {
        print("Không tìm thấy CAShapeLayer trong SVG")
    }
}

func changePathColor(svgImage: SVGKImage, pathIndex: Int, color: UIColor) {
    // Lấy root layer của SVG
    guard let layer = svgImage.layer(withIdentifier: nil) else {
        print("Không thể lấy layer từ SVGKImage")
        return
    }
    
    // Hàm đệ quy để tìm tất cả CAShapeLayer
    func findAllShapeLayers(in layer: CALayer, shapeLayers: inout [CAShapeLayer]) {
        if let shapeLayer = layer as? CAShapeLayer {
            shapeLayers.append(shapeLayer)
        }
        for sublayer in layer.sublayers ?? [] {
            findAllShapeLayers(in: sublayer, shapeLayers: &shapeLayers)
        }
    }
    
    // Lưu tất cả CAShapeLayer vào một mảng
    var shapeLayers: [CAShapeLayer] = []
    findAllShapeLayers(in: layer, shapeLayers: &shapeLayers)
    
    // Kiểm tra và thay đổi màu của path thứ k
    if pathIndex >= 0 && pathIndex < shapeLayers.count {
        let targetShapeLayer = shapeLayers[pathIndex]
        targetShapeLayer.fillColor = color.cgColor
        // Nếu muốn thay đổi màu viền:
        // targetShapeLayer.strokeColor = color.cgColor
    } else {
        print("Path thứ \(pathIndex) không tồn tại. Tổng số path: \(shapeLayers.count)")
    }
}
