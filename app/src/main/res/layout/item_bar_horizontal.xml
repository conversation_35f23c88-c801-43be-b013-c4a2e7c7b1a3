<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="#0f00"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1937.6:259.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/item_background"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/nhanbiet_btn_bar_horizontal1"
            android:scaleX="1.24"
            android:scaleY="1.24" />

        <TextView
            android:id="@+id/textview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="a"
            android:textColor="#68C1FF"
            android:visibility="invisible"
            app:autoSizeMaxTextSize="1500dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.0"
            app:layout_constraintWidth_percent="0.8" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0dp"
        android:clipChildren="true"
        android:clipToPadding="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1937.6:259.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/top_line"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            app:layout_constraintHeight_percent="0.12"></View>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:clipChildren="true"
            android:clipToPadding="true"
            app:layout_constraintHeight_percent="1"
            app:layout_constraintTop_toBottomOf="@id/top_line">

            <com.caverock.androidsvg.SVGImageView
                android:id="@+id/svg_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#ff0"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>