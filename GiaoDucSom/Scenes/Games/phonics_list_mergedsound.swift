//
//  phonics_list_mergedsound.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_mergedsound: GameFragment {
    private var values : [String?] = []
    var xamlContainer = UIView()
    var xaml: XAMLAnimationView?
    var leftContainer = UIView()
    var leftButton = SVGButton(SVGIcon: "bot btn replay")
    var rightContainer = UIView()
    var rightButton = SVGButton(SVGIcon: "bot btn next")
    var xamlData: XAMLModel.UserControl?
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#849BFE")
        addSubview(xamlContainer)
        xamlContainer.makeViewCenterAndKeep(ratio: 1)
        do {
            xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: game.xaml!)!)
        } catch {}
        addSubview(leftContainer)
        addSubview(rightContainer)
        let leftBackground = SVGImageView(SVGName: "bot bg left")
        let rightBackground = SVGImageView(SVGName: "bot bg right")
        leftContainer.addSubview(leftBackground)
        rightContainer.addSubview(rightBackground)
        leftContainer.addSubview(leftButton)
        rightContainer.addSubview(rightButton)
        leftContainer.snp.makeConstraints{ make in
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        rightContainer.snp.makeConstraints{ make in
            make.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        leftBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.1)
            make.width.equalTo(leftBackground.snp.height).multipliedBy(628.0/401.0)
        }
        rightBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.85)
            make.width.equalTo(rightBackground.snp.height).multipliedBy(628.0/401.0)
        }
        leftButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        leftButton.addTarget(self, action: #selector(replay), for: .touchUpInside)
        rightButton.addTarget(self, action: #selector(loadNext), for: .touchUpInside)
    }
    override func createGame() {
        super.createGame()
        leftContainer.alpha = 0.01
        rightContainer.alpha = 0.01
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.leftContainer.alpha = 1
            self.rightContainer.alpha = 1
        })
        hideMenu()
        restart()
    }
    
    func restart(){
        xamlContainer.removeAllSubviews()
        xaml = XAMLAnimationView()
        xamlContainer.addSubviewWithInset(subview: xaml!, inset: 0)
        xaml!.loadView(from: xamlData!)
        loadText(key: "box1", value: game.box1)
        loadText(key: "box2", value: game.box2)
        loadText(key: "box3", value: game.box3)
        loadText(key: "box4", value: game.box4)
        start()
    }
    func start(){
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        values = game.values == nil ? [game.box1, game.box2, game.box3, game.box4] as! [String?]: (game.values?.compactMap { $0.value as? String })!
        values.append(nil)
        for i in 0..<values.count {
            let I = i
            let value = values[i]
            if value != nil {
                let lastSb = values[i+1] == nil
                scheduler.schedule(delay: delay, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    self.playSound(name: lastSb ? "effects/slide" : "effects/slide1")
                    self.xaml!.startAnimationStoryboard(with: "sb\(I+1)")
                })
                if lastSb {
                    delay += 0.8
                } else {
                    delay += 0.5
                }
                let d = self.playSound(name: value!, delay: delay)
                delay += max(d + 1.0,2.0)
            } else {
                scheduler.schedule(delay: delay, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    stop()
                })
                break
            }
        }
    }
    func loadText(key:String, value:String?){
        let box = xaml!.findGridviewByName(name: key)
        if let box = box {
            box.backgroundColor = .clear
            box.removeAllSubviews()
            let label = AutosizeLabel()
            label.text = value
            label.textColor = .color(hex: "#849BFE")
            box.addSubviewWithInset(subview: label, inset: 0)
        }
    }
    func stop(){
        startGame()
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.playSound(name: "effects/slide1")
            self.showMenu()
        })
    }
    @objc func loadNext(){
        finishGame()
    }
    @objc func replay(){
        hideMenu()
        restart()
    }

    func hideMenu(){
        leftContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -leftContainer.bounds.width * 1.2 - 20.0]
        let animValues2: [Double] = [0, rightContainer.bounds.width * 1.2 + 20.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showMenu(){
        startGame()
        leftContainer.transform = CGAffineTransformMakeTranslation(-leftContainer.bounds.width, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(rightContainer.bounds.width, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [-leftContainer.bounds.width, 0]
        let animValues2: [Double] = [rightContainer.bounds.width, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
}
