<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EEE3D0"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.33">


        <com.kidsup.giaoducsom.view.MathNumpad
            android:id="@+id/numpad"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.05" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.67">
        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/svg_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1510.4:1155.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="20dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1510.4:1155.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>