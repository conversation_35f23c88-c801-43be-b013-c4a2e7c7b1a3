<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="1dp"
        android:background="@drawable/option_bg_white_shadow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="32:34"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:id="@+id/stroke_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.0"
            android:background="#f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/text_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="1dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:lines="1"
            android:text=""
            android:textAlignment="center"
            android:textColor="#849BFE"
            app:autoSizeMaxTextSize="1000dp"
            app:autoSizeMinTextSize="10dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35"
            app:layout_constraintWidth_percent="0.7" />

        <com.caverock.androidsvg.SVGImageView
            android:id="@+id/svg_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.7" />
    </androidx.constraintlayout.widget.ConstraintLayout>


    <View
        android:id="@+id/snap_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#4f00"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.01"></View>
</androidx.constraintlayout.widget.ConstraintLayout>