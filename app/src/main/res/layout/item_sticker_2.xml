<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/drag_bg2"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="56:62"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/text_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:lines="1"
            android:text="Alligator"
            android:textColor="#1497E0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.46"
            app:layout_constraintWidth_percent="0.6" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>