<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.94"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:visibility="invisible"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_puzzle_calculation_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="392.2:321.4"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_puzzle_calculation_4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="392.2:321.4"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>