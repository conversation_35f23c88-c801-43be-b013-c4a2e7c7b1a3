<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false"
    android:orientation="vertical">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:clipChildren="false"
        android:clipToPadding="false">

        <FrameLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/background_hint"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.LetterDashView
                android:id="@+id/dashed_line"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.PathView
                android:id="@+id/background"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.PathView
                android:id="@+id/true_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.PathView
                android:id="@+id/drawed_path_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <FrameLayout
                android:id="@+id/bottom_arrow_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:alpha="0.001" />

            <com.kidsup.giaoducsom.view.PathView
                android:id="@+id/hint_path_view_2"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/hint_svg_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <com.kidsup.giaoducsom.view.WritingOlyView
                android:id="@+id/write_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

            <FrameLayout
                android:id="@+id/hand_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:translationY="0dp">

                <com.kidsup.giaoducsom.view.SVGAutosizeView
                    android:id="@+id/hand_svg"
                    android:layout_width="150dp"
                    android:layout_height="142dp"
                    android:background="#0f00"
                    android:translationY="0dp" />
            </FrameLayout>
        </FrameLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</FrameLayout>