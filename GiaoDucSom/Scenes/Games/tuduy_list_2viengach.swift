//
//  tuduy_list_2viengach.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 8/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_2viengach: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var gridLayout2: UIView!
    private var coinView: UIView!
    private var answerList : [String] = []
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 248/255, green: 239/255, blue: 222/255, alpha: 1) // #F8EFDE
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 238/255, green: 227/255, blue: 208/255, alpha: 1) // #EEE3D0
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.6)
        }
        
        gridLayout2 = UIView()
        addSubview(gridLayout2)
        gridLayout2.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.4)
        }
        
        let orangeBrick1 = UIImageView(image: Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        orangeBrick1.contentMode = .scaleAspectFit
        gridLayout2.addSubview(orangeBrick1)
        orangeBrick1.snp.makeConstraints { make in
            make.height.equalTo(gridLayout2).multipliedBy(0.3)
            make.width.equalTo(orangeBrick1.snp.height).multipliedBy(2.0) // Ratio 2:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            orangeBrick1.snapToHorizontalBias(horizontalBias: 0.3)
        }
        
        let orangeBrick2 = UIImageView(image: Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        orangeBrick2.contentMode = .scaleAspectFit
        gridLayout2.addSubview(orangeBrick2)
        orangeBrick2.snp.makeConstraints { make in
            make.height.equalTo(gridLayout2).multipliedBy(0.3)
            make.width.equalTo(orangeBrick2.snp.height).multipliedBy(2.0) // Ratio 2:1
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            orangeBrick2.snapToHorizontalBias(horizontalBias: 0.7)
        }
        
        coinView = UIView()
        addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.3)
            make.height.equalTo(coinView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let files = StorageManager.manager.list(path: "2viengach").filter { $0.hasSuffix(".svg") }
        let answer = files.filter { $0.contains("false") }.shuffled().first ?? ""
        answerList = files.filter { $0.contains("true") }.shuffled().prefix(4).map { $0 }
        answerList.insert(answer, at: 0)
        
        var views: [UIView] = []
        for i in 0..<answerList.count {
            let view = UIImageView()
            view.image = Utilities.SVGImage(named: "2viengach/\(answerList[i])")
            view.contentMode = .scaleAspectFit
            view.tag = i
            view.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            //AnimationUtils.setTouchEffect(view: view)
            views.append(view)
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
        }
        
        gridLayout.columns = 5
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views.shuffled())
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_2 vien gach")
        scheduler.schedule(after: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.gridLayout2.transform = .identity
            }
            UIView.animate(withDuration: 0.5, delay: 0.5) {
                self.gridLayout.alpha = 1
            }
        }
        
        let gridDelay = delay + 1.0 + gridLayout.showItems(startDelay: delay + 1.0)
        scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_2 vien gach")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        gridLayout2.transform = CGAffineTransform(translationX: 0, y: 0)
        gridLayout.alpha = 0
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int else { return }
        let selectedAnswer = answerList[index]
        
        pauseGame()
        if selectedAnswer == answerList[0] { // Answer luôn ở index 0
            UIView.animate(withDuration: 0) {
                self.coinView.frame = view.frame
            }
            animateCoinIfCorrect(view: coinView)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

