//
//  toancoban_list_sotrongguong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sotrongguong: NhanBietGameFragment {
    // MARK: - Properties
    private var textName: UILabel!
    private var gridLayout: MyGridView!
    private var number: Int = 0
    private var numbers: [Int] = []
    private var svgView: UIView!
    private let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1)
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.5)
        }
        rightBg.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        svgView = UIView()
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
            make.width.equalTo(svgView.snp.height) // Ratio 1:1
            make.top.equalToSuperview().inset(view.frame.height * 0.05) // Margin top 5% height
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "toan_sotrongguong_bg"))
        bgImage.contentMode = .scaleAspectFit
        svgView.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        textName = AutosizeLabel()
        textName.textColor = .white
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.002 // 1dp / 500dp
        //textName.maximumContentSize = CGSize(width: 500, height: 500)
        textName.numberOfLines = 1
        textName.transform = CGAffineTransform(scaleX: -1, y: 1) // Mirror horizontally
        svgView.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.13)
            make.width.equalTo(textName.snp.height).multipliedBy(2) // Ratio 2:1
            make.centerX.equalToSuperview().multipliedBy(0.9) // Bias 0.45 -> centerX offset
            make.centerY.equalToSuperview().multipliedBy(1.02) // Bias 0.51 -> centerY offset
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        number = 1000 + Int.random(in: 0..<9000) // 1000 to 9999
        textName.text = String(number)
        
        let number2 = Int(String(String(number).reversed())) ?? 0
        let number3 = (number % 10) * 1000 + (number / 10)
        let number4 = (number3 % 10) * 1000 + (number3 / 10)
        var number5 = 1000 + Int.random(in: 0..<9000)
        var number6 = 1000 + Int.random(in: 0..<9000)
        
        let numberStr = String(number)
        if numberStr.contains("2") {
            number5 = Int(numberStr.replacingOccurrences(of: "2", with: "5")) ?? number5
        } else if numberStr.contains("5") {
            number5 = Int(numberStr.replacingOccurrences(of: "5", with: "2")) ?? number5
        }
        if numberStr.contains("1") {
            number6 = Int(numberStr.replacingOccurrences(of: "1", with: "7")) ?? number6
        } else if numberStr.contains("7") {
            number6 = Int(numberStr.replacingOccurrences(of: "7", with: "1")) ?? number6
        }
        
        numbers = []
        if number2 != number { numbers.append(number2) }
        if number3 != number && number3 != number2 { numbers.append(number3) }
        if number4 != number && number4 != number2 && number4 != number3 { numbers.append(number4) }
        if number5 != number && number5 != number2 && number5 != number3 && number5 != number4 { numbers.append(number5) }
        if number6 != number && number6 != number2 && number6 != number3 && number6 != number4 && number6 != number5 { numbers.append(number6) }
        
        numbers = numbers.shuffled().take(count: 3);
        numbers.append(number)
        numbers = numbers.shuffled()
        
        var views: [UIView] = []
        for i in 0..<numbers.count {
            let value = numbers[i]
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_so trong guong")
        svgView.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.svgView.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.svgView.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so trong guong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid Item
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == number
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound(finishCorrect1Sounds())
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
