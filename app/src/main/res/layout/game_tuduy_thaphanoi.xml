<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#fff"
    android:clipChildren="false"
    android:clipToPadding="false">


    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/bg_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01000000"
            android:src="@drawable/tuduy_thaphanoi_bg"
            app:layout_constraintBottom_toBottomOf="@id/bg_2"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.25" />

        <ImageView
            android:id="@+id/bg_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01000000"
            android:src="@drawable/tuduy_thaphanoi_bg"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9"
            app:layout_constraintWidth_percent="0.25" />

        <ImageView
            android:id="@+id/bg_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#01000000"
            android:src="@drawable/tuduy_thaphanoi_bg"
            app:layout_constraintBottom_toBottomOf="@id/bg_2"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.95"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.25" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/disc_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:src="@drawable/tuduy_thaphanoi_bg"
            app:layout_constraintBottom_toBottomOf="@id/bg_1"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="@id/bg_1"
            app:layout_constraintRight_toRightOf="@id/bg_1"
            app:layout_constraintTop_toTopOf="@id/bg_1"
            app:layout_constraintWidth_percent="0.25">

            <ImageView
                android:id="@+id/disc_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_green"
                android:tag="3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.812"
                app:layout_constraintWidth_percent="0.9" />

            <ImageView
                android:id="@+id/disc_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_blue"
                android:tag="2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.812"
                app:layout_constraintWidth_percent="0.9" />

            <ImageView
                android:id="@+id/disc_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_red"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.812"
                app:layout_constraintWidth_percent="0.9" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout

            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_thaphanoi_bg"
            app:layout_constraintBottom_toBottomOf="@id/bg_3"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="@id/bg_3"
            app:layout_constraintRight_toRightOf="@id/bg_3"
            app:layout_constraintTop_toTopOf="@id/bg_3"
            app:layout_constraintWidth_percent="0.25">

            <ImageView
                android:id="@+id/disc_4"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_green2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.812"
                app:layout_constraintWidth_percent="0.9" />

            <ImageView
                android:id="@+id/disc_5"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_blue2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.49"
                app:layout_constraintWidth_percent="0.9" />

            <ImageView
                android:id="@+id/disc6"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_thaphanoi_red2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="520:140"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.165"
                app:layout_constraintWidth_percent="0.9" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/top_view_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4"
            app:layout_constraintLeft_toLeftOf="@id/bg_1"
            app:layout_constraintRight_toRightOf="@id/bg_1"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2" />

        <View
            android:id="@+id/top_view_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            app:layout_constraintDimensionRatio="4"
            app:layout_constraintLeft_toLeftOf="@id/bg_2"
            app:layout_constraintRight_toRightOf="@id/bg_2"
            app:layout_constraintTop_toTopOf="@id/top_view_1" />

        <View
            android:id="@+id/top_view_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            app:layout_constraintDimensionRatio="4"
            app:layout_constraintLeft_toLeftOf="@id/bg_3"
            app:layout_constraintRight_toRightOf="@id/bg_3"
            app:layout_constraintTop_toTopOf="@id/top_view_1" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.3"/>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>