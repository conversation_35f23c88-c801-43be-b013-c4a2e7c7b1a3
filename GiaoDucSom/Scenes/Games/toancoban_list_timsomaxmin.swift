//
//  toancoban_list_timsomaxmin.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_timsomaxmin: NhanBietGameFragment {
    // MARK: - Properties
    private var lonnhat: Bool = false
    private var gridLayout: MyGridView!
    private var numbers: [Int] = []
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        lonnhat = Bool.random()
        numbers = Utils.generatePermutation(10).shuffled().take(count:5)
        let maxmin = lonnhat ? numbers.max() ?? 0 : numbers.min() ?? 0
        var views: [UIView] = []
        
        for (i, number) in numbers.enumerated() {
            let view = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "numbers/\(number).svg"))
            view?.contentMode = .scaleAspectFit
            view?.transform = CGAffineTransform(scaleX: 1.3, y: 1.3) // contentScale 1.3f
            view?.isUserInteractionEnabled = true
            views.append(view ?? UIView())
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view?.addGestureRecognizer(tapGesture)
            view?.tag = number // Lưu number vào tag để kiểm tra
        }
        
        gridLayout.columns = views.count
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.0
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), lonnhat ? "toan/toan_tim so max min pham vi 10_max" : "toan/toan_tim so max min pham vi 10_min")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(lonnhat ? "toan/toan_tim so max min pham vi 10_max" : "toan/toan_tim so max min pham vi 10_min")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let number = view.tag
        let maxmin = lonnhat ? numbers.max() ?? 0 : numbers.min() ?? 0
        let correct = number == maxmin
        
        pauseGame()
        if correct {
            let delay = playSound("topics/Numbers/\(maxmin)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.animateCoinIfCorrect(view: self?.coinView ?? UIView())
            }
            let finishDelay = delay + playSound(delay: delay, names: finishCorrect1Sounds())
            scheduler.schedule(delay: finishDelay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("topics/Numbers/\(number)")
            let resumeDelay = delay + playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: resumeDelay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}
