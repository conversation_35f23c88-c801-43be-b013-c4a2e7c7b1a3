//
//  amnhac_list_noinotnhac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/4/25.
//


import UIKit
import SnapKit
import SVGKit

class amnhac_list_noinotnhac: MusicGameFragment {
    // MARK: - Properties
    private let maxItem = 2
    private var selectedViews: [UIView] = []
    private var doneCount: Int = 0
    private var viewCount: Int = 0
    private var itemContainer: UIView!
    private var svgAnimationView: SVGKFastImageView!
    private var animationSmoke2: SVGKImage?
    private var zIndex: Int = 100
    private let notes: [Character] = ["C", "D", "E", "F", "G", "A", "B"]
    private let notes2: [Int] = [0, 1, 2, 3, 4, 5, 6]
    private let ids: [Int] = [
        R14.drawable.ic_8c1,
        R14.drawable.ic_8d1,
        R14.drawable.ic_8e1,
        R14.drawable.ic_8f1,
        R14.drawable.ic_8g1,
        R14.drawable.ic_8a1,
        R14.drawable.ic_8b1
    ]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_cloud"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 3.0 / 2.0)
        
        let positions: [(horizontalBias: CGFloat, verticalBias: CGFloat)] = [
            (0.08, 0.12),
            (0.08, 0.88),
            (0.5, 0.12),
            (0.5, 0.88),
            (0.92, 0.12),
            (0.92, 0.88)
        ]
        
        for (index, position) in positions.enumerated() {
            let item = createItemGhepCap(note: "B", imageRes: ids[6], showText: false)
            item.tag = index
            itemContainer.addSubview(item)
            item.snp.makeConstraints { make in
                make.height.equalTo(itemContainer).multipliedBy(0.4)
                make.height.equalTo(item.snp.width) // Ratio 1:1
                //make.centerX.equalToSuperview().multipliedBy(position.horizontalBias * 2)
                //make.centerY.equalToSuperview().multipliedBy(position.verticalBias * 2)
            }
            addActionOnLayoutSubviews {
                item.snapToHorizontalBias(horizontalBias: position.horizontalBias)
                item.snapToVerticalBias(verticalBias: position.verticalBias)
            }
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            item.addGestureRecognizer(tapGesture)
            item.isUserInteractionEnabled = true
        }
        animationSmoke2 = Utilities.GetSVGKImage(named: "animations/animation_smoke2.svg")
        svgAnimationView = SVGKFastImageView(svgkImage: animationSmoke2)
        svgAnimationView.alpha = 0.001
        svgAnimationView.stringTag = "svg_animation_view"
        svgAnimationView.contentMode = .scaleAspectFit
        svgAnimationView.isUserInteractionEnabled = false
        view.addSubview(svgAnimationView)
        svgAnimationView.snp.makeConstraints { make in
            make.width.equalTo(svgAnimationView.snp.height) // Ratio 1:1
            make.height.equalTo(view).multipliedBy(0.5)
            make.center.equalToSuperview()
        }
        
        viewCount = positions.count
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        var items = notes2.shuffled().prefix(3).map { $0 }
        items += items.map { $0 + 10 }
        items.shuffle()
        
        for i in 0..<itemContainer.subviews.count {
            let item = itemContainer.subviews[i]
            item.tag = items[i] % 10
            let value = items[i]
            guard let viewText = item.viewWithStringTag("view_text") as? UILabel,
                  let viewImage = item.viewWithStringTag("view_image"),
                  let imageNote = viewImage.viewWithStringTag("image_note") as? UIImageView else { continue }
            
            if value >= 10 {
                viewImage.isHidden = true
                viewText.isHidden = false
                viewText.text = String(notes[value - 10])
            } else {
                viewImage.isHidden = false
                viewText.isHidden = true
                imageNote.image = Utilities.SVGImage(named: "music/note/\(ids[value].toDrawableNameNNN()!).svg").withRenderingMode(.alwaysTemplate)
                imageNote.tintColor = .color(hex: "#74B6FF")
            }
        }
        let delay = playSound("music/noinotnhac")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/noinotnhac")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let item = gesture.view else { return }
        
        item.layer.zPosition = CGFloat(zIndex)
        zIndex += 1
        
        if selectedViews.contains(item) {
            selectedViews.removeAll { $0 == item }
            UIView.animate(withDuration: 0.2) {
                item.transform = .identity
            }
            playSound("effect/answer_correct")
            return
        }
        
        if selectedViews.count >= maxItem {
            playSound("effect/answer_wrong")
            return
        }
        
        selectedViews.append(item)
        UIView.animate(withDuration: 0.2) {
            item.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }
        
        let tag = item.tag
        guard let viewImage = item.viewWithStringTag("view_image") else { return }
        let isName = viewImage.isHidden
        if isName {
            playSound("topics/Music Notes/\(notes[tag].lowercased())")
        } else {
            playSound("effect/music/piano_2\(notes[tag].lowercased())1")
        }
        
        if selectedViews.count >= 2 {
            checkSelectedItems()
        }
    }
    
    // MARK: - Helper Methods
    private func checkSelectedItems() {
        let selectedNotes = selectedViews.map { $0.tag }.uniqued()
        
        if selectedNotes.count > 1 {
            setGameWrong()
            playSound(name: "effect/answer_wrong", delay: 0.3)
            for view in selectedViews {
                animateDifferenceCards(view: view)
            }
            selectedViews.removeAll()
        } else {
            if selectedViews.count == maxItem {
                let sortedViews = selectedViews.sorted { $0.frame.minX < $1.frame.minX }
                let leftSock = sortedViews[0]
                let rightSock = sortedViews[1]
                
                scheduler.schedule(after: 0.2) { [weak self] in
                    guard let self = self else { return }
                    let pointLeft = leftSock.distanceFromCenterToCenter(to: self.svgAnimationView)
                    let pointRight = rightSock.distanceFromCenterToCenter(to: self.svgAnimationView)
                                        
                    UIView.animate(withDuration: 0.5) {
                        leftSock.transform = CGAffineTransform(translationX: -pointLeft.x - self.itemContainer.frame.height / 4, y: -pointLeft.y)
                        rightSock.transform = CGAffineTransform(translationX: -pointRight.x + self.itemContainer.frame.height / 4, y: -pointRight.y)
                    }
                    
                    self.scheduler.schedule(after: 0.5) { [weak self] in
                        guard let self = self else { return }
                        UIView.animate(withDuration: 0.3) {
                            leftSock.alpha = 0
                            rightSock.alpha = 0
                            leftSock.transform = CGAffineTransform(translationX: -pointLeft.x, y: -pointLeft.y)
                            rightSock.transform = CGAffineTransform(translationX: -pointRight.x, y: -pointRight.y)
                            
                        } completion: { _ in
                            leftSock.isHidden = true
                            rightSock.isHidden = true
                            self.playSound("effect/boom")
                            self.svgAnimationView.alpha = 1
                            if let groups = self.animationSmoke2?.caLayerTree.sublayers {
                                for (i, group) in groups.enumerated() {
                                    self.scheduler.schedule(delay: TimeInterval(i) * 0.05) { [weak self] in
                                        guard let self = self else { return }
                                        for k in 0..<groups.count {
                                            groups[k].isHidden = k != i
                                        }
                                        self.svgAnimationView.image = self.animationSmoke2
                                    }
                                }
                                self.scheduler.schedule(delay: TimeInterval(groups.count) * 0.05) { [weak self] in
                                    guard let self = self else { return }
                                    self.svgAnimationView.alpha = 0
                                }
                            }
                        }
                        
                        self.doneCount += self.maxItem
                        if self.viewCount == self.doneCount {
                            self.pauseGame()
                            self.scheduler.schedule(after: 1.5) { [weak self] in
                                self?.animateCoinIfCorrect(view: self?.itemContainer ?? UIView())
                            }
                            self.scheduler.schedule(after: 4.0) { [weak self] in
                                self?.finishGame()
                            }
                        } else {
                            self.resumeGame()
                        }
                    }
                }
                
                for view in selectedViews {
                    self.animateMatchingCards(view: view)
                }
                selectedViews.removeAll()
            }
        }
    }
    
    private func animateDifferenceCards(view: UIView) {
        UIView.animate(withDuration: 0.2, delay: 0.9) {
            view.transform = .identity
        }
    }
    
    private func animateMatchingCards(view: UIView) {
        view.isUserInteractionEnabled = false
        UIView.animate(withDuration: 0.3, delay: 0.1) {
            view.transform = .identity
        }
    }
    
    private func createItemGhepCap(note: Character, imageRes: Int, showText: Bool) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_card1")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let bgWhite = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_card2"))
        container.addSubview(bgWhite)
        bgWhite.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let innerContainer = UIView()
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height).multipliedBy(1.5) // Ratio 1.5:1
            make.width.equalTo(container).multipliedBy(0.95)
            make.center.equalToSuperview()
        }
        
        let viewImage = UIView()
        viewImage.stringTag = "view_image"
        viewImage.isHidden = showText
        innerContainer.addSubview(viewImage)
        viewImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        viewImage.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(viewImage).multipliedBy(0.94)
            make.height.equalTo(viewImage)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg"))
        barline.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg"))
        viewImage.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let imageNote = UIImageView(image: Utilities.SVGImage(named: "music/note/\(imageRes.toDrawableNameNNN()!).svg").withRenderingMode(.alwaysTemplate))
        imageNote.stringTag = "image_note"
        imageNote.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.width.equalTo(imageNote.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(1.3) // Bias 0.65
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg"))
        clef.contentMode = .scaleAspectFill
        clef.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(0.4) // Bias 0.15
            make.centerY.equalToSuperview()
        }
        
        let viewText = HeightRatioTextView()
        viewText.setHeightRatio(0.7)
        viewText.stringTag = "view_text"
        viewText.text = String(note)
        viewText.font = .Freude(size: 20)
        viewText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewText.textAlignment = .center
        viewText.backgroundColor = .clear
        viewText.isHidden = !showText
        container.addSubview(viewText)
        viewText.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(container).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        return view
    }
}


struct R14 {
    struct drawable {
        static let nhanbiet_bg_cloud = 1
        static let nhanbiet_bg_card1 = 2
        static let nhanbiet_bg_card2 = 3
        static let music_bg = 4
        static let ic_barline = 5
        static let endline = 6
        static let ic_8c1 = 7
        static let ic_8d1 = 8
        static let ic_8e1 = 9
        static let ic_8f1 = 10
        static let ic_8g1 = 11
        static let ic_8a1 = 12
        static let ic_8b1 = 13
        static let music_clef = 14
        static let music_noinotnhac = 15
        static let music_item_ghepcap = 16
    }
}

extension Int {
    func toDrawableNameNNN() -> String? {
        switch self {
        case R14.drawable.nhanbiet_bg_cloud:
            return "nhanbiet_bg_cloud"
        case R14.drawable.nhanbiet_bg_card1:
            return "nhanbiet_bg_card1"
        case R14.drawable.nhanbiet_bg_card2:
            return "nhanbiet_bg_card2"
        case R14.drawable.music_bg:
            return "music_bg"
        case R14.drawable.ic_barline:
            return "ic_barline"
        case R14.drawable.endline:
            return "endline"
        case R14.drawable.ic_8c1:
            return "8c1"
        case R14.drawable.ic_8d1:
            return "8d1"
        case R14.drawable.ic_8e1:
            return "8e1"
        case R14.drawable.ic_8f1:
            return "8f1"
        case R14.drawable.ic_8g1:
            return "8g1"
        case R14.drawable.ic_8a1:
            return "8a1"
        case R14.drawable.ic_8b1:
            return "8b1"
        case R14.drawable.music_clef:
            return "music_clef"
        case R14.drawable.music_noinotnhac:
            return "music_noinotnhac"
        case R14.drawable.music_item_ghepcap:
            return "music_item_ghepcap"
        default:
            return "empty"
        }
    }
}



extension Sequence where Element: Equatable {
    func uniqued() -> [Element] {
        var result: [Element] = []
        for element in self {
            if !result.contains(element) {
                result.append(element)
            }
        }
        return result
    }
}
