//
//  amnhac_list_dongvat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/4/25.
//


import UIKit
import SnapKit
import SVGKit

class amnhac_list_dongvat: MusicGameFragment {
    // MARK: - Properties
    private let folder = "animals"
    private var gridLayout: MyGridView!
    private var meIndex: Int = 0
    private var btnReplay: KUButton!
    private var items: [String] = []
    private var itemContainer: UIView!
    private var imageLeft: UIImageView!
    private var imageRight: UIImageView!
    private var svgView: UIImageView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 218/255, green: 253/255, blue: 182/255, alpha: 1) // #DAFDB6
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "music_amsac_bg5"))
        bgImage.contentMode = .scaleToFill
        itemContainer.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let innerContainer = UIView()
        itemContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 2688.0 / 1236.0) // Ratio 2688:1236
        
        svgView = UIImageView()
        svgView.accessibilityIdentifier = "svg_view"
        svgView.contentMode = .scaleAspectFit
        svgView.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
        innerContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.height.equalTo(innerContainer).multipliedBy(0.5)
            make.height.equalTo(svgView.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        imageLeft = UIImageView(image: Utilities.SVGImage(named: "music_amsac_bg6"))
        imageLeft.contentMode = .scaleAspectFill
        innerContainer.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageRight = UIImageView(image: Utilities.SVGImage(named: "music_amsac_bg7"))
        imageRight.contentMode = .scaleAspectFill
        innerContainer.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let buttonContainer = UIView()
        buttonContainer.clipsToBounds = false
        view.addSubview(buttonContainer)
        buttonContainer.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        btnReplay = KUButton()
        btnReplay.alpha = 0.01
        btnReplay.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
        buttonContainer.addSubview(btnReplay)
        btnReplay.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(buttonContainer).multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        btnReplay.addTarget(self, action: #selector(handleReplayTap(_:)), for: .touchUpInside)
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
    }
    
    override func createGame() {
        super.createGame()
        
        itemContainer.transform = CGAffineTransform(translationX: 0, y: itemContainer.frame.height / 4)
        
        items = StorageManager.manager.list(path: "music/\(folder)")
            .shuffled()
            .prefix(4)
            .map { $0 }
        meIndex = Int.random(in: 0..<items.count)
        
        var delay = playSound("music/am sac dong vat", "effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let item = createItemWhiteCorner(svgPath: "music/\(folder)/\(items[i])")
            item.backgroundColor = .clear
            item.tag = i
            item.alpha = 0.01
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            item.addGestureRecognizer(tapGesture)
            item.isUserInteractionEnabled = true
            views.append(item)
            if i == meIndex {
                svgView.image = Utilities.SVGImage(named: "music/\(folder)/\(items[i])")
            }
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.03
        gridLayout.insetRatio = 0.05
        gridLayout.columns = 4
        gridLayout.reloadItemViews(views: views)
        
        scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.itemContainer.transform = .identity
            }
            self.gridLayout.showItems(startDelay: 0.4)
        }
        
        delay += 2.0
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/am sac dong vat", "effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleReplayTap(_ sender: UIButton) {
        pauseGame()
        let delay = playSound("effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.resumeGame()
        }
    }
    
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        pauseGame()
        let index = view.tag
        if index == meIndex {
            var delay = 0.3
            delay += playSound(delay: delay, names: [answerCorrect1EffectSound()])
            for (j, subview) in gridLayout.subviews.enumerated() {
                UIView.animate(withDuration: 0.8, delay: 0.5 + TimeInterval(j) * 0.5, usingSpringWithDamping: 0.5, initialSpringVelocity: 0.5, options: []) {
                    subview.alpha = 0.01
                    subview.transform = CGAffineTransformMakeScale(0.1, 0.1)
                }
            }
            
            scheduler.schedule(after: delay) { [weak self] in
                guard let self = self else { return }
                let duration = 2.0
                UIView.animate(withDuration: duration) {
                    self.imageLeft.transform = CGAffineTransform(translationX: -self.imageLeft.frame.width / 4, y: 0)
                    self.imageRight.transform = CGAffineTransform(translationX: self.imageRight.frame.width / 4, y: 0)
                    self.svgView.transform = .identity
                    self.itemContainer.transform = CGAffineTransform(translationX: 0, y: self.itemContainer.frame.height / 4).scaledBy(x: 1.5, y: 1.5)
                } completion: { _ in
                    self.scheduler.schedule(delay: 2.0) { [weak self] in
                        self?.finishGame()
                    }
                }
            }
            
            delay += playSound(delay: delay, names: [ "music/\(folder)/\(items[index].replacingOccurrences(of: ".svg", with: ""))", getCorrectHumanSound(), endGameSound()])
            animateCoinIfCorrect(view: view)
            
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemWhiteCorner(svgPath: String) -> UIView {
        let view = KUButton()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let svgImage = UIImageView()
        svgImage.accessibilityIdentifier = "svg_image"
        svgImage.image = Utilities.SVGImage(named: svgPath)
        svgImage.contentMode = .scaleAspectFit
        container.addSubview(svgImage)
        svgImage.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        
        return view
    }
}

