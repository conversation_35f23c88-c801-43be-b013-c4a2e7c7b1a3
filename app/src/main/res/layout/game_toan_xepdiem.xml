<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EEE3D0"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <include
            android:id="@+id/main_item"
            layout="@layout/item_xepdiem"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <include
            android:id="@+id/sub_item"
            layout="@layout/item_xepdiem"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_margin="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintHorizontal_bias="0.15"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.821"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.841"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:rotation="90"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.821"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:rotation="90"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.821"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.92" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:rotation="90"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.821"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.84" />

        <ImageView
            android:id="@+id/item_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.01"
            android:background="#0f00"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="120:516.9"
            app:layout_constraintHeight_percent="0.45"
            app:layout_constraintHorizontal_bias="0.83"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.01"
            android:background="#0f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="516.9:120"
            app:layout_constraintHeight_percent="0.11"
            app:layout_constraintHorizontal_bias="0.91"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.76">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="63.5:516.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.12" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.3" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>