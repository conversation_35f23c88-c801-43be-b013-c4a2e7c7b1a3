//
//  BaseSubjectViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/3/25.
//

import UIKit
import SnapKit

class BaseSubjectViewController: BaseViewController {
    // MARK: - Properties
    // Màu mặc định cho title và background
    open var titleColor: UIColor = UIColor(hex: "#74B6FF") {
        didSet {
            titleLabel.textColor = titleColor
            backButton.setImage(Utilities.btnBackWithColor(color: titleColor), for: .normal)
        }
    }
    open var backgroundColor: UIColor = UIColor(hex: "#E9F7FF") {
        didSet {
            view.backgroundColor = backgroundColor
            gradientView.setupGradient(color: backgroundColor)
        }
    }
    
    public let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumLineSpacing = 10
        layout.minimumInteritemSpacing = 10
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        return collectionView
    }()
    
    let backButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(Utilities.SVGImage(named: "btn_back"), for: .normal)
        button.contentMode = .scaleAspectFit
        return button
    }()
    
    private let titleLabel: HeightRatioTextView = {
        let label = HeightRatioTextView()
        label.font = .Freude(size: 20)
        label.setHeightRatio(0.6)
        return label
    }()
    
    private let premiumButton: UIButton = {
        let button = UIButton(type: .custom)
        button.setImage(UIImage(named: "btn_premium"), for: .normal)
        button.contentMode = .scaleAspectFit
        return button
    }()
    
    private let gradientView: FadeGradientView = {
        let view = FadeGradientView()
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }
    
    // MARK: - Setup UI
    private func setupUI() {
        // Sử dụng các thuộc tính màu
        view.backgroundColor = backgroundColor
        titleLabel.textColor = titleColor
        gradientView.setupGradient(color: backgroundColor)
        
        // Add subviews
        view.addSubview(backButton)
        view.addSubview(titleLabel)
        view.addSubview(premiumButton)
        view.addSubview(collectionView)
        view.addSubview(gradientView)
        
        // Layout with SnapKit
        backButton.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(UIScreen.main.bounds.height / 20)
            make.left.equalToSuperview().offset(UIScreen.main.bounds.height / 20)
            make.width.equalTo(backButton.snp.height)
            make.height.equalToSuperview().multipliedBy(0.1)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.top.bottom.equalTo(backButton)
        }
        
        titleLabel.snpLeftTop(ratio: 3.6)
        
        premiumButton.snp.makeConstraints { make in
            make.centerY.equalTo(backButton)
            make.trailing.equalToSuperview().offset(-20)
            make.width.equalTo(100)
            make.height.equalTo(30)
        }
        
        gradientView.snp.makeConstraints { make in
            make.top.equalTo(backButton.snp.bottom).offset(10)
            make.leading.trailing.equalToSuperview()
            make.height.equalTo(30)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(gradientView.snp.top)
            if hasSafeArea() {
                make.left.equalTo(view.safeAreaLayoutGuide)
                make.right.equalTo(view.safeAreaLayoutGuide)
            } else {
                make.left.right.equalToSuperview().inset(UIScreen.main.bounds.height / 20)
            }
            make.bottom.equalTo(view.safeAreaLayoutGuide)
        }
        // Thêm khoảng trống 50 điểm ở phía trên nội dung của collectionView
        collectionView.contentInset = UIEdgeInsets(top: 20, left: 0, bottom: 0, right: 0)
        
        // Điều chỉnh scroll indicator để không bị ảnh hưởng bởi contentInset
        collectionView.scrollIndicatorInsets = UIEdgeInsets(top: 20, left: 0, bottom: 0, right: 0)
        
        // Actions
        backButton.addTarget(self, action: #selector(backTapped), for: .touchUpInside)
        premiumButton.addTarget(self, action: #selector(premiumTapped), for: .touchUpInside)
    }
    
    // MARK: - Actions
    @objc private func backTapped() {
        navigationController?.popViewController(animated: true)
        // self.dismiss(animated: true)
    }
    
    @objc private func premiumTapped() {
        // Để trống, để các class con override nếu cần
    }
    
    // MARK: - Public Methods
    public func setTitle(_ title: String) {
        titleLabel.text = title
    }
    
    public func hasSafeArea() -> Bool {
        if let window = UIApplication.shared.windows.first {
            let safeAreaInsets = window.safeAreaInsets
            return safeAreaInsets.top > 0 || safeAreaInsets.left > 0 || safeAreaInsets.right > 0
        }
        return false
    }
}

// MARK: - UIColor Extension
extension UIColor {
    convenience init(hex: String) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")
        
        var rgb: UInt64 = 0
        Scanner(string: hexSanitized).scanHexInt64(&rgb)
        
        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0
        
        self.init(red: red, green: green, blue: blue, alpha: 1.0)
    }
}
