//
//  phonics_list_crab.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 30/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate
import AnyCodable

class phonics_list_crab: GameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var correctCount: Int = 0
    private var bias: CGFloat = 0
    private var lastestAnswer: String?
    private var values: [String] = []
    private var step: Int = 0
    private var answer: String = ""
    private var chooseValues: [String] = []
    private var coinView: UIView!
    private let crabSvg = Utilities.SVGImage(named: "crab") // G<PERSON><PERSON> định SVG chung, không có frame animation
    private lazy var viewToAnim = [UIView: Interpolate]()
    private var xamlData: XAMLModel.UserControl?
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .clear
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        do {
            xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "crab")!)
        } catch {
            print(error)
        }
        let background = UIImageView(image: Utilities.SVGImage(named: "english_crab_bg"))
        background.contentMode = .scaleToFill // fitXY
        view.addSubview(background)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Giả định hai view nền trong suốt (#7f00) không cần thiết, vì không có trong phonics_list_bee
        let progressBar = UIView() // Giả định include view_progress_bar
        view.addSubview(progressBar)
        progressBar.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalTo(progressBar.snp.width).multipliedBy(1.0 / 20.0) // Ratio 20:1
            make.top.equalToSuperview().offset(view.frame.height * 0.05) // Vertical bias 0.05
            make.centerX.equalToSuperview()
            make.height.lessThanOrEqualTo(20)
        }
        
        coinView = UIView() // Giả định include item_coin_view
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        values = data?.value?.split(separator: ",").map { String($0) } ?? []
        data?.values = values.map { AnyCodable($0) }
        // crabData không được sử dụng vì XamlAnimationView không áp dụng trong Swift
    }
    
    override func createGame() {
        super.createGame()
        
        let texts = parseIntroText() ?? []
        var delay: TimeInterval = playSound(openGameSound())
        for text in texts {
            delay += playSound(text, delay: delay)
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
            self?.loadNextStep()
            self?.born()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let texts = parseIntroText() ?? []
            var delay: TimeInterval = 0.5
            for text in texts {
                delay += playSound(text, delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
           
    
    // MARK: - Helper Methods
    private func loadNextStep() {
        setProgress(index: step, count: values.count)
        if step >= 1 {
            var delay: TimeInterval = 0.5
            delay += playSound(getCorrectHumanSound(), delay: delay)
            delay += playSound(endGameSound(), delay: delay)
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
            return
        }
        
        answer = values[step]
        correctCount = 0
        step += 1
        chooseValues = [values[0]]
        scheduler.schedule(after: 1) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    private func getNextBias() -> CGFloat {
        var count = 0
        while true {
            count += 1
            let nextBias = 0.2 + CGFloat.random(in: 0...0.8)
            if abs(nextBias - bias) > 0.3 || count > 20 {
                bias = nextBias
                return bias
            }
        }
    }
    
    private func getNextAnswer() -> String {
        if values.count <= 2 {
            return values.randomElement()!
        }
        while true {
            let answer = values.randomElement()!
            if answer != lastestAnswer {
                lastestAnswer = answer
                return answer
            }
        }
    }
    
    func born(){
        if gameState == .finished {return}
        let apple = createView()
        let text = getNextAnswer()
        if let label = apple.viewWithTag(1) as? AutosizeLabel {
            label.text = text
            
        }
        if let label = apple.viewWithTag(5) as? AutosizeLabel {
            label.text = text.replacingOccurrences(of: "1", with: "")
            if text.count == 1 {
                label.transform = CGAffineTransformMakeScale(2, 2)
            } else {
                label.transform = CGAffineTransformMakeScale(1, 1)
            }
        }
        itemContainer.addSubview(apple)
        let width = itemContainer.bounds.width
        
        
        apple.snp.makeConstraints { make in
            make.bottom.equalToSuperview().multipliedBy(Double.random(in: 0.2...0.9))
            make.height.equalToSuperview().multipliedBy(0.3)
            make.width.equalTo(apple.snp.height).multipliedBy(1)
            make.left.equalToSuperview()
        }
        apple.transform = CGAffineTransformMakeTranslation(-0.35*width, 0)
        let animValues: [Double] = [-0.35*width,width]
        let timeChange = Interpolate(values: animValues,
        apply: { [weak self] (value) in
            apple.transform = CGAffineTransformMakeTranslation(value, 0)
        })
        timeChange.animate(1, duration: 8){
            [weak self] in
            guard let self = self else { return }
            self.scheduler.schedule(delay: 2) {
                [weak self] in
                guard let self = self else { return }
                apple.removeFromSuperview()
            }
        }
        viewToAnim[apple] = timeChange
        
        scheduler.schedule(delay: 1.5 + Double.random(in: 0..<0.2)) {
            [weak self] in
            guard let self = self else { return }
            self.born()
        }
        
        apple.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
    }
   
    @objc func itemClick(_ sender: UIControl){
        if let label = sender.viewWithTag(1) as? AutosizeLabel {
            let text = label.text
            let xaml = sender.viewWithTag(2) as? XAMLAnimationView
            viewToAnim[sender]?.stopAnimation()
            viewToAnim.removeValue(forKey: sender)
            playSound(name: text?.lowercased() ?? "", delay: 0.3)
            if text?.lowercased() == answer.lowercased() {
                playSound(answerCorrect1EffectSound())
                xaml?.repeatCount = 0
                xaml?.startAnimationStoryboard(with: "sb_true")
                
                correctCount += 1
                let totalQuestions = 6
                let stepCount = 1
                let totalProgress = Double(step - 1) / Double(stepCount)
                let subProgress = Double(correctCount) / Double(totalQuestions) / Double(stepCount)
                let progress = totalProgress + subProgress
                
                scheduler.schedule(delay: 0.5, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    setGameProgress(progress)
                })
                                
                if correctCount >= 6 {
                    pauseGame()
                    //animateCoinIfCorrect(view: sender)
                    animateCoinIfCorrect(view: coinView)
                    scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.loadNextStep()
                    }
                }
            } else {
                playSound("en/english phonics/effects/balloon_fail\(random(1,2,3))")
                setGameWrong()
                xaml?.repeatCount = 0
                xaml?.startAnimationStoryboard(with: "sb_fail")
            }
            sender.isUserInteractionEnabled = false
        }
    }
    
    func createView()->UIControl {
        let view = UIControl()
        let xamlView = XAMLAnimationView()
        xamlView.loadView(from: xamlData!)
        xamlView.tag = 2
        view.addSubviewWithInset(subview: xamlView, inset: 0)
        let label = AutosizeLabel()
        label.tag = 5
        label.font = .UTMAvo(size: 17)
        label.textColor = .color(hex: "#630000")
        label.text = "a"
        
        let box = xamlView.findGridviewByName(name: "box1_2")
        box?.backgroundColor = .clear
        box?.subviews.forEach{ $0.isHidden = true }
        box?.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.equalToSuperview()
            make.top.equalToSuperview().offset(Utilities.isIPad ? -8 : 0)
        }
        
        xamlView.repeatCount = 5
        xamlView.startAnimationStoryboard(with: "sb")
        xamlView.isUserInteractionEnabled = false
        let label2 = AutosizeLabel()
        label2.tag = 1
        label2.alpha = 0.0001
        view.addSubview(label2)
        return view
    }
    
    override func getSkills() -> [GameSkill] {
        return [.GameReading]
    }
    
    override func getScore() -> Float {
        return 6.0 / (6.0 + Float(incorrect))
    }
    
    override func BackgroundMusicName() -> String {
        return "effect/crab_bg"
    }
    
    deinit {
        viewToAnim.removeAll()
    }
}
