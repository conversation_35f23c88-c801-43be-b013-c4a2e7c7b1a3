//
//  phonics_list_stickers.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate
import AnyCodable

class phonics_list_stickers: GameFragment {
    // MARK: - Properties
    private var gridTopLayout: MyGridView!
    private var gridBottomLayout: MyGridView!
    private var values: [String] = []
    private var draggedView: UIControl?
    private var dragOffset: CGPoint = .zero
    var selectedView : UIView?
    var zPosition : CGFloat = 0
    private var doneViews: [UIView] = []
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF") // Background #7CD2FF
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        gridTopLayout = MyGridView()
        gridTopLayout.itemRatio = 56.0/62.0
        gridTopLayout.itemSpacingRatio = 0.01
        gridTopLayout.insetRatio = 0.01
        view.addSubview(gridTopLayout)
        gridTopLayout.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        gridBottomLayout = MyGridView()
        gridBottomLayout.itemRatio = 56.0/62.0
        gridBottomLayout.itemSpacingRatio = 0.01
        gridBottomLayout.insetRatio = 0.01
        view.addSubview(gridBottomLayout)
        gridBottomLayout.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        values = (data!.values?.compactMap { $0.value as? String})!
        setupGridLayouts()
    }
    
    override func createGame() {
        super.createGame()
        
        let texts = parseIntroText() ?? []
        var delay: TimeInterval = playSound(openGameSound())
        for text in texts {
            delay += playSound(text, delay: delay)
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let texts = parseIntroText() ?? []
            var delay: TimeInterval = 0.5
            for text in texts {
                delay += playSound(text, delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func setupGridLayouts() {
        
        let values1 = values.shuffled()
        var views: [UIView] = []
        for value in values1 {
            let view = createTopStickerView(value: value)
            views.append(view)
        }
        gridTopLayout.columns = views.count
        gridTopLayout.reloadItemViews(views: views)
        
        views = []
        let values2 = values.shuffled()
        for value in values2 {
            let view = createBottomStickerView(value: value)
            views.append(view)
        }
        gridBottomLayout.columns = views.count
        gridBottomLayout.reloadItemViews(views: views)
    }
    
    private func createTopStickerView(value: String) -> UIControl {
        let view = UIControl()
        let background = UIImageView()
        background.tag = 2
        background.image = Utilities.SVGImage(named: "drag_bg2")
        view.addSubviewWithInset(subview: background, inset: 0)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(background.snp.height).multipliedBy(56.0 / 62.0)
        }
        
        let textName = AutosizeLabel()
        textName.tag = 1
        textName.text = value
        textName.textColor = .color(hex: "#1497E0")
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.1
        background.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.2)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.92) // vertical_bias=0.46
        }
        
        view.stringTag = value
        return view
    }
    
    private func createBottomStickerView(value: String) -> UIControl {
        let view = UIControl()
        let background = SVGImageView(frame: .zero)
        background.tag = 2
        background.image = Utilities.SVGImage(named: "drag_bg1")
        view.addSubviewWithInset(subview: background, inset: 0)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(background.snp.height).multipliedBy(56.0 / 62.0)
        }
        
        let roundedView = RoundedView()
        background.addSubview(roundedView)
        roundedView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(roundedView.snp.width)
        }
        
        let svgView = SVGImageView(frame: .zero)
        //svgView.backgroundColor = .red
        //svgView.tag = 2
        svgView.contentMode = .scaleAspectFit
        let index = data?.values!.firstIndex(where: { ($0.value as? String) == value }) ?? 0
        let path = tapdoc || isVocab() ? data?.paths![index] ?? "" : "english phonics/\(data?.level ?? "")/\(value).svg"
        svgView.image = Utilities.SVGImage(named: path)
        roundedView.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(1.2)
            make.height.equalTo(svgView.snp.width)
        }
        
        view.stringTag = value
        view.isUserInteractionEnabled = true
        return view
    }
        
    
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            for view in gridBottomLayout.subviews {
                if sender.placeInView(view: view) && !doneViews.contains(view){
                    selectedView = view
                    zPosition += 1
                    view.layer.zPosition = zPosition
                    UIView.animate(withDuration: 0.3) {
                        view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                    }
                    let background = view.viewWithTag(2) as! SVGImageView
                    background.SVGName = "drag bg3"
                    playSound("effect/cungchoi_pick\(random(1,2))")
                    return;
                }
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if state == .ended || state == .cancelled || state == .failed {
            var foundTarget = false
            for view in gridTopLayout.subviews {
                if sender.placeInView(view: view){
                    foundTarget = true
                    guard let selectedView = selectedView else { return }
                    if view.stringTag == selectedView.stringTag {
                        doneViews.append(selectedView)
                        var delay = playSound("effect/word puzzle drop")
                        delay += playSound(delay: delay, names: [
                            (tapdoc || isVocab()) ? self.data?.sounds![self.data?.values!.firstIndex(of: AnyCodable(view.stringTag)) ?? 0] ?? view.stringTag! : view.stringTag!
                        ])
                        //selectedView.animateCoin(answer: true)
                        var p = view.convert( CGPointMake(view.bounds.width / 2, view.bounds.height / 2), to: selectedView)
                        p.x -= selectedView.bounds.width / 2
                        p.y -= selectedView.bounds.height / 2
                        UIView.animate(withDuration: 0.1) {
                            var tran = selectedView.transform
                            tran.tx += p.x
                            tran.ty += p.y
                            selectedView.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
                            view.alpha = 0
                        } completion: { [weak self] done in
                            guard let self = self else { return }
                            
                        }
                        var finished = true
                        for view in self.gridTopLayout.subviews {
                            if view.alpha == 1 {
                                finished = false
                            }
                        }
                        if finished {
                            pauseGame()
                        }
                        scheduler.schedule(delay: delay, execute: {
                            [weak self] in
                            guard let self = self else { return }
                            if finished {
                                pauseGame(stopMusic: false)
                                animateCoinIfCorrect(view: view)
                                delay += playSound(delay: delay, names: ["effect/answer_end",
                                                                         getCorrectHumanSound(), endGameSound()])
                                scheduler.schedule(after: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            } else {
                                self.resumeGame()
                            }
                        })
                    } else {
                        pauseGame()
                        self.playSound(name: "en/english phonics/effects/balloon_fail\(Int.random(in: 1...3))", delay: 0)
                        //playFailSound()
                        UIView.animate(withDuration: 0.3) {
                            self.selectedView?.transform = .identity
                        }
                        scheduler.schedule(delay: 0.5) {
                            [weak self] in
                            guard let self = self else { return }
                            self.resumeGame()
                        }
                        let background = selectedView.viewWithTag(2) as! SVGImageView
                        background.SVGName = "drag bg1"
                        //selectedView.animateCoin(answer: false)
                        incorrect += 1
                        setGameWrong()
                    }
                    break
                } else {
                    self.playSound("effect/slide2")                    
                }
            }
            if !foundTarget {
                //playFailSound()
                UIView.animate(withDuration: 0.3) {
                    self.selectedView?.transform = .identity
                }
                let background = selectedView?.viewWithTag(2) as! SVGImageView
                background.SVGName = "drag bg1"
                //selectedView?.animateCoin(answer: false)
                //incorrect += 1
            }
        }
        
    }
    
    override func getSkills() -> [GameSkill] {
        return [.GameReading]
    }
    
    override func getScore() -> Float {
        return 4.0 / (4.0 + Float(incorrect))
    }
}
