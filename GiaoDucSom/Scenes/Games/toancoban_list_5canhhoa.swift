//
//  toancoban_list_5canhhoa.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/4/25.
//


import UIKit
import SnapKit

class toancoban_list_5canhhoa: NhanBietGameFragment {
    // MARK: - Properties
    private var image1: UIImageView!
    private var image2: UIImageView!
    private var image3: UIImageView!
    private var image4: UIImageView!
    private var imageViews: [UIImageView] = []
    private let ids = ["math_5canhhoa_5_1", "math_5canhhoa_5_2", "math_5canhhoa_5_3"]
    private let wrongIds = ["math_5canhhoa_0_1", "math_5canhhoa_0_2", "math_5canhhoa_0_3", "math_5canhhoa_0_4", "math_5canhhoa_0_5", "math_5canhhoa_0_6"]
    private var meIndex: Int = 0
        
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 220/255, green: 255/255, blue: 183/255, alpha: 1) // #DCFFB7
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "math_5canhhoa_bg"))
        bgImage.contentMode = .scaleAspectFill // Stretch như centerCrop
        bgImage.clipsToBounds = true
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let lineBottom = UIView() // Guideline
        view.addSubview(lineBottom)
        lineBottom.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.82) // 0.82 percent
        }
        
        let container = UIView()
        container.isUserInteractionEnabled = true // splitMotionEvents="false"
        container.isMultipleTouchEnabled = false
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.width.equalTo(container.snp.height).multipliedBy(3.5) // Ratio 3.5
            make.bottom.equalTo(lineBottom)
        }
        
        image1 = UIImageView(image: UIImage(named: "math_5canhhoa_5_3"))
        image1.isUserInteractionEnabled = true
        container.addSubview(image1)
        image1.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.25)
            make.height.equalTo(image1.snp.width)
            make.left.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        image2 = UIImageView(image: UIImage(named: "math_5canhhoa_5_3"))
        image2.isUserInteractionEnabled = true
        container.addSubview(image2)
        image2.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.25)
            make.height.equalTo(image2.snp.width)
            make.left.equalTo(image1.snp.right)
            make.top.bottom.equalToSuperview()
        }
        
        image3 = UIImageView(image: UIImage(named: "math_5canhhoa_5_3"))
        image3.isUserInteractionEnabled = true
        container.addSubview(image3)
        image3.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.25)
            make.height.equalTo(image3.snp.width)
            make.left.equalTo(image2.snp.right)
            make.top.bottom.equalToSuperview()
        }
        
        image4 = UIImageView(image: UIImage(named: "math_5canhhoa_5_3"))
        image4.isUserInteractionEnabled = true
        container.addSubview(image4)
        image4.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.25)
            make.height.equalTo(image4.snp.width)
            make.left.equalTo(image3.snp.right)
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        imageViews = [image1, image2, image3, image4]
        
        for (index, imageView) in imageViews.enumerated() {
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            imageView.addGestureRecognizer(tapGesture)
            imageView.tag = index
        }
    }
    
    // MARK: - Gesture Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UIImageView else { return }
        let index = view.tag
        let oldPosition = view.layer.position
        view.layer.anchorPoint = CGPoint(x: 0.5, y: 1.0)
        view.layer.position = CGPoint(
            x: oldPosition.x,
            y: oldPosition.y// + view.frame.height / 2
        )
        view.transform = CGAffineTransformMakeTranslation(0, view.bounds.height/2)
        //setAnchorPoint(CGPoint(x: 1.0, y: 1.0))
        if index == meIndex {
            animateCoinIfCorrect(view: view)
            let delay = playSound(finishCorrect1Sounds())
            pauseGame()
            animateView(
                view,
                scaleValues: [1.0, 0.95, 1.0, 0.95, 1.0],
                rotationDegreesValues: [0, 6, 0, -6, 0],
                duration: 2.0,
                repeatCount: 3,
                animationKey: "originalAnimation"
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            pauseGame()
            animateView(
                view,
                scaleValues: [1.0, 0.98, 1.0],
                rotationDegreesValues: [0, 6, 0],
                duration: 1.0,
                repeatCount: 1,
                animationKey: "originalAnimation"
            )
            /*
            UIView.animate(withDuration: 1.0, animations: {
                view.transform = CGAffineTransform(scaleX: 0.98, y: 0.98)
                    .rotated(by: .pi / 90) // 2 độ
                view.transform = CGAffineTransform(scaleX: 0.98, y: 0.98)
                    .rotated(by: -.pi / 90) // -2 độ
                view.transform = .identity
            })
             */
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        meIndex = Int.random(in: 0..<4)
        let indexes = Utils.generatePermutation(wrongIds.count)
        for i in 0..<4 {
            if i == meIndex {
                imageViews[i].image = Utilities.SVGImage(named: ids[Int.random(in: 0..<ids.count)])
            } else {
                imageViews[i].image = Utilities.SVGImage(named: wrongIds[indexes[i]])
            }
        }
        let delay = playSound(openGameSound(), "toan/toan_5 canh hoa")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_5 canh hoa")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
}

import UIKit

// Hàm trợ giúp chuyển đổi độ sang radian
fileprivate func degreesToRadians(_ degrees: CGFloat) -> CGFloat {
    return degrees * .pi / 180.0
}

/// Tạo hoạt ảnh scale và rotation cho một UIView với các giá trị keyframe tùy chỉnh.
///
/// - Parameters:
///   - view: UIView cần tạo hoạt ảnh.
///   - scaleValues: Một mảng các giá trị scale (ví dụ: [1.0, 0.95, 1.0]).
///   - rotationDegreesValues: Một mảng các giá trị rotation tính bằng độ (ví dụ: [0, 6, 0, -6, 0]).
///                            Mảng này PHẢI có cùng số lượng phần tử với `scaleValues`.
///   - duration: Thời gian (tính bằng giây) cho một chu kỳ hoạt ảnh.
///   - repeatCount: Tổng số lần hoạt ảnh sẽ chạy (ví dụ: 1 cho chạy một lần, 3 cho chạy ba lần).
///                  Sử dụng `.infinity` để lặp lại vô hạn.
///   - animationKey: Một chuỗi định danh tùy chọn để thêm vào layer, hữu ích cho việc quản lý hoặc xóa hoạt ảnh sau này.
func animateView(
    _ view: UIView,
    scaleValues: [CGFloat],
    rotationDegreesValues: [CGFloat],
    duration: TimeInterval,
    repeatCount: Float,
    animationKey: String? = "customScaleRotateAnimation" // Cung cấp key mặc định
) {
    
    // --- Kiểm tra đầu vào ---
    guard !scaleValues.isEmpty, !rotationDegreesValues.isEmpty else {
        print("Lỗi: Mảng scaleValues hoặc rotationDegreesValues không được rỗng.")
        return
    }
    guard scaleValues.count == rotationDegreesValues.count else {
        print("Lỗi: Số lượng giá trị scale (\(scaleValues.count)) phải bằng số lượng giá trị rotation (\(rotationDegreesValues.count)).")
        return
    }

    let keyFrameCount = scaleValues.count

    // --- Tính toán keyTimes ---
    // Phân bổ đều các điểm thời gian từ 0.0 đến 1.0
    let calculatedKeyTimes: [NSNumber]
    if keyFrameCount <= 1 {
        calculatedKeyTimes = [0.0] // Nếu chỉ có 1 frame, nó ở thời điểm bắt đầu
    } else {
        // Tạo các số từ 0 đến keyFrameCount-1, sau đó chia cho (keyFrameCount-1)
        // để có các phân số từ 0.0 đến 1.0
        calculatedKeyTimes = (0..<keyFrameCount).map { index in
            NSNumber(value: Double(index) / Double(keyFrameCount - 1))
        }
    }

    // --- Tạo hoạt ảnh Scale ---
    let scaleAnimation = CAKeyframeAnimation(keyPath: "transform.scale")
    scaleAnimation.values = scaleValues
    scaleAnimation.keyTimes = calculatedKeyTimes

    // --- Tạo hoạt ảnh Rotation ---
    let rotationAnimation = CAKeyframeAnimation(keyPath: "transform.rotation.z")
    // Chuyển đổi giá trị độ đầu vào thành radian
    rotationAnimation.values = rotationDegreesValues.map(degreesToRadians)
    rotationAnimation.keyTimes = calculatedKeyTimes // Sử dụng cùng keyTimes

    // --- Nhóm các hoạt ảnh ---
    let animationGroup = CAAnimationGroup()
    animationGroup.animations = [scaleAnimation, rotationAnimation]

    // --- Thiết lập thuộc tính chung từ tham số ---
    animationGroup.duration = duration
    animationGroup.repeatCount = repeatCount

    // (Tùy chọn) Để giữ trạng thái cuối cùng sau khi hoàn thành:
    // animationGroup.isRemovedOnCompletion = false
    // animationGroup.fillMode = .forwards

    // --- Áp dụng hoạt ảnh vào layer ---
    // Xóa hoạt ảnh cũ có cùng key (nếu có) trước khi thêm cái mới
    view.layer.removeAnimation(forKey: animationKey ?? "") // Sử dụng ?? "" để tránh lỗi nếu key là nil
    view.layer.add(animationGroup, forKey: animationKey)
}
