//
//  toancoban_list_taochunhat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 8/4/25.
//


import UIKit
import SnapKit

class toancoban_list_taochunhat: NhanBietGameFragment {
    // MARK: - Properties
    private var rectCountTextView: HeightRatioTextView!
    private var pieceView: PieceView!
    private var pieceViewLeft: PieceView!
    private var pieceViewRight: PieceView!
    private var pieceGrid: [[Int]] = []
    private var rectView: ResizableRectangleView!
    private var textQuestion: HeightRatioTextView!
    private var count: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let insetContainer = UIView()
        addSubviewWithPercentInset(subview: insetContainer, percentInset: 5)
        let container = UIView()
        //container.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #00f0
        insetContainer.addSubview(container)
        //container.backgroundColor = .red
        container.makeViewCenterAndKeep(ratio: 2.0)
        
        pieceView = PieceView()
        pieceView.updateColor1(color1: .clear)
        container.addSubview(pieceView)
        pieceView.snp.makeConstraints { make in
            make.top.bottom.centerX.equalToSuperview()
            make.width.equalTo(pieceView.snp.height) // Ratio 1:1
        }
        
        rectView = ResizableRectangleView()
        rectView.listener = self
        
        container.addSubview(rectView)
        rectView.snp.makeConstraints { make in
            make.top.bottom.centerX.equalToSuperview()
            make.width.equalTo(rectView.snp.height) // Ratio 1:1
        }
        
        textQuestion = HeightRatioTextView()
        textQuestion.textColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 1) // #419DEE
        textQuestion.font = .Freude(size: 20)
        textQuestion.setHeightRatio(0.7)
        textQuestion.textAlignment = .center
        textQuestion.adjustsFontSizeToFitWidth = true
        textQuestion.minimumScaleFactor = 0.1
        container.addSubview(textQuestion)
        textQuestion.snp.makeConstraints { make in
            make.height.equalTo(textQuestion.snp.width)
            make.height.equalTo(container).multipliedBy(0.22)
        }
        addActionOnLayoutSubviews {
            self.textQuestion.snapToHorizontalBias(horizontalBias: 0.03)
            self.textQuestion.snapToVerticalBias(verticalBias: 0.49)
        }
        
        pieceViewLeft = PieceView()
        pieceViewLeft.setPiece([[2]])
        container.addSubview(pieceViewLeft)
        pieceViewLeft.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.125)
            make.width.equalTo(pieceViewLeft.snp.height)
            make.centerY.equalToSuperview()
        }
        
        addActionOnLayoutSubviews {
            self.pieceViewLeft.snapToHorizontalBias(horizontalBias: 0.14)
        }
        
        rectCountTextView = HeightRatioTextView()
        rectCountTextView.textColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 1) // #419DEE
        rectCountTextView.font = .Freude(size: 20)
        rectCountTextView.setHeightRatio(0.7)
        rectCountTextView.textAlignment = .center
        rectCountTextView.adjustsFontSizeToFitWidth = true
        rectCountTextView.minimumScaleFactor = 0.1
        container.addSubview(rectCountTextView)
        rectCountTextView.snp.makeConstraints { make in
            make.height.equalTo(rectCountTextView.snp.width)
            make.height.equalTo(container).multipliedBy(0.22)
        }
        addActionOnLayoutSubviews {
            self.rectCountTextView.snapToHorizontalBias(horizontalBias: 0.87)
            self.rectCountTextView.snapToVerticalBias(verticalBias: 0.49)
        }
        
        pieceViewRight = PieceView()
        pieceViewRight.setPiece([[2]])
        container.addSubview(pieceViewRight)
        pieceViewRight.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.125)
            make.width.equalTo(pieceViewRight.snp.height)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.pieceViewRight.snapToHorizontalBias(horizontalBias: 0.94)
        }
    }
    var countUpdate = 0
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        countUpdate += 1
        if countUpdate == 2 {
            return
        }
        let size = 8
        pieceGrid = Array(repeating: Array(repeating: 1, count: size), count: size)
        pieceGrid[3][3] = 2
        pieceGrid[3][4] = 2
        pieceGrid[4][3] = 2
        pieceGrid[4][4] = 2
        pieceView.setPiece(pieceGrid)
        
        count = (1 + Int.random(in: 0..<8)) * (1 + Int.random(in: 0..<8))
        textQuestion.text = String(count)
        
        let delay = playSound(openGameSound(), "toan/toan_tao chu nhat", "topics/Numbers/\(count)", "toan/toan_tao chu nhat_o vuong")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
         
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_tao chu nhat", "topics/Numbers/\(count)", "toan/toan_tao chu nhat_o vuong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - ResizableRectangleViewListener
extension toancoban_list_taochunhat: ResizableRectangleViewListener {
    func onRectUpdated(rect: CGRect) {
        for i in 0..<pieceGrid.count {
            pieceGrid[i] = Array(repeating: 1, count: pieceGrid[i].count)
        }
        let size = rectView.frame.height / 8
        let x = Int(round(rect.minX / size))
        let y = Int(round(rect.minY / size))
        let width = Int(round(rect.width / size))
        let height = Int(round(rect.height / size))
        
        for i in x..<(x + width) {
            for j in y..<(y + height) {
                if i >= 0 && i < pieceGrid.count && j >= 0 && j < pieceGrid[0].count {
                    pieceGrid[j][i] = 2
                }
            }
        }
        pieceView.setPiece(pieceGrid)
        
        let rectSize = width * height
        rectCountTextView.text = String(rectSize)
    }
    
    func onFinish() {
        let rectSize = Int(rectCountTextView.text ?? "0") ?? 0
        let delay = playSound("topics/Numbers/\(rectSize)")
        if rectSize == count {
            pauseGame()
            animateCoinIfCorrect(view: rectView)
            rectCountTextView.textColor = UIColor(red: 136/255, green: 215/255, blue: 88/255, alpha: 1) // #88D758
            let totalDelay = delay + playSound(delay: delay, names: finishCorrect1Sounds())
            scheduler.schedule(delay: totalDelay) { [weak self] in
                self?.finishGame()
            }
        } else {
            rectCountTextView.textColor = UIColor(red: 255/255, green: 119/255, blue: 97/255, alpha: 1) // #FF7761
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                self?.rectCountTextView.textColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 1) // #419DEE
            }
        }
    }
    
    func onBegin() {
        rectCountTextView.textColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 1) // #419DEE
    }
}

// MARK: - ResizableRectangleView
class ResizableRectangleView: UIView {
    // MARK: - Properties
    weak var listener: ResizableRectangleViewListener?
    private var gridSize: CGFloat = 100
    private var rectangle: CGRect = .zero
    private let circleRadius: CGFloat = 30
    private var controlPoints: [CGPoint] = []
    private var activePointIndex: Int = -1
    
    // Shape Layers
    private let rectangleLayer = CAShapeLayer()
    private let controlPointLayers: [CAShapeLayer] = (0..<4).map { _ in CAShapeLayer() }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }
    
    private func initView() {
        backgroundColor = .clear
        clipsToBounds = false
        
        rectangleLayer.fillColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 0).cgColor
        layer.addSublayer(rectangleLayer)
        
        controlPointLayers.forEach { layer in
            layer.fillColor = UIColor(red: 65/255, green: 157/255, blue: 238/255, alpha: 1).cgColor
            self.layer.addSublayer(layer)
        }
    }
    
    // MARK: - Layout
    override func layoutSubviews() {
        super.layoutSubviews()
        gridSize = bounds.width / 8
        rectangle = CGRect(x: gridSize * 3, y: gridSize * 3, width: gridSize * 2, height: gridSize * 2)
        controlPoints = [
            CGPoint(x: rectangle.minX, y: rectangle.minY),
            CGPoint(x: rectangle.maxX, y: rectangle.minY),
            CGPoint(x: rectangle.minX, y: rectangle.maxY),
            CGPoint(x: rectangle.maxX, y: rectangle.maxY)
        ]
        updateLayers()
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, event?.allTouches?.count ?? 0 <= 1 else { return super.touchesBegan(touches, with: event) }
        let location = touch.location(in: self)
        activePointIndex = getTouchedControlPoint(x: location.x, y: location.y)
        if activePointIndex != -1 {
            Utils.vibrate()
            listener?.onBegin()
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first, activePointIndex != -1 else { return super.touchesMoved(touches, with: event) }
        var x = touch.location(in: self).x
        var y = touch.location(in: self).y
        
        // Giới hạn vị trí trong bounds
        x = max(0, min(x, bounds.width))
        y = max(0, min(y, bounds.height))
        
        // Đảm bảo kích thước tối thiểu
        if activePointIndex == 1 || activePointIndex == 3 {
            x = max(x, rectangle.minX + gridSize)
        }
        if activePointIndex == 0 || activePointIndex == 2 {
            x = min(x, rectangle.maxX - gridSize)
        }
        if activePointIndex == 2 || activePointIndex == 3 {
            y = max(y, rectangle.minY + gridSize)
        }
        if activePointIndex == 0 || activePointIndex == 1 {
            y = min(y, rectangle.maxY - gridSize)
        }
        
        // Cập nhật vị trí control point đang kéo
        controlPoints[activePointIndex] = CGPoint(x: x, y: y)
        
        let otherIndex = otherActivePointIndex(index: activePointIndex)
        let left = snapToGrid(value: min(controlPoints[activePointIndex].x, controlPoints[otherIndex].x))
        let top = snapToGrid(value: min(controlPoints[activePointIndex].y, controlPoints[otherIndex].y))
        let right = snapToGrid(value: max(controlPoints[activePointIndex].x, controlPoints[otherIndex].x))
        let bottom = snapToGrid(value: max(controlPoints[activePointIndex].y, controlPoints[otherIndex].y))
        
        let changed = left != rectangle.minX || top != rectangle.minY || right != rectangle.maxX || bottom != rectangle.maxY
        if changed {
            updateRectangle()
            updateLayers()
        }
        // Cập nhật layer của chấm tròn đang kéo ngay lập tức
        updateActiveControlPointLayer()
        
        // Cập nhật rectangle (chỉ khi cần)
        updateRectangle()
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard activePointIndex != -1 else { return super.touchesEnded(touches, with: event) }
        Utils.vibrate()
        controlPoints[activePointIndex] = getPointFromRect(pointIndex: activePointIndex)
        activePointIndex = -1
        updateLayers()
        listener?.onFinish()
    }
    
    // MARK: - Helper Methods
    private func snapToGrid(value: CGFloat) -> CGFloat {
        return round(value / gridSize) * gridSize
    }
    
    private func getTouchedControlPoint(x: CGFloat, y: CGFloat) -> Int {
        for (i, point) in controlPoints.enumerated() {
            if hypot(point.x - x, point.y - y) <= circleRadius {
                return i
            }
        }
        return -1
    }
    
    private func otherActivePointIndex(index: Int) -> Int {
        switch index {
        case 0: return 3
        case 1: return 2
        case 2: return 1
        case 3: return 0
        default: return -1
        }
    }
    
    private func updateRectangle() {
        let otherIndex = otherActivePointIndex(index: activePointIndex)
        let left = snapToGrid(value: min(controlPoints[activePointIndex].x, controlPoints[otherIndex].x))
        let top = snapToGrid(value: min(controlPoints[activePointIndex].y, controlPoints[otherIndex].y))
        let right = snapToGrid(value: max(controlPoints[activePointIndex].x, controlPoints[otherIndex].x))
        let bottom = snapToGrid(value: max(controlPoints[activePointIndex].y, controlPoints[otherIndex].y))
        
        let changed = left != rectangle.minX || top != rectangle.minY || right != rectangle.maxX || bottom != rectangle.maxY
        if left != right && top != bottom && changed {
            rectangle = CGRect(
                x: max(0, left),
                y: max(0, top),
                width: min(right, bounds.width) - max(0, left),
                height: min(bottom, bounds.height) - max(0, top) // Sửa custom thành giá trị hợp lệ
            )
            
            if activePointIndex != 0 { controlPoints[0] = CGPoint(x: rectangle.minX, y: rectangle.minY) }
            if activePointIndex != 1 { controlPoints[1] = CGPoint(x: rectangle.maxX, y: rectangle.minY) }
            if activePointIndex != 2 { controlPoints[2] = CGPoint(x: rectangle.minX, y: rectangle.maxY) }
            if activePointIndex != 3 { controlPoints[3] = CGPoint(x: rectangle.maxX, y: rectangle.maxY) }
            
            // Chỉ cập nhật rectangle layer
            rectangleLayer.path = UIBezierPath(rect: rectangle).cgPath
            listener?.onRectUpdated(rect: rectangle)
        }
    }
    
    private func getPointFromRect(pointIndex: Int) -> CGPoint {
        switch pointIndex {
        case 0: return CGPoint(x: rectangle.minX, y: rectangle.minY)
        case 1: return CGPoint(x: rectangle.maxX, y: rectangle.minY)
        case 2: return CGPoint(x: rectangle.minX, y: rectangle.maxY)
        case 3: return CGPoint(x: rectangle.maxX, y: rectangle.maxY)
        default: return .zero
        }
    }
    
    private func updateLayers() {
        rectangleLayer.path = UIBezierPath(rect: rectangle).cgPath
        for (index, layer) in controlPointLayers.enumerated() {
            let point = controlPoints[index]
            let snappedPoint = CGPoint(x: snapToGrid(value: point.x), y: snapToGrid(value: point.y))
            let circleRect = CGRect(x: snappedPoint.x - circleRadius, y: snappedPoint.y - circleRadius, width: circleRadius * 2, height: circleRadius * 2)
            layer.path = UIBezierPath(ovalIn: circleRect).cgPath
        }
    }
    
    private func updateActiveControlPointLayer() {
        guard activePointIndex != -1 else { return }
        let point = controlPoints[activePointIndex]
        let circleRect = CGRect(x: point.x - circleRadius, y: point.y - circleRadius, width: circleRadius * 2, height: circleRadius * 2)
        controlPointLayers[activePointIndex].path = UIBezierPath(ovalIn: circleRect).cgPath
    }
}

// MARK: - ResizableRectangleViewListener Protocol
protocol ResizableRectangleViewListener: AnyObject {
    func onRectUpdated(rect: CGRect)
    func onFinish()
    func onBegin()
}
