<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="0.8"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/view_background"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/mythuat_tranhtomau_list_bg" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_name"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="Name"
                android:textColor="#f39a71"
                app:heightRatio="0.7"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.85" />

            <com.kidsup.giaoducsom.view.BlackSVGImageView
                android:id="@+id/svg_thumbnail"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.25" />
            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_question"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:text="?"
                android:textColor="#f39a71"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                app:heightRatio="0.8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.25" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/btn_buy"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/mythuat_hopmau_btn_buy"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="40:18"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9"
                app:layout_constraintWidth_percent="0.8">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_item_coin"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="0"
                    android:textColor="#fff700"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintHeight_percent="0.8"
                    app:layout_constraintHorizontal_bias="0.8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.3"
                    app:layout_constraintWidth_percent="0.5" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>