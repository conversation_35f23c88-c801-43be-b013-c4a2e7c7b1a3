//
//  tuduy_list_xepgach.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 9/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_xepgach: NhanBietGameFragment {
    // MARK: - Properties
    private var viewLeft: MyGridView!
    private var viewRight: MyGridView!
    private var viewCenter: UIView!
    private var viewFrame: SVGImageView!
    private var viewGrid: SVGImageView!
    private var containerLayout: UIView!
    private var itemContainer: UIView!
    private let ROW = 4
    private var COL = Int.random(in: 3...4)
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    private var deltaX2: CGFloat = 0
    private var deltaY2: CGFloat = 0
    private var doneViews: [UIView] = []
    private var filledPieces: [[[Bool]]] = []
    private var solver: TetrisSolver!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    private var stepX: CGFloat = 0
    private var stepY: CGFloat = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        //view.backgroundColor = UIColor(red: 204/255, green: 255/255, blue: 209/255, alpha: 1) // #CCFFD1
        
        let bgView = SVGImageView(frame: .zero)
        bgView.SVGName = "tuduy_brick_bg"
        bgView.contentMode = .scaleAspectFill
        addSubview(bgView)
        bringSubviewToFront(view)
        bgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        containerLayout = UIView()
        containerLayout.clipsToBounds = false
        view.addSubview(containerLayout)
        containerLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewCenter = UIView()
        viewCenter.backgroundColor = UIColor.black.withAlphaComponent(0.006) // #0f00
        viewCenter.clipsToBounds = false
        containerLayout.addSubview(viewCenter)
        viewCenter.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.32)
            make.center.equalToSuperview()
        }
        
        viewLeft = MyGridView()
        containerLayout.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.9)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
            make.right.equalTo(viewCenter.snp.left)
        }
        
        viewRight = MyGridView()
        containerLayout.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.9)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
            make.left.equalTo(viewCenter.snp.right)
        }
        
       
        
        viewFrame = SVGImageView(frame: .zero)
        viewFrame.SVGName = COL == 4 ? "brick/44_bg2.svg" : "brick/34_bg2.svg"
        viewFrame.clipsToBounds = false
        viewCenter.addSubview(viewFrame)
        viewFrame.makeViewCenterAndKeep(ratio: COL == 4 ? 1.0 : 3.0 / 3.6)
        
        viewGrid = SVGImageView(frame: .zero)
        viewGrid.SVGName = COL == 4 ? "brick/44_bg.svg" : "brick/34_bg.svg"
        viewGrid.clipsToBounds = false
        viewFrame.addSubview(viewGrid)
        viewGrid.snp.makeConstraints { make in
            make.width.equalTo(viewFrame).multipliedBy(COL == 4 ? 0.74 : 0.68)
            make.height.equalTo(viewGrid.snp.width).multipliedBy(COL == 4 ? 1.0 : 4.0 / 3.0) // Ratio 3:4 cho COL = 3
            make.center.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        containerLayout.addGestureRecognizer(panGesture)
         
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "tuduy/xep gach")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/xep gach")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        solver = TetrisSolver(pieces: getSamplePieces(), rows: ROW, cols: COL)
        if solver.solve() {
            filledPieces = solver.getFilledPieces()
            if filledPieces.count == 3 {
                filledPieces.append([])
            }
            if filledPieces.count == 5 {
                filledPieces.insert([], at: 4)
            }
            
            let count = filledPieces.count / 2
            var viewLefts: [UIView] = []
            for _ in 0..<count {
                let view = UIView()
                view.backgroundColor = UIColor.black.withAlphaComponent(0.004) // #01000000
                viewLefts.append(view)
            }
            viewLeft.columns = 1
            viewLeft.itemRatio = 1
            viewLeft.itemSpacingRatio = 0.05
            viewLeft.insetRatio = 0.05
            viewLeft.reloadItemViews(views: viewLefts)
            
            var viewRights: [UIView] = []
            for _ in count..<filledPieces.count {
                let view = UIView()
                view.backgroundColor = UIColor.black.withAlphaComponent(0.004) // #01000000
                viewRights.append(view)
            }

            viewRight.columns = 1
            viewRight.itemRatio = 1
            viewRight.itemSpacingRatio = 0.05
            viewRight.insetRatio = 0.05
            viewRight.reloadItemViews(views: viewRights)
            
            for (i, piece) in filledPieces.enumerated() {
                let view = TetrisView()
                view.setPiece(piece: piece.isEmpty ? nil : piece)
                viewGrid.addSubview(view)
                view.snp.makeConstraints { make in
                    make.left.equalToSuperview()
                    make.top.equalToSuperview()
                    if COL == 3 {
                        make.right.equalToSuperview()
                    } else {
                        make.width.equalToSuperview().multipliedBy(0.75)
                    }
                    make.height.equalTo(view.snp.width) // Ratio 1:1
                }
                view.alpha = 0.01
                
                scheduler.schedule(after: 0.1) { [weak self] in
                    guard let self = self else { return }
                    let itemView = i < count ? viewLeft.subviews[i] : viewRight.subviews[i-count]
                    view.moveToCenter(of: itemView, duration: 0.2)
                    UIView.animate(withDuration: 0.2) {
                        view.alpha = 1
                    }
                }
            }
        }
    }
    var minX : CGFloat = 0
    var minY : CGFloat = 0
    func removeTransform(view: UIView){
        if view.transform != .identity {
            let currentFrame = view.frame
            view.transform = .identity
            view.frame = currentFrame
        }
    }
    var zindex = 5
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: containerLayout)
        switch gesture.state {
        case .began:
            let containerLocation = containerLayout.convert(containerLayout.bounds.origin, to: nil)
            let gridLocation = viewGrid.convert(viewGrid.bounds.origin, to: nil)
            deltaX = gridLocation.x - containerLocation.x
            deltaY = gridLocation.y - containerLocation.y
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                removeTransform(view: currentView)
                minX = currentView.frame.minX
                minY = currentView.frame.minY
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                let originX = currentView.frame.minX
                let originY = currentView.frame.minY
                //currentView.bringSubviewToFront(self)
                //currentView.transform = .init(scaleX: 1.0, y: 1.0)
                doneViews.removeAll { $0 == currentView }
                containerLayout.alpha = 1
                playSound("effect/word puzzle drop")
                currentView.layer.zPosition = CGFloat(zindex)
                zindex += 1
                //currentView.superview?.bringSubviewToFront(currentView)
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = max(-currentView.frame.width / 2 - deltaX, min(location.x + dX, containerLayout.frame.width - currentView.frame.width / 2 - deltaX))
                let newY = max(-currentView.frame.height / 2 - deltaY, min(location.y + dY, containerLayout.frame.height - currentView.frame.height / 2 - deltaY))
                print(newX)
                print(newY)
                currentView.frame = CGRect(x: newX, y: newY, width: currentView.frame.width, height: currentView.frame.height)
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                let checkingView = currentView
                let step = viewGrid.frame.height / CGFloat(ROW)
                UIView.animate(withDuration: 0.2) {
                    checkingView.transform = .init(translationX: round(checkingView.frame.minX / step) * step - checkingView.frame.minX, y: round(checkingView.frame.minY / step) * step - checkingView.frame.minY)
                    self.removeTransform(view: checkingView)
                } completion: { _ in
                    if true {
                        //return
                    }
                    let valid = self.checkValid(view: checkingView)
                    if !valid {
                        UIView.animate(withDuration: 0.2) {
                            [weak self] in
                            guard let self = self else { return }
                            currentView.frame = CGRect(x: self.minX, y: self.minY, width: currentView.frame.width, height: currentView.frame.height)
                        }
                        self.setGameWrong()
                    } else {
                        self.doneViews.append(checkingView)
                        let finish = self.checkFinish()
                        if finish {
                            self.animateCoinIfCorrect(view: self.viewCenter)
                            self.pauseGame()
                            let delay = self.playSound(self.answerCorrect1EffectSound(), self.getCorrectHumanSound(), self.endGameSound())
                            self.scheduler.schedule(delay: delay) { [weak self] in
                                self?.finishGame()
                            }
                        }
                    }
                }
                playSound("effect/brick_drop")
            }
            currentView = nil
            
        default:
            break
        }
    }
    func abc()->[[Bool]] {
        return [[true],[true,true]]
    }
    // MARK: - Helper Methods
    private func getSamplePieces() -> [[[Bool]]] {
        return [
            [[true, true, false], [false, true, true]],
            [[false, true, true], [true, true, false]],
            [[false, true], [true, true], [true, false]],
            [[true, false], [true, true], [false, true]],
            [[true]],
            [[false, true, false], [true, true, true]],
            [[true, true, true], [false, true, false]],
            [[true, false], [true, true], [true, false]],
            [[false, true], [true, true], [false, true]],
            [[true, true]],
            [[true], [true]],
            [[true, true, true]],
            [[true], [true], [true]],
            [[true, true], [true, true]],
            [[true, true, true], [true, false, false]],
            [[true, true, true], [false, false, true]],
            [[true, false, false], [true, true, true]],
            [[false, false, true], [true, true, true]],
            [[true, true], [true, false]],
            [[true, true], [false, true]],
            [[false, true], [true, true]],
            [[true, false], [true, true]],
            [[true, true], [true, false], [true, false]],
            [[true, true], [false, true], [false, true]],
            [[true, false], [true, false], [true, true]],
            [[false, true], [false, true], [true, true]]
        ]
    }
    
    private func checkValid(view: UIView) -> Bool {
        let step = viewGrid.frame.height / CGFloat(ROW)
        var board = Array(repeating: Array(repeating: false, count: COL), count: ROW)
        var views = doneViews
        views.append(view)
        
        for k in 0..<views.count {
            guard let tetrisView = views[k] as? TetrisView, let piece = tetrisView.piece else { continue }
            let deltaX = Int(round(tetrisView.frame.minX / step))
            let deltaY = Int(round(tetrisView.frame.minY / step))
            let width = piece[0].count
            let height = piece.count
            let deltaWidth = (3 - width) / 2
            let deltaHeight = (3 - height) / 2
            
            for i in 0..<height {
                for j in 0..<width {
                    if piece[i][j] {
                        let x = i + deltaY + deltaHeight
                        let y = j + deltaX + deltaWidth
                        if x < -1 || x > ROW || y < -1 || y > COL { continue }
                        if x == -1 || x == ROW || y == -1 || y == COL {
                            return false
                        }
                        if board[x][y] {
                            return false
                        }
                        board[x][y] = true
                    }
                }
            }
        }
        return true
    }
    
    private func checkFinish() -> Bool {
        let step = viewGrid.frame.height / CGFloat(ROW)
        var board = Array(repeating: Array(repeating: false, count: COL), count: ROW)
        
        for k in 0..<doneViews.count {
            guard let tetrisView = doneViews[k] as? TetrisView, let piece = tetrisView.piece else { continue }
            let deltaX = Int(round(tetrisView.frame.minX / step))
            let deltaY = Int(round(tetrisView.frame.minY / step))
            let width = piece[0].count
            let height = piece.count
            let deltaWidth = (3 - width) / 2
            let deltaHeight = (3 - height) / 2
            
            for i in 0..<height {
                for j in 0..<width {
                    if piece[i][j] {
                        let x = i + deltaY + deltaHeight
                        let y = j + deltaX + deltaWidth
                        if x < -1 || x > ROW || y < -1 || y > COL { continue }
                        if x == -1 || x == ROW || y == -1 || y == COL { return false }
                        if board[x][y] { return false }
                        board[x][y] = true
                    }
                }
            }
        }
        
        for i in 0..<ROW {
            for j in 0..<COL {
                if !board[i][j] { return false }
            }
        }
        return true
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        let adjustedX = x - deltaX
        let adjustedY = y - deltaY
        for i in (0..<viewGrid.subviews.count).reversed() {
            guard let view = viewGrid.subviews[i] as? TetrisView, view.piece != nil else { continue }
            if adjustedX >= view.frame.minX && adjustedX <= view.frame.maxX && adjustedY >= view.frame.minY && adjustedY <= view.frame.maxY {
                if isPixelVisible(view: view, x: Int(adjustedX), y: Int(adjustedY)) {
                    return view
                }
            }
        }
        return nil
    }
    
    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard view.isHidden == false else { return false }
        return view.isPixelVisible(x: x, y: y)
    }

    
    // MARK: - TetrisView
    class TetrisView: MyGridView {
        var piece: [[Bool]]?
        let ROW = 3
        let COL = 3
        static let pieceImage : UIImage = Utilities.SVGImage(named: "brick/full piece/tuduy_brick_1_1.svg")
        override init(frame: CGRect) {
            super.init(frame: frame)
            initView()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initView()
        }
        
        private func initView() {
            var views: [UIView] = []
            for _ in 0..<ROW {
                for _ in 0..<COL {
                    let view = UIImageView()
                    views.append(view)
                }
            }
            columns = 3
            itemRatio = 1
            itemSpacingRatio = 0
            insetRatio = 0
            reloadItemViews(views: views)
        }
        
        func setPiece(piece: [[Bool]]?) -> TetrisView {
            self.piece = piece
            guard let piece = piece else { return self }
            let width = piece[0].count
            let height = piece.count
            let deltaWidth = (COL - width) / 2
            let deltaHeight = (ROW - height) / 2
            for i in 0..<ROW {
                for j in 0..<COL {
                    if i - deltaHeight >= 0 && j - deltaWidth >= 0 && i - deltaHeight < height && j - deltaWidth < width && piece[i - deltaHeight][j - deltaWidth] {
                        let view = subviews[COL * i + j] as! UIImageView
                        view.image =  TetrisView.pieceImage
                    }
                }
            }
            return self
        }
    }
    
    // MARK: - TetrisSolver
    class TetrisSolver {
        private let ROWS: Int
        private let COLS: Int
        private var board: [[Bool]]
        private var boardPieces: [[Int]]
        private var pieceIndex: Int = 0
        private let pieces: [[[Bool]]]
        private var usedPieces: [Bool]
        
        init(pieces: [[[Bool]]], rows: Int, cols: Int) {
            self.ROWS = rows
            self.COLS = cols
            self.board = Array(repeating: Array(repeating: false, count: cols), count: rows)
            self.boardPieces = Array(repeating: Array(repeating: 0, count: cols), count: rows)
            self.pieces = pieces
            self.usedPieces = Array(repeating: false, count: pieces.count)
        }
        
        func getFilledPieces() -> [[[Bool]]] {
            var filledPieces: [[[Bool]]] = []
            for i in 0..<pieces.count {
                if usedPieces[i] {
                    filledPieces.append(pieces[i])
                }
            }
            return filledPieces
        }
        
        func solve() -> Bool {
            var shuffledPieces = pieces
            shuffledPieces.shuffle()
            return solveRecursive(row: 0, col: 0)
        }
        
        private func solveRecursive(row: Int, col: Int) -> Bool {
            var r = row
            var c = col
            while r < ROWS && c < COLS && board[r][c] {
                c += 1
                if c == COLS {
                    c = 0
                    r += 1
                }
            }
            if r == ROWS { return true }
            
            for i in 0..<pieces.count {
                if usedPieces[i] { continue }
                let piece = pieces[i]
                if canPlacePiece(piece: piece, row: r, col: c) {
                    placePiece(piece: piece, row: r, col: c, value: true)
                    usedPieces[i] = true
                    if solveRecursive(row: r, col: c) { return true }
                    placePiece(piece: piece, row: r, col: c, value: false)
                    usedPieces[i] = false
                }
            }
            return false
        }
        
        private func canPlacePiece(piece: [[Bool]], row: Int, col: Int) -> Bool {
            var deltaI = 0
            var deltaJ = 0
            var foundTopLeft = false
            for i in 0..<piece.count {
                for j in 0..<piece[0].count {
                    if piece[i][j] {
                        deltaI = i
                        deltaJ = j
                        foundTopLeft = true
                        break
                    }
                }
                if foundTopLeft { break }
            }
            for i in 0..<piece.count {
                for j in 0..<piece[0].count {
                    if piece[i][j] {
                        let newRow = row - deltaI + i
                        let newCol = col - deltaJ + j
                        if newRow < 0 || newCol < 0 || newRow >= ROWS || newCol >= COLS || board[newRow][newCol] {
                            return false
                        }
                    }
                }
            }
            return true
        }
        
        private func placePiece(piece: [[Bool]], row: Int, col: Int, value: Bool) {
            pieceIndex += value ? 1 : -1
            var deltaI = 0
            var deltaJ = 0
            var foundTopLeft = false
            for i in 0..<piece.count {
                for j in 0..<piece[0].count {
                    if piece[i][j] {
                        deltaI = i
                        deltaJ = j
                        foundTopLeft = true
                        break
                    }
                }
                if foundTopLeft { break }
            }
            for i in 0..<piece.count {
                for j in 0..<piece[0].count {
                    if piece[i][j] {
                        board[row - deltaI + i][col - deltaJ + j] = value
                        if value {
                            boardPieces[row - deltaI + i][col - deltaJ + j] = pieceIndex
                        }
                    }
                }
            }
        }
    }
}

