//
//  nhanbiet_list_ruottraicay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_ruottraicay: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var svgView: SVGImageView!
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bg = Utilities.GetSVGKImage(named: "nhanbiet_bg_inside")
        let bgImage = SVGKFastImageView(svgkImage: bg)!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = .white.withAlphaComponent(0.3)
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        svgView.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
            make.bottom.equalToSuperview().inset(0.2 * view.frame.height) // Margin 0.2
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let folder = getFolder(), let item = getItem() else { return }
        
        var delay: TimeInterval = 0.5
        if getLanguage() == "vi" {
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(getLanguage())/nhanbiet/nhanbiet_inside1",
                "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))",
                "\(getLanguage())/nhanbiet/nhanbiet_inside3"
            ])
        } else {
            let article = ArticleHelper.getArticle(item.name.en ?? "")
            delay += playSound(delay: delay, names: [
                openGameSound(),
                "\(getLanguage())/nhanbiet/nhanbiet_inside_\(article)",
                "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        }
        
        svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item.path!)").uiImage
        
        var views: [UIView] = []
        var randomorder: [Item] = []
        
        // Filter and select items from Fruits pack
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs where pack.folder == "Fruits" {
            var listItems: [Item]
            var count = 0
            while true {
                count += 1
                listItems = pack.items.shuffled().prefix(3).map { $0 }
                if listItems.contains(where: { ["blueberry", "cherry", "grapes", "green grapes"].contains($0.name.en?.lowercased()) })
                || !listItems.contains(where: { $0.name.en == item.name.en })
                //|| !listItems.contains(where: { $0.name.en == "lemon" })
                {
                    continue
                }
                setListItems(listItems)
                break
            }
            randomorder = listItems.shuffled()
            break
        }
    
        
        for (i, item) in randomorder.enumerated() {
            let view = KUButton()
            view.backgroundColor = .clear
            let innerContainer = UIImageView()
            innerContainer.image = Utilities.SVGImage(named: "btn_white_bg")
            view.addSubview(innerContainer)
            innerContainer.makeViewCenterAndKeep(ratio: 1)
            
            let svgView = SVGImageView(frame: .zero)
            svgView.stringTag = "svg_view"
            svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/inside/\(item.path!)").uiImage
            innerContainer.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let textView = UILabel()
            textView.stringTag = "textview"
            textView.textColor = UIColor(hex: "#99B6C1")
            textView.textAlignment = .center
            textView.font = UIFont(name: "SVN-Freude", size: 1000)
            textView.adjustsFontSizeToFitWidth = true
            textView.minimumScaleFactor = 0.1
            textView.numberOfLines = 1
            innerContainer.addSubview(textView)
            textView.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.7)
                make.height.equalToSuperview().multipliedBy(0.6)
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(0.8) // Vertical bias 0.4
            }
            
            view.stringTag = "\(i)"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0.1, y: 0.1)
            view.alpha = 0.001
        }
        
        meIndex = randomorder.firstIndex(where: { $0.name.en == item.name.en }) ?? 0
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.gridLayout.showItems(startDelay: 0)
        }
        scheduler.schedule(after: delay + 1.0) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let folder = getFolder(), let item = getItem() else { return }
            var delay: TimeInterval = 0
            if getLanguage() == "vi" {
                delay += playSound(delay: delay, names: [
                    "\(getLanguage())/nhanbiet/nhanbiet_inside1",
                    "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))",
                    "\(getLanguage())/nhanbiet/nhanbiet_inside3"
                ])
            } else {
                let article = ArticleHelper.getArticle(item.name.en ?? "")
                delay += playSound(delay: delay, names: [
                    "\(getLanguage())/nhanbiet/nhanbiet_inside_\(article)",
                    "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
                ])
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: KUButton) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == meIndex {
            if let svgView = sender.viewWithStringTag("svg_view") {
                animateCoinIfCorrect(view: svgView)
            }
            let delay = playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
