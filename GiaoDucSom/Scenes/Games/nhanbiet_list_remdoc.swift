//
//  nhanbiet_list_remdoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate

class nhanbiet_list_remdoc: NhanBietGameFragment {
    // MARK: - Properties
    private var svgList: [SVGKImage] = []
    private var svg: SVGKImage?
    private var svgView: SVGImageView!
    private var svgView1: SVGImageView!
    private var svgView2: SVGImageView!
    private var svgView3: SVGImageView!
    private var curtain1: UIImageView!
    private var itemContainer: UIView!
    private var rightView: UIView!
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#F9B24B")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.backgroundColor = UIColor(hex: "#110E00")
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        itemContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.height) // Ratio 1:1
            make.height.equalToSuperview().multipliedBy(0.38)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.2) // Vertical bias 0.6
        }        
        
        curtain1 = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_curtain2"))
        curtain1.contentMode = .scaleToFill
        itemContainer.addSubview(curtain1)
        curtain1.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(1.0)
            make.top.equalToSuperview()
        }
        
        let curtainBg = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_curtain1"))
        itemContainer.addSubview(curtainBg)
        curtainBg.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(curtainBg.snp.width).multipliedBy(324.0/1833.0) // Ratio 1833:324
            make.top.equalToSuperview()
        }
        
        rightView = UIView()
        rightView.clipsToBounds = false
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.6) // Vertical bias 0.3
        }
        
        let view1Container = createOptionView(bias: 0.05)
        view1Container.tag = 0
        svgView1 = view1Container.viewWithStringTag("svg_view") as! SVGImageView
        rightView.addSubview(view1Container)
        view1Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.centerX.equalToSuperview().multipliedBy(0.1) // Horizontal bias 0.05
            make.centerY.equalToSuperview()
        }
        
        let view2Container = createOptionView(bias: 0.5)
        view2Container.tag = 1
        svgView2 = view2Container.viewWithStringTag("svg_view") as! SVGImageView
        rightView.addSubview(view2Container)
        view2Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.centerX.equalToSuperview() // Horizontal bias 0.5
            make.centerY.equalToSuperview()
        }
        
        let view3Container = createOptionView(bias: 0.95)
        view3Container.tag = 2
        svgView3 = view3Container.viewWithStringTag("svg_view") as! SVGImageView
        rightView.addSubview(view3Container)
        view3Container.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview()
            make.centerX.equalToSuperview().multipliedBy(1.9) // Horizontal bias 0.95
            make.centerY.equalToSuperview()
        }
        
        // Add tap gesture to itemContainer for curtain
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleContainerTap))
        itemContainer.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        svgList = []
        guard let listItems = getListItems(), let folder = getFolder() else { return }
        for item in listItems {
            let svgImage = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item.path!)")
            svgList.append(svgImage)
            if item == self.getItem() {
                self.svg = svgImage
            }        
        }
        
        svgList.shuffle()
        meIndex = svgList.firstIndex(where: { $0 === svg }) ?? 0
        
        svgView.image = svg?.uiImage
        svgView1.image = svgList[0].uiImage
        svgView2.image = svgList.count > 1 ? svgList[1].uiImage : nil
        svgView3.image = svgList.count > 2 ? svgList[2].uiImage : nil
        
        svgView1.superview?.tag = 0
        svgView2.superview?.tag = 1
        svgView3.superview?.tag = 2
        
        hide(view: svgView1)
        hide(view: svgView2)
        hide(view: svgView3)
        
        let soundPath = getLanguage() == "vi" ? "vi/nhanbiet/nhanbiet_curtain1" : "en/nhanbiet/nhanbiet_curtain"
        let delay = playSound(delay: 0, names: [soundPath])
        scheduler.schedule(after: delay) { [weak self] in
            self?.closeCurtain()
        }
        scheduler.schedule(after: delay + 0.7) { [weak self] in
            guard let self = self else { return }
            self.show(view: self.svgView1, index: 0)
            self.show(view: self.svgView2, index: 1)
            self.show(view: self.svgView3, index: 2)
            scheduler.schedule(delay: 1.6) {
                [weak self] in
                guard let self = self else { return }
                self.startGame()
            }            
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let soundPath = getLanguage() == "vi" ? "vi/nhanbiet/nhanbiet_curtain1" : "en/nhanbiet/nhanbiet_curtain"
            let delay = playSound(soundPath)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        curtain1.transform = CGAffineTransform(translationX: 0, y: -curtain1.frame.height * 0.4)
    }
    
    // MARK: - Touch Handling
    @objc private func handleContainerTap() {
        pauseGame(stopMusic: false)
        openCurtain(full: false)
        scheduler.schedule(after: 2.0) { [weak self] in
            self?.closeCurtain()
            self?.resumeGame(startMusic: false)
        }
    }
    
    @objc private func itemClick(_ sender: UIControl) {
        if gameState != .playing { return }
        let tag = sender.tag
        if tag == meIndex {
            pauseGame(stopMusic: false)
            UIView.animate(withDuration: 0.3, delay: 0, animations: {
                self.svgView1.superview?.superview?.alpha = 0
                self.svgView1.superview?.superview?.transform = CGAffineTransform(scaleX: 0, y: 0)
            })
            UIView.animate(withDuration: 0.3, delay: 0.1, animations: {
                self.svgView2.superview?.superview?.alpha = 0
                self.svgView2.superview?.superview?.transform = CGAffineTransform(scaleX: 0, y: 0)
            })
            UIView.animate(withDuration: 0.3, delay: 0.2, animations: {
                self.svgView3.superview?.superview?.alpha = 0
                self.svgView3.superview?.superview?.transform = CGAffineTransform(scaleX: 0, y: 0)
            })
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.openCurtain(full: true)
            }
            scheduler.schedule(after: 1.0) { [weak self] in
                self?.animateCoinIfCorrect(view: self?.svgView)
            }
            var delay: TimeInterval = 0
            if let folder = self.getFolder(), let item = self.getItem() {
                delay += self.playSound(delay: delay, names: [
                    "effect/answer_end",
                    "\(self.getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))",
                    self.getCorrectHumanSound(),
                    self.endGameSound()
                ])
            }
            scheduler.schedule(after: delay + 1.0) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            pauseGame(stopMusic: false)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createOptionView(bias: CGFloat) -> UIControl {
        let outerContainer = UIControl()
        outerContainer.clipsToBounds = false
        //outerContainer.tag = Int(bias * 100) // Temporary tag, will be set later
        outerContainer.addTarget(self, action: #selector(itemClick), for: .touchUpInside)
        
        let innerContainer = UIView()
        innerContainer.isUserInteractionEnabled = false
        innerContainer.clipsToBounds = false
        outerContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 2)
        
        let maskContainer = MaskableView()
        maskContainer.setMask(mask: Utilities.SVGImage(named: "nhanbiet_mask1"))
        maskContainer.porterDuffMode = .destinationIn
        innerContainer.addSubview(maskContainer)
        maskContainer.snp.makeConstraints { make in
            make.width.equalTo(maskContainer.snp.height) // Ratio 1:1
            make.left.top.right.equalToSuperview()
        }
        
        let maskBg = UIImageView()
        maskBg.image = Utilities.SVGImage(named: "nhanbiet_mask1")
        maskContainer.addSubview(maskBg)
        maskBg.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let svgView = SVGImageView(frame: .zero)
        svgView.stringTag = "svg_view"
        svgView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        maskContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        return outerContainer
    }
    
    private func hide(view: SVGImageView) {
        guard let parent = view.superview?.superview else { return }
        parent.transform = CGAffineTransform(scaleX: 0, y: 0)
        parent.alpha = 0
    }
    
    private func show(view: SVGImageView, index: Int) {
        guard let parent = view.superview?.superview else { return }
        scheduler.schedule(after: TimeInterval(index) * 0.8) { [weak self] in
            UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0.5, options: [], animations: {
                parent.alpha = 1
                parent.transform = .identity
            })
            self?.playSound("effect/bubble\(index + 1)")
        }
    }
    
    private func closeCurtain() {
        UIView.animate(withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: [], animations: {
            self.curtain1.transform = .identity
        })
        playSound("effect/curtain")
    }
    
    private func openCurtain(full: Bool) {
        let translation = full ? -self.curtain1.frame.height : -self.curtain1.frame.height * 0.4
        UIView.animate(withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.7, initialSpringVelocity: 0.5, options: [], animations: {
            self.curtain1.transform = CGAffineTransform(translationX: 0, y: translation)
        })
        playSound("effect/curtain")
    }
}
