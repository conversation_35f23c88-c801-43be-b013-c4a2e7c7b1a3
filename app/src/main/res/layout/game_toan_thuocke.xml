<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2.0"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercent="0.05">

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_distance"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="8 cm"
            android:textColor="#74B6FF"
            app:heightRatio="0.7"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/svg_ruler_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
             />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_container"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="0"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.043"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.133"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.223"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="3"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.314"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="4"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.405"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.497"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="6"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.588"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="7"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.679"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.770"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="9"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.861"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/toan_slider_blue"
                android:tag="10"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintHorizontal_bias="0.953"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/view_line"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#74B6FF"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.01"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.74" />

        <ImageView
            android:id="@+id/image_origin"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.6"
            android:src="@drawable/toan_slider_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.15"
            app:layout_constraintHorizontal_bias="0.043"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9" />

        <ImageView
            android:id="@+id/image_drag"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/toan_slider_blue"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.15"
            app:layout_constraintHorizontal_bias="0.043"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>