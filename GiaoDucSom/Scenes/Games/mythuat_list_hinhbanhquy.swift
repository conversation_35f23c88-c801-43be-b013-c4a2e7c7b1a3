//
//  mythuat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/5/25.
//


import UIKit
import SnapKit
import SVGKit

class mythuat_list_hinhbanhquy: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: SVGKFastImageView!
    private let basicShapes = ["circle", "square", "triangle", "rectangle", "star", "heart"]
    private var meIndex: Int = 0
    private var gridLayout: MyGridView!
    private var allShapes: [String] = []
    private var size: Int = 0
    private var chooseShapes: [String] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        view.backgroundColor = UIColor(red: 255/255, green: 241/255, blue: 186/255, alpha: 1) // #FFF1BA
        
        let lineCenter = UIView()
        view.addSubview(lineCenter)
        lineCenter.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.height.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        let leftContainer = UIImageView(image: Utilities.SVGImage(named: "taptrung_nhanpizza_boy"))
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(leftContainer.snp.height).multipliedBy(1057.6 / 1183.0)
            make.right.lessThanOrEqualToSuperview().multipliedBy(0.45)
            make.right.equalToSuperview().multipliedBy(0.45).priority(.high)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.left.bottom.equalToSuperview()            
        }
        
        svgView = SVGKFastImageView(svgkImage: nil)!
        leftContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(leftContainer).multipliedBy(0.4)
            make.height.equalTo(svgView.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            self.svgView.snapToVerticalBias(verticalBias: 0.05)
            self.svgView.snapToHorizontalBias(horizontalBias: 0.8)
        }
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.left.equalTo(leftContainer.snp.right)
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        gridLayout = MyGridView()
        rightView.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 1)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let files = StorageManager.manager.list(path: "cookie shape")
        chooseShapes = basicShapes.shuffled().prefix(4).map { $0 }
        var shapes = chooseShapes + chooseShapes + chooseShapes + chooseShapes
        meIndex = Int.random(in: 0..<chooseShapes.count)
        svgView.image = Utilities.GetSVGKImage(named: "topics/2D Shapes/\(chooseShapes[meIndex]).svg")
        
        while true {
            allShapes = shapes.shuffled().prefix(9).map { $0 }
            size = allShapes.filter { $0 == chooseShapes[meIndex] }.count
            if size >= 2 && size <= 4 {
                break
            }
        }
        
        var views: [UIView] = []
        for shape in allShapes {
            let imageView = SVGKFastImageView(svgkImage: nil)!
            let shapeFiles = files.filter { $0.hasPrefix(shape) }
            if let file = shapeFiles.randomElement() {
                imageView.image = Utilities.GetSVGKImage(named: "cookie shape/\(file)")
            }
            imageView.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            imageView.addGestureRecognizer(tapGesture)
            imageView.accessibilityIdentifier = shape // Lưu shape để kiểm tra
            views.append(imageView)
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.columns = 3
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "mythuat/mythuat_hinhbanhquy", "topics/2D Shapes/\(chooseShapes[meIndex])")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("mythuat/mythuat_hinhbanhquy", "topics/2D Shapes/\(chooseShapes[meIndex])")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let imageView = gesture.view as? SVGKImageView,
              let shape = imageView.accessibilityIdentifier else { return }
        pauseGame(stopMusic: false)
        
        if shape == chooseShapes[meIndex] {
            size -= 1
            UIView.animate(withDuration: 0.2) {
                imageView.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
                imageView.alpha = 0
            }
            if size == 0 {
                animateCoinIfCorrect(view: svgView)
                let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                playSound("effect/answer_correct")
                scheduler.schedule(delay: 0.2) { [weak self] in
                    self?.resumeGame(startMusic: false)
                }
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            UIView.animate(withDuration: 0.2, animations: {
                imageView.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            }) { _ in
                imageView.transform = .identity
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}

