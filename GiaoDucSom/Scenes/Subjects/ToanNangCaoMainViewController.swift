//
//  ToanNangCaoMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class ToanNangCaoMainViewController: SubjectMainViewController {
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Toán Nâng Cao")
        titleColor = UIColor(hex: "#849BFD")
        backgroundColor = UIColor(hex: "#CFDDFF")
    }
    
    override func setupData() {
        // Section 1: Coming Soon
        let comingSoonSection = SectionData(
            header: "Coming Soon",
            items: [
                // Trong Android, ComingSoonItem không có dữ liệu cụ thể, nên tôi để trống
                // Nếu bạn muốn hiển thị một item "Coming Soon", bạn có thể tạo một loại cell mới
            ]
        )
        
        // Section 2: <PERSON><PERSON> năng đếm
        let kyNangDemSection = SectionData(
            header: "<PERSON><PERSON> năng đếm",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_demdoanday", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demdongvatnuoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demgach", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demgachkhuyet", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demhinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_lanhdiaong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demhinhquyluat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demtaytraiphai", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_dembantay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_nhadiem", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_demtiengga", isTapDocGame: false)
            ]
        )
        
        // Section 3: Suy luận hình học
        let suyLuanHinhHocSection = SectionData(
            header: "Suy luận hình học",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_banhrangxoay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_dienhop", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_tamopsango", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_chiapizza", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_quangduongchu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_bothop", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_botxu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_laphoptrong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_duongongbay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_loaibohinh", isTapDocGame: false)
            ]
        )
        
        // Section 4: Kết hợp hình và số
        let ketHopHinhVaSoSection = SectionData(
            header: "Kết hợp hình và số",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_congtraicay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_conghinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_hinhnangnhe", isTapDocGame: false)
            ]
        )
        
        // Section 5: Quy luật dãy số
        let quyLuatDaySoSection = SectionData(
            header: "Quy luật dãy số",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_dayso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_xucxac", isTapDocGame: false)
            ]
        )
        
        // Section 6: Giải quyết tình huống
        let giaiQuyetTinhHuongSection = SectionData(
            header: "Giải quyết tình huống",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_dotonhen", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_chiakeo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_cantui", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_chokeo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_tinhgionhay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_12thang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_dichuyenquediem", isTapDocGame: false)
            ]
        )
        
        // Section 7: Quy đổi
        let quyDoiSection = SectionData(
            header: "Quy đổi",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_doitien", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_doixu", isTapDocGame: false)
            ]
        )
        
        // Section 8: Lịch
        let lichSection = SectionData(
            header: "Lịch",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_songaytuan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_ngaythu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_ngaytuanthang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_ngaymai", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_tuantruoc", isTapDocGame: false)
            ]
        )
        
        // Section 9: Phân tích & Giải mã
        let phanTichGiaiMaSection = SectionData(
            header: "Phân tích & Giải mã",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_xeppheptoan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_mahoacaesar", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_quyluatchu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_thapvuongtron", isTapDocGame: false)
            ]
        )
        
        // Section 10: Tính toán số học
        let tinhToanSoHocSection = SectionData(
            header: "Tính toán số học",
            items: [
                TroChoiNgonNguItem(data: "toannangcao_list_banhxebian", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_banhxemau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_o5so", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_taopheptinhpv20", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_taopheptinhpv50", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_taopheptinhpv100", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_tongcanhtamgiac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_toanchuthappv10", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_toanchuthappv20", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_toanchuthappv50", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toannangcao_list_toanchuthappv100", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [
            // comingSoonSection, // Bỏ qua vì không có dữ liệu cụ thể
            kyNangDemSection,
            suyLuanHinhHocSection,
            ketHopHinhVaSoSection,
            quyLuatDaySoSection,
            giaiQuyetTinhHuongSection,
            quyDoiSection,
            lichSection,
            phanTichGiaiMaSection,
            tinhToanSoHocSection
        ]
    }
}
