//
//  toancoban_list_congchamtron.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_congchamtron: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var textResult: UILabel!
    private var number: Int = 0
    var a = 0
    var b = 0
    private let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 235/255, green: 250/255, blue: 251/255, alpha: 1) // #EBFAFB
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.5)
        }
        rightBg.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        let container = UIView()
        //container.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        textResult = AutosizeLabel()
        textResult.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        container.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        number = 2 + Int.random(in: 0..<8) // 2 to 9
        textResult.text = String(number)
        
        let start = max(2, number - Int.random(in: 0..<4))
        let ids = [
            "toan_congchamtron1",
            "toan_congchamtron2",
            "toan_congchamtron3",
            "toan_congchamtron4",
            "toan_congchamtron5"
        ]
        var views: [UIView] = []
        
        for i in 0..<4 {
            let value = start + i
            let a = 1 + Int.random(in: 0..<value - 1)
            let b = value - a
            if value == number {
                self.a = a
                self.b = b
            }
            let view = createItem(a: a, b: b, ids: ids)
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_cong cham tron", "topics/Numbers/\(number)")
        textResult.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.textResult.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.textResult.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_cong cham tron", "topics/Numbers/\(number)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Item Creation
    private func createItem(a: Int, b: Int, ids: [String]) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(0.95) // Ratio 0.95
        }
        
        let innerContainer = UIView()
        viewBackground.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.96) // Bias 0.46 -> centerY offset
        }
        
        let image1 = UIImageView(image: a > 5 ? Utilities.SVGImage(named: "toan_congchamtron5") : nil)
        image1.contentMode = .scaleAspectFit
        innerContainer.addSubview(image1)
        image1.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.2)
            make.height.equalTo(image1.snp.width).multipliedBy(5) // Ratio 0.2
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let image2 = UIImageView(image: a > 5 ? Utilities.SVGImage(named: ids[a - 5 - 1]) : Utilities.SVGImage(named: ids[a - 1]))
        image2.contentMode = .scaleAspectFit
        innerContainer.addSubview(image2)
        image2.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.2)
            make.height.equalTo(image2.snp.width).multipliedBy(5) // Ratio 0.2
            make.left.equalTo(image1.snp.right)
            make.centerY.equalToSuperview()
        }
        
        let textPlus = HeightRatioTextView()
        textPlus.text = "+"
        textPlus.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textPlus.font = .Freude(size: 20)
        textPlus.setHeightRatio(0.8)
        textPlus.textAlignment = .center
        innerContainer.addSubview(textPlus)
        textPlus.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.2)
            make.height.equalTo(innerContainer).multipliedBy(0.4)
            make.left.equalTo(image2.snp.right)
            make.centerY.equalToSuperview()
        }
        
        let image3 = UIImageView(image: Utilities.SVGImage(named: b >= 5 ? "toan_congchamtron5" : ids[b - 1]))
        image3.contentMode = .scaleAspectFit
        innerContainer.addSubview(image3)
        image3.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.2)
            make.height.equalTo(image3.snp.width).multipliedBy(5) // Ratio 0.2
            make.left.equalTo(textPlus.snp.right)
            make.centerY.equalToSuperview()
        }
        
        let image4 = UIImageView(image: b > 5 ? Utilities.SVGImage(named: ids[b - 5 - 1]) : nil)
        image4.contentMode = .scaleAspectFit
        innerContainer.addSubview(image4)
        image4.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.2)
            make.height.equalTo(image4.snp.width).multipliedBy(5) // Ratio 0.2
            make.left.equalTo(image3.snp.right)
            make.centerY.equalToSuperview()
        }
        
        container.tag = a + b
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        pauseGame()
        let value = view.tag
        let correct = value == number
        var delay: TimeInterval = 0
        
        if correct {
            if let innerContainer = view.subviews.first?.subviews.first {
                animateCoinIfCorrect(view: innerContainer)
            }
            delay += playSound(delay: delay, names: [
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                "topics/Numbers/\(value)",
                "toan/bằng",
                "topics/Numbers/\(a)",
                "toan/cộng",
                "topics/Numbers/\(b)",
                endGameSound()
            ])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}
