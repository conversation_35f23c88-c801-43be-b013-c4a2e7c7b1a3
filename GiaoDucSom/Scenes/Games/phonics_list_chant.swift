//
//  phonics_list_chant.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 21/6/24.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AVFAudio
import SwiftSVG

class phonics_list_chant: GameFragment {
    var textName = AutosizeLabel()
    var svgView = SVGImageView(frame: CGRectZero)
    var svgView2 = SVGImageView(frame: CGRectZero)
    var values: [String] = []
    var words: [CsvWord] = []
    let gridLayout = MyGridView()
    var music = SVGImageView(frame: .zero)
    let dotLayout = UIView()
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = .color(hex: "#7BD2FF")
        
        self.addSubview(gridLayout)
        //gridLayout.backgroundColor = .red.withAlphaComponent(0.3)
        gridLayout.makeViewCenterAndKeep(ratio: 2.5)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerY.equalToSuperview().multipliedBy(1.3)
        }
        let rightView = UIView()
        rightView.backgroundColor = .color(hex: "#7BD2FF")
        music.addSubview(rightView)
        music.backgroundColor = .color(hex: "#7BD2FF")
        rightView.snp.makeConstraints { make in
            make.left.equalTo(music.snp.centerX)
            make.centerY.equalToSuperview()
            make.height.equalTo(2000)
            make.width.equalTo(2000)
        }
        let musicImage = SVGImageView(frame: .zero)
        music.addSubview(musicImage)
        musicImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        musicImage.SVGName = "english_icon_chant"
        var lastView: UIView?
        //dotLayout.backgroundColor = .green.withAlphaComponent(0.3)
        self.addSubview(dotLayout)
        values = (game.values?.compactMap { $0.value as? String })!
        dotLayout.snp.makeConstraints { make in
            make.left.right.height.equalTo(gridLayout)
            make.bottom.equalTo(gridLayout.snp.centerY).multipliedBy(values.count == 4 ? 1 : 0.9)
        }
        for i in 0...values.count {
            let view = SVGImageView(SVGName: "chant_step2")
            view.alpha = 0
            dotLayout.addSubview(view)
            if let lastView = lastView {
                view.snp.makeConstraints { make in
                    make.centerY.equalToSuperview()
                    make.height.equalTo(view.snp.width).multipliedBy(75/190.0)
                    make.left.equalTo(lastView.snp.right)
                    make.width.equalToSuperview().multipliedBy(1.0 / Float(values.count))
                }
            } else {
                view.snp.makeConstraints { make in
                    make.centerY.equalToSuperview()
                    make.height.equalTo(view.snp.width).multipliedBy(75/190.0)
                    make.centerX.equalToSuperview{$0.snp.left}
                    make.width.equalToSuperview().multipliedBy(1.0 / Float(values.count))
                }
            }
            lastView = view
        }
        self.bringSubviewToFront(gridLayout)
    }
   
    func moveMusic(_ view:UIView){
        // SVG path data
        let svgPathData = "M0,70L0,70C0.1,33.9,42.6,4.6,95,4.6s95,29.3,95,65.4"
        
        // Parse the SVG path data to create a UIBezierPath
        let path = transformPathToFitInsideParent(UIBezierPath(pathString: svgPathData), parentViewBounds: view.bounds)
        
        // Create a UIView to animate
        
        let size = view.bounds.width / 3
        music.frame = CGRectMake(0, 0, size, size)
        let animatedView = music
        animatedView.removeFromSuperview()
        view.addSubview(animatedView)
        
        // Create the animation
        let animation = CAKeyframeAnimation(keyPath: "position")
        animation.path = path.cgPath
        animation.duration = 0.5
        
        // Optionally add other animation properties
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards
        animation.repeatCount = 1
        
        // Add the animation to the view's layer
        animatedView.layer.add(animation, forKey: "animate position along path")
        view.alpha = 1
        //dotLayout.bringSubviewToFront(view)
    }
    var startTime: Date = Date()
    var backgroundName: String = ""
    func transformPathToFitInsideParent(_ path: UIBezierPath, parentViewBounds: CGRect) -> UIBezierPath {
        // Calculate the bounding box of the path
        let pathBounds = path.bounds
        
        // Calculate the scale factors to fit the path inside the parent view bounds
        let scaleX = parentViewBounds.width / pathBounds.width
        let scaleY = parentViewBounds.height / pathBounds.height
        let scale = min(scaleX, scaleY) // Use the smaller scale factor to maintain aspect ratio

        // Calculate the translation needed to center the scaled path within the parent view bounds
        let scaledPathBounds = pathBounds.applying(CGAffineTransform(scaleX: scale, y: scale))
        let translationX = parentViewBounds.midX - scaledPathBounds.midX
        let translationY = parentViewBounds.midY - scaledPathBounds.midY
        
        // Create the final transformation
        var transform = CGAffineTransform.identity
        transform = transform.scaledBy(x: scale, y: scale)
        transform = transform.translatedBy(x: translationX, y: translationY)
        
        // Apply the transformation to the path
        let transformedPath = UIBezierPath(cgPath: path.cgPath)
        transformedPath.apply(transform)
        
        return transformedPath
    }
    override func createGame() {
        super.createGame()
        var views: [UIView] = []
        for value in values {
            let background = SVGImageView(SVGName: "btn white bg")
            let svgView = SVGImageView(frame: .zero)
            svgView.SVGName = "flashcards/\(self.game.level!)/\(value).svg"
            //svgView.backgroundColor = .red.withAlphaComponent(0.3)
            background.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.width.height.equalToSuperview().multipliedBy(0.7)
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(0.9)
            }
            views.append(background)
        }
        gridLayout.columns = values.count
        gridLayout.insetRatio = 0.001
        gridLayout.itemSpacingRatio = 0.001
        gridLayout.reloadItemViews(views: views)
        
        backgroundName = values.count ==  3 ? "en/english phonics/effects/chant_beat3" : "en/english phonics/effects/chant_beat"
        SoundPreloadManager.shared.loadSound(named: backgroundName)
        for value in values {
            SoundPreloadManager.shared.loadSound(named: "chant/\(value)")
        }
        words = parse("effects/chant_beat")
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            SoundPreloadManager.shared.playSound(named: backgroundName)
            for i in 0...values.count {
                let finalI = i
                scheduler.schedule(delay: words[i].Start - 0.4) {
                    [weak self] in
                    guard let self = self else { return }
                    if finalI < values.count {
                        scheduler.schedule(delay: 0.3) {
                            [weak self] in
                            guard let self = self else { return }
                            SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(values[finalI])")
                        }
                    }
                    moveMusic(dotLayout.subviews[finalI])
                }
            }
            for i in 0...values.count {
                let finalI = i
                scheduler.schedule(delay: words[values.count*2 + i].Start - 0.4) {
                    [weak self] in
                    guard let self = self else { return }
                    if finalI == 0 {
                        for subview in dotLayout.subviews {
                            subview.alpha = 0
                        }
                    }
                    if finalI < values.count {
                        scheduler.schedule(delay: 0.3) {
                            [weak self] in
                            guard let self = self else { return }
                            SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(values[finalI])")
                        }
                    }
                    moveMusic(dotLayout.subviews[finalI])
                }
            }
            scheduler.schedule(delay: SoundPreloadManager.shared.getDuration(named: backgroundName)) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            self.updateTime()
        }
    }
    func updateTime(){
        var pos = SoundPreloadManager.shared.getCurrentTime(named: backgroundName)
        if self.gameState != .finished {
            //print(pos)
            scheduler.schedule(delay: 0.05) {
                [weak self] in
                guard let self = self else { return }
                self.updateTime()
            }
        }
    }
    
    func printTime(index:Int){
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS" // Customize the date format as needed
        let currentTime = dateFormatter.string(from: Date())
        print("Current time is \(index): \(currentTime)")
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: "Sounds/en/english phonics/\(name)", withExtension: "csv") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for line in lines {
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 && w[1] != "Start" {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    words.append(word)
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        if texts.count == 3 {
            let minutes = Double(texts[0])!
            let seconds = Double(texts[1])!
            let miliseconds = Double(texts[2])!
            return minutes * 60.0 + seconds + miliseconds / 1000.0
        }
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
    deinit{
        SoundPreloadManager.shared.stopAll()
    }
}


class SoundPreloadManager {
    
    static let shared = SoundPreloadManager() // Singleton instance
    
    private var soundCache: [String: AVAudioPlayer] = [:]
    
    private init() {} // Private initializer for singleton
    
    // Load sound from file and cache it
    public func loadSound(named name: String) -> AVAudioPlayer? {
        guard let url = Utilities.url(soundPath: name) else {
            print("Sound file \(name) not found.")
            return nil
        }
        
        do {
            let player = try AVAudioPlayer(contentsOf: url)
            player.prepareToPlay()
            soundCache[name] = player;
            //player.numberOfLoops = -1
            return player
        } catch {
            print("Error loading sound \(name): \(error.localizedDescription)")
            return nil
        }
    }
    
    // Play sound by name with no delay
    func playSound(named name: String) {
        if let player = soundCache[name] {
            player.currentTime = 0
            player.play()
        } else if let newPlayer = loadSound(named: name) {
            soundCache[name] = newPlayer
            newPlayer.play()
        }
    }
    func getCurrentTime(named name:String)->TimeInterval{
        if let player = soundCache[name] {
            return player.currentTime
        }
        return 0
    }
    func getDuration(named name:String)->TimeInterval{
        if let player = soundCache[name] {
            return player.duration
        }
        return 0
    }
    func stopAll(){
        for player in soundCache.values {
            player.stop()
        }
        soundCache.removeAll()
    }
}
