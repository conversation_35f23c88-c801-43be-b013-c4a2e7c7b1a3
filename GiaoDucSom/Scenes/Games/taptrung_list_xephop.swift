//
//  taptrung_list_xephop.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/6/25.
//


import UIKit
import SnapKit
import SVGKit

class taptrung_list_xephop: BaseBoxGameFragment {
    // MARK: - Properties
    private var viewLeft: SVGKFastImageView!
    private var viewRight: SVGKFastImageView!
    private var blockView: BlockView!
    private var blockSampleView: BlockView!
    private var sampleContainer: UIView!
    private var mainContainer: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        
        let bgContainer = UIView()
        view.addSubview(bgContainer)
        bgContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "taptrung_box_bg"))
        bgImage.contentMode = .scaleAspectFill
        bgContainer.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.bottom.left.equalTo(self)
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        viewLeft = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "taptrung_bg_left"))!
        leftContainer.addSubview(viewLeft)
        viewLeft.makeViewCenterAndKeep(ratio: 1)
        viewLeft.snp.makeConstraints { make in
            make.left.bottom.equalToSuperview()
        }
        
        sampleContainer = UIView()
        viewLeft.addSubview(sampleContainer)
        sampleContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(sampleContainer.snp.height).multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
        }
        
        blockSampleView = BlockView(frame: .zero)
        sampleContainer.addSubview(blockSampleView)
        blockSampleView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let rightContainer = UIView()
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.bottom.right.equalTo(self)
            make.width.equalToSuperview().multipliedBy(0.25)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        viewRight = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "taptrung_bg_right"))!        
        rightContainer.addSubview(viewRight)
        viewRight.makeViewCenterAndKeep(ratio: 1)
        viewRight.snp.makeConstraints { make in
            make.right.bottom.equalToSuperview()
        }
        
        mainContainer = UIView()
        //mainContainer.clipsToChildren = false
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        blockView = BlockView(frame: .zero)
        blockView.game = self
        mainContainer.addSubview(blockView)
        blockView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        
        var d: [[Int]] = []
        while true {
            d = createArray(rows: 3 + Int.random(in: 0..<2), cols: 3 + Int.random(in: 0..<2))
            let sum = sumArray(d)
            if sum > 5 && sum < 20 && checkValidation(d) {
                break
            }
        }
        
        let newData = BlockData(currentData: Array(repeating: Array(repeating: 0, count: d[0].count), count: d.count), originData: d)
        blockView.setData(newData)
        
        waitForViewDragging {
            self.blockView.viewPreview?.alpha = 0
        }
        
        scheduler.schedule(delay: 3.0) { [weak self] in
            self?.blockView.moveToCorner(newItem: false)
        }
        
        let fullData = BlockData(currentData: d, originData: d)
        blockSampleView.setData(fullData)
        
        var delay = playSound(openGameSound(), "taptrung/xep hop")
        viewLeft.transform = CGAffineTransform(translationX: -viewLeft.frame.width, y: 0)
        viewRight.transform = CGAffineTransform(translationX: viewRight.frame.width, y: 0)
        
        UIView.animate(withDuration: 0.5, delay: 1.0, animations: {
            self.viewLeft.transform = .identity
        })
        playSound(name: "effect/slide2", delay: 1.0)
        
        scheduler.schedule(delay: (delay + 0.3)) { [weak self] in
            guard let self = self else { return }
            let tx = self.blockView.viewPreview?.frame.origin.x ?? 0
            UIView.animate(withDuration: 0.5, animations: {
                self.blockView.viewPreview?.alpha = 1
                self.blockView.viewPreview?.transform = CGAffineTransform(translationX: tx + self.viewRight.frame.width, y: 0)
                self.viewRight.transform = .identity
            })
            self.playSound("effect/slide2")
        }
        
        delay += 0.5
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("taptrung/xep hop")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Utility Methods
    private func waitForViewDragging(_ completion: @escaping () -> Void) {
        if blockView.viewDragging == nil {
            scheduler.schedule(delay: 0.01) { [weak self] in
                self?.waitForViewDragging(completion)
            }
            return
        }
        scheduler.schedule(delay: 0.01) {
            completion()
        }
    }
}
