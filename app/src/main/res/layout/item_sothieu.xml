<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <TextView
        android:id="@+id/text_number"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:autoSizeMaxTextSize="500dp"
        app:autoSizeTextType="uniform"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:text="a"
        android:textColor="#74B6FF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>