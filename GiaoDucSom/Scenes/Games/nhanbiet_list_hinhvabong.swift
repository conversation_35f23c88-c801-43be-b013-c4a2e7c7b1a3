//
//  nhanbiet_list_hinhvabong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_hinhvabong: NhanBietGameFragment {
    // MARK: - Properties
    private var svgList: [SVGKImage] = []
    private var svgBottomList: [SVGKImage] = []
    private var itemContainer: UIView!
    private var gridLayout: MyGridView!
    private var bottomGridLayout: MyGridView!
    private var svgItemMap: [SVGKImage: Item] = [:]
    private var hashToSvg: [Int: SVGKImage] = [:]
    private var coinView: UIView!
    private var currentView: SVGKFastImageView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#35373E")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        let paddingView = UIView()
        view.addSubviewWithInset(subview: paddingView, inset: 20)
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        paddingView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1.8)
        
        
        gridLayout = MyGridView()
        itemContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
            make.top.left.equalToSuperview()
        }
        
        bottomGridLayout = MyGridView()
        itemContainer.addSubview(bottomGridLayout)
        bottomGridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
            make.bottom.left.equalToSuperview()
        }
        
        itemContainer.bringSubviewToFront(gridLayout)
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        // Add pan gesture for drag
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(),
              listItems.allSatisfy({ $0.path != nil && !$0.path!.isEmpty }) else { return }
        
        svgItemMap = [:]
        svgList = []
        for listItem in listItems {
            let svgImage = Utilities.GetSVGKImage(named: "topics/\(folder)/\(listItem.path!)")
            svgList.append(svgImage)
            svgItemMap[svgImage] = listItem
        }
        guard svgList.count > 0 else { return }
        
        svgBottomList = svgList.shuffled()
        
        var views: [UIView] = []
        for (i, svg) in svgList.enumerated() {
            let svgImageView = SVGKFastImageView(svgkImage: svg)!
            svgImageView.stringTag = "svg_\(i)"
            svgImageView.tag = svg.hashValue
            views.append(svgImageView)
        }
        gridLayout.columns = views.count
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        views = []
        for (i, svg) in svgBottomList.enumerated() {
            let svgImageView = SVGImageView(frame: .zero)
            svgImageView.stringTag = "shadow_\(i)"
             let shadowImage = Utilities.GetSVGKImage(named: "topics/\(folder)/shadow/\(svgItemMap[svg]!.path!)")
            svgImageView.image = Utilities.SVGImage(named: "bg_white_circle")
            svgImageView.tag = svg.hashValue
            hashToSvg[svg.hashValue] = svg
            let imageView = SVGKFastImageView(svgkImage: shadowImage)!
            svgImageView.addSubviewWithInset(subview: imageView, inset: 0)
            views.append(svgImageView)
        }
        bottomGridLayout.columns = views.count
        bottomGridLayout.itemRatio = 1
        bottomGridLayout.itemSpacingRatio = 0.1
        bottomGridLayout.insetRatio = 0.1
        bottomGridLayout.reloadItemViews(views: views)
        
        let delay = playSound(delay: 0.5, names: ["\(getLanguage())/nhanbiet/nhanbiet_shadow"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_shadow")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    var oX = 0.0, oY = 0.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: gridLayout)
            currentView = findViewUnder(x: location.x, y: location.y) as? SVGKFastImageView
            if let currentView = currentView {
                dX = currentView.frame.minX - gesture.location(in: gridLayout).x
                dY = currentView.frame.minY - gesture.location(in: gridLayout).y
                oX = currentView.frame.minX
                oY = currentView.frame.minY
                currentView.layer.zPosition = 1
                playSound("effect/cungchoi_pick1")
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: gridLayout)
                let newX = translation.x + oX
                let newY = translation.y + oY
                currentView.frame.origin = CGPoint(x: newX, y: newY)
            }
            
        case .ended:
            if let currentView = currentView {
                var minDistance = CGFloat.greatestFiniteMagnitude
                var closestView: UIView?
                
                for i in 0..<bottomGridLayout.subviews.count {
                    let svgImageView = bottomGridLayout.subviews[i]
                    let vector = currentView.distanceFromCenterToCenter(to: svgImageView)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        closestView = svgImageView
                    }
                }
                
                if let closestView = closestView, minDistance < currentView.frame.width / 2 {
                    let item = currentView.tag
                    let bottomItem = closestView.tag
                    if item == bottomItem {
                        pauseGame(stopMusic: false)
                        currentView.tag = -1
                        closestView.tag = -1
                        currentView.moveToCenter(of: closestView, duration: 0.2) {
                            [weak self] _ in
                            guard let self = self else { return }
                        }
                        
                        var finished = true
                        for i in 0..<self.bottomGridLayout.subviews.count {
                            if self.bottomGridLayout.subviews[i].tag != -1 {
                                finished = false
                                break
                            }
                        }
                        var delay: TimeInterval = 0
                        let item = svgItemMap[hashToSvg[item]!]!
                        if let folder = self.getFolder() {
                            delay += self.playSound(delay: delay, names: [
                                "effect/word puzzle drop",
                                "effect/answer_correct",
                                "\(self.getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
                            ])
                        }
                        if finished {
                            self.stopBackgroundMusic()
                            self.animateCoinIfCorrect(view: self.coinView)
                            delay += self.playSound(delay: delay, names: ["effect/answer_end", self.getCorrectHumanSound()])
                            self.scheduler.schedule(after: delay + 1.0) { [weak self] in
                                self?.finishGame()
                            }
                        } else {
                            self.scheduler.schedule(after: 0.3) { [weak self] in
                                self?.resumeGame(startMusic: false)
                            }
                        }
                    } else {
                        resetCurrentView()
                    }
                } else {
                    resetCurrentView()
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for child in gridLayout.subviews.reversed() {
            if x >= child.frame.minX && x <= child.frame.maxX &&
               y >= child.frame.minY && y <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func resetCurrentView() {
        guard let currentView = currentView else { return }
        pauseGame(stopMusic: false)
        scheduler.schedule(after: 0.3) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
        playSound("effect/slide2")
        UIView.animate(withDuration: 0.5, animations: {
            currentView.frame.origin = CGPoint(x: self.oX, y: self.oY)
        })
    }
}
