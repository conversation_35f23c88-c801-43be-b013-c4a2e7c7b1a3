<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#80DEFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:src="@drawable/tuduy_trinhtu_bg1" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="9:4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.1">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/dest_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_trinhtu_dich2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="369:225"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toRightOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/chuonchuon_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_trinhtu_dich1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:id="@+id/item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="7:4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/view_rock"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_trinhtu_la1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.235"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.1">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/frog_container"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
                        android:id="@+id/view_animation"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:rotation="90"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>


            <ImageView
                android:id="@+id/view_end"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tuduy_trinhtu_la1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.235"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.1" />

            <com.kidsup.giaoducsom.view.MyGridLayout
                android:id="@+id/grid_layout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="5:4"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

        </com.kidsup.giaoducsom.view.PercentConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/tuduy_trinhtu_bg2"
        app:layout_constraintDimensionRatio="2688:285"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:src="@drawable/tuduy_trinhtu_bg3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2688:285"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/tuduy_trinhtu_sign_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="402:141"
        app:layout_constraintHorizontal_bias="0.01"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.99"
        app:layout_constraintWidth_percent="0.2">

        <ImageView
            android:id="@+id/view_sign_1"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/tuduy_trinhtu_sign_color1" />

        <ImageView
            android:id="@+id/view_sign_2"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/tuduy_trinhtu_sign_color2" />

        <ImageView
            android:id="@+id/view_sign_3"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/tuduy_trinhtu_sign_color3" />

        <ImageView
            android:id="@+id/check_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_trinhtu_sign_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/check_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_trinhtu_sign_check"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintHorizontal_bias="0.41"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/check_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_trinhtu_sign_check"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintHorizontal_bias="0.78"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>