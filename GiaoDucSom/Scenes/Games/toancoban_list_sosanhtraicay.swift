//
//  toancoban_list_sosanhtraicay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_sosanhtraicay: NhanBietGameFragment {
    // MARK: - Properties
    private var values1: [String] = []
    private var leftContainer: UIView!
    private var rightContainer: UIView!
    private var left: Int = 0
    private var right: Int = 0
    private var meIndex: Int = 0
    private var button1: UIView!
    private var button2: UIView!
    private var button3: UIView!
    private var buttonResult: UIImageView!
    private var textResult: UILabel!
    private var containerLayout: UIView!
    private var coinView: UIView!
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 197/255, green: 255/255, blue: 224/255, alpha: 1) // #C5FFE0
        
        containerLayout = UIView()
        containerLayout.clipsToBounds = false
        addSubviewWithPercentInset(subview: containerLayout, percentInset: 5)
        containerLayout.makeViewCenterAndKeep(ratio: 1.9)
        
        let bottomContainer = UIView()
        bottomContainer.alpha = 0
        containerLayout.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.height.equalTo(containerLayout).multipliedBy(0.4)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews{
            bottomContainer.snapToVerticalBias(verticalBias: 0.9)
            bottomContainer.alpha = 1
        }
        
        let buttonContainer = UIView()
        bottomContainer.addSubview(buttonContainer)
        buttonContainer.makeViewCenterAndKeep(ratio: 4.0)
        
        button1 = createButton(withText: ">")
        button1.tag = 0
        buttonContainer.addSubview(button1)
        button1.snp.makeConstraints { make in
            make.width.equalTo(button1.snp.height) // Ratio 1:1
            make.left.top.bottom.equalToSuperview()
        }
        
        button2 = createButton(withText: "<")
        button2.tag = 1
        buttonContainer.addSubview(button2)
        button2.snp.makeConstraints { make in
            make.width.equalTo(button2.snp.height) // Ratio 1:1
            make.top.bottom.centerX.equalToSuperview()
        }
        
        button3 = createButton(withText: "=")
        button3.tag = 2
        buttonContainer.addSubview(button3)
        button3.snp.makeConstraints { make in
            make.width.equalTo(button3.snp.height) // Ratio 1:1
            make.right.top.bottom.equalToSuperview()
        }
        
        let topContainer = UIView()
        containerLayout.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.height.equalTo(containerLayout).multipliedBy(0.4)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews{
            topContainer.snapToVerticalBias(verticalBias: 0.1)
        }
        
        let middleContainer = UIView()
        topContainer.addSubview(middleContainer)
        middleContainer.makeViewCenterAndKeep(ratio: 4.0)
        
        leftContainer = UIView()
        //leftContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        middleContainer.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(leftContainer.snp.height) // Ratio 1:1
            make.centerY.top.bottom.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.leftContainer.snapToHorizontalBias(horizontalBias: 0.05)
        }
        
        let middleView = UIImageView()
        middleView.image = Utilities.SVGImage(named: "math_sosanhqua")
        middleContainer.addSubview(middleView)
        middleView.snp.makeConstraints { make in
            make.width.equalTo(middleView.snp.height) // Ratio 1:1
            make.center.top.bottom.equalToSuperview()
        }
        
        buttonResult = UIImageView()
        buttonResult.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        buttonResult.isHidden = true
        middleView.addSubview(buttonResult)
        buttonResult.snp.makeConstraints { make in
            make.width.equalTo(buttonResult.snp.height) // Ratio 1:1
            make.center.top.bottom.equalToSuperview()
        }
        
        textResult = AutosizeLabel()
        textResult.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        buttonResult.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.height.equalTo(buttonResult).multipliedBy(1.15)
            make.centerX.equalToSuperview()
            make.left.bottom.right.equalToSuperview()
        }
        
        rightContainer = UIView()
        //rightContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        middleContainer.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(rightContainer.snp.height) // Ratio 1:1
            make.centerY.top.right.bottom.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.rightContainer.snapToHorizontalBias(horizontalBias: 0.95)
        }
        
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button1.addGestureRecognizer(tap1)
        button1.isExclusiveTouch = true // Prevents multiple taps
        button1.isUserInteractionEnabled = true
        
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button2.addGestureRecognizer(tap2)
        button1.isExclusiveTouch = true // Prevents multiple taps
        button2.isUserInteractionEnabled = true
        
        let tap3 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button3.addGestureRecognizer(tap3)
        button1.isExclusiveTouch = true // Prevents multiple taps
        button3.isUserInteractionEnabled = true
         
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let files = StorageManager.manager.list(path: "topics/Fruits/numbers")
            .filter { $0.hasSuffix(".svg") }
            .shuffled()
        var values: [Int] = []
        while true {
            left = 1 + Int.random(in: 0..<9) // 1 to 9
            right = 1 + Int.random(in: 0..<9) // 1 to 9
            if abs(left - right) <= 2 {
                values = [left, right]
                break
            }
        }
        meIndex = left > right ? 0 : left < right ? 1 : 2
        textResult.text = left > right ? ">" : left < right ? "<" : "="
        values1 = values.map { value in
            files.first { $0.hasPrefix("\(value)_") } ?? ""
        }
         
    }
    
    override func createGame() {
        super.createGame()
        let svgPaths = values1.flatMap { ["topics/Fruits/numbers/\($0)", "topics/Fruits/\($0.dropFirst(2))"] }
        let svgImages = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        
        buildItems(container: leftContainer, svgNumber: svgImages[0], svgItem: svgImages[1])
        buildItems(container: rightContainer, svgNumber: svgImages[2], svgItem: svgImages[3])
        
        var delay = playSound(openGameSound(), "toan/toan_so sanh qua")
        let buttons = [button1!, button2!, button3!]
        for (i, button) in buttons.enumerated() {
            button.transform = CGAffineTransform(scaleX: 0, y: 0)
            button.alpha = 0
            UIView.animate(withDuration: 0.5, delay: delay) {
                button.alpha = 1
                button.transform = .identity
            }
            delay += playSound(name: "effect/bubble\(i + 1)", delay: delay)
            delay += 0.4
        }
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so sanh qua")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func buildItems(container: UIView, svgNumber: SVGKImage, svgItem: SVGKImage) {
        for i in 0..<svgNumber.caLayerTree.sublayers!.count {
            let bounds = svgNumber.caLayerTree.sublayers![i].shapeContentBounds!
            let width = svgNumber.size.width
            let height = svgNumber.size.height
            let item = UIImageView(image: svgItem.uiImage)
            item.contentMode = .scaleAspectFit
            container.addSubview(item)
            item.snp.makeConstraints { make in
                make.width.equalTo(container).multipliedBy(bounds.width / width)
                make.height.equalTo(container).multipliedBy(bounds.height / height)
                make.right.equalToSuperview().multipliedBy(bounds.maxX / width)
                make.bottom.equalToSuperview().multipliedBy(bounds.maxY / height)
            }
        }
    }
    
    @objc private func onButtonTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        pauseGame()
        let correct = view.tag == meIndex
        guard let textView = view.subviews.first(where: { $0 is UILabel }) as? UILabel else { return }
        
        if correct {
            animateCoinIfCorrect(view: coinView)
            buttonResult.alpha = 0
            buttonResult.isHidden = false
            textView.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            UIView.animate(withDuration: 0.2, delay: 1.0) {
                self.buttonResult.alpha = 1
            }
            UIView.animate(withDuration: 0.2, delay: 1.0) {
                view.alpha = 0
                view.transform = CGAffineTransform(scaleX: 0, y: 0)
            }
            let buttons = [button1!, button2!, button3!]
            var step = 0
            for button in buttons where button != view {
                step += 1
                UIView.animate(withDuration: 0.2, delay: 1.0 + 0.2 * Double(step)) {
                    button.alpha = 0
                    button.transform = CGAffineTransform(scaleX: 0, y: 0)
                }
            }
            UIView.animate(withDuration: 0.2, delay: 1.0 + 0.2 * Double(step) + 0.5) {
                self.leftContainer.superview?.transform = CGAffineTransform(translationX: 0, y: self.containerLayout.frame.height / 4)
            }
            let delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound(), "topics/Numbers/\(left)", left > right ? "toan/lớn hơn" : left == right ? "toan/bằng" : "toan/nhỏ hơn", "topics/Numbers/\(right)", endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            textView.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textView.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
    
    // MARK: - Helper Method
    private func createButton(withText text: String) -> UIView {
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        
        let label = AutosizeLabel()
        label.text = text
        label.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        label.font = .Freude(size: 20)
        label.textAlignment = .center
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.1
        container.addSubview(label)
        label.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(1.15)
            make.left.bottom.right.equalToSuperview()
        }
        
        return container
    }
}
