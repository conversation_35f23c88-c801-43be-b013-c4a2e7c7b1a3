//
//  toancoban_list_bia.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_bia: NhanBietGameFragment{
    // MARK: - Properties
    var numbers: [Int] = []
    var validNumbers: [Int] = []
    private var gridLayout: MyGridView!
    private let ids = [
        "math_bia1", "math_bia2", "math_bia3", "math_bia4", "math_bia5",
        "math_bia6", "math_bia7", "math_bia8", "math_bia9", "math_bia10",
        "math_bia11", "math_bia12", "math_bia13", "math_bia14", "math_bia15"
    ]
    private var textIntro: UILabel!
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 0/255, green: 139/255, blue: 40/255, alpha: 1) // #008B28
        
        let container = UIView()
        addSubviewWithPercentInset(subview: container, percentInset: 5)
        gridLayout = MyGridView()
        container.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 7.0 / 3.0)
        
        textIntro = AutosizeLabel()
        textIntro.textColor = .red
        textIntro.textAlignment = .center
        textIntro.adjustsFontSizeToFitWidth = true
        textIntro.minimumScaleFactor = 0.1
        addSubview(textIntro)
        textIntro.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.05)
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    public override func updateData() {
        super.updateData()       
        buildData()
        
        let column = 7
        let row = 3
        guard numbers.count == column * row else { return }
        
        var views: [UIView] = []
        for i in 0..<numbers.count {
            let view = createItemBia(number: numbers[i])
            view.tag = numbers[i]
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            views.append(view)
        }
        
        gridLayout.columns = column
        gridLayout.itemSpacingRatio = 0.025
        gridLayout.insetRatio = 0.025
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    // MARK: - Abstract Method
    open func buildData() {
        fatalError("Subclasses must override buildData()")
    }
    
    // MARK: - Helper Methods
    private func getNumberDrawableId(number: Int) -> String {
        var adjustedNumber = number
        while adjustedNumber > 15 {
            adjustedNumber -= 7
        }
        return ids[adjustedNumber - 1]
    }
    
    private func createItemBia(number: Int) -> UIView {
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: getNumberDrawableId(number: number))
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(number)
        textNumber.textColor = .black
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        container.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.4)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.92) // Bias 0.46
            make.left.right.equalToSuperview()
        }
        
        return container
    }
    
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let clickNumber = view.tag as? Int else { return }
        
        pauseGame(stopMusic: false)
        if validNumbers.contains(clickNumber) {
            let delay = playSound("effect/answer_correct", "topics/Numbers/\(clickNumber)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
            UIView.animate(withDuration: 0.5) {
                view.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
                view.alpha = 0
            }
            view.tag = 0 // Nullify tag
            checkFinish()
        } else {
            setGameWrong()
            let delay = playSound("topics/Numbers/\(clickNumber)", "effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    private func checkFinish() {
        for i in 0..<gridLayout.subviews.count {
            let item = gridLayout.subviews[i]
            if let tag = item.tag as? Int, tag != 0, validNumbers.contains(tag) {
                return
            }
        }
        pauseGame()
        let delay = 1.0 + playSound(delay: 1.0, names: finishCorrect1Sounds())
        animateCoinIfCorrect(view: gridLayout)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.finishGame()
        }
    }
}

