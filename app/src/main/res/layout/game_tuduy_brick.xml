<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#CCFFD1"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:id="@+id/view_bg"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/tuduy_brick_bg"></ImageView>

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/view_left"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/view_center" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/view_right"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.9"
        app:layout_constraintLeft_toRightOf="@id/view_center"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_center"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.32">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_frame"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_brick_34_bg2"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="3:3.6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/view_grid"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_brick_34_bg"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="3:4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.68">

            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>