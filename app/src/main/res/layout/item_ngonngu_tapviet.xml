<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="#0f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="554:505"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginBottomPercent="0.1">

        <ImageView
            android:id="@+id/background_image"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/ngonngu_list_tapviet" />


        <com.caverock.androidsvg.SVGImageView
            app:tint="#f00"
            app:backgroundTint="#f00"
            android:id="@+id/svg_thumbnail"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.46"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35"
            app:layout_constraintWidth_percent="0.6" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</com.kidsup.giaoducsom.view.PercentConstraintLayout>