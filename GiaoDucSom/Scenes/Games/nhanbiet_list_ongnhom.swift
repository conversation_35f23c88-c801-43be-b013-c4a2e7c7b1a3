//
//  nhanbiet_list_ongnhom.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreImage

class nhanbiet_list_ongnhom: NhanBietGameFragment {
    // MARK: - Properties
    private var image: UIImage!
    private var svgView2: UIImageView!
    private var svg: SVGKImage?
    private var initRadius: Int = 0
    private var initRadiusUp: Bool = false
    private var currentRadius: Int = 0
    private var lastRadius: Int = 0
    private var viewSlider: UIImageView!
    private var bgSlider: UIImageView!
    private var coinView: UIView!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#35373F")
        
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview()
            make.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        let mainInnerView = UIView()
        mainContainer.addSubview(mainInnerView)
        mainInnerView.makeViewCenterAndKeep(ratio: 1)
        
        let viewBino = UIImageView()
        viewBino.clipsToBounds = false
        viewBino.image = Utilities.SVGImage(named: "nhanbiet_bg_binoculars2")
        mainInnerView.addSubview(viewBino)        
        viewBino.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(viewBino.snp.width).multipliedBy(1068.0 / 1837.0) // Ratio 1837:1068
        }
        
        bgSlider = UIImageView()
        bgSlider.clipsToBounds = false
        bgSlider.isUserInteractionEnabled = true
        bgSlider.image = Utilities.SVGImage(named: "nhanbiet_bg_binoculars3")
        //bgSlider.backgroundColor = .red
        bgSlider.contentMode = .scaleAspectFill
        mainInnerView.addSubview(bgSlider)
        bgSlider.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.15)
            make.height.equalTo(bgSlider.snp.width).multipliedBy(189.0 / 729.0) // Ratio 729:189
        }
        
        viewSlider = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_btn_binoculars"))
        bgSlider.addSubview(viewSlider)
        viewSlider.makeViewCenterAndKeep(ratio: 1)
        
        let maskedContainer = MaskableView()
        maskedContainer.clipsToBounds = true
        //maskedContainer.setMask(mask: Utilities.SVGImage(named: "nhanbiet_bg_binoculars2"))
        viewBino.addSubview(maskedContainer)
        maskedContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let svgContainer = UIView()
        //svgContainer.accessibilityIdentifier = "svgContainer"
        svgContainer.backgroundColor = .white
        maskedContainer.addSubview(svgContainer)
        svgContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let imageMask = UIImageView()
        imageMask.image = Utilities.SVGImage(named: "nhanbiet_bg_binoculars2")
        maskedContainer.addSubviewWithInset(subview: imageMask, inset: 0)
                
        
        svgView2 = UIImageView()
        svgContainer.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalTo(svgView2.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_binoculars"))
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.7).priority(.high)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.7)
            make.height.lessThanOrEqualTo(500)
            make.width.equalTo(bgImage.snp.height).multipliedBy(412.7 / 753.0) // Ratio 412.7:753
            make.left.equalToSuperview().inset(20)
            make.bottom.equalTo(self)
        }
        
        coinView = UIView()
        coinView.clipsToBounds = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(coinView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        bgSlider.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        svg = Utilities.GetSVGKImage(named: "topics/\(getFolder()!)/\(getItem()!.path!)")
        image = svg?.uiImage
                
        let delay = playSound(delay: 0, names:[openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_binoculars"])
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        
        initRadius = 10 + Int.random(in: 0..<15)
        initRadiusUp = Bool.random()
        lastRadius = initRadius
        currentRadius = 0
        applyBlur(radius: CGFloat(initRadius))
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_binoculars")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func removeFromSuperview() {
        super.removeFromSuperview()
        // Cleanup RenderScript equivalent (if any)
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                var newX = currentView.frame.minX + translation.x
                let newY = currentView.frame.minY + translation.y
                
                newX = max(0, min(newX, bgSlider.frame.width - currentView.frame.width))
                // newY bị bỏ qua vì slider chỉ di chuyển ngang
                currentView.frame = CGRect(
                    x: newX,
                    y: currentView.frame.minY,
                    width: currentView.frame.width,
                    height: currentView.frame.height
                )
                
                var delta = (currentView.frame.minX / (bgSlider.frame.width - viewSlider.frame.width)) * 50
                if !initRadiusUp {
                    delta = -delta
                }
                
                var radius = Int(abs(CGFloat(initRadius) + delta))
                while radius < 0 { radius += 50 }
                while radius > 50 { radius -= 50 }
                while radius <= 0 { radius += 50 }
                if radius > 25 {
                    radius = 50 - radius
                }
                
                if radius != lastRadius {
                    lastRadius = radius
                    if radius % 5 == 0 {
                        self.playGearSound()
                        Utils.vibrate()
                    }
                }
                
                applyBlur(radius: CGFloat(radius))
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if currentView != nil {
                if currentRadius < 3 {
                    pauseGame(stopMusic: false)
                    animateCoinIfCorrect(view: coinView)
                    let delay = playSound(delay: 0, names: [
                        answerCorrect1EffectSound(),
                        "\(getLanguage())/topics/\(getFolder()!)/\(getItem()?.path!.replacingOccurrences(of: ".svg", with: "") ?? "")",
                        getCorrectHumanSound()
                    ])
                    self.scheduler.schedule(after: delay + 1) { [weak self] in
                        self?.finishGame()
                    }
                    self.applyBlur(radius: 1)
                    UIView.animate(withDuration: 0.8, animations: {
                        self.svgView2.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
                    }, completion: { _ in
                        UIView.animate(withDuration: 0.8, animations: {
                            self.svgView2.transform = CGAffineTransform.identity
                        }, completion: { _ in
                            
                        })
                    })
                } else {
                    setGameWrong()
                    playSound("effect/answer_wrong")
                }
                currentView = nil
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<bgSlider.subviews.count).reversed() {
            let child = bgSlider.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func applyBlur(radius: CGFloat) {
        guard currentRadius != Int(radius) else { return }
        
        // Chuyển UIImage thành CIImage
        guard let inputImage = image?.ciImage ?? CIImage(image: image!) else { return }
        
        // Tạo bộ lọc Gaussian Blur
        let blurFilter = CIFilter(name: "CIGaussianBlur")!
        blurFilter.setValue(inputImage, forKey: kCIInputImageKey)
        blurFilter.setValue(radius, forKey: kCIInputRadiusKey)
        
        // Lấy CIImage đầu ra
        guard let outputImage = blurFilter.outputImage else { return }
        
        // Chuyển CIImage thành UIImage
        let context = CIContext(options: nil)
        if let cgImage = context.createCGImage(outputImage, from: inputImage.extent) {
            let blurredImage = UIImage(cgImage: cgImage)
            svgView2.image = blurredImage
        }
        
        currentRadius = Int(radius)
    }
    var player: AVAudioPlayer?
    func playGearSound(){
        if player == nil {
            if let url = Utilities.url(soundPath: "effect/gear2") {
                do {
                    player = try AVAudioPlayer(contentsOf: url)
                    player?.prepareToPlay()
                } catch {
                }
            }
        }
        if player != nil {
            player?.currentTime = 0
            player?.play()
        }
    }
}
