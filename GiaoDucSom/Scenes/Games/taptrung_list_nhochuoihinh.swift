//
//  taptrung_list_nhochuoihinh.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_nhochuoihinh: NhanBietGameFragment {
    // MARK: - Properties
    private var topContainer: UIImageView!
    private var bottomContainer: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var items: [Item] = []
    private var topIndexes: [Int] = []
    private var doneViews: [UIView] = []
    private var player: AVAudioPlayer?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.white
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_puzzle2"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let topPaddingView = UIView()
        view.addSubview(topPaddingView)
        topPaddingView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.4)
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.5)
        }
        topContainer = UIImageView()
        topContainer.clipsToBounds = false
        topContainer.image = Utilities.SVGImage(named: "taptrung_nhochuoi_bg1")
        topPaddingView.addSubview(topContainer)
        topContainer.makeViewCenterAndKeep(ratio: 1844.0/527.0)
        
        
        for i in 0..<4 {
            let optionView = UIImageView()
            optionView.stringTag = "svg_\(i)"
            optionView.image = Utilities.SVGImage(named: "option_bg_white_shadow")
            topContainer.addSubview(optionView)
            let bias: CGFloat = [0.025, 0.34, 0.66, 0.975][i]
            optionView.snp.makeConstraints { make in
                make.width.equalTo(optionView.snp.height).multipliedBy(346.0/373.0)
                make.height.equalToSuperview().multipliedBy(0.85)
                make.centerY.equalToSuperview()
            }
            addActionOnLayoutSubviews{
                optionView.snapToHorizontalBias(horizontalBias: bias)
            }
        }
        
        let gridLayout = UIView()
        gridLayout.clipsToBounds = false
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.4)
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.5)
        }
        
        let bottomView = UIImageView()
        gridLayout.addSubview(bottomView)
        bottomView.makeViewCenterAndKeep(ratio: 1844.0/527.0)
        
        bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        bottomContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        gridLayout.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.bottom.equalTo(bottomView)
        }
        
        for i in 0..<4 {
            let optionContainer = UIImageView()
            optionContainer.alpha = 0.01
            bottomContainer.addSubview(optionContainer)
            let bias: CGFloat = [0.17, 0.39, 0.61, 0.83][i] // Tính toán bias cho 4 ô
            optionContainer.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.85 * 346.0 / 1844.0)
                make.height.equalTo(optionContainer.snp.width).multipliedBy(373.0 / 346.0) // Ratio 346:373
                make.centerY.equalToSuperview()
            }
            addActionOnLayoutSubviews{
                optionContainer.snapToHorizontalBias(horizontalBias: bias)
            }
            let svgContainer = UIImageView()
            svgContainer.image = Utilities.SVGImage(named: "option_bg_white_shadow")
            optionContainer.addSubviewWithInset(subview: svgContainer, inset: 0)
            let svgView = SVGImageView(frame: .zero)
            svgContainer.stringTag = "\(i)"
            svgContainer.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.8)
                make.height.equalTo(svgView.snp.width) // Ratio 1:1
                make.center.equalToSuperview()
            }
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        bottomContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let folder = FlashcardsManager.shared.getPacks()
            .filter { $0.recognize == true }
            .shuffled()
            .first!
        items = folder.items
            .shuffled()
            .prefix(4)
            .map { $0 }
        topIndexes = (0..<4).shuffled()
        
        let views = bottomContainer.findSubviews(ofType: SVGImageView.self)
        for i in 0..<4 {
            let view = views[i]
            let svgPath = "topics/\(folder.folder)/\(items[topIndexes[i]].path!)"
            view.SVGName = svgPath
            view.stringTag = "svg_\(i)"
        }
    }
    
    override func createGame() {
        super.createGame()
        
        for i in 0..<topContainer.subviews.count {
            let view = topContainer.subviews[i]
            view.stringTag = "\(topIndexes[i])"
            view.isHidden = true
        }
        
        var delay = playSound(delay: 0, names: [openGameSound(), "taptrung/nho chuoi"])
        //delay += 3.0
        
        for i in 0..<bottomContainer.subviews.count {
            let view = bottomContainer.subviews[i]
            view.stringTag = "\(i)"
            let targetView = topContainer.subviews[topIndexes.firstIndex(of: i)!]
            view.isHidden = false
            delay += 0.3
            let DELAY = delay
            scheduler.schedule(delay: 0.5) {
                [weak self] in
                guard let self = self else { return }
                view.subviews[0].moveToCenter(of: targetView, duration: 0.0)
                view.alpha = 1
                scheduler.schedule(after: DELAY) {
                    UIView.animate(withDuration: 0.5) {
                        view.subviews[0].transform = .identity
                    }
                    //self.playSound("effect/slide2")
                }
            }
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("taptrung/nho chuoi")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    var zPosition = 100.0
    var originX = 0.0, originY = 0.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.layer.zPosition = zPosition
                zPosition += 1
                playPickSound()
                originX = currentView.frame.minX
                originY = currentView.frame.minY
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                let newX = currentView.frame.minX + translation.x
                let newY = currentView.frame.minY + translation.y
                currentView.frame = CGRect(
                    x: newX,
                    y: newY,
                    width: currentView.frame.width,
                    height: currentView.frame.height
                )
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if let currentView = currentView {
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for view in topContainer.subviews {
                    let vector = currentView.distanceFromCenterToCenter(to: view)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = view
                    }
                }
                
                if let targetView = targetView, minDistance < targetView.frame.height / 2 {
                    let targetTag = (targetView.stringTag ?? "0")!
                    let tag = (currentView.stringTag ?? "0")!
                    if tag == targetTag {
                        if !doneViews.contains(currentView) {
                            doneViews.append(currentView)
                            if doneViews.count == 4 {
                                pauseGame(stopMusic: false)
                                animateCoinIfCorrect(view: bottomContainer)
                                let delay = playSound(delay: 0, names: [
                                    "effect/answer_end",
                                    getCorrectHumanSound(),
                                    endGameSound()
                                ])
                                scheduler.schedule(after: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            }
                            playSound("effect/word puzzle drop")
                            currentView.moveToCenter(of: targetView, duration: 0.2)
                            self.currentView = nil
                            return
                        }
                    } else {
                        setGameWrong()
                    }
                }
                
                playSound("effect/slide2")
                UIView.animate(withDuration: 0.5) {
                    currentView.transform = .identity
                    currentView.frame = CGRectMake(self.originX, self.originY , currentView.frame.width, currentView.frame.height)
                }
                self.currentView = nil
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<bottomContainer.subviews.count).reversed() {
            let child = bottomContainer.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child.subviews[0]
            }
        }
        return nil
    }
    
    private func playPickSound() {
        if let url = Utilities.url(soundPath: "effect/cungchoi_pick\(Int.random(in: 1...2))") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.prepareToPlay()
                player?.play()
            } catch {
                print("Error playing pick sound: \(error)")
            }
        }
    }
}
