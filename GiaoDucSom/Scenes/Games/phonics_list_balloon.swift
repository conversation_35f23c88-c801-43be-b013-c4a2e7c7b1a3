//
//  phonics_list_balloon.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate

class phonics_list_balloon: GameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private let colorList: [UIColor] = [
        UIColor.color(hex: "#0593FF"),
        UIColor.color(hex: "#EE2957"),
        UIColor.color(hex: "#4BDB0D"),
        UIColor.color(hex: "#F050D8"),
        UIColor.color(hex: "#FFB901")
    ]
    private var correctCount: Int = 0
    private var lastestColor: UIColor?
    private var lastestAnswer: String?
    private var originScale: [UIView: CGFloat] = [:]
    private var coinView: UIView!
    private var svg: SVGKImage?
    private var values: [String] = []
    private var step: Int = 0
    private var answer: String = ""
    private var chooseValues: [String] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .clear
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        //view.isUserInteractionEnabled = false
        let background = UIImageView(image: Utilities.SVGImage(named: "math_5canhhoa_bg"))
        background.contentMode = .scaleAspectFill
        view.addSubview(background)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let progressBar = UIView() // Giả định include view_progress_bar
        view.addSubview(progressBar)
        progressBar.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalTo(progressBar.snp.width).multipliedBy(1.0 / 20.0) // Ratio 20:1
            make.top.equalToSuperview().offset(view.frame.height * 0.05) // Vertical bias 0.05
            make.centerX.equalToSuperview()
            make.height.lessThanOrEqualTo(20)
        }               
        
        coinView = UIView() // Giả định include item_coin_view
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        
        let testButton  = KUButton()
        testButton.backgroundColor = .red
        //view.addSubviewWithInset(subview: testButton, inset: 50)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        values = data?.value!.split(separator: ",").map { String($0) } ?? []
    }
    
    override func createGame() {
        super.createGame()
        
        let texts = parseIntroText()!
        var delay: TimeInterval = playSound(openGameSound(), delay: 0)
        for text in texts {
            delay += playSound(text, delay: delay)
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
            self?.loadNextStep()
            self?.svg = Utilities.GetSVGKImage(named: "balloon")
            self?.createItems()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let texts = parseIntroText()!
            var delay: TimeInterval = 0.5
            for text in texts {
                delay += playSound(text, delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    /*
    override func onHintClick() {
        super.onHintClick()
        if !isBusy(2) {
            playSound("games/select balloon\(data?.type ?? "")", answer)
        }
    }
     */
    
    // MARK: - Helper Methods
    private func loadNextStep() {
        pauseGame(stopMusic: false)
        setProgress(index: step, count: values.count)
        if step >= 1 {
            pauseGame(stopMusic: false)
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(delay: 0.5, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
            return
        }
        
        answer = values[step]
        step += 1
        chooseValues = [values[0]]
        correctCount = 0
        scheduler.schedule(after: 1) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    private func getNextColor() -> UIColor {
        while true {
            let color = colorList.randomElement()!
            if color != lastestColor {
                lastestColor = color
                return color
            }
        }
    }
    
    private func getNextAnswer() -> String {
        if values.count <= 2 {
            return values.randomElement()!
        }
        while true {
            let answer = values.randomElement()!
            if answer != lastestAnswer {
                lastestAnswer = answer
                return answer
            }
        }
    }
    
    func createItems(){
        if gameState == .finished {return}
        let height = itemContainer.bounds.height
        
        let apple = createView()
        let text = getNextAnswer()
        if let label = apple.viewWithTag(1) as? AutosizeLabel {
            label.text = text
        }
        if let label2 = apple.viewWithTag(5) as? AutosizeLabel {
            label2.text = text.replacingOccurrences(of: "1", with: "")
        }
        itemContainer.addSubview(apple)
        apple.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.25)
            make.width.equalTo(apple.snp.height).multipliedBy(0.8)
            make.right.equalToSuperview().multipliedBy(Double.random(in: 0.2..<0.9))
        }
        
        let scale = 1 + Double.random(in: 0..<0.5)
        apple.transform = CGAffineTransformMakeScale(scale, scale).concatenating(CGAffineTransformMakeTranslation(0, height))
        let animValues: [Double] = [height,-height*0.4]
        let timeChange = Interpolate(values: animValues,
        apply: { [weak self] (value) in
            apple.transform = CGAffineTransformMakeScale(scale, scale).concatenating(CGAffineTransformMakeTranslation(0, value))
        })
        let duration = 6/scale + Double.random(in: 0..<2)
        timeChange.animate(1, duration: duration)
        scheduler.schedule(delay: duration) {
            [weak self] in
            guard let self = self else { return }
            apple.isHidden = true
            apple.alpha = 0
            apple.removeFromSuperview()
        }
        if let button = apple.viewWithTag(2) as? UIControl {
            button.addTarget(self, action: #selector(itemClick(_:)), for: .touchDown)
        }
        
        scheduler.schedule(delay: 1 + Double.random(in: 0..<0.2)) {
            [weak self] in
            guard let self = self else { return }
            self.createItems()
        }
    }
    func createView()->UIView {
        let colorIndex = getNextColorIndex()
        let view = UIView()
        let container = UIControl()
        container.tag = 2
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 0.8)
        let img = SVGImageView(frame: .zero)
        img.contentMode = .scaleAspectFit
        container.addSubview(img)
        img.snp.makeConstraints { make in
            make.width.height.equalToSuperview().multipliedBy(2.5)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.5)
        }
        img.tag = 3
        img.image = fillIndex(index: 0, colorIndex: colorIndex)
        
        let label = AutosizeLabel()
        label.tag = 1
        label.alpha = 0.001
        label.font = .UTMAvo(size: 15)
        label.textColor = .white
        label.text = "a"
        container.addSubview(label)
        label.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95)
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        let label2 = AutosizeLabel()
        label2.tag = 5
        label2.font = .UTMAvo(size: 15)
        label2.textColor = .white
        label2.text = "a"
        container.addSubview(label2)
        label2.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95)
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        view.tag = colorIndex + 10
        return view
    }
    func fillIndex(index:Int, colorIndex: Int)->UIImage {
        let svg = svg!
        let count = svg.caLayerTree.sublayers!.count
        if let layer = svg.layer(withIdentifier: "_x31_") as? CAShapeLayer {
            layer.fillColor = colorList[colorIndex].cgColor
        }
        if let layer = svg.layer(withIdentifier: "_x32_") as? CAShapeLayer {
            layer.fillColor = colorList[colorIndex].cgColor
        }
        if let layer = svg.layer(withIdentifier: "_x33_") as? CAShapeLayer {
            layer.fillColor = colorList[colorIndex].cgColor
        }
        if let layer = svg.layer(withIdentifier: "_x34_") as? CAShapeLayer {
            layer.fillColor = colorList[colorIndex].cgColor
        }
        for i in 0..<count {
            svg.caLayerTree.sublayers![i].isHidden = index != i
        }
        return svg.uiImage
    }
    private var lastestColorIndex = 0
    
    func getNextColorIndex()->Int {
        while true {
            let color = Int.random(in: 0..<colorList.count)
            if (color != lastestColorIndex) {
                lastestColorIndex = color;
                return color;
            }
        }
    }
    
    @objc func itemClick(_ sender : UIControl){
        if let label = sender.viewWithTag(1) as? AutosizeLabel {
            let text = label.text
            playSound(name: text?.lowercased() ?? "", delay: 0.3)
            if text?.lowercased() == answer.lowercased() {
                label.superview?.isUserInteractionEnabled = false
                label.isHidden = true
                if let label2 = sender.viewWithTag(5) as? AutosizeLabel {
                    label2.isHidden = true
                }
                right(view: sender)
                //sender.animateCoin(answer: true)
                playSound("effect/balloonpop\(random(1,2,3))")
            } else {
                incorrect += 1
                //sender.animateCoin(answer: false)
                //playEffectSound("balloon_fail" + String(Int.random(in: 1...3)))
                setGameWrong()
                playSound(name: "en/english phonics/effects/balloon_fail\(random(1,2,3))", delay: 0)
                UIView.animate(withDuration: 0.1) {
                    sender.transform = CGAffineTransformMakeScale(0.6, 0.6)
                } completion: { Bool in
                    UIView.animate(
                        withDuration: 0.8,
                        delay: 0,
                        usingSpringWithDamping: 0.15,
                        initialSpringVelocity: 4,
                        options: [.curveEaseOut, .allowUserInteraction],
                        animations: {
                            sender.transform = CGAffineTransformMakeScale(1, 1)
                        }
                    )
                }
                
            }
        }
    }
    
    func right(view: UIView){
        if let img = view.viewWithTag(3) as? SVGImageView {
            let count = svg!.caLayerTree.sublayers!.count
            for i in 1..<count {
                scheduler.schedule(delay: 0.1 * Double(i)) {
                    [weak self] in
                    guard let self = self else { return }
                    img.image = self.fillIndex(index: i, colorIndex: view.superview!.tag - 10)
                }
            }
            scheduler.schedule(delay: 0.1 * Double(count)) {
                [weak self] in
                guard let self = self else { return }
                img.alpha = 0
            }
            //playEffectSound("balloonpop")
            correctCount += 1
            var totalQuestions = 6
            let stepCount = 1
            let totalProgress = Double(self.step - 1) / Double(stepCount)
            let subProgress = Double(self.correctCount) / Double(totalQuestions) / Double(stepCount)
            let progress = totalProgress + subProgress
            //self.setGameProgress(progress: progress)
            if correctCount >= totalQuestions {
                loadNextStep()
            }
        }
    }
    
    override func getSkills() -> [GameSkill] {
        return [.GameReading]
    }
    
    override func getScore() -> Float {
        return 6.0 / (6.0 + Float(incorrect))
    }
}


extension UIColor {
    func toHexString(includeAlpha: Bool = false) -> String {
        var r: CGFloat = 0
        var g: CGFloat = 0
        var b: CGFloat = 0
        var a: CGFloat = 0
        
        getRed(&r, green: &g, blue: &b, alpha: &a)
        
        let red = Int(r * 255)
        let green = Int(g * 255)
        let blue = Int(b * 255)
        let alpha = Int(a * 255)
        
        if includeAlpha {
            return String(format: "#%02X%02X%02X%02X", red, green, blue, alpha)
        } else {
            return String(format: "#%02X%02X%02X", red, green, blue)
        }
    }
}
