//
//  tuduy_list_khoigachmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 8/4/25.
//


import UIKit
import SnapKit

class tuduy_list_khoigachmau: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var gridLayout2: MyGridView!
    private var redBlock: Int = 0
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 248/255, green: 239/255, blue: 222/255, alpha: 1) // #F8EFDE
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 238/255, green: 227/255, blue: 208/255, alpha: 1) // #EEE3D0
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.6)
        }
        
        gridLayout2 = MyGridView()
        addSubview(gridLayout2)
        gridLayout2.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.4)
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        gridLayout2.transform = CGAffineTransform(translationX: 0, y: 0)
        gridLayout.alpha = 0
        
        redBlock = random(1, 2, 3)
        meIndex = Int.random(in: 0..<5)
        
        var viewsTop: [UIView] = []
        for i in 0..<4 {
            let imageView = UIImageView(image: Utilities.SVGImage(named: i < redBlock ? "toan_khoigachmau_red1" : "toan_khoigachmau_orange1"))
            imageView.contentMode = .scaleAspectFit
            viewsTop.append(imageView)
        }
        gridLayout2.columns = 4
        gridLayout2.itemRatio = 250 / 150
        gridLayout2.itemSpacingRatio = 0.1
        gridLayout2.insetRatio = 0.1
        gridLayout2.reloadItemViews(views: viewsTop)
        
        var items: [[[Int]]] = []
        for _ in 0..<5 {
            while true {
                let item = generateData()
                if !containList(lists: items, list: item) {
                    items.append(item)
                    break
                }
            }
        }
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let item = items[i]
            let view = createItemKhoiGachMau(item: item, isMeIndex: i == meIndex)
            view.tag = i
            view.isUserInteractionEnabled = true
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            //AnimationUtils.setTouchEffect(view: view)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        gridLayout.columns = items.count
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.01
        gridLayout.insetRatio = 0.01
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_khoi gach mau")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.gridLayout2.transform = .identity
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
        }
        let gridDelay = delay + 1.0 + gridLayout.showItems(startDelay: delay + 1.0)
        scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_khoi gach mau")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int else { return }
        
        pauseGame()
        if index == meIndex {
            if let firstChild = view.subviews.first {
                animateCoinIfCorrect(view: firstChild)
            }
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func containsItem(list: [[Int]], item: [Int]) -> Bool {
        list.contains { $0.elementsEqual(item) }
    }
    
    private func sameList(list1: [[Int]], list2: [[Int]]) -> Bool {
        guard list1.count == list2.count else { return false }
        return list1.elementsEqual(list2) { $0.elementsEqual($1) }
    }
    
    private func containList(lists: [[[Int]]], list: [[Int]]) -> Bool {
        lists.contains { sameList(list1: $0, list2: list) }
    }
    
    private func generateData() -> [[Int]] {
        let a = [1, 3, 6, 8]
        let b = [[2, 4, 5], [2, 4, 7], [2, 5, 7], [4, 5, 7]]
        while true {
            let list = [2, 4, 5, 7].shuffled()
            if b[0].contains(list[0]) && b[1].contains(list[1]) && b[2].contains(list[2]) && b[3].contains(list[3]) {
                return [
                    [a[0], list[0]],
                    [a[1], list[1]],
                    [a[2], list[2]],
                    [a[3], list[3]]
                ]
            }
        }
    }
    
    private func createItemKhoiGachMau(item: [[Int]], isMeIndex: Bool) -> UIView {
        let container = UIView()
        //container.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        
        let brick56 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange3"))
        brick56.tag = 3
        brick56.contentMode = .scaleAspectFit
        let contain56 = containsItem(list: item, item: [6, 5])
        brick56.isHidden = !contain56
        container.addSubview(brick56)
        brick56.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.67)
            make.width.equalTo(brick56.snp.height) // Ratio 200:200
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        brick56.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        let brick67 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        brick67.tag = 1
        brick67.contentMode = .scaleAspectFit
        let contain67 = containsItem(list: item, item: [6, 7])
        brick67.isHidden = !contain67
        container.addSubview(brick67)
        brick67.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.832)
            make.height.equalTo(brick67.snp.width).multipliedBy(150.0 / 250.0) // Ratio 250:150
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(5.0/6.0) // Bias 0.66
        }
        brick67.transform = CGAffineTransform(scaleX: 0.97, y: 0.95)
        
        let brick26 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange2"))
        brick26.tag = 2
        brick26.contentMode = .scaleAspectFit
        let contain26 = containsItem(list: item, item: [6, 2])
        brick26.isHidden = !contain26
        container.addSubview(brick26)
        brick26.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.83)
            make.width.equalTo(brick26.snp.height).multipliedBy(150.0 / 250.0) // Ratio 150:250
            make.right.equalToSuperview().multipliedBy(4.0/6.0) // Bias 0.33
            make.top.equalToSuperview()
        }
        brick26.transform = CGAffineTransform(scaleX: 0.95, y: 0.97)
        
        let container1 = UIView()
        container.addSubviewWithInset(subview: container1, inset: 0)
        let brick78 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange3"))
        brick78.tag = 3
        brick78.contentMode = .scaleAspectFit
        let contain78 = containsItem(list: item, item: [8, 7])
        brick78.isHidden = !contain78
        container1.addSubview(brick78)
        brick78.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.67)
            make.width.equalTo(brick78.snp.height) // Ratio 200:200
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        brick78.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        let brick23 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        brick23.tag = 1
        brick23.contentMode = .scaleAspectFit
        let contain23 = containsItem(list: item, item: [3, 2])
        brick23.isHidden = !contain23
        container1.addSubview(brick23)
        brick23.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.832)
            make.height.equalTo(brick23.snp.width).multipliedBy(150.0 / 250.0) // Ratio 250:150
            make.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        brick23.transform = CGAffineTransform(scaleX: 0.97, y: 0.95)
        
        let brick15 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange2"))
        brick15.tag = 2
        brick15.contentMode = .scaleAspectFit
        let contain15 = containsItem(list: item, item: [1, 5])
        brick15.isHidden = !contain15
        container1.addSubview(brick15)
        brick15.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.83)
            make.width.equalTo(brick15.snp.height).multipliedBy(150.0 / 250.0) // Ratio 150:250
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        brick15.transform = CGAffineTransform(scaleX: 0.95, y: 0.97)
        
        let container2 = UIView()
        container.addSubviewWithInset(subview: container2, inset: 0)
        let brick58 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        brick58.tag = 1
        brick58.contentMode = .scaleAspectFit
        let contain58 = containsItem(list: item, item: [8, 5])
        brick58.isHidden = !contain58
        container2.addSubview(brick58)
        brick58.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.832)
            make.height.equalTo(brick58.snp.width).multipliedBy(150.0 / 250.0) // Ratio 250:150
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        brick58.transform = CGAffineTransform(scaleX: 0.97, y: 0.95)
        
        
        let brick37 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange2"))
        brick37.tag = 2
        brick37.contentMode = .scaleAspectFit
        let contain37 = containsItem(list: item, item: [3, 7])
        brick37.isHidden = !contain37
        container2.addSubview(brick37)
        brick37.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.83)
            make.width.equalTo(brick37.snp.height).multipliedBy(150.0 / 250.0) // Ratio 150:250
            make.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        brick37.transform = CGAffineTransform(scaleX: 0.95, y: 0.97)
        
        
        let brick12 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange3"))
        brick12.tag = 3
        brick12.contentMode = .scaleAspectFit
        let contain12 = containsItem(list: item, item: [1, 2])
        brick12.isHidden = !contain12
        container2.addSubview(brick12)
        brick12.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.67)
            make.width.equalTo(brick12.snp.height) // Ratio 200:200
            make.left.equalToSuperview()
            make.top.equalToSuperview()
        }
        brick12.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        let brick14 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange1"))
        brick14.tag = 1
        brick14.contentMode = .scaleAspectFit
        let contain14 = containsItem(list: item, item: [1, 4])
        brick14.isHidden = !contain14
        container.addSubview(brick14)
        brick14.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.832)
            make.height.equalTo(brick14.snp.width).multipliedBy(150.0 / 250.0) // Ratio 250:150
            make.left.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(4.0/6.0) // Bias 0.34
        }
        brick14.transform = CGAffineTransform(scaleX: 0.97, y: 0.95)
        
        
        
        let brick34 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange3"))
        brick34.tag = 3
        brick34.contentMode = .scaleAspectFit
        let contain34 = containsItem(list: item, item: [3, 4])
        brick34.isHidden = !contain34
        container.addSubview(brick34)
        brick34.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.67)
            make.width.equalTo(brick34.snp.height) // Ratio 200:200
            make.right.equalToSuperview()
            make.top.equalToSuperview()
        }
        brick34.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        
        let brick48 = UIImageView(image:  Utilities.SVGImage(named: "toan_khoigachmau_orange2"))
        brick48.tag = 2
        brick48.contentMode = .scaleAspectFit
        let contain48 = containsItem(list: item, item: [8, 4])
        brick48.isHidden = !contain48
        container.addSubview(brick48)
        brick48.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.83)
            make.width.equalTo(brick48.snp.height).multipliedBy(150.0 / 250.0) // Ratio 150:250
            make.right.equalToSuperview().multipliedBy(5.0/6.0) // Bias 0.66
            make.bottom.equalToSuperview()
        }
        brick48.transform = CGAffineTransform(scaleX: 0.95, y: 0.97)
        
        
        
        // Bring to front logic
        if contain15 && contain23 {
            brick15.superview?.bringSubviewToFront(brick15)
        }
        if contain78 && contain23 {
            brick23.superview?.bringSubviewToFront(brick23)
        }
        if contain15 && contain78 {
            brick78.superview?.bringSubviewToFront(brick78)
        }
        if contain12 && contain58 {
            brick12.superview?.bringSubviewToFront(brick12)
        }
        if contain37 && contain58 {
            brick58.superview?.bringSubviewToFront(brick58)
        }
        if contain37 && contain12 {
            brick37.superview?.bringSubviewToFront(brick37)
        }
        
        var pieces: [UIImageView] = []
        if contain12 {
            pieces.append(brick12)
        }
        if contain14 {
            pieces.append(brick14)
        }
        if contain15 {
            pieces.append(brick15)
        }
        if contain23 {
            pieces.append(brick23)
        }
        if contain34 {
            pieces.append(brick34)
        }
        if contain37 {
            pieces.append(brick37)
        }
        if contain26 {
            pieces.append(brick26)
        }
        if contain56 {
            pieces.append(brick56)
        }
        if contain67 {
            pieces.append(brick67)
        }
        if contain48 {
            pieces.append(brick48)
        }
        if contain58 {
            pieces.append(brick58)
        }
        if contain78 {
            pieces.append(brick78)
        }
        var countRed = 0
        while true {
            countRed = random(1,2,3)
            if countRed != redBlock {
                break
            }
        }
        let count = isMeIndex ? redBlock : countRed
        let indexes = (0..<4).shuffled().prefix(count).map { $0 }
        print(count)
        print(indexes)
        for j in 0..<pieces.count {
            let piece = pieces[j]
            let tag = String(piece.tag)
            if indexes.contains(j) {
                piece.image = Utilities.SVGImage(named: "toan_khoigachmau_red\(tag)")
            } else {
                piece.image =  Utilities.SVGImage(named: "toan_khoigachmau_orange\(tag)")
            }
        }
        
        return container
    }
}

