//
//  LanguageMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//

import UIKit

class LanguageMainViewController: SubjectMainViewController {
    override var backgroundMusicFile: String? {
        "bg_list8"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Ngôn ngữ")
    }
    
    override func setupData() {
        // Section 1: T<PERSON>y<PERSON><PERSON> thơ tiềm thức
        let lyricSection = SectionData(
            header: "Truyện thơ tiềm thức",
            items: [
                LyricItem(title: "Th<PERSON>", imageName: "ngonngu_list_tho"),
                LyricItem(title: "Đồng dao", imageName: "ngonngu_list_dongdao"),
                LyricItem(title: "Truyện", imageName: "ngonngu_list_truyen"),
                LyricItem(title: "<PERSON><PERSON><PERSON> đố", imageName: "ngonngu_list_caudo")
            ]
        )
        
        // Section 2: Tập đọc
        let tapDocItems = generateTapDocItems()
        let tapDocSection = SectionData(header: "Tập đọc", items: tapDocItems)
        
        // Section 3: Tập viết chữ thường
        let vietnameseAlphabet = ["A", "Ă", "Â", "B", "C", "D", "Đ", "E", "Ê", "G", "H", "I", "K", "L", "M", "N", "O", "Ô", "Ơ", "P", "Q", "R", "S", "T", "U", "Ư", "V", "X", "Y"]
        let tapVietChuThuongItems = vietnameseAlphabet.map { letter in
            TapVietItem(
                type: .write,
                text: letter,
                data: letter.lowercased() + "1",
                isFreeToPlay: letter.lowercased() == "a"
            )
        }
        let tapVietChuThuongSection = SectionData(header: "Tập viết chữ thường", items: tapVietChuThuongItems)
        
        // Section 4: Tập viết chữ hoa
        let tapVietChuHoaItems = vietnameseAlphabet.map { letter in
            TapVietItem(
                type: .write,
                text: letter,
                data: letter.lowercased() + "2",
                isFreeToPlay: letter.lowercased() == "a"
            )
        }
        let tapVietChuHoaSection = SectionData(header: "Tập viết chữ hoa", items: tapVietChuHoaItems)
        
        // Section 5: Tập viết chữ hoa kiểu 2
        let tapVietChuHoaKieu2Items = ["A", "Ă", "Â", "M", "N", "Q", "V"].map { letter in
            TapVietItem(
                type: .write,
                text: letter,
                data: letter.lowercased() + "3",
                isFreeToPlay: letter.lowercased() == "a"
            )
        }
        let tapVietChuHoaKieu2Section = SectionData(header: "Tập viết chữ hoa kiểu 2", items: tapVietChuHoaKieu2Items)
        
        // Section 6: Tập viết số kiểu 1
        let tapVietSoKieu1Items = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"].map { number in
            TapVietItem(
                type: .write,
                text: number,
                data: number,
                isFreeToPlay: false
            )
        }
        let tapVietSoKieu1Section = SectionData(header: "Tập viết số kiểu 1", items: tapVietSoKieu1Items)
        
        // Section 7: Tập viết số kiểu 2
        let tapVietSoKieu2Items = ["2", "3", "4", "5", "7"].map { number in
            TapVietItem(
                type: .write,
                text: number,
                data: number + "_2",
                isFreeToPlay: false
            )
        }
        let tapVietSoKieu2Section = SectionData(header: "Tập viết số kiểu 2", items: tapVietSoKieu2Items)
        
        // Section 8: Trò chơi ngôn ngữ
        let troChoiItems = [
            //#if DEBUG
            TroChoiNgonNguItem(data: "ngonngu_list_tho", isTapDocGame: false),
            //#endif
            TroChoiNgonNguItem(data: "ngonngu_list_tutrainghia", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_daiduong", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_rungram", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_dovattheocap", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_timchu", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_timdau", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_chonchu", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_hinhcungdauthanh", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_noitughep", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_sapxepcau", isTapDocGame: false),
            TroChoiNgonNguItem(data: "ngonngu_list_balloon", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_bee", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_crab", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_stickers", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_readtheword", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_wordpuzzle", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_unscramble", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_memory", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_listenandcheck", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_whatletteristhis", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_readandcheck", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_letterandsound", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_missingletter", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_basketball", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_whatsoundisthis", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_listenfor2", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_canyouhear", isTapDocGame: true),
            TroChoiNgonNguItem(data: "ngonngu_list_listenfor1", isTapDocGame: true)
        ]
        let troChoiSection = SectionData(header: "Trò chơi ngôn ngữ", items: troChoiItems)
        
        // Add all sections
        sections = [
            troChoiSection,
            lyricSection,
            tapDocSection,
            tapVietChuThuongSection,
            tapVietChuHoaSection,
            tapVietChuHoaKieu2Section,
            tapVietSoKieu1Section,
            tapVietSoKieu2Section,
            troChoiSection,
        ]
    }
    
    private func generateTapDocItems() -> [TapDocItem] {
        let alphabets: [[String]] = [
            ["a", "ă", "â"], ["b", "c"], ["d", "đ"], ["e", "ê"], ["g", "h"],
            ["i", "k"], ["l", "m", "n"], ["o", "ô", "ơ"], ["p", "q"],
            ["r", "s", "t"], ["u", "ư"], ["v", "x", "y"]
        ]
        
        var items: [TapDocItem] = []
        for (i, alphabet) in alphabets.enumerated() {
            for (j, letter) in alphabet.enumerated() {
                let isFree = i == 0 && j == 0
                items.append(TapDocItem(
                    name: "Chữ \(letter.uppercased())",
                    thumb: "\(letter.uppercased())\(letter.lowercased())",
                    kind: "letter",
                    letters: [letter],
                    isFreeToPlay: isFree
                ))
                items.append(TapDocItem(
                    name: "Âm \(letter.uppercased())",
                    thumb: "\(letter.uppercased())\(letter.lowercased())",
                    kind: "sound",
                    letters: [letter],
                    isFreeToPlay: isFree
                ))
            }
            let combined = alphabet.joined().uppercased()
            items.append(TapDocItem(
                name: "Ôn \(combined)",
                thumb: combined,
                kind: "practice",
                letters: alphabet,
                isFreeToPlay: false
            ))
        }
        return items
    }
}
// MARK: - Models

// MARK: - Extensions
