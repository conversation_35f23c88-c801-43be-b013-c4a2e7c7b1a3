//
//  phonics_list_listenfor2.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_listenfor2: GameFragment {
    var topView : UIView?
    var bottomGrid = MyGridView()
    var btnSound = SVGButton().then{
        $0.contentMode = .scaleAspectFit
        let image = SVGImageView(SVGName: "english_option_bg_white_shadow_intro")
        $0.addSubviewWithInset(subview: image, inset: 0)
        image.contentMode = .scaleAspectFit
    }
    private var values : [String] = []
    var meIndex = 0
    var chooseKey = ""
    var rightCount = 0
    var corrects : [UIView] = []
    override func configureLayout(_ view: UIView) {
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#7CD7FF")
        view.addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
                
        let rightView = UIView()
        rightView.backgroundColor = .color(hex: "#849BFD")
        view.addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(leftView.snp.right)
        }
        
        let leftColor = UIView()
        leftColor.backgroundColor = leftView.backgroundColor
        leftView.addSubview(leftColor)
        leftColor.snp.makeConstraints{ make in
            make.top.left.bottom.equalTo(self)
            make.right.equalToSuperview()
        }
        
        let rightColor = UIView()
        rightColor.backgroundColor = rightView.backgroundColor
        rightView.addSubview(rightColor)
        rightColor.snp.makeConstraints{ make in
            make.top.right.bottom.equalTo(self)
            make.left.equalToSuperview()
        }
                
        rightView.addSubview(btnSound)
        btnSound.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalTo(btnSound.snp.width)
            make.center.equalToSuperview()
        }
        
        leftView.addSubview(bottomGrid)
        bottomGrid.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightView.addSubview(btnSound)
        btnSound.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.6)
        }
                        
        btnSound.addTarget(self, action: #selector(soundClick), for: .touchUpInside)
    }
    @objc func soundClick(){
        playSound(name: chooseKey)
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{$0.replacingOccurrences(of: "@", with: chooseKey)})
            for i in 0..<values.count {
                delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[i])))!] : values[i].replacingOccurrences(of: "_", with: ""), delay: delay)
            }
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 4)
        let values1 = (game.values1?.compactMap { $0.value as? String })!
        var meIndex = Int.random(in: 0..<values.count)
        var foundKey = false

        for key in values1 {
            for value in values {
                if vietnamese.removeSign(value).contains("_\(key)_") {
                    chooseKey = key
                    foundKey = true
                    break
                }
            }
            if foundKey {
                break
            }
        }

        for value in values {
            if vietnamese.removeSign(value).contains("_\(chooseKey)_") {
                rightCount += 1
            }
        }
        
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{$0.replacingOccurrences(of: "@", with: chooseKey)})
        var listViews : [UIView] = []
        for i in 0..<values.count {
            let view = createGridItem()
            let label = view.viewWithTag(3) as! AutosizeLabel
            label.text = values[i].replacingOccurrences(of: "_", with: "")
            view.tag = 100 + i
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        bottomGrid.itemRatio = 57.0/23.5
        bottomGrid.itemSpacingRatio = 0.01
        bottomGrid.insetRatio = 0.03
        bottomGrid.columns = 1
        bottomGrid.reloadItemViews(views: listViews)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.bottomGrid.playSound = false
            self.bottomGrid.step = 1.5
            self.bottomGrid.showItems()
            for i in 0..<values.count {
                self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[i])))!] : values[i].replacingOccurrences(of: "_", with: ""), delay: Double(i) * 1.5)
            }
            self.scheduler.schedule(delay: 1.5 * Double(values.count), execute: {
                [weak self] in
                guard let self = self else { return }
                self.startGame()
            })
        })
    }
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue long")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 57.0/23.5)
        let label = AutosizeLabel()
        label.tag = 3
        label.textColor = .color(hex: "#68C1FF")
        view.addSubview(label)
        label.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.6)
        }
        return view
    }
    func createIntroButton()->UIControl{
        let view = SVGButton(SVGIcon: "btn sound no bg")
        addSubview(view)
        view.snp.makeConstraints{ make in
            make.top.equalTo(20)
            make.height.equalTo(55)
            make.centerX.equalToSuperview()
            make.width.equalTo(view.snp.height).multipliedBy(96.0/18.0)
        }
        let label = AutosizeLabel()
        label.tag = 1
        view.addSubview(label)
        label.snp.makeConstraints{ make in
            make.centerX.equalToSuperview().multipliedBy(1.15)
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        label.text = "Listen for"
        label.textColor = .color(hex: "#99B6C1")
        view.addTarget(self, action: #selector(playIntro), for: .touchUpInside)
        return view
    }
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{$0.replacingOccurrences(of: "@", with: chooseKey)})
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    @objc func itemClick(_ sender : UIControl){
        let label = sender.viewWithTag(3) as! AutosizeLabel
        let index = sender.tag - 100
        let text = values[index]
        touch += 1
        if let pos = vietnamese.removeSign(text).findPosition(of: "_\(chooseKey)_") {
            correctTouch += 1
            if !corrects.contains(sender) {
                corrects.append(sender)
                label.alpha = 1
                label.textColor = .color(hex: "#73D048")
                rightCount -= 1
                if rightCount == 0 {
                    pauseGame()
                    animateCoinIfCorrect(view: sender)
                    var delay = 0.3
                    delay += self.playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()])
                    scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.finishGame()
                    })
                } else {
                    playSound(answerCorrect1EffectSound())
                }
            }
        } else {
            incorrect += 1
            setGameWrong()
            label.textColor = .color(hex: "#FF7761")
            var delay = 0.3
            delay += playSound(delay: delay, names: chooseWrongSounds())
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening]
    }
    var correctTouch = 0
    var touch = 0
    override func getScore()->Float{
        return Float(correctTouch) / Float(touch)
    }
}
