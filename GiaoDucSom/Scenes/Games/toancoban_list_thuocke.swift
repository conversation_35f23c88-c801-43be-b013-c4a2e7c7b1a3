//
//  toancoban_list_thuocke.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_thuocke: NhanBietGameFragment {
    // MARK: - Properties
    var startPoint: Int = 0
    var distance: Int = 0
    private var imageDrag: UIImageView!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var itemContainer: UIView!
    private var originX: CGFloat = 0
    private var imageOrigin: UIImageView!
    private var textDistance: UILabel!
    var snapIndex: Int = -1
    var svgRulerView: UIImageView!
    var svg: SVGKImage?
    var right: Bool?
    private var lineLayer: CAShapeLayer!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        let container = UIView()
        addSubviewWithPercentInset(subview: container, percentInset: 5)
                
        let rulerContainer = UIView()
        container.addSubview(rulerContainer)
        rulerContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        textDistance = AutosizeLabel()
        textDistance.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textDistance.font = .Freude(size: 20)
        textDistance.textAlignment = .center
        textDistance.adjustsFontSizeToFitWidth = true
        textDistance.minimumScaleFactor = 0.1
        rulerContainer.addSubview(textDistance)
        textDistance.snp.makeConstraints { make in
            make.height.equalTo(rulerContainer).multipliedBy(0.3 * 0.7) // HeightRatio 0.7
            make.top.equalToSuperview()
            make.left.right.equalToSuperview()
        }
        
        svgRulerView = UIImageView()
        svgRulerView.contentMode = .scaleAspectFit
        rulerContainer.addSubview(svgRulerView)
        svgRulerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.alpha = 0
        rulerContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let biases = [0.043, 0.133, 0.223, 0.314, 0.405, 0.497, 0.588, 0.679, 0.770, 0.861, 0.953]
        for i in 0...10 {
            let slider = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
            slider.contentMode = .scaleAspectFit
            slider.tag = i
            itemContainer.addSubview(slider)
            slider.snp.makeConstraints { make in
                make.height.equalTo(itemContainer).multipliedBy(0.15)
                make.width.equalTo(slider.snp.height) // Ratio 1:1
                make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
            }
            let bias = biases[i]
            addActionOnLayoutSubviews {
                slider.snapToHorizontalBias(horizontalBias: bias)
            }
        }
        
        imageOrigin = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
        imageOrigin.contentMode = .scaleAspectFit
        imageOrigin.alpha = 0
        rulerContainer.addSubview(imageOrigin)
        imageOrigin.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.15)
            make.width.equalTo(imageOrigin.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
        }
        
        imageDrag = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
        imageDrag.translatesAutoresizingMaskIntoConstraints = false
        imageDrag.contentMode = .scaleAspectFit
        imageDrag.alpha = 0
        rulerContainer.addSubview(imageDrag)
        imageDrag.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.15)
            make.width.equalTo(imageDrag.snp.height) // Ratio 1:1
            make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
        }
        
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.imageOrigin.snapToHorizontalBias(horizontalBias: biases[startPoint])
            self.imageDrag.snapToHorizontalBias(horizontalBias: biases[startPoint])
            self.imageOrigin.alpha = 0.6
            self.imageDrag.alpha = 1
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        imageDrag.addGestureRecognizer(panGesture)
        imageDrag.isUserInteractionEnabled = true
        
        svg = Utilities.GetSVGKImage(named: "images/toan_thuocke.svg")
        svgRulerView.image = svg?.uiImage
               
        lineLayer = CAShapeLayer()
        lineLayer.strokeColor = UIColor.init(hex: "#87D657").cgColor
        lineLayer.lineCap = .round
        lineLayer.lineJoin = .round
        rulerContainer.layer.addSublayer(lineLayer)
        
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.3)
        coinView.isUserInteractionEnabled = false
    }
    let coinView = UIView()
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        distance = 1 + Int.random(in: 0..<9) // 1 to 9
        let delay = playSound(openGameSound(), "toan/toan_thuoc ke1", "topics/Numbers/\(distance)", "toan/toan_thuoc ke2")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        snap(index: startPoint)
        textDistance.text = "\(distance) cm"
        
        guard let view = itemContainer.subviews.first(where: { $0.tag == startPoint }) else { return }
        imageOrigin.frame = view.frame
        imageDrag.frame = view.frame
        scheduler.schedule(after: 0.1) {
            [weak self] in
            guard let self = self else { return }
            originX = imageDrag.frame.minX
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_thuoc ke1", "topics/Numbers/\(distance)", "toan/toan_thuoc ke2")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    var oX = 0.0
    var oY = 0.0
    var dragSnapIndex = -1
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        if true
        {
            guard let draggedView = gesture.view else { return } // Lấy view đang được kéo (imageDrag)
            
            // Lấy vị trí di chuyển của ngón tay trong view cha
            let translation = gesture.translation(in: draggedView.superview)
            
            switch gesture.state {
            case .began:
                // Khi bắt đầu kéo, không cần làm gì đặc biệt ở đây
                dragSnapIndex = -1
                lineLayer.strokeColor = UIColor.color(hex: "#74B6FF").cgColor
                right = nil
                imageDrag.image = Utilities.SVGImage(named: "toan_slider_blue")
                playSound("effect/cungchoi_pick\(random(1,2))")
                break
                
            case .changed:
                // Khi ngón tay di chuyển, cập nhật vị trí của view
                draggedView.center = CGPoint(
                    x: draggedView.center.x + translation.x,
                    y: draggedView.center.y
                )
                // Reset translation về 0 để tránh tích lũy giá trị
                gesture.setTranslation(.zero, in: draggedView.superview)
                // Tạo đường path
                let path = UIBezierPath()
                path.move(to: CGPointMake(imageOrigin.center.x,imageOrigin.center.y - imageOrigin.frame.height))
                path.addLine(to: CGPointMake(draggedView.center.x,draggedView.center.y - draggedView.frame.height))
                
                // Gán path cho lineLayer
                lineLayer.lineWidth = imageOrigin.frame.width / 10
                lineLayer.path = path.cgPath
                //updateConnectorPosition()
                
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetIndex = -1
                for (i, subview) in itemContainer.subviews.enumerated() {
                    let distance = abs( imageDrag.distanceFromCenterToCenter(to: subview).x)
                    if distance < minDistance {
                        minDistance = distance
                        targetIndex = i
                    }
                }
                dragSnapIndex = minDistance < imageDrag.frame.width ? targetIndex : -1
                snap(index: dragSnapIndex)
                
                
            case .ended:
                // Khi thả tay, có thể thêm logic bổ sung (ví dụ: kiểm tra vị trí cuối cùng)
                if dragSnapIndex != -1 {
                    playSound("effect/word puzzle drop")
                    let targetView = itemContainer.subviews[dragSnapIndex]
                    UIView.animate(withDuration: 0.3, animations: {
                        [weak self] in
                        guard let self = self else { return }
                        self.imageDrag.center = targetView.center
                        let path = UIBezierPath()
                        path.move(to: CGPointMake(self.imageOrigin.center.x,self.imageOrigin.center.y - self.imageOrigin.frame.height))
                        path.addLine(to: CGPointMake(draggedView.center.x,draggedView.center.y - draggedView.frame.height))
                        
                        // Gán path cho lineLayer
                        self.lineLayer.lineWidth = self.imageOrigin.frame.width / 10
                        self.lineLayer.path = path.cgPath
                    })
                    
                    let dragDistance = abs(dragSnapIndex - startPoint)
                    if dragDistance == distance {
                        pauseGame()
                        animateCoinIfCorrect(view: coinView)
                        lineLayer.strokeColor = UIColor.color(hex: "#87D657").cgColor
                        let delay = 0.5 + playSound(delay: 0.5, names: finishEndSounds())
                        scheduler.schedule(delay: delay) { [weak self] in
                            self?.finishGame()
                        }
                        imageDrag.image = Utilities.SVGImage(named: "toan_slider_green")
                        right = true
                        snap(index: snapIndex)
                    } else {
                        setGameWrong()
                        lineLayer.strokeColor = UIColor.color(hex: "#FF7760").cgColor
                        let delay = 0.5 + playSound(delay: 0.5, names: ["effect/answer_wrong", getWrongHumanSound()])
                        pauseGame()
                        scheduler.schedule(delay: delay) { [weak self] in
                            self?.resumeGame()
                        }
                        imageDrag.image = Utilities.SVGImage(named: "toan_slider_red")
                        right = false
                        snap(index: snapIndex)
                    }
                    
                } else {
                    let targetView = itemContainer.subviews[0]
                    UIView.animate(withDuration: 0.3, animations: {
                        self.imageDrag.center = targetView.center
                        let path = UIBezierPath()
                        path.move(to: CGPointMake(self.imageOrigin.center.x,self.imageOrigin.center.y - self.imageOrigin.frame.height))
                        path.addLine(to: CGPointMake(draggedView.center.x,draggedView.center.y - draggedView.frame.height))
                        
                        // Gán path cho lineLayer
                        self.lineLayer.lineWidth = self.imageOrigin.frame.width / 10
                        self.lineLayer.path = path.cgPath
                    })
                }
                break
                
            default:
                break
            }
        }
    }
   
    // MARK: - Snap Logic
    func snap(index: Int) {
        print(index)
        guard let svg = svg else {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.snap(index: index)
            }
            return
        }
        
        if index != snapIndex || right != nil {
            //Utils.vibrate(context: self)
            for i in 0...10 {
                let color = index == i ? (right == nil ? UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) : right! ? UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) : UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1)) : UIColor(red: 145/255, green: 162/255, blue: 174/255, alpha: 1) // #74B6FF, #87D657, #FF7760, #91A2AE
                (svg.caLayerTree.sublayers?[i + 2] as? CAShapeLayer)?.fillColor = color.cgColor
                svg.caLayerTree.sublayers?[i + 2].opacity = 1
            }
            svgRulerView.image = svg.uiImage
            snapIndex = index
        }
    }
}
