//
//  mythuat_list_vehinhcoban.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics


// MARK: - HinhCoBanGameFragment
class mythuat_list_vehinhcoban: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: SVGImageView!
    private var svgView2: SVGImageView!
    private var tracingView: TracingHinhCoBanView!
    private var itemContainer: UIView!
    private var imageBoy: UIImageView!
    private var filename: String?
    var svg: SVGKImage!
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFFFFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        while true {
            let filenames = StorageManager.manager.list(path: "draw shapes")
            filename = filenames.randomElement()
            if filename?.contains("boy") ?? false {
                continue
            }
            break
        }
        
        guard let filename = filename else { return }
        svg = Utilities.GetSVGKImage(named: "draw shapes/\(filename)")
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(1.5)
            make.width.equalTo(itemContainer.snp.height).multipliedBy(svg.size.width/svg.size.height)
            make.top.equalToSuperview()
        }
        itemContainer.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            itemContainer.snapToHorizontalBias(horizontalBias: 0.1)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        svgView.stringTag = "svg_view"
        svgView.alpha = 0.2
        itemContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        svgView2 = SVGImageView(frame: .zero)
        svgView2.contentMode = .scaleAspectFit
        svgView2.stringTag = "svg_view_2"
        itemContainer.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        tracingView = TracingHinhCoBanView()
        tracingView.stringTag = "tracing_view"
        itemContainer.addSubview(tracingView)
        tracingView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.left.equalTo(itemContainer.snp.right)
            make.right.bottom.equalToSuperview()
            make.top.equalToSuperview().inset(50)
        }
        imageBoy = UIImageView()
        imageBoy.stringTag = "image_boy"
        imageBoy.image = Utilities.SVGImage(named: "draw shapes/mythuat_boy.svg")
        imageBoy.contentMode = .scaleAspectFit
        rightView.addSubview(imageBoy)
        imageBoy.makeViewCenterAndKeep(ratio: 1)
        imageBoy.snp.makeConstraints { make in
            make.bottom.equalToSuperview()
        }

    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
            
    }
    
    override func createGame() {
        super.createGame()
        
        let layer1 = svg.caLayerTree.sublayers?.first
        svg.caLayerTree.sublayers?.forEach { layer in
            layer.isHidden = layer != layer1
        }
        svgView.image = svg.uiImage
        let layer2 = svg.caLayerTree.sublayers?[svg.caLayerTree.sublayers!.count - 2]
        svg.caLayerTree.sublayers?.forEach { layer in
            layer.isHidden = layer != layer2
        }
        svgView2.image = svg.uiImage
        tracingView.setSvg(svg)
        if let color = svg.caLayerTree.sublayers?[svg.caLayerTree.sublayers!.count - 2].getFillColor() {
            tracingView.setColor(color.uiColor!)
        }
        tracingView.setOnTracingListener {
            [weak self] in
            guard let self = self else { return }
            self.onDone()
        }
                
        imageBoy.transform = CGAffineTransform(translationX: imageBoy.frame.width, y: 0)
        let delay = playSound(delay: 0, names: ["mythuat/mythuat_hinh co ban", "\(getLanguage() ?? "vi")/topics/2D Shapes/\(filename!.replacingOccurrences(of: ".svg", with: ""))"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
            UIView.animate(withDuration: 0.5) {
                self?.imageBoy.transform = .identity
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("mythuat/mythuat_hinh co ban")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func onDone() {
        pauseGame(stopMusic: false)
        let delay = playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound()])
        animateCoinIfCorrect(view: svgView)
        scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.tracingView.alpha = 0
                self.svgView2.alpha = 0
                self.svgView.alpha = 1
                self.imageBoy.transform = CGAffineTransform(translationX: self.imageBoy.frame.width, y: 0)
            }
            //self.itemContainer.layer.anchorPoint = CGPoint(x: 0.5, y: 0)
            var tran = self.itemContainer.moveToCenterInfo(of: self.itemContainer.superview!)
            //self.itemContainer.moveToCenter(of: self.itemContainer.superview!)
            UIView.animate(withDuration: 0.5) {
                //tran.ty = 0
                tran = tran.concatenating(CGAffineTransform(scaleX: 0.66667, y: 0.66667))
                self.itemContainer.transform = tran
            }
            //VisualTree.moveElement(self.itemContainer, to: self.itemContainer.superview, translationY: 0, scale: 0.66667, duration: 0.5)
            
            var delay2: TimeInterval = 0.5
            if let filename = self.filename {
                delay2 += self.playSound(name: "\(self.getLanguage() ?? "vi")/topics/2D Shapes/\(filename.replacingOccurrences(of: ".svg", with: ""))", delay: delay2)
            }
            delay2 += 2
            self.scheduler.schedule(after: delay2) { [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
        }
    }
}

// MARK: - Helper Structs
struct PaintHinhCoBan {
    //var style: CGPaintingStyle = .stroke
    var strokeColor: CGColor = UIColor.black.cgColor
    var lineWidth: CGFloat = 1
    var lineCap: CGLineCap = .round
    var lineJoin: CGLineJoin = .round
    var lineDashPattern: [CGFloat]?
}

import UIKit
import CoreGraphics

class CGPathMeasure {
    private let path: CGPath
    private var pathElements: [PathElement] = []
    private var cumulativeLengths: [CGFloat] = []
    private var totalLength: CGFloat = 0
    
    var length: CGFloat {
        return totalLength
    }
    
    init(path: CGPath) {
        self.path = path
        analyzePath()
    }
    
    private struct PathElement {
        let type: CGPathElementType
        let points: [CGPoint]
        let length: CGFloat
        let startPoint: CGPoint
        let endPoint: CGPoint
    }
    
    private func analyzePath() {
        var currentPoint = CGPoint.zero
        var startPoint = CGPoint.zero
        var elements: [PathElement] = []
        var lengths: [CGFloat] = []
        var totalLen: CGFloat = 0
        
        path.applyWithBlock { elementPointer in
            let element = elementPointer.pointee
            let points = Array(UnsafeBufferPointer(start: element.points, count: numberOfPoints(for: element.type)))
            
            var elementLength: CGFloat = 0
            var elementStartPoint = currentPoint
            var elementEndPoint = currentPoint
            
            switch element.type {
            case .moveToPoint:
                currentPoint = points[0]
                startPoint = points[0]
                elementEndPoint = points[0]
                
            case .addLineToPoint:
                let endPoint = points[0]
                elementLength = distance(from: currentPoint, to: endPoint)
                elementEndPoint = endPoint
                currentPoint = endPoint
                
            case .addQuadCurveToPoint:
                let controlPoint = points[0]
                let endPoint = points[1]
                elementLength = quadraticBezierLength(from: currentPoint, control: controlPoint, to: endPoint)
                elementEndPoint = endPoint
                currentPoint = endPoint
                
            case .addCurveToPoint:
                let controlPoint1 = points[0]
                let controlPoint2 = points[1]
                let endPoint = points[2]
                elementLength = cubicBezierLength(from: currentPoint, control1: controlPoint1, control2: controlPoint2, to: endPoint)
                elementEndPoint = endPoint
                currentPoint = endPoint
                
            case .closeSubpath:
                elementLength = distance(from: currentPoint, to: startPoint)
                elementEndPoint = startPoint
                currentPoint = startPoint
                
            @unknown default:
                break
            }
            
            let pathElement = PathElement(
                type: element.type,
                points: points,
                length: elementLength,
                startPoint: elementStartPoint,
                endPoint: elementEndPoint
            )
            
            elements.append(pathElement)
            totalLen += elementLength
            lengths.append(totalLen)
        }
        
        self.pathElements = elements
        self.cumulativeLengths = lengths
        self.totalLength = totalLen
    }
    
    func position(at distance: CGFloat) -> CGPoint? {
        guard distance >= 0 && distance <= totalLength else { return nil }
        guard !pathElements.isEmpty else { return nil }
        
        // Find the segment that contains this distance
        var segmentIndex = 0
        var previousLength: CGFloat = 0
        
        for (index, cumulativeLength) in cumulativeLengths.enumerated() {
            if distance <= cumulativeLength {
                segmentIndex = index
                previousLength = index > 0 ? cumulativeLengths[index - 1] : 0
                break
            }
        }
        
        let element = pathElements[segmentIndex]
        let segmentDistance = distance - previousLength
        let t = element.length > 0 ? segmentDistance / element.length : 0
        
        return interpolatePoint(in: element, at: t)
    }
    
    func tangent(at distance: CGFloat) -> CGPoint? {
        guard distance >= 0 && distance <= totalLength else { return nil }
        guard !pathElements.isEmpty else { return nil }
        
        // Find the segment that contains this distance
        var segmentIndex = 0
        var previousLength: CGFloat = 0
        
        for (index, cumulativeLength) in cumulativeLengths.enumerated() {
            if distance <= cumulativeLength {
                segmentIndex = index
                previousLength = index > 0 ? cumulativeLengths[index - 1] : 0
                break
            }
        }
        
        let element = pathElements[segmentIndex]
        let segmentDistance = distance - previousLength
        let t = element.length > 0 ? segmentDistance / element.length : 0
        
        return interpolateTangent(in: element, at: t)
    }
    
    private func interpolatePoint(in element: PathElement, at t: CGFloat) -> CGPoint {
        let clampedT = max(0, min(1, t))
        
        switch element.type {
        case .moveToPoint:
            return element.startPoint
            
        case .addLineToPoint, .closeSubpath:
            return CGPoint(
                x: element.startPoint.x + (element.endPoint.x - element.startPoint.x) * clampedT,
                y: element.startPoint.y + (element.endPoint.y - element.startPoint.y) * clampedT
            )
            
        case .addQuadCurveToPoint:
            let controlPoint = element.points[0]
            return quadraticBezierPoint(
                from: element.startPoint,
                control: controlPoint,
                to: element.endPoint,
                t: clampedT
            )
            
        case .addCurveToPoint:
            let controlPoint1 = element.points[0]
            let controlPoint2 = element.points[1]
            return cubicBezierPoint(
                from: element.startPoint,
                control1: controlPoint1,
                control2: controlPoint2,
                to: element.endPoint,
                t: clampedT
            )
            
        @unknown default:
            return element.startPoint
        }
    }
    
    private func interpolateTangent(in element: PathElement, at t: CGFloat) -> CGPoint {
        let clampedT = max(0, min(1, t))
        
        switch element.type {
        case .moveToPoint:
            return CGPoint(x: 1, y: 0)
            
        case .addLineToPoint, .closeSubpath:
            let dx = element.endPoint.x - element.startPoint.x
            let dy = element.endPoint.y - element.startPoint.y
            let length = sqrt(dx * dx + dy * dy)
            return length > 0 ? CGPoint(x: dx / length, y: dy / length) : CGPoint(x: 1, y: 0)
            
        case .addQuadCurveToPoint:
            let controlPoint = element.points[0]
            return quadraticBezierTangent(
                from: element.startPoint,
                control: controlPoint,
                to: element.endPoint,
                t: clampedT
            )
            
        case .addCurveToPoint:
            let controlPoint1 = element.points[0]
            let controlPoint2 = element.points[1]
            return cubicBezierTangent(
                from: element.startPoint,
                control1: controlPoint1,
                control2: controlPoint2,
                to: element.endPoint,
                t: clampedT
            )
            
        @unknown default:
            return CGPoint(x: 1, y: 0)
        }
    }
    
    // MARK: - Helper Functions
    
    private func numberOfPoints(for type: CGPathElementType) -> Int {
        switch type {
        case .moveToPoint, .addLineToPoint:
            return 1
        case .addQuadCurveToPoint:
            return 2
        case .addCurveToPoint:
            return 3
        case .closeSubpath:
            return 0
        @unknown default:
            return 0
        }
    }
    
    private func distance(from point1: CGPoint, to point2: CGPoint) -> CGFloat {
        let dx = point2.x - point1.x
        let dy = point2.y - point1.y
        return sqrt(dx * dx + dy * dy)
    }
    
    // Quadratic Bezier functions
    private func quadraticBezierPoint(from start: CGPoint, control: CGPoint, to end: CGPoint, t: CGFloat) -> CGPoint {
        let oneMinusT = 1 - t
        let x = oneMinusT * oneMinusT * start.x + 2 * oneMinusT * t * control.x + t * t * end.x
        let y = oneMinusT * oneMinusT * start.y + 2 * oneMinusT * t * control.y + t * t * end.y
        return CGPoint(x: x, y: y)
    }
    
    private func quadraticBezierTangent(from start: CGPoint, control: CGPoint, to end: CGPoint, t: CGFloat) -> CGPoint {
        let oneMinusT = 1 - t
        let dx = 2 * oneMinusT * (control.x - start.x) + 2 * t * (end.x - control.x)
        let dy = 2 * oneMinusT * (control.y - start.y) + 2 * t * (end.y - control.y)
        let length = sqrt(dx * dx + dy * dy)
        return length > 0 ? CGPoint(x: dx / length, y: dy / length) : CGPoint(x: 1, y: 0)
    }
    
    private func quadraticBezierLength(from start: CGPoint, control: CGPoint, to end: CGPoint) -> CGFloat {
        // Approximate using multiple line segments
        let segments = 20
        var totalLength: CGFloat = 0
        var previousPoint = start
        
        for i in 1...segments {
            let t = CGFloat(i) / CGFloat(segments)
            let currentPoint = quadraticBezierPoint(from: start, control: control, to: end, t: t)
            totalLength += distance(from: previousPoint, to: currentPoint)
            previousPoint = currentPoint
        }
        
        return totalLength
    }
    
    // Cubic Bezier functions
    private func cubicBezierPoint(from start: CGPoint, control1: CGPoint, control2: CGPoint, to end: CGPoint, t: CGFloat) -> CGPoint {
        let oneMinusT = 1 - t
        let oneMinusTSquared = oneMinusT * oneMinusT
        let oneMinusTCubed = oneMinusTSquared * oneMinusT
        let tSquared = t * t
        let tCubed = tSquared * t
        
        let x = oneMinusTCubed * start.x + 3 * oneMinusTSquared * t * control1.x + 3 * oneMinusT * tSquared * control2.x + tCubed * end.x
        let y = oneMinusTCubed * start.y + 3 * oneMinusTSquared * t * control1.y + 3 * oneMinusT * tSquared * control2.y + tCubed * end.y
        
        return CGPoint(x: x, y: y)
    }
    
    private func cubicBezierTangent(from start: CGPoint, control1: CGPoint, control2: CGPoint, to end: CGPoint, t: CGFloat) -> CGPoint {
        let oneMinusT = 1 - t
        let oneMinusTSquared = oneMinusT * oneMinusT
        let tSquared = t * t
        
        let dx = 3 * oneMinusTSquared * (control1.x - start.x) + 6 * oneMinusT * t * (control2.x - control1.x) + 3 * tSquared * (end.x - control2.x)
        let dy = 3 * oneMinusTSquared * (control1.y - start.y) + 6 * oneMinusT * t * (control2.y - control1.y) + 3 * tSquared * (end.y - control2.y)
        
        let length = sqrt(dx * dx + dy * dy)
        return length > 0 ? CGPoint(x: dx / length, y: dy / length) : CGPoint(x: 1, y: 0)
    }
    
    private func cubicBezierLength(from start: CGPoint, control1: CGPoint, control2: CGPoint, to end: CGPoint) -> CGFloat {
        // Approximate using multiple line segments
        let segments = 20
        var totalLength: CGFloat = 0
        var previousPoint = start
        
        for i in 1...segments {
            let t = CGFloat(i) / CGFloat(segments)
            let currentPoint = cubicBezierPoint(from: start, control1: control1, control2: control2, to: end, t: t)
            totalLength += distance(from: previousPoint, to: currentPoint)
            previousPoint = currentPoint
        }
        
        return totalLength
    }
}


// MARK: - TracingView
class TracingHinhCoBanView: UIView {
    // MARK: - Properties
    private var player: AVAudioPlayer?
    private var onTracingDone: (() -> Void)?
    private var dashedPaint = Paint()
    private var paint = Paint()
    private var paint2 = Paint()
    private var dashed2Paint = Paint()
    private var points: [CGPoint] = []
    private var leftPoints: [CGPoint] = []
    private var paths: [CGPath] = []
    private var newPath: CGMutablePath?
    private var newPathJustCreated: Bool = false
    private var startPoint: CGPoint?
    private var startIndex: Int = 0
    private var svg: SVGKImage?
    private var path: CGPath?
    private var path2: CGMutablePath?
    private var color: UIColor = .red
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
        //wantsLayer = true
        //dashedPaint.style = .stroke
        dashedPaint.strokeColor = UIColor.white.cgColor
        dashedPaint.lineCap = .round
        dashedPaint.lineJoin = .round
        
        //paint.style = .stroke
        paint.strokeColor = UIColor(hex: "#FFB924").cgColor
        paint.lineCap = .round
        paint.lineJoin = .round
        
        //dashed2Paint.style = .stroke
        dashed2Paint.strokeColor = UIColor.red.cgColor
        dashed2Paint.lineCap = .round
        dashed2Paint.lineJoin = .round
        
        //paint2.style = .stroke
        //paint2.strokeColor = UIColor(hex: "#74B6FF").cgColor
        paint2.lineCap = .round
        paint2.lineJoin = .round
        
        playWritingSound()
    }
    func setColor(_ color: UIColor) {
        self.color = color
        paint2.strokeColor = color.cgColor
    }
    // MARK: - Public Methods
    func setOnTracingListener(_ listener: @escaping () -> Void) {
        self.onTracingDone = listener
    }
    var scale = 1.0
    func setSvg(_ svg: SVGKImage) {
        self.svg = svg
        let tenDp = bounds.width / 50
        dashedPaint.lineDashPattern = [0.5 * tenDp, 2 * tenDp]
        dashed2Paint.lineDashPattern = [0.2 * tenDp, tenDp]
        scale = frame.width / svg.size.width
        if let svgPath = svg.caLayerTree.sublayers?.last as? CAShapeLayer, let cgPath = svgPath.path {
            let point = svgPath.convert( CGRect.zero, to: svg.caLayerTree)
            let bounds = svgPath.shapeContentBounds
            path = cgPath
            
            points = []
            
            points = self.getPoints(path: UIBezierPath(cgPath: path!))
            points = points.map{CGPoint(x: (point.minX+$0.x) * self.scale, y: (point.minY+$0.y) * self.scale)}
            
            
            let distance = tenDp / 5
            var currentDistance: CGFloat = 0
            path2 = CGMutablePath()
            for i in 0..<points.count {
                if i == 0 {
                    path2?.move(to: points[i])
                } else {
                    path2?.addLine(to: points[i])
                }
            }
            leftPoints = points
            setNeedsDisplay()
        }
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        
        guard let context = UIGraphicsGetCurrentContext() else { return }
        if let path = path2 {
            /*
            context.addPath(path)
            context.setStrokeColor(paint.strokeColor)
            context.setLineWidth(paint.lineWidth)
            context.setLineCap(paint.lineCap)
            context.setLineJoin(paint.lineJoin)
            context.strokePath()
            
            
            context.addPath(path)
            context.setStrokeColor(dashedPaint.strokeColor)
            context.setLineWidth(dashedPaint.lineWidth)
            context.setLineCap(dashedPaint.lineCap)
            context.setLineJoin(dashedPaint.lineJoin)
            context.setLineDash(phase: 0, lengths: dashedPaint.lineDashPattern ?? [])
            context.strokePath()
                    
            context.setLineDash(phase: 0, lengths: [])
            */
            for path in paths {
                context.addPath(path)
                context.setStrokeColor(paint2.strokeColor)
                context.setLineWidth(paint2.lineWidth)
                context.setLineCap(paint2.lineCap)
                context.setLineJoin(paint2.lineJoin)
                context.strokePath()
            }
            
            if let newPath = newPath {
                context.addPath(newPath)
                context.setStrokeColor(paint2.strokeColor)
                context.setLineWidth(paint2.lineWidth)
                context.setLineCap(paint2.lineCap)
                context.setLineJoin(paint2.lineJoin)
                context.strokePath()
            }
        }
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            newPath = CGMutablePath()
            newPathJustCreated = true
            startPoint = nearest
            startIndex = points.firstIndex(of: nearest) ?? 0
            newPath?.move(to: nearest)
            setNeedsDisplay()
            player?.currentTime = 0
            player?.play()
            Utils.vibrate()
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        if let nearest = nearestPoint(to: point) {
            if newPath == nil {
                newPath = CGMutablePath()
                newPathJustCreated = true
                startPoint = nearest
                startIndex = points.firstIndex(of: nearest) ?? 0
                newPath?.move(to: nearest)
            } else {
                lineTo2(nearest)
            }
            setNeedsDisplay()
            if !(player?.isPlaying ?? false) {
                player?.play()
            }
        } else {
            player?.pause()
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = nil
            setNeedsDisplay()
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if let newPath = newPath, !newPathJustCreated {
            paths.append(newPath)
        }
        self.newPath = nil
        let longestContinueLeftPoints = findLongestContinueLeftPoints()
        if longestContinueLeftPoints < 20 {
            onTracingDone?()
        }
        player?.pause()
        setNeedsDisplay()
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func nearestPoint(to touch: CGPoint) -> CGPoint? {
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        for point in points {
            let distance = hypot(touch.x - point.x, touch.y - point.y)
            if distance < minDistance {
                minDistance = distance
                nearest = point
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        return nearest
    }
    
    private func lineTo2(_ point: CGPoint) {
        guard let endIndex = points.firstIndex(of: point) else { return }
        if endIndex == startIndex { return }
        
        let max = Swift.max(startIndex, endIndex)
        let min = Swift.min(startIndex, endIndex)
        let distance = Swift.min(max - min, min + points.count - max)
        
        if distance > 10 {
            if let newPath = newPath, !newPathJustCreated {
                paths.append(newPath)
            }
            self.newPath = CGMutablePath()
            self.newPath?.move(to: point)
            startIndex = endIndex
            newPathJustCreated = true
            return
        }
        
        if distance == max - min {
            if startIndex < endIndex {
                for i in (startIndex + 1)...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (endIndex...startIndex - 1).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        } else {
            if startIndex < endIndex {
                if startIndex > 0 {
                    for i in (0...(startIndex - 1)).reversed() {
                        newPath?.addLine(to: points[i])
                        leftPoints.removeAll { $0 == points[i] }
                    }
                }
                for i in (endIndex...(points.count - 1)).reversed() {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            } else {
                for i in (startIndex + 1)..<points.count {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
                for i in 0...endIndex {
                    newPath?.addLine(to: points[i])
                    leftPoints.removeAll { $0 == points[i] }
                }
            }
        }
        
        startIndex = endIndex
        newPathJustCreated = false
    }
    
    private func findLongestContinueLeftPoints() -> Int {
        var max = 0
        var count = 0
        for point in points {
            if leftPoints.contains(point) {
                count += 1
            } else {
                max = Swift.max(max, count)
                count = 0
            }
        }
        return Swift.max(max, count)
    }
    
    private func playWritingSound() {
        if let url = Bundle.main.url(forResource: "writing", withExtension: "mp3", subdirectory: "Sounds/effect") {
            do {
                player = try AVAudioPlayer(contentsOf: url)
                player?.numberOfLoops = -1
                player?.prepareToPlay()
            } catch {
                if BuildConfig.DEBUG {
                    print(error)
                }
            }
        }
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        paint.lineWidth = bounds.width / 16
        dashedPaint.lineWidth = bounds.width / 60
        paint2.lineWidth = bounds.width / 16
    }
    
    // MARK: - Helper Structs
    struct Paint {
        //var style: CGPaintingStyle = .stroke
        var strokeColor: CGColor = UIColor.black.cgColor
        var lineWidth: CGFloat = 1
        var lineCap: CGLineCap = .round
        var lineJoin: CGLineJoin = .round
        var lineDashPattern: [CGFloat]?
    }
    
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        var points: [CGPoint] = []
        let count = 500
        return path.evenlySpacedPointsUsingDash(count: count)
    }
}
