//
//  ngonngu_list_tutrainghia.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class ngonngu_list_tutrainghia: NhanBietGameFragment {
    // MARK: - Properties
    private var svgImage1: SVGImageView!
    private var svgImage2: SVGImageView!
    private var tvTitle1: HeightRatioTextView!
    private var tvTitle2: HeightRatioTextView!
    private var itemView1: KUButton!
    private var itemView2: KUButton!
    private var meIndex: Int = 0
    private var list: [Word] = []
    private var player: AVAudioPlayer?
    
    // MARK: - Word Model
    struct Word: Codable {
        let en: String
        let vi: String
        let group: Int
    }
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#DFF6F7")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftContainer = UIView()
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview()
        }
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        rightContainer.backgroundColor = UIColor.color(hex: "#EBFBFC")
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        let item1Container = UIView()
        leftContainer.addSubviewWithPercentInset(subview: item1Container, percentInset: 10)
        
        itemView1 = KUButton()
        itemView1.clipsToBounds = false
        itemView1.isUserInteractionEnabled = true
        item1Container.addSubview(itemView1)
        itemView1.makeViewCenterAndKeep(ratio: 0.8)
        
        let innerView1 = UIImageView()
        innerView1.image = Utilities.SVGImage(named: "bg_rounded_corner")
        itemView1.addSubview(innerView1)
        innerView1.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(innerView1.snp.width) // Ratio 1:1
        }
        
        svgImage1 = SVGImageView(frame: .zero)
        svgImage1.accessibilityIdentifier = "svg_image_1"
        innerView1.addSubview(svgImage1)
        svgImage1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        
        tvTitle1 = HeightRatioTextView()
        tvTitle1.setHeightRatio(0.8)
        tvTitle1.textAlignment = .center
        tvTitle1.textColor = UIColor.color(hex: "#74B6FF")
        tvTitle1.font = .Freude(size: 20)
        itemView1.addSubview(tvTitle1)
        tvTitle1.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.15)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let item2Container = UIView()
        rightContainer.addSubviewWithPercentInset(subview: item2Container, percentInset: 10)
        
        itemView2 = KUButton()
        itemView2.clipsToBounds = false
        itemView2.isUserInteractionEnabled = true
        item2Container.addSubview(itemView2)
        itemView2.makeViewCenterAndKeep(ratio: 0.8)
        
        let innerView2 = UIImageView()
        innerView2.image = Utilities.SVGImage(named: "bg_rounded_corner")
        itemView2.addSubview(innerView2)
        innerView2.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(innerView2.snp.width) // Ratio 1:1
        }
        
        svgImage2 = SVGImageView(frame: .zero)
        svgImage2.accessibilityIdentifier = "svg_image_2"
        innerView2.addSubview(svgImage2)
        svgImage2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()           
        }
        
        tvTitle2 = HeightRatioTextView()
        tvTitle2.setHeightRatio(0.8)
        tvTitle2.textAlignment = .center
        tvTitle2.textColor = UIColor.color(hex: "#74B6FF")
        tvTitle2.font = UIFont(name: "SVN-Freude", size: 20) // Giả định font size
        itemView2.addSubview(tvTitle2)
        tvTitle2.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.15)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let tapGesture1 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        itemView1.addGestureRecognizer(tapGesture1)
        let tapGesture2 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        itemView2.addGestureRecognizer(tapGesture2)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        guard let url = Bundle.main.url(forResource: "opposite", withExtension: "json") else {
            print("Error: Could not find sticker_size in the bundle")
            return
        }
        var words : [Word] = []
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            words = try decoder.decode([Word].self, from: data)
        } catch {
            print("Error decoding JSON: \(error)")
            return
        }
        let index = Int.random(in: 0..<words.count)
        let word = words[index]
        var word2: Word?
        for i in 0..<words.count {
            if words[i].group == word.group && i != index {
                word2 = words[i]
                break
            }
        }
        
        guard let word2 = word2 else {
            print("No opposite word found")
            return
        }
        
        list = [word, word2].shuffled()
        tvTitle1.text = list[0].vi
        tvTitle2.text = list[1].vi
        svgImage1.image = Utilities.GetSVGKImage(named: "opposite/\(list[0].group) \(list[0].en).svg").uiImage
        svgImage2.image = Utilities.GetSVGKImage(named: "opposite/\(list[1].group) \(list[1].en).svg").uiImage
        meIndex = Int.random(in: 0..<2)
    }
    
    override func createGame() {
        super.createGame()
        
        var delay: TimeInterval = 1.0
        delay += playSound(delay: delay, names: [openGameSound(), "ngonngu/tu trai nghia/intro1"])
        delay += 1.0
        delay += playSound(delay: delay, names: ["ngonngu/tu trai nghia/\(list[meIndex].group) \(list[meIndex].en)"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.itemView1.isUserInteractionEnabled = true
            self?.itemView2.isUserInteractionEnabled = true
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay: TimeInterval = 0.3
            delay += playSound(delay: delay, names: ["ngonngu/tu trai nghia/intro1"])
            delay += 1.0
            delay += playSound(delay: delay, names: ["ngonngu/tu trai nghia/\(list[meIndex].group) \(list[meIndex].en)"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        if view == itemView1 {
            if meIndex == 0 {
                correct(view: view)
            } else {
                wrong()
            }
        } else if view == itemView2 {
            if meIndex == 1 {
                correct(view: view)
            } else {
                wrong()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func wrong() {
        pauseGame(stopMusic: false)
        setGameWrong()
        let delay = playSound(delay: 0, names: [
            "effect/answer_wrong",
            "answer_wrong\(Int.random(in: 1...2))"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    private func correct(view: UIView) {
        pauseGame(stopMusic: false)
        animateCoinIfCorrect(view: view)
        var delay = playSound(delay: 0, names: [
            "effect/answer_correct1",
            getCorrectHumanSound(),
            "ngonngu/tu trai nghia/intro2",
            "ngonngu/tu trai nghia/\(list[meIndex].group) \(list[meIndex].en)",
            "ngonngu/tu trai nghia/intro3"])
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            let otherView = view == self.itemView1 ? self.itemView2 : self.itemView1            
            UIView.animate(withDuration: 0.2, animations: {
                otherView?.transform = CGAffineTransformMakeScale(0.8, 0.8)
            }, completion: {_ in
                UIView.animate(withDuration: 0.2) {
                    otherView?.transform = .identity
                }
            })
        }
        delay += playSound(delay: delay, names: [
            "ngonngu/tu trai nghia/\(list[1 - meIndex].group) \(list[1 - meIndex].en)",
            endGameSound()
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
}
