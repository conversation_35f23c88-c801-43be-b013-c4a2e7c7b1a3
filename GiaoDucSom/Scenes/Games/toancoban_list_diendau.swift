//
//  toancoban_list_diendau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/6/25.
//


import UIKit
import SnapKit

class toancoban_list_diendau: NhanBietGameFragment {
    // MARK: - Properties
    private var a: Int = 0
    private var b: Int = 0
    private var c: Int = 0
    private var textA: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textC: AutosizeLabel!
    private var textBSign: AutosizeLabel!
    private var textCSign: AutosizeLabel!
    private var textResult: AutosizeLabel!
    private var bSignContainer: UIImageView!
    private var cSignContainer: UIImageView!
    private var viewBPlus: UIImageView!
    private var viewBMinus: UIImageView!
    private var viewCPlus: UIImageView!
    private var viewCMinus: UIImageView!
    private var bClosed: Bool = true
    private var cClosed: Bool = true
    private var bSign: Int = 0
    private var cSign: Int = 0
    private var zPos: CGFloat = 100.0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#C5F7FF")
        
        let mainContainer = UIView()
        view.addSubview(mainContainer)
        mainContainer.makeViewCenterAndKeep(ratio: 8.0/3.2)
        
        let innerMainContainer = UIView()
        mainContainer.addSubview(innerMainContainer)
        innerMainContainer.makeViewCenterAndKeep(ratio: 8.0)
        
        let textALayout = UIView()
        innerMainContainer.addSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(textALayout.snp.width)
            make.top.bottom.equalToSuperview()
        }
        
        textA = AutosizeLabel()
        textA.text = "2"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        bSignContainer = UIImageView(image: Utilities.SVGImage(named: "math_diendau"))
        bSignContainer.isUserInteractionEnabled = true
        innerMainContainer.addSubview(bSignContainer)
        bSignContainer.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(bSignContainer.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textALayout.snp.right)
        }
        bSignContainer.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(bSignContainerTapped)))
        
        textBSign = AutosizeLabel()
        textBSign.text = "?"
        textBSign.textColor = UIColor(hex: "#74B6FF")
        textBSign.font = .Freude(size: 20)
        textBSign.textAlignment = .center
        textBSign.adjustsFontSizeToFitWidth = true
        textBSign.minimumScaleFactor = 0.1
        bSignContainer.addSubview(textBSign)
        textBSign.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewBPlus = UIImageView(image: Utilities.SVGImage(named: "math_diendau_plus"))
        viewBPlus.isUserInteractionEnabled = true
        viewBPlus.alpha = 0.01
        bSignContainer.addSubview(viewBPlus)
        viewBPlus.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        
        viewBMinus = UIImageView(image: Utilities.SVGImage(named: "math_diendau_minus"))
        viewBMinus.isUserInteractionEnabled = true
        viewBMinus.alpha = 0.01
        bSignContainer.addSubview(viewBMinus)
        viewBMinus.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        
        let textBLayout = UIView()
        innerMainContainer.addSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(textBLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(bSignContainer.snp.right)
        }
        
        textB = AutosizeLabel()
        textB.text = "4"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        cSignContainer = UIImageView(image: Utilities.SVGImage(named: "math_diendau"))
        cSignContainer.isUserInteractionEnabled = true
        innerMainContainer.addSubview(cSignContainer)
        cSignContainer.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(cSignContainer.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textBLayout.snp.right)
            make.centerX.equalToSuperview()
        }
        cSignContainer.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(cSignContainerTapped)))
        
        textCSign = AutosizeLabel()
        textCSign.text = "?"
        textCSign.textColor = UIColor(hex: "#74B6FF")
        textCSign.font = .Freude(size: 20)
        textCSign.textAlignment = .center
        textCSign.adjustsFontSizeToFitWidth = true
        textCSign.minimumScaleFactor = 0.1
        cSignContainer.addSubview(textCSign)
        textCSign.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewCPlus = UIImageView(image: Utilities.SVGImage(named: "math_diendau_plus"))
        viewCPlus.isUserInteractionEnabled = true
        viewCPlus.alpha = 0.01
        cSignContainer.addSubview(viewCPlus)
        viewCPlus.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
       
        viewCMinus = UIImageView(image: Utilities.SVGImage(named: "math_diendau_minus"))
        viewCMinus.isUserInteractionEnabled = true
        viewCMinus.alpha = 0.01
        cSignContainer.addSubview(viewCMinus)
        viewCMinus.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textCLayout = UIView()
        innerMainContainer.addSubview(textCLayout)
        textCLayout.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(textCLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(cSignContainer.snp.right)
        }
        
        textC = AutosizeLabel()
        textC.text = "3"
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 20)
        textC.textAlignment = .center
        textC.adjustsFontSizeToFitWidth = true
        textC.minimumScaleFactor = 0.1
        textCLayout.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let equalLayout = UIView()
        innerMainContainer.addSubview(equalLayout)
        equalLayout.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(equalLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textCLayout.snp.right)
        }
        
        let equalText = AutosizeLabel()
        equalText.text = "="
        equalText.textColor = UIColor(hex: "#74B6FF")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalLayout.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textResultLayout = UIView()
        innerMainContainer.addSubview(textResultLayout)
        textResultLayout.snp.makeConstraints { make in
            make.width.equalTo(innerMainContainer).multipliedBy(1.0 / 8.0)
            make.height.equalTo(textResultLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(equalLayout.snp.right)
        }
        
        textResult = AutosizeLabel()
        textResult.text = "3"
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        textResultLayout.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let viewBPlusOverlay = UIView()
        mainContainer.addSubview(viewBPlusOverlay)
        viewBPlusOverlay.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(viewBPlusOverlay.snp.width)
            make.top.equalToSuperview()
        }
        viewBPlusOverlay.waitForLayout {
            viewBPlusOverlay.snapToHorizontalBias(horizontalBias: 0.215)
        }
        
        let viewBMinusOverlay = UIView()
        mainContainer.addSubview(viewBMinusOverlay)
        viewBMinusOverlay.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(viewBMinusOverlay.snp.width)
            make.bottom.equalToSuperview()
        }
        viewBMinusOverlay.waitForLayout {
            viewBMinusOverlay.snapToHorizontalBias(horizontalBias: 0.215)
        }
        
        let viewCPlusOverlay = UIView()
        mainContainer.addSubview(viewCPlusOverlay)
        viewCPlusOverlay.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(viewCPlusOverlay.snp.width)
            make.top.equalToSuperview()
        }
        viewCPlusOverlay.waitForLayout {
            viewCPlusOverlay.snapToHorizontalBias(horizontalBias: 0.5) // Giả lập, căn giữa vì XML không rõ ràng
        }
        
        let viewCMinusOverlay = UIView()
        mainContainer.addSubview(viewCMinusOverlay)
        viewCMinusOverlay.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(viewCMinusOverlay.snp.width)
            make.bottom.equalToSuperview()
        }
        viewCMinusOverlay.waitForLayout {
            viewCMinusOverlay.snapToHorizontalBias(horizontalBias: 0.5) // Giả lập, căn giữa vì XML không rõ ràng
        }
        
        viewBPlusOverlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(bPlusTapped)))
        viewBMinusOverlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(bMinusTapped)))
        viewCPlusOverlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(cPlusTapped)))
        viewCMinusOverlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(cMinusTapped)))
    }
    
    // MARK: - Gesture Handlers
    @objc private func bSignContainerTapped() {
        playSound("effect/slide")
        if !cClosed {
            cSignToggle()
        }
        bSignToggle()
    }
    
    @objc private func cSignContainerTapped() {
        playSound("effect/slide")
        if !bClosed {
            bSignToggle()
        }
        cSignToggle()
    }
    
    @objc private func bPlusTapped() {
        if !bClosed {
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))", "toan/cộng")
            bSign = 1
            textBSign.text = "+"
            textBSign.alpha = 0.01
            bSignToggle()
            bSignContainer.image = Utilities.SVGImage(named: "math_diendau_plus")
        }
    }
    
    @objc private func bMinusTapped() {
        if !bClosed {
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))", "toan/trừ")
            bSign = -1
            textBSign.text = "-"
            textBSign.alpha = 0.01
            bSignToggle()
            bSignContainer.image = Utilities.SVGImage(named: "math_diendau_minus")
        }
    }
    
    @objc private func cPlusTapped() {
        if !cClosed {
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))", "toan/cộng")
            cSign = 1
            textCSign.text = "+"
            textCSign.alpha = 0.01
            cSignToggle()
            cSignContainer.image = Utilities.SVGImage(named: "math_diendau_plus")
        }
    }
    
    @objc private func cMinusTapped() {
        if !cClosed {
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))", "toan/trừ")
            cSign = -1
            textCSign.text = "-"
            textCSign.alpha = 0.01
            cSignToggle()
            cSignContainer.image = Utilities.SVGImage(named: "math_diendau_minus")
        }
    }
    
    // MARK: - Animation Logic
    private func bSignToggle() {
        pauseGame()
        let translationYPlus = bClosed ? -bSignContainer.frame.height * 1.1 : 0
        let translationYMinus = bClosed ? bSignContainer.frame.height * 1.1 : 0
        let alpha = bClosed ? 1.0 : 0.01
        
        UIView.animate(withDuration: 0.5, delay: 0, options: [.curveLinear]) {
            self.viewBPlus.alpha = CGFloat(alpha)
            self.viewBPlus.transform = CGAffineTransform(translationX: 0, y: translationYPlus)
            self.viewBMinus.alpha = CGFloat(alpha)
            self.viewBMinus.transform = CGAffineTransform(translationX: 0, y: translationYMinus)
        } completion: { _ in
            self.bClosed = !self.bClosed
            self.resumeGame()
            self.checkFinish()
        }
    }
    
    private func cSignToggle() {
        pauseGame()
        let translationYPlus = cClosed ? -cSignContainer.frame.height * 1.1 : 0
        let translationYMinus = cClosed ? cSignContainer.frame.height * 1.1 : 0
        let alpha = cClosed ? 1.0 : 0.01
        
        UIView.animate(withDuration: 0.5, delay: 0, options: [.curveLinear]) {
            self.viewCPlus.alpha = CGFloat(alpha)
            self.viewCPlus.transform = CGAffineTransform(translationX: 0, y: translationYPlus)
            self.viewCMinus.alpha = CGFloat(alpha)
            self.viewCMinus.transform = CGAffineTransform(translationX: 0, y: translationYMinus)
        } completion: { _ in
            self.cClosed = !self.cClosed
            self.resumeGame()
            self.checkFinish()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 1 + Int.random(in: 0..<10)
            b = Int.random(in: -10..<10)
            c = Int.random(in: -10..<10)
            if b != 0 && c != 0 && b + c != 0 && a + b > 0 && a + b + c > 0 && a + b + c < 20 {
                break
            }
        }
        
        textA.text = String(a)
        textB.text = String(abs(b))
        textC.text = String(abs(c))
        textBSign.text = "?"
        textCSign.text = "?"
        textResult.text = String(a + b + c)
        
        let delay = playSound(openGameSound(), "toan/toan_dien dau")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dien dau")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    private func checkFinish() {
        if bSign * b > 0 && cSign * c > 0 {
            pauseGame()
            animateCoinIfCorrect(view: textResult)
            let bSignText = textBSign.text == "+" ? "toan/cộng" : "toan/trừ"
            let cSignText = textCSign.text == "+" ? "toan/cộng" : "toan/trừ"
            let delay = playSound(
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                "topics/Numbers/\(a)",
                bSignText,
                "topics/Numbers/\(abs(b))",
                cSignText,
                "topics/Numbers/\(abs(c))",
                "toan/bằng",
                "topics/Numbers/\(a + b + c)",
                endGameSound()
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else if bSign * cSign != 0 && bClosed && cClosed {
            setGameWrong()
            pauseGame()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

