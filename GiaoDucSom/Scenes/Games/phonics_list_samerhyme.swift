//
//  phonics_list_samerhyme.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_samerhyme: GameFragment {
    private var values : [String] = []
    var answerLayout = MyGridView()
    var rightAnswer = ""
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
    }
    var corrects : [UIView] = []
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        while true {
            // Assuming data.values is an array of Strings.
            let _values = values.randomOrder().take(count: 3)

            let v0 = _values[0].prefix(1)
            let v1 = _values[1].prefix(1)
            let v2 = _values[2].prefix(1)

            var unique: [Substring] = [v0]
            

            if !unique.contains(v1) {
                unique.append(v1)
            } else {
                rightAnswer = v1.string
            }

            if !unique.contains(v2) {
                unique.append(v2)
            } else {
                rightAnswer = v2.string
            }

            if unique.count == 2 {
                values = _values
                break
                // Break the loop
            }
        }
        
                
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        var listViews : [UIView] = []
        for i in 0..<values.count {
            let view = createItem(text: String(values[i].dropFirst(2)).replacingOccurrences(of: "_", with: ""))
            view.tag = 100 + i
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 0.7
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.03
        answerLayout.columns = 0
        answerLayout.reloadItemViews(views: listViews)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.answerLayout.playSound = false
            self.answerLayout.step = 1.5
            self.answerLayout.showItems()
            for i in 0..<values.count {
                self.playSound(name: String(values[i].dropFirst(2)).replacingOccurrences(of: "_", with: ""), delay: Double(i) * 1.5)
            }
            self.scheduler.schedule(delay: 1.5 * Double(values.count), execute: {
                [weak self] in
                guard let self = self else { return }
                self.startGame()
            })
        })
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            for i in 0..<values.count {
                delay += self.playSound(name: values[i].dropFirst(2).replacingOccurrences(of: "_", with: ""), delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(background.snp.width).multipliedBy(42.0/40.0)
        }
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#FFFFFF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.top.equalTo(background.snp.bottom)
            make.height.equalTo(autoLabel.snp.width).multipliedBy(0.3)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        let thumb = SVGImageView(SVGName: "flashcards/\(game.level!)/\(text).svg").then{
            $0.contentMode = .scaleAspectFit
        }
        background.addSubview(thumb)
        thumb.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        var tag = sender.tag - 100
        if values[tag].starts(with: "\(rightAnswer).") {
            playSound(delay: 0.0, names: [answerCorrect1EffectSound(), "\(label.text!)"])
            if !corrects.contains(sender) {
                let value = String(values[tag].dropFirst(2))
                if let i1 = value.findPosition(of: "_"), let i2 = value.findLastPosition(of: "_") {
                    let spannable = NSMutableAttributedString(string: value.replacingOccurrences(of: "_", with: ""))
                    spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#73D048"), range: NSRange(location: i1, length: i2 - i1 - 1))
                    label.attributedText = spannable
                }
                corrects.append(sender)
                if corrects.count == 2 {
                    animateCoinIfCorrect(view: sender)
                    var delay = 0.5
                    pauseGame()
                    delay += playSound(delay: delay, names: [ "effects/cheer\(Int.random(in: 1...4))","effects/end game"])
                    scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.finishGame()
                    })
                }
            }
        } else {
            incorrect += 1
            setGameWrong()
            let value = String(values[tag].dropFirst(2))
            if let i1 = value.findPosition(of: "_"), let i2 = value.findLastPosition(of: "_") {
                let spannable = NSMutableAttributedString(string: value.replacingOccurrences(of: "_", with: ""))
                spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#FF7761"), range: NSRange(location: i1, length: i2 - i1 - 1))
                label.attributedText = spannable
            }
            playSound(name: "\(label.text!)", delay: 0.3)
            scheduler.schedule(delay: 0.7, execute: {
                [weak self] in
                guard let self = self else { return }
                label.text = String(values[tag].dropFirst(2)).replacingOccurrences(of: "_", with: "")
                label.textColor = .color(hex: "#FFFFFF")
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return Float(corrects.count) / Float(corrects.count + 2 * incorrect)
    }
}
