<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFD3E6"
    android:clipChildren="false"
    android:clipToPadding="false">


    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/grid_layout"
        app:layout_constraintWidth_percent="0.8">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_background_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#FFA3C5"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="8:1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercentPerHeight="0.05">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/main_container_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="10.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/number_2_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_2"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:clipChildren="false"
                    android:clipToPadding="false"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_3"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_1"
                    app:layout_constraintTop_toTopOf="parent">


                </TextView>

                <TextView
                    android:id="@+id/number_2_3"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_4"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_2"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_4"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_5"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_3"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_5"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_6"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_4"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_6"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_7"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_5"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_7"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_8"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_6"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_8"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_matma"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#F15C99"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/number_2_9"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_7"
                    app:layout_constraintTop_toTopOf="parent">

                </TextView>

                <TextView
                    android:id="@+id/number_2_9"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_quyluatchu2"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="?"
                    android:textColor="#FFFFFF"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/number_2_8"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>


    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#FFE9F3"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="6.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintVertical_bias="0.3" />


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>