//
//  phonics_list_samebeginningsound.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_samebeginningsound: GameFragment {
    private var values : [String] = []
    var answerLayout = MyGridView()
    var rightAnswer = ""
    var questions : [String] = []
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
    }
    var corrects : [UIView] = []
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            for i in 0..<questions.count {
                delay += self.playSound(name: questions[i], delay: delay) + 0.1
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        var firstLetters = [String]()
        for value in values {
            let letter = String(value.prefix(1))
            if !firstLetters.contains(letter) {
                firstLetters.append(letter)
            }
        }

        var firstLetterGroup = [String: [String]]()
        for firstLetter in firstLetters {
            firstLetterGroup[firstLetter] = [String]()
        }

        for value in values {
            if let firstLetter = value.first.map({ String($0) }) {
                firstLetterGroup[firstLetter]?.append(value)
            }
        }

        while true {
            let twoLetters = firstLetters.randomOrder().take(count: 2)
            if let firstGroup = firstLetterGroup[twoLetters[0]], firstGroup.count >= 3 {
                rightAnswer = twoLetters[0]
                if let secondGroup = firstLetterGroup[twoLetters[1]] {
                    questions += firstGroup.randomOrder().take(count: 3)
                    questions += secondGroup.randomOrder().take(count: 1)
                    break
                }
            }
        }

        questions.shuffle()
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        var listViews : [UIView] = []
        for i in 0..<questions.count {
            let view = createItem(text: questions[i])
            view.tag = 100 + i
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 0.7
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.03
        answerLayout.columns = 0
        answerLayout.reloadItemViews(views: listViews)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.answerLayout.playSound = false
            self.answerLayout.step = 1.5
            self.answerLayout.showItems()
            for i in 0..<questions.count {
                self.playSound(name: questions[i], delay: Double(i) * 1.5)
            }
            self.scheduler.schedule(delay: 1.5 * Double(questions.count), execute: {
                [weak self] in
                guard let self = self else { return }
                self.startGame()
            })
        })
    }    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(background.snp.width).multipliedBy(42.0/40.0)
        }
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#FFFFFF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.top.equalTo(background.snp.bottom)
            make.height.equalTo(autoLabel.snp.width).multipliedBy(0.3)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        let thumb = SVGImageView(SVGName: "flashcards/\(game.level!)/\(text).svg").then{
            $0.contentMode = .scaleAspectFit
        }
        background.addSubview(thumb)
        thumb.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        if label.text!.starts(with: rightAnswer) {
            //sender.animateCoin(answer: true)
            if !corrects.contains(sender) {
                label.textColor = .color(hex: "#FFFF55")
                corrects.append(sender)
                if corrects.count == 3 {
                    var delay = 0.5
                    pauseGame()
                    animateCoinIfCorrect(view: sender)
                    delay += playSound(delay: delay, names: [ "effects/cheer\(Int.random(in: 1...4))","effects/end game"])
                    scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.finishGame()
                    })
                }
            }
        } else {
            setGameWrong()
            incorrect += 1
            label.textColor = .color(hex: "#FF7761")
            playSound(name: "\(label.text!)", delay: 0.3)
            scheduler.schedule(delay: 0.7, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#FFFFFF")
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return incorrect > 0 ? 0 : 1
    }
}
