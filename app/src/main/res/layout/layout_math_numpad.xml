<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="0dp"
        android:background="@drawable/math_numpad_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="817.7:1087"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/math_numpad_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.06"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.06"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.91"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.06"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_4"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_5"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_6"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.91"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_7"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_7"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_8"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_9"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_9"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.91"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_del"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_del"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.09"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.94"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_0"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.94"
            app:layout_constraintWidth_percent="0.26" />

        <ImageView
            android:id="@+id/math_numpad_check"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_numpad_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="211.4:224"
            app:layout_constraintHorizontal_bias="0.91"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.94"
            app:layout_constraintWidth_percent="0.26" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>