//
//  taptrung_list_chonnhanpizza.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 21/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_chonnhanpizza: NhanBietGameFragment {
    // MARK: - Properties
    private let toppings = [
        "taptrung_nhanpizza_option1",
        "taptrung_nhanpizza_option2",
        "taptrung_nhanpizza_option3",
        "taptrung_nhanpizza_option4",
        "taptrung_nhanpizza_option5",
        "taptrung_nhanpizza_option6",
        "taptrung_nhanpizza_option7",
        "taptrung_nhanpizza_option8",
        "taptrung_nhanpizza_option9",
        "taptrung_nhanpizza_option10",
        "taptrung_nhanpizza_option11",
        "taptrung_nhanpizza_option12"
    ]
    private var option2_0: UIImageView!
    private var option2_1: UIImageView!
    private var option2_2: UIImageView!
    private var option2_3: UIImageView!
    private var option0: UIImageView!
    private var option1: UIImageView!
    private var option2: UIImageView!
    private var option3: UIImageView!
    private var pizza1View: SVGImageView!
    private var pizza2View: SVGImageView!
    private var pizza3View: UIView!
    private let pizza1InnerVIew = UIView()
    private var gridView: MyGridView!
    private var showOptions: [Bool] = [false, false, false, false]
    private var lockIndex: Int = 0
    private var selectedToppings: [Int] = []
    private var trayToppings: [Int] = []
    private var answer: [Int] = [0, 0, 0, 0]
    private var snapViews: [UIView: UIView] = [:]
    private var toppingContainer: UIView!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    var loadingSounds = false
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF2BB")
        
        let bottomBg = UIView()
        bottomBg.backgroundColor = UIColor.color(hex: "#F3EBFF")
        view.addSubview(bottomBg)
        bottomBg.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.25)
        }
        
        let tableContainer = SVGImageView(frame: .zero)
        tableContainer.clipsToBounds = false
        view.addSubview(tableContainer)
        tableContainer.makeViewCenterAndKeep(ratio: 1)
        
        let tableImage = SVGImageView(frame: .zero)
        tableImage.SVGName = "taptrung_nhanpizza_table"
        tableContainer.addSubview(tableImage)
        tableImage.snp.makeConstraints { make in
            make.width.equalTo(tableImage.snp.height).multipliedBy(1851.6 / 730.2) // Ratio 1851.6:730.2
            make.bottom.left.right.equalToSuperview()
        }
        
        pizza2View = SVGImageView(frame: .zero)
        pizza2View.SVGName = "taptrung_nhanpizza_pizza"
        pizza2View.transform = CGAffineTransform(scaleX: 1, y: 0.6)
        tableContainer.addSubview(pizza2View)
        pizza2View.snp.makeConstraints { make in
            make.width.equalTo(pizza2View.snp.height) // Ratio 1:1
            make.height.equalTo(tableContainer).multipliedBy(0.4)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.95) // verticalBias=0.9
            //make.centerY.equalToSuperview()
        }
        
        // Define the configuration data (bias and verticalBias) without UIImageView references
        let option2Views: [(bias: CGFloat, verticalBias: CGFloat)] = [
            (bias: 0.5, verticalBias: 0.2),
            (bias: 0.2, verticalBias: 0.5),
            (bias: 0.5, verticalBias: 0.8),
            (bias: 0.8, verticalBias: 0.5)
        ]

        // Array to store the created UIImageView instances (if needed for later use)
        var createdImageViews: [UIImageView] = []

        for (index, option) in option2Views.enumerated() {
            // Create a new UIImageView
            let imageView = UIImageView(image: Utilities.SVGImage(named: "taptrung_nhanpizza_option0"))
            createdImageViews.append(imageView) // Store the image view if needed
            
            // Create the container
            let optionContainer = SVGImageView(frame: .zero)
            optionContainer.SVGName = "taptrung_nhanpizza_option"
            pizza2View.addSubview(optionContainer)
            
            // Set up constraints for the container
            optionContainer.snp.makeConstraints { make in
                make.width.equalTo(optionContainer.snp.height) // Ratio 1:1
                make.height.equalTo(pizza2View).multipliedBy(0.25)
                //make.centerY.equalToSuperview().multipliedBy(option.verticalBias * 2)
            }
            
            // Snap to horizontal bias when layout updates
            addActionOnLayoutSubviews {
                optionContainer.snapToHorizontalBias(horizontalBias: option.bias)
                optionContainer.snapToVerticalBias(verticalBias: option.verticalBias)
            }
            
            // Add imageView as a subview of the container
            optionContainer.addSubview(imageView)
            
            // Set up constraints for the imageView
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            if index == 0 {
                option2_0 = imageView
            }
            if index == 1 {
                option2_1 = imageView
            }
            if index == 2 {
                option2_2 = imageView
            }
            if index == 3 {
                option2_3 = imageView
            }
        }
        
        pizza3View = UIView()
        pizza3View.alpha = 0.001
        tableContainer.addSubview(pizza3View)
        pizza3View.snp.makeConstraints { make in
            make.width.equalTo(pizza3View.snp.height) // Ratio 1:1
            make.height.equalTo(tableContainer).multipliedBy(0.4)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.95) // verticalBias=0.9
        }
        
        let pizza3Options: [(bias: CGFloat, verticalBias: CGFloat)] = [
            (bias: 0.5, verticalBias: 0.3),
            (bias: 0.2, verticalBias: 0.5),
            (bias: 0.5, verticalBias: 0.7),
            (bias: 0.8, verticalBias: 0.5)
        ]
        
        for option in pizza3Options {
            let optionContainer = SVGImageView(frame: .zero)
            optionContainer.SVGName = "taptrung_nhanpizza_option"
            pizza3View.addSubview(optionContainer)
            optionContainer.snp.makeConstraints { make in
                make.width.equalTo(optionContainer.snp.height) // Ratio 1:1
                make.height.equalTo(pizza3View).multipliedBy(0.25)
                //make.centerY.equalToSuperview().multipliedBy(option.verticalBias * 2)
            }
            addActionOnLayoutSubviews {
                optionContainer.snapToHorizontalBias(horizontalBias: option.bias)
                optionContainer.snapToVerticalBias(verticalBias: option.verticalBias)
            }
        }
        
        let boyContainer = SVGImageView(frame: .zero)
        boyContainer.SVGName = "taptrung_nhanpizza_boy"
        view.addSubview(boyContainer)
        boyContainer.snp.makeConstraints { make in
            make.width.equalTo(boyContainer.snp.height).multipliedBy(1057.6 / 1183) // Ratio 1057.6:1183
            make.height.equalToSuperview().multipliedBy(0.7)
            make.bottom.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            boyContainer.snapToHorizontalBias(horizontalBias: 0.0)
        }
        
        pizza1View = SVGImageView(frame: .zero)
        pizza1View.SVGName = "taptrung_nhanpizza_pizza"
        boyContainer.addSubview(pizza1View)
        pizza1View.snp.makeConstraints { make in
            make.width.equalTo(pizza1View.snp.height) // Ratio 1:1
            make.height.equalTo(boyContainer).multipliedBy(0.35)
            //make.bottom.equalToSuperview().multipliedBy(0.525) // verticalBias=0.05
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.pizza1View.snapToHorizontalBias(horizontalBias: 0.82)
            self.pizza1View.snapToVerticalBias(verticalBias: 0.05)
        }
        
        
        pizza1View.addSubviewWithInset(subview: pizza1InnerVIew, inset: 0)
        
        // Define the configuration data (bias and verticalBias) without UIImageView references
        let optionViews: [(bias: CGFloat, verticalBias: CGFloat)] = [
            (bias: 0.5, verticalBias: 0.2),
            (bias: 0.2, verticalBias: 0.5),
            (bias: 0.5, verticalBias: 0.8),
            (bias: 0.8, verticalBias: 0.5)
        ]

        // Array to store the created UIImageView instances (optional, if needed for later use)
        var createdImageViews2: [UIImageView] = []

        for (index,option) in optionViews.enumerated() {
            // Create a new UIImageView
            let imageView = UIImageView(image: Utilities.SVGImage(named: "taptrung_nhanpizza_option0"))
            createdImageViews2.append(imageView) // Store if needed
            
            // Create the container
            let optionContainer = UIImageView()
            pizza1InnerVIew.addSubview(optionContainer)
            
            // Set up constraints for the container
            optionContainer.snp.makeConstraints { make in
                make.width.equalTo(optionContainer.snp.height) // Ratio 1:1
                make.height.equalToSuperview().multipliedBy(0.25)
                make.centerY.equalToSuperview().multipliedBy(option.verticalBias * 2)
            }
            
            // Snap to horizontal bias when layout updates
            addActionOnLayoutSubviews {
                optionContainer.snapToHorizontalBias(horizontalBias: option.bias)
            }
            
            // Add imageView as a subview of the container
            optionContainer.addSubview(imageView)
            
            // Set up constraints for the imageView
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            if index == 0 {
                option0 = imageView
            }
            if index == 1 {
                option1 = imageView
            }
            if index == 2 {
                option2 = imageView
            }
            if index == 3 {
                option3 = imageView
            }
        }
        
        toppingContainer = UIView()
        toppingContainer.clipsToBounds = false
        view.addSubview(toppingContainer)
        toppingContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.26)
            make.height.equalToSuperview().multipliedBy(0.7)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.toppingContainer.snapToHorizontalBias(horizontalBias: 1.0)
        }
        
        let trayContainer = UIView()
        trayContainer.clipsToBounds = false
        toppingContainer.addSubview(trayContainer)
        trayContainer.snp.makeConstraints { make in
            make.width.equalTo(trayContainer.snp.height).multipliedBy(0.65) // Ratio 0.65:1
            make.width.equalTo(toppingContainer).multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        
        let trayImage = SVGImageView(frame: .zero)
        trayImage.SVGName = "taptrung_nhanpizza_tray"
        trayContainer.addSubview(trayImage)
        trayImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.black.withAlphaComponent(0.18) // #2f00
        gridView.clipsToBounds = false
        trayContainer.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.width.equalTo(trayContainer).multipliedBy(0.92)
            make.height.equalTo(trayContainer).multipliedBy(0.89)
            make.center.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleGridPan(_:)))
        gridView.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        selectedToppings = (0..<toppings.count).shuffled().prefix(4).map { $0 }
        let options = [option0, option1, option2, option3]
        let options2 = [option2_0, option2_1, option2_2, option2_3]
        lockIndex = Int.random(in: 0..<4)
        for i in 0..<answer.count {
            answer[i] = i == lockIndex ? selectedToppings[i] : -1
        }
        showOptions[lockIndex] = true
        
        for i in 0..<selectedToppings.count {
            options[i]?.image = Utilities.SVGImage(named: toppings[selectedToppings[i]])
            options2[i]?.image = Utilities.SVGImage(named: showOptions[i] ? toppings[selectedToppings[i]] : "taptrung_nhanpizza_option0")
            (options2[i]?.superview as? SVGImageView)?.SVGName = showOptions[i] ? nil : "taptrung_nhanpizza_option"
            //options[i]?.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0...360) * .pi / 180)
            //options2[i]?.transform = CGAffineTransform(rotationAngle: showOptions[i] ? CGFloat.random(in: 0...360) * .pi / 180 : 0)
        }
        
        pizza1InnerVIew.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0...360) * .pi / 180)
        trayToppings = selectedToppings
        while trayToppings.count < 6 {
            let topping = Int.random(in: 0..<toppings.count)
            if !trayToppings.contains(topping) {
                trayToppings.append(topping)
            }
        }
        
        var views: [UIView] = []
        for i in 0..<trayToppings.count {
            let view = UIView()
            let imageView = UIImageView(image: Utilities.SVGImage(named: toppings[trayToppings[i]]))
            imageView.transform = CGAffineTransform(rotationAngle: CGFloat.random(in: 0...360) * .pi / 180)
            view.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            view.stringTag = "\(trayToppings[i])"
            views.append(view)
        }
        
        trayToppings.shuffle()
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.reloadItemViews(views: views)
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/nhan pizza"])
        UIView.animate(withDuration: 0.5, delay: delay, animations: { [weak self] in
            //self?.pizza2View.transform = .identity
        })
        playSound(name: "effect/slide2", delay: delay)
        delay += 1.0
        UIView.animate(withDuration: 0.5, delay: delay, animations: { [weak self] in
            self?.toppingContainer.transform = .identity
        })
        playSound(name: "effect/cungchoi_pizza_khay", delay: delay)
        
        scheduler.schedule(after: delay) { [weak self] in
            UIView.animate(withDuration: 0.5) {
                [weak self] in
                guard let self = self else { return }
                self.toppingContainer.transform = .identity
            }
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        //pizza2View.transform = CGAffineTransform(translationX: 0, y: pizza2View.frame.height)
        toppingContainer.transform = CGAffineTransform(translationX: toppingContainer.frame.width, y: 0)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay: TimeInterval = playSound("taptrung/nhan pizza")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    var scale = 0.0
    var originX = 0.0 , originY = 0.0
    var zPosition: CGFloat = 10
    var viewToPoints: [UIView: CGRect] = [:]
    @objc private func handleGridPan(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                originX = currentView.frame.minX
                originY = currentView.frame.minY
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.layer.zPosition = zPosition
                zPosition += 1
                UIView.animate(withDuration: 0.1) {
                    currentView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
                }
                scale = pizza3View.frame.height / gridView.frame.height
                playSound("effect/cungchoi_pick1")
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                if !viewToPoints.keys.contains(currentView) {
                    viewToPoints[currentView] = currentView.frame
                }
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: view)
                
                var minDistance = CGFloat.greatestFiniteMagnitude
                var nearestView: UIView?
                for i in 0..<pizza3View.subviews.count {
                    let child = pizza3View.subviews[i]
                    let option2Container = pizza2View.subviews[i] as! SVGImageView
                    option2Container.SVGName = showOptions[i] ? nil : "taptrung_nhanpizza_option"
                    let vector = currentView.distanceFromCenterToCenter(to: child)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        nearestView = child
                    }
                }
                
                if let nearestView = nearestView, minDistance < nearestView.frame.width {
                    let index = pizza3View.subviews.firstIndex(of: nearestView) ?? 0
                    if index != lockIndex {
                        (pizza2View.subviews[index] as? SVGImageView)?.SVGName = "taptrung_nhanpizza_option_light"
                    }
                    UIView.animate(withDuration: 0.1) {
                        currentView.transform = CGAffineTransform(scaleX: 0.7 * self.scale, y: 0.7 * self.scale * 0.7)
                    }
                } else {
                    UIView.animate(withDuration: 0.1) {
                        currentView.transform = CGAffineTransform(scaleX: 1, y: 1)
                    }
                }
            }
            
        case .ended:
            if let currentView = currentView {
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                var minDistance = CGFloat.greatestFiniteMagnitude
                var nearestView: UIView?
                for i in 0..<pizza3View.subviews.count {
                    let child = pizza3View.subviews[i]
                    let vector = currentView.distanceFromCenterToCenter(to: child)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        nearestView = child
                    }
                }
                
                if let nearestView = nearestView, minDistance < nearestView.frame.width {
                    let index = pizza3View.subviews.firstIndex(of: nearestView) ?? 0
                    if index != lockIndex {
                        playSound("effect/word puzzle drop")
                        currentView.moveToCenter(of: nearestView, duration: 0.2)
                        UIView.animate(withDuration: 0.2) {
                            currentView.alpha = 0
                        }
                        
                        let imageView = pizza2View.subviews[index].subviews.first as? UIImageView
                        if let tag = Int(currentView.stringTag ?? "0") {
                            imageView?.image = Utilities.SVGImage(named: toppings[tag])
                        }
                        
                        if showOptions[index] {
                            if let oldView = snapViews[nearestView] {
                                UIView.animate(withDuration: 0.2) {
                                    oldView.alpha = 1
                                    oldView.transform = .identity
                                    if let frame = self.viewToPoints[oldView] {
                                        oldView.frame = frame
                                    }
                                }
                            }
                        }
                        
                        showOptions[index] = true
                        answer[index] = Int(currentView.stringTag ?? "0") ?? 0
                        (pizza2View.subviews[index] as? SVGImageView)?.SVGName = nil
                        snapViews[currentView] = nearestView
                        snapViews[nearestView] = currentView
                        
                        if showOptions.allSatisfy({ $0 }) {
                            if answer[0] == selectedToppings[0] && answer[1] == selectedToppings[1] && answer[2] == selectedToppings[2] && answer[3] == selectedToppings[3] {
                                let delay = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
                                pauseGame()
                                animateCoinIfCorrect(view: pizza1View)
                                scheduler.schedule(after: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            } else {
                                setGameWrong()
                                playSound("effect/answer_wrong")
                            }
                        }
                    } else {
                        UIView.animate(withDuration: 0.2) {
                            currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                            currentView.transform = .identity
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.2) {
                        currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                        currentView.transform = .identity
                    }
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<gridView.subviews.count).reversed() {
            let child = gridView.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
}
