//
//  PhonicsActivity.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import AnyCodable

class PhonicsActivity: TwoClickExitActivity, OnGameFragmentListener {
    // MARK: - Properties
    private var gameIndex: Int = 0
    private var oldCoin: Int = 0
    private var scores: [Float] = []
    var chapterIndex: Int = 0
    var lessonIndex: Int = 0
    private var chapter: GroupLesson?
    private var lesson: Lesson?
    private var games: [Animation] = []
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        checkTimeoutAndRun(
            { [weak self] in
                guard let self = self else { return }
                self.initActivity()
            },
            cancelAction: { [weak self] in
                guard let self = self else { return }
                self.dismiss(animated: true, completion: nil)
                self.navigationController?.popViewController(animated: true)
            }
        )
    }
    
    deinit {
        scheduler.clearAll()
    }
    
    // MARK: - Initialization
    private func initActivity() {
        //hideStatusBar()
        
        //chapterIndex = navigationController?.getIntExtra(key: "chapterIndex") ?? 0
        //lessonIndex = navigationController?.getIntExtra(key: "lessonIndex") ?? 0
        chapter = PhonicsManager.shared.getChapters()[chapterIndex]
        lesson = chapter?.lessons[lessonIndex]
        
        oldCoin = CoinManager.shared.getCoin()
        
        guard let lesson = lesson else { return }
        let lessonComplete = PhonicsManager.shared.isLessonComplete(lesson: lesson)
        let counted = PhonicsManager.shared.countLessonComplete(lesson: lesson)
        
        games = []
        for section in lesson.intro {
            if let animations = section.animations {
                games.append(contentsOf: animations)
            }
            if let animation = section.animation {
                games.append(animation)
            }
        }
        
        loadGame(index: 0)
    }
    
    // MARK: - Game Loading
    private func loadGame(index: Int) {
        
        if index >= games.count {
            
            let completeDialog = LessonCompleteDialog()
            completeDialog.onClosed = { [weak self] in
                guard let self = self, let lesson = self.lesson else { return }
                let countedLessonComplete = PhonicsManager.shared.countLessonComplete(lesson: lesson)
                if countedLessonComplete == 3 {
                    scheduler.schedule(after: 2.0) { [weak self] in
                        guard let self = self, let lesson = self.lesson else { return }
                        /*
                        let hoc3LanDialog = Hoc3LanTapDocDialogFragment()
                        hoc3LanDialog.lessonName = lesson.name
                        hoc3LanDialog.thumb = lesson.thumb
                        hoc3LanDialog.isTapdoc = false
                        hoc3LanDialog.show(in: self.navigationController, tag: "hoc-3-lan")
                         */
                    }
                }
                self.dismiss(animated: true, completion: nil)
                self.navigationController?.popViewController(animated: true)
            }
            
            let sumScore = scores.reduce(0, +)
            let aveScore = scores.isEmpty ? 0 : sumScore / Float(scores.count)
            completeDialog.setScore(aveScore)
            
            var coinReward = CoinManager.shared.getCoin() - oldCoin
            if coinReward < 0 {
                coinReward = 0
            }
            completeDialog.setCoinReward(coinReward)
            completeDialog.setLanguage("en")
            
            completeDialog.setupPopupPresentation()
            self.present(completeDialog, animated: false) {
                completeDialog.presentPopup()
            }
            PhonicsManager.shared.logCompleteLesson(lesson: lesson!, score: Double(aveScore))
            return
        }
        
        self.gameIndex = index
        let game = games[index]
        
        let gameFragment = GameFragment.createGameFragment(by: game, tapdoc: false)
        gameFragment.setLanguage("en")
        gameFragment.onGameFragmentListener = self
        
        if let gameView = gameView {
            gameView.removeAllSubviews()
            gameView.addSubviewWithPercentInset(subview: gameFragment, percentInset: 0.0)
        }
    }
    
    // MARK: - OnGameFragmentListener
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        scores.append(gameFragment.score)
        loadGame(index: gameIndex + 1)
    }
    
    func onUpdateGameProgress(_ progress: Float) {
        // Không cần xử lý tiến độ trong trường hợp này
    }
}
