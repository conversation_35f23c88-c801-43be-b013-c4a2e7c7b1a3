//
//  MyThuatVeTuDoViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/6/25.
//


import UIKit
import SnapKit
import SVGKit

class MyThuatVeTuDoViewController: BaseViewController {
    var selectedIndex: Int = -1
    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        return UICollectionView(frame: .zero, collectionViewLayout: layout)
    }()
    private let btnClose = KUButton()
    private let gradientView = FadeGradientView()
    private let txtTitle = AutosizeLabel()
    
    private var data: [URL] = []
        
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if selectedIndex != -1 {
            collectionView.reloadItems(at: [IndexPath(item: selectedIndex, section: 0)])
        }
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            self.loadData()
        }
    }
    
    private func setupUI() {
        view.removeAllSubviews()
        view.backgroundColor = UIColor(hex: "#DD9FF8")
        // btnClose
        btnClose.setImage(Utilities.SVGImage(named: "btn_back_mythuat_vetungnet"), for: .normal)
        btnClose.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.width.equalTo(btnClose.snp.height)
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
        }
        btnClose.snpLeftTop(ratio: 1)
        // txtTitle
        txtTitle.text = "Vẽ tự do"
        txtTitle.font = .Freude(size: 20)
        txtTitle.textColor = .white
        txtTitle.textAlignment = .left
        txtTitle.overrideTextAlignment = false
        txtTitle.numberOfLines = 1
        view.addSubview(txtTitle)
        txtTitle.snp.makeConstraints { make in
            make.centerY.equalTo(btnClose)
            make.width.equalTo(view.snp.width).multipliedBy(0.45)
            make.height.equalTo(view.snp.height).multipliedBy(0.06)
        }
        txtTitle.snpLeftTop(ratio: 3.0)
        
        
                
        // collectionView
        collectionView.backgroundColor = .clear
        collectionView.clipsToBounds = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(MyThuatCell.self, forCellWithReuseIdentifier: "MyThuatCell")
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            let column = Utilities.isIPad ? 4 : 3
            let dpToPx = view.bounds.width / CGFloat(column) * 0.92
            layout.itemSize = CGSize(width: dpToPx, height: dpToPx * 616 / 560)
            layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        }
        view.addSubview(collectionView)
        
        
        // gradientView
        gradientView.setupGradient(color: .color(hex: "#DD9FF8"))
        view.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(gradientView.snp.width).dividedBy(30)
            make.top.equalTo(btnClose.snp.bottom)
        }
        
        let viewTop = UIView()
        viewTop.backgroundColor = .color(hex: "#DD9FF8")
        view.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(gradientView.snp.top)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(gradientView.snp.bottom)
            make.left.equalTo(btnClose)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let coinView = CoinView()
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.top.bottom.equalTo(btnClose)
        }
        coinView.snpRightTop(ratio: 1)
        view.bringSubviewToFront(btnClose)
        view.bringSubviewToFront(txtTitle)
    }
    
    private func loadData() {
        data = getAllFilesInAppDirectory()
        if data.isEmpty && !createdNewPage {
            newPage()
            data = getAllFilesInAppDirectory()
        }
        collectionView.reloadData()
    }
    
    @objc private func closeTapped() {
        navigationController?.popViewController(animated: true) // Adjust based on presentation style
    }
    
    private func getAllFilesInAppDirectory() -> [URL] {
        let fileManager = FileManager.default
        guard let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else { return [] }
        do {
            let fileURLs = try fileManager.contentsOfDirectory(at: documentsURL, includingPropertiesForKeys: nil)
            let prefix = "vetudo_\(DataManager.shared.currentProfile?.id ?? "")_"
            let filteredFiles = fileURLs.filter { $0.lastPathComponent.hasPrefix(prefix) && $0.pathExtension == "json" }
            let dates = filteredFiles.map { Date(fileModificationDate: $0)}
            return filteredFiles.sorted { dates[filteredFiles.firstIndex(of: $0)!] > dates[filteredFiles.firstIndex(of: $1)!]}
        } catch {
            print("Error listing files: \(error)")
            return []
        }
    }
    var createdNewPage: Bool = false
    private func newPage() {
        createdNewPage = true
        let fileManager = FileManager.default
        guard let documentsURL = fileManager.urls(for: .documentDirectory, in: .userDomainMask).first else { return }
        var filename: String
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyyMMdd"
        let currentDate = formatter.string(from: Date())
        let profileId = DataManager.shared.currentProfile?.id ?? ""
        
        repeat {
            let randomNum = Int.random(in: 0..<1000000)
            filename = "vetudo_\(profileId)_\(currentDate)_\(randomNum).json"
        } while fileManager.fileExists(atPath: documentsURL.appendingPathComponent(filename).path)
        
        
        openDrawing(filename: filename)
    }
    private func openDrawing(filename: String) {
        let vc = SingleGameActivity()
        vc.game = mythuat_list_vetudo.className
        vc.tapdoc = false
        vc.phonics = false
        vc.data = filename
        self.navigationController?.pushViewController(vc, animated: true)
    }
    private func openDrawing(file: URL) {
        openDrawing(filename: file.lastPathComponent)
    }
}

extension MyThuatVeTuDoViewController: UICollectionViewDelegate, UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return data.count + 1 // +1 for the add new item
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "MyThuatCell", for: indexPath) as! MyThuatCell
        if indexPath.item == 0 {
            cell.configureForAddNew()
        } else {
            let file = data[indexPath.item - 1]
            cell.configure(with: file)
        }
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if indexPath.item == 0 {
            self.playSound("vi/popup_becodongydung", "vi/topics/Numbers/\(CoinManager.PRICE_PAPER)", "vi/popup_xu_giay")
            let dialog = MessageDialogView()
                .setTitle("Mua giấy mới")
                .setMessage("Bé có đồng ý dùng \(CoinManager.PRICE_PAPER) xu để mua giấy mới không?")
                .setImageResId("icon_buy")
                .setButtonLaterText("Để sau")
                .setButtonOkText("Mua")
                .setShowLaterButton(true)
                .setShowOkButton(true)
                .setListener { buttonIndex in
                    if buttonIndex == .ok {
                        self.scheduler.clearAll()
                        if CoinManager.shared.useCoin(CoinManager.PRICE_PAPER) {
                            self.playSound("effect/unlock item");
                            self.newPage()
                        } else {
                            self.playSound("vi/popup_khongdu_giay")
                            let noCoinDialog = MessageDialogView()
                                .setTitle("Không đủ xu")
                                .setMessage("Bé không đủ xu để mua giấy mới")
                                .setImageResId("icon_no_coin")
                            noCoinDialog.showIn(self.view)
                        }
                    }
                }
            dialog.showIn(self.view)                                   
        } else {
            let file = data[indexPath.item - 1]
            openDrawing(file: file)
        }
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let spanCount = Utilities.isIPad ? 4 : 3
        let totalSpacing = CGFloat(spanCount - 1) * 10 + 20 // 10 là minimumInteritemSpacing, 20 là padding trái + phải từ sectionInset
        let width = (collectionView.bounds.width - totalSpacing) / CGFloat(spanCount)
        return CGSize(width: width, height: width / 0.8)
    }
}

class MyThuatCell: UICollectionViewCell {
    private var bgView: UIView!
    private var nameLabel: HeightRatioTextView!
    private var plusLabel: HeightRatioTextView!
    private var thumbnailView: SmoothStrokeView! // Placeholder for SmoothStrokeView
    var file: URL!
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        let ratioView = UIView()        
        contentView.addSubview(ratioView)
        ratioView.makeViewCenterAndKeep(ratio: 0.8)
        bgView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "mythuat_tranhtomau_list_bg"))
        ratioView.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.width.equalTo(bgView.snp.height).multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.9)
        }
        
        thumbnailView = SmoothStrokeView()
        thumbnailView.isUserInteractionEnabled = false
        thumbnailView.backgroundColor = .white // Placeholder for SmoothStrokeView
        bgView.addSubview(thumbnailView)
        thumbnailView.snp.makeConstraints { make in
            make.width.equalTo(bgView).multipliedBy(0.8)
            make.height.equalTo(thumbnailView.snp.width).dividedBy(2)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        nameLabel = HeightRatioTextView()
        nameLabel.setHeightRatio(0.5)
        nameLabel.textColor = UIColor(hex: "#f39a71")
        nameLabel.font = .Freude(size: 20)
        nameLabel.textAlignment = .center
        bgView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.15)
        }
        nameLabel.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            nameLabel.snapToVerticalBias(verticalBias: 0.9)
        }
        
        plusLabel = HeightRatioTextView()
        plusLabel.setHeightRatio(0.5)
        plusLabel.text = "+"
        plusLabel.textColor = .black
        plusLabel.font = .Freude(size: 20)
        plusLabel.textAlignment = .center
        bgView.addSubview(plusLabel)
        plusLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    func configureForAddNew() {
        thumbnailView.isHidden = true
        nameLabel.isHidden = true
        plusLabel.isHidden = false
    }
    
    func configure(with file: URL) {
        self.file = file
        thumbnailView.isHidden = false
        nameLabel.isHidden = false
        plusLabel.isHidden = true
        // Placeholder for loading thumbnail; assumes SmoothStrokeView equivalent exists
        nameLabel.text = getFriendlyTime(date: Date(fileModificationDate: file))
        Scheduler().schedule(delay: 0.1) {
            [weak self] in
            guard let self = self else { return }
            if file == self.file {
                thumbnailView.load(filename: file.lastPathComponent)
            }
        }
    }
    
    private func getFriendlyTime(date: Date) -> String {
        let diff = Date().timeIntervalSince(date)
        let seconds = Int(diff)
        let minutes = seconds / 60
        let hours = minutes / 60
        let days = hours / 24
        
        if seconds < 60 {
            return "\(seconds) giây trước"
        } else if minutes < 60 {
            return "\(minutes) phút trước"
        } else if hours < 24 {
            return "\(hours) giờ trước"
        } else if days == 1 {
            return "Hôm qua"
        } else if days <= 6 {
            return "\(days) hôm trước"
        } else {
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM-dd"
            return formatter.string(from: date)
        }
    }
}



// Placeholder for VeTuDoGameViewController
class VeTuDoGameViewController: UIViewController {
    private let filename: String
    
    init(filename: String) {
        self.filename = filename
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .red
        // Implement drawing functionality here
    }
}


// Extension to get file modification date
extension Date {
    init(fileModificationDate url: URL) {
        let attributes = try? FileManager.default.attributesOfItem(atPath: url.path)
        self = (attributes?[.modificationDate] as? Date) ?? Date()
    }
}
