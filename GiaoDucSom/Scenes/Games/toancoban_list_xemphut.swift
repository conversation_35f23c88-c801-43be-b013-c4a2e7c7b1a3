//
//  toancoban_list_xemphut.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_xemphut
class toancoban_list_xemphut: NhanBietGameFragment {
    // MARK: - Properties
    private var hour = 0
    private var minute = 0
    private var sliderView: MySliderView!
    private var clockDigital: ClockDigital!

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")

        let itemContainer = UIView()
        itemContainer.stringTag = "item_container"
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
            make.width.equalTo(itemContainer.snp.height)
        }

        let clockContainer = UIView()
        clockContainer.stringTag = "clock_container"
        itemContainer.addSubview(clockContainer)
        clockContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(clockContainer.snp.height)
            make.center.equalToSuperview()
        }

        clockDigital = ClockDigital()
        clockDigital.stringTag = "clock_digital"
        clockContainer.addSubview(clockDigital)
        clockDigital.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        let bottomView = UIView()
        bottomView.stringTag = "bottom_view"
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(itemContainer.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        sliderView = MySliderView()
        sliderView.horizontalPadding = 1.5
        sliderView.stringTag = "slider_view"
        bottomView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(sliderView.snp.width).dividedBy(6)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }

        sliderView.setListener { [weak self] value in
            guard let self = self else { return }
            let correct = value == self.minute
            self.pauseGame()
            self.sliderView.setRight(correct)
            if correct {
                self.animateCoinIfCorrect(view: self.sliderView)
                let delay = self.playSound(self.finishCorrect1Sounds())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                self.setGameWrong()
                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.startGame()
                }
            }
        }
        sliderView.setScrollToListener { [weak self] value, position in
            guard let self = self else { return }
            self.sliderView.setTextAtPosition("\(self.hour):\(String(format: "%02d", value))", at: position)
        }
        sliderView.setScrollEndListener { [weak self] value, position in
            guard let self = self else { return }
            let correct = value == self.minute
            self.sliderView.setRight(correct)
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        hour = Int.random(in: 1...12)
        minute = Int.random(in: 0..<12) * 5
        clockDigital.setTime(hour: hour, minute: minute)
        var minValue = minute / 5 - Int.random(in: 0..<5)
        if minValue < 0 { minValue = 0 }
        if minValue > 6 { minValue = 6 }
        let maxValue = minValue + 5
        sliderView.setMinValue(minValue * 5)
        sliderView.setMaxValue(maxValue * 5)
        sliderView.setStep(5)
        while true {
            let tick = Int.random(in: minValue...maxValue)
            if tick != minute / 5 {
                sliderView.setSelectedTick(tick - minValue)
                sliderView.setTextAtPosition("\(hour):\(String(format: "%02d", tick * 5))", at: tick - minValue)
                break
            }
        }
    }

    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_xem phut")
        scheduler.schedule(delay: Double(delay)) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_xem phut")
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}
