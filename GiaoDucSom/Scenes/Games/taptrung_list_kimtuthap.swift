//
//  taptrung_list_kimtuthap.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 22/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_kimtuthap: NhanBietGameFragment {
    // MARK: - Properties
    private let colors: [UIColor] = [
        UIColor.color(hex: "#FFE017"),
        UIColor.color(hex: "#4FED5E"),
        UIColor.color(hex: "#FF7060"),
        UIColor.color(hex: "#F96EC7")
    ]
    private var gridView: MyGridView!
    private var leftContainer: UIView!
    private var svgView: SVGImageView!
    private var indexes: [Int] = (0..<4).shuffled()
    private var svgs: [SVGKImage] = []
    private var leftFromTop: Bool = false
    private var leftItem: Item!
    private var items: [Item] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        leftContainer = UIView()
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit // contentScale=0.75
        svgView.transform = CGAffineTransformMakeScale(0.75, 0.75)
        leftContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.color(hex: "#D7FBFF")
        gridView.clipsToBounds = false
        view.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        leftContainer.transform = .identity
        gridView.alpha = 0
        
        let svgPaths = ["taptrung_kimtuthap1", "taptrung_kimtuthap2"]
        svgs = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        loadData()
    }
    
    private func loadData() {
        leftFromTop = Bool.random()
        leftItem = Item()
        items = [leftItem]
        
        let rotateItem = leftItem.clone()
        rotate(umbrella: &rotateItem.tops)
        randomRotate(item: rotateItem)
        if !equalRotate(leftItem: leftItem, rightItem: rotateItem) {
            items.append(rotateItem)
        }
        
        while items.count < 4 {
            let newItem = leftItem.clone()
            if Bool.random() {
                newItem.tops[Int.random(in: 0..<newItem.tops.count)] = Int.random(in: 0..<colors.count)
            } else {
                newItem.bottoms[Int.random(in: 0..<newItem.bottoms.count)] = Int.random(in: 0..<colors.count)
            }
            randomRotate(item: newItem)
            let valid = items.allSatisfy { !equalRotate(leftItem: $0, rightItem: newItem) }
            if valid {
                items.append(newItem)
            }
        }
        
        updateData(view: svgView, item: leftItem, left: true)
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = KUButton()
            view.backgroundColor = .clear
            let itemView = SVGImageView(frame: .zero)
            itemView.contentMode = .scaleAspectFit
            itemView.transform = CGAffineTransformMakeScale(0.75, 0.75)
            updateData(view: itemView, item: items[indexes[i]], left: false)
            view.addSubview(itemView)
            itemView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            view.stringTag = "\(indexes[i])"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "nhanbiet_bg_option_white"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            bgContainer.addSubviewWithPercentInset(subview: itemView, percentInset: 0.15)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.itemSpacingRatio = 0.05
        gridView.insetRatio = 0.05
        gridView.reloadItemViews(views: views)
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/kim tu thap"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.leftContainer.transform = .identity
            }
            UIView.animate(withDuration: 0.2, delay: 0.5) {
                self.gridView.alpha = 1
            }
        }
        
        delay += 0.7
        delay += gridView.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/kim tu thap")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == 0 {
            animateCoinIfCorrect(view: svgView)
            let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func rotate(item: Item) {
        rotate(umbrella: &item.tops)
        rotate(umbrella: &item.bottoms)
    }
    
    private func randomRotate(item: Item) {
        let random = Int.random(in: 0..<4)
        for _ in 0..<random {
            rotate(item: item)
        }
    }
    
    private func rotate(umbrella: inout [Int]) {
        guard !umbrella.isEmpty else { return }
        let color = umbrella.removeFirst()
        umbrella.append(color)
    }
    
    private func equalLeft(umbrellaA: [Int], umbrellaB: [Int]) -> Bool {
        let size = min(umbrellaA.count, umbrellaB.count)
        for i in 0..<size {
            if umbrellaA[i] != umbrellaB[i] {
                return false
            }
        }
        return true
    }
    
    private func equalRotate(leftItem: Item, rightItem: Item) -> Bool {
        var leftTops = leftItem.tops
        var leftBottoms = leftItem.bottoms
        for _ in 0..<leftTops.count {
            rotate(umbrella: &leftTops)
            rotate(umbrella: &leftBottoms)
            if equalLeft(umbrellaA: leftTops, umbrellaB: rightItem.tops) &&
               equalLeft(umbrellaA: leftBottoms, umbrellaB: rightItem.bottoms) {
                return true
            }
        }
        return false
    }
    
    private func updateData(view: SVGImageView, item: Item, left: Bool) {
        let svg = svgs[left == leftFromTop ? 0 : 1]
        for i in 0..<item.tops.count {
            svg.caLayerTree.sublayers![i].setFillColor(color: colors[item.tops[i]])
        }
        for i in 0..<item.bottoms.count {
            svg.caLayerTree.sublayers![i + 4].setFillColor(color: colors[item.bottoms[i]])
        }
        view.image = svg.uiImage
    }
    
    // MARK: - Model Classes
    class Item {
        var tops: [Int]
        var bottoms: [Int]
        
        init() {
            tops = (0..<4).map { _ in Int.random(in: 0..<4) }
            bottoms = (0..<4).map { _ in Int.random(in: 0..<4) }
        }
        
        func clone() -> Item {
            let item = Item()
            item.tops = tops
            item.bottoms = bottoms
            return item
        }
    }
}
