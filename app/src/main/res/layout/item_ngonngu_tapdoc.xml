<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="737:559"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="737:559"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/background_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/ngonngu_list_tapdoc" />

            <ImageView
                android:id="@+id/check_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/english_list_check_1" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_thumb"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                android:fontFamily="@font/svn_freude"
                android:gravity="bottom|right"
                android:letterSpacing="0"
                android:text="Abcd"
                android:textColor="#74B6FF"
                app:heightRatio="0.6"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.8"
                app:layout_constraintWidth_percent="0.8" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_name"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                android:fontFamily="@font/svn_freude"
                android:gravity="bottom|left"
                android:text="Ôn ABC"
                android:textColor="#FFF"
                app:heightRatio="0.6"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.3"
                app:layout_constraintHorizontal_bias="0.2"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.2"
                app:layout_constraintWidth_percent="0.6" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>