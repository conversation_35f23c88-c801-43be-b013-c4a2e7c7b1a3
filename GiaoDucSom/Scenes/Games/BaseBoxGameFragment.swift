//
//  BaseBoxGameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit
import SVGKit

class BaseBoxGameFragment: NhanBietGameFragment {
    // MARK: - Properties
    var MAXValue: Int = 3
    
    // MARK: - Utility Methods
    func createArray(rows: Int, cols: Int) -> [[Int]] {
        var d = Array(repeating: Array(repeating: 0, count: cols), count: rows)
        
        for i in 0..<rows {
            for j in 0..<cols {
                var maxValue = MAXValue
                if j > 0 {
                    maxValue = min(maxValue, d[i][j - 1]) // Left element
                }
                if i > 0 {
                    maxValue = min(maxValue, d[i - 1][j]) // Top element
                }
                d[i][j] = Int.random(in: 0...maxValue)
            }
        }
        
        return d
    }
    
    func sumArray(_ d: [[Int]]) -> Int {
        return d.reduce(0) { $0 + $1.reduce(0, +) }
    }
    
    func checkValidation(_ data: [[Int]]) -> Bool {
        let rows = data.count
        let columns = data[0].count
        for i in 0..<rows {
            for j in 0..<columns {
                let row = rows - 1 - i
                let column = columns - 1 - j
                let leftValue = getValue(data: data, row: row + 1, column: column + 1)
                let rightValue = getValue(data: data, row: row + 1, column: column)
                let belowValue = getValue(data: data, row: row, column: column + 1)
                let value = data[row][column]
                if (value == 0 && belowValue <= 1 && min(leftValue, rightValue) <= 1) ||
                   (value >= belowValue && value >= min(leftValue, rightValue)) {
                    // Valid
                } else {
                    return false
                }
            }
        }
        if sumAtRow(data: data, row: 0) == 0 ||
           sumAtRow(data: data, row: data.count - 1) == 0 ||
           sumAtColumn(data: data, column: 0) == 0 ||
           sumAtColumn(data: data, column: data[0].count - 1) == 0 {
            return false
        }
        return true
    }
    
    func sumValue(_ data: [[Int]]) -> Int {
        return data.reduce(0) { $0 + $1.reduce(0, +) }
    }
    
    func sumAtRow(data: [[Int]], row: Int) -> Int {
        return data[row].reduce(0, +)
    }
    
    func sumAtColumn(data: [[Int]], column: Int) -> Int {
        return data.reduce(0) { $0 + $1[column] }
    }
    
    func getValue(data: [[Int]], row: Int, column: Int) -> Int {
        let maxRow = data.count
        let maxColumn = data[0].count
        if row >= maxRow || column >= maxColumn || row < 0 || column < 0 {
            return 0
        }
        return data[row][column]
    }
    
    // MARK: - Data Structures
    struct Data {
        var items: [String]
        var value: Int
        var rows: Int
        var columns: Int
    }
    
    struct BlockData {
        var currentData: [[Int]]
        var originData: [[Int]]
    }
    
    // MARK: - BlockView
    class BlockView: UIView {
        var viewPreview: SVGKFastImageView!
        var viewDragging: SVGKFastImageView!
        private var mData: BlockData?
        private var loadedImages = false
        private var contentLayout: UIView!
        private var dragViewContainer: UIView!
        private var svgBlock: SVGKImage?
        private var svgRight: SVGKImage?
        private var svgWrong: SVGKImage?
        private var svgBlue: SVGKImage?
        private var svgGray: SVGKImage?
        private var deltaWidth: CGFloat = 0
        private var deltaHeight: CGFloat = 0
        private var width: CGFloat = 0
        private var viewOnTop: [Int: UIView] = [:]
        private var lastTopView: UIView?
        private var rightLocation: Bool = false
        private var locationI: Int = 0
        private var locationJ: Int = 0
        private var inCorner: Bool = false
        weak var game: NhanBietGameFragment?
        private var moved = false
        override init(frame: CGRect) {
            super.init(frame: frame)
            initView()
        }
        var hintView: UIImageView!
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initView()
        }
        
        private func initView() {                    
            contentLayout = UIView()
            contentLayout.tag = RBox.id.item_content_layout
            addSubview(contentLayout)
            contentLayout.makeViewCenterAndKeep(ratio: 0.8)
            dragViewContainer = UIView()
            dragViewContainer.tag = RBox.id.drag_view_container
            addSubviewWithInset(subview: dragViewContainer, inset: 0)
            
            // Giả lập SVGManager.loadSVG
            svgBlock = Utilities.GetSVGKImage(named: "taptrung_box")
            svgRight = Utilities.GetSVGKImage(named: "taptrung_box_shadow").withFillColor(UIColor(hex: "#FFD580"))
            svgWrong = Utilities.GetSVGKImage(named: "taptrung_box_shadow").withFillColor(UIColor(hex: "#FFD580"))
            svgGray = Utilities.GetSVGKImage(named: "taptrung_box_shadow").withFillColor(UIColor(hex: "#1DD7DB"))
            svgBlue = Utilities.GetSVGKImage(named: "taptrung_box_shadow").withFillColor(.red)
            loadedImages = true
            
            if let data = mData {
                setData(data)
            }
        }
        
        func moveToCorner(newItem: Bool) {
            inCorner = true
            guard let parent = viewDragging?.superview else { return }
            let containerWidth = parent.frame.width
            let containerHeight = parent.frame.height
            
            if newItem {
                viewDragging?.transform = CGAffineTransform(translationX: containerWidth / 2 + width, y: containerHeight / 2 - width / 2)
                viewPreview?.transform = CGAffineTransform(translationX: containerWidth / 2 + width, y: containerHeight / 2 - width / 2)
            }
            
            let tenDP = 10.0 // Giả lập Utils.dpToPx
            UIView.animate(withDuration: 0.2) {
                self.viewDragging?.transform = CGAffineTransform(translationX: containerWidth / 2 - self.width / 2 - containerHeight / 20, y: containerHeight / 2 - self.width / 2 - containerHeight / 20)
                self.viewPreview?.transform = CGAffineTransform(translationX: containerWidth / 2 - self.width / 2 - containerHeight / 20, y: containerHeight / 2 - self.width / 2 - containerHeight / 20)
            } completion: { _ in
                self.viewPreview?.superview!.bringSubviewToFront(self.viewPreview!)
                self.viewDragging?.superview!.bringSubviewToFront(self.viewDragging!)
            }
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            if inCorner {
                moveToCorner(newItem: false)
            }
            if mData != nil {
                setData(mData!)
            }
        }
        
        func setData(_ data: BlockData) {
            mData = data
            guard loadedImages else { return }
            
            contentLayout.subviews.forEach { $0.removeFromSuperview() }
            let maxDim = max(data.originData[0][0], max(data.originData.count, data.originData[0].count))
            width = contentLayout.frame.width / CGFloat(maxDim)
            deltaWidth = width / 2
            deltaHeight = width * 57.864 / 300
            
            for i in 0..<data.currentData.count {
                let line = data.currentData[i]
                for j in 0..<line.count {
                    let count = line[j]
                    let lp = CGSize(width: width, height: width)
                    
                    let bgView = UIImageView()
                    bgView.contentMode = .scaleAspectFit
                    bgView.image = (svgGray?.uiImage)
                    //bgView.frame.size = lp
                    contentLayout.addSubview(bgView)
                    bgView.snp.makeConstraints { make in
                        make.center.equalToSuperview()
                        make.width.height.equalTo(width)
                    }
                    bgView.transform = CGAffineTransform(translationX: -deltaWidth * CGFloat(i) + deltaWidth * CGFloat(j), y: deltaHeight * 0.5 + deltaHeight * CGFloat(i) + deltaHeight * CGFloat(j) + (width - 2 * deltaHeight) / 2)
                    //bgView.contentScale = 0.98
                    bgView.tag = RBox.id.background
                    bgView.alpha = data.originData[i][j] > 0 ? 1 : 0.99
                    viewOnTop[i * 10 + j] = bgView
                    
                    for c in 0..<count {
                        let blockView = SVGKFastImageView(svgkImage: nil)!
                        blockView.image = (svgBlock)
                        //blockView.frame.size = lp
                        contentLayout.addSubview(blockView)
                        blockView.snp.makeConstraints { make in
                            make.center.equalToSuperview()
                            make.width.height.equalTo(width)
                        }
                        blockView.transform = CGAffineTransform(translationX: -deltaWidth * CGFloat(i) + deltaWidth * CGFloat(j), y: deltaHeight * 0.5 + deltaHeight * CGFloat(i) + deltaHeight * CGFloat(j) - CGFloat(c) * (width - 2 * deltaHeight))
                        //blockView.contentScale = 0.98
                        blockView.tag = RBox.id.block
                        viewOnTop[i * 10 + j] = blockView
                    }
                }
            }
            
            if data.currentData != data.originData {
                let lp = CGSize(width: width, height: width)
                hintView = UIImageView()
                hintView.contentMode = .scaleAspectFit
                hintView.image = (svgWrong?.uiImage)
                hintView.alpha = 0
                //hintView.frame.size = lp
                contentLayout.addSubview(hintView)
                hintView.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                    make.width.height.equalTo(width)
                }
                //hintView.contentScale = 0.98
                
                viewPreview = SVGKFastImageView(svgkImage: nil)!
                viewPreview.accessibilityIdentifier = "preview_view"
                viewPreview!.image = (svgBlock)
                //viewPreview!.frame.size = lp
                contentLayout.addSubview(viewPreview!)
                viewPreview.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                    make.width.height.equalTo(width)
                }
                
                viewDragging = SVGKFastImageView(svgkImage: nil)!
                viewDragging.accessibilityIdentifier = "dragging_view"
                viewDragging.image = (svgBlock)
                viewDragging.alpha = 0.9001
                //viewDragging.frame.size = lp
                dragViewContainer.addSubview(viewDragging!)
                viewDragging.snp.makeConstraints { make in
                    make.center.equalToSuperview()
                    make.width.height.equalTo(width)
                }
                moveToCorner(newItem: true)
                
                self.addGestureRecognizer(UIPanGestureRecognizer(target: self, action: #selector(handleDrag(_:))))
            }
        }
        var dX: CGFloat = 0
        var dY: CGFloat = 0
        
        @objc private func handleDrag(_ gesture: UIPanGestureRecognizer) {
            guard
                let viewDragging = viewDragging,
                let viewPreview = viewPreview
                //let hintView = contentLayout.subviews.first(where: { $0 is SVGKImageView && $0.alpha == 0 }),
                //let hintView = hintView as? SVGKImageView
            else { return }
            
            switch gesture.state {
            case .began:
                moved = false
                
                var moved = false
                dX = viewDragging.frame.origin.x - gesture.location(in: self).x
                dY = viewDragging.frame.origin.y - gesture.location(in: self).y
                viewDragging.superview!.bringSubviewToFront(viewDragging)
                hintView.alpha = 1
                QueueSoundPlayer.shared.play("effect/cungchoi_pick\(Int.random(in: 1...2))")
                
            case .changed:
                moved = true
                let translation = gesture.translation(in: gesture.view)
                viewDragging.center = CGPoint(
                    x: viewDragging.center.x + translation.x,
                    y: viewDragging.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: gesture.view)
                /*
                let point = gesture.location(in: self)
                viewDragging.frame.origin = CGPoint(x: point.x + dX, y: point.y + dY)
                print("Dragging: \(viewDragging.frame.origin)")
                 */
                viewPreview.center = CGPoint(x: viewDragging.center.x - (dragViewContainer.frame.width - contentLayout.frame.width) / 2, y: viewDragging.center.y)
                
                var minDistance: CGFloat = .greatestFiniteMagnitude
                var mTranslationX: CGFloat = 0
                var mTranslationY: CGFloat = 0
                var mI = 0
                var mJ = 0
                
                for i in 0..<mData!.currentData.count {
                    for j in 0..<mData!.currentData[i].count {
                        let value = mData!.currentData[i][j]
                        let translationX = -deltaWidth * CGFloat(i) + deltaWidth * CGFloat(j)
                        let translationY = deltaHeight * 0.5 + deltaHeight * CGFloat(i) + deltaHeight * CGFloat(j) + (width - 2 * deltaHeight) / 2 - CGFloat(value) * (width - 2 * deltaHeight)
                        let delta = 10 * pow(viewDragging.frame.origin.x - translationX, 2) + pow(viewDragging.frame.origin.y + width / 2 - translationY, 2)
                        if delta < minDistance {
                            minDistance = delta
                            mTranslationX = translationX
                            mTranslationY = translationY
                            mI = i
                            mJ = j
                        }
                    }
                }
                
                locationI = mI
                locationJ = mJ
                print("Dragging to: (\(mI), \(mJ)")
                let topView = viewOnTop[mI * 10 + mJ]
                if topView != lastTopView {
                    lastTopView = topView
                    rightLocation = mData!.currentData[mI][mJ] < mData!.originData[mI][mJ]
                    hintView.image = (rightLocation ? svgRight?.uiImage : svgWrong?.uiImage)
                    hintView.transform = CGAffineTransform(translationX: mTranslationX, y: mTranslationY)
                    hintView.alpha = 1
                    hintView.removeFromSuperview()
                    viewPreview.removeFromSuperview()
                    if let index = contentLayout.subviews.firstIndex(of: topView!) {
                        contentLayout.insertSubview(hintView, at: index + 1)
                        contentLayout.insertSubview(viewPreview, at: index + 2)
                    }
                }
                
            case .ended:
                if !moved { return }
                game?.pauseGame()
                if let lastTopView = lastTopView {
                    hintView.alpha = 0
                    let point = CGPoint(x: lastTopView.frame.origin.x, y: hintView.frame.origin.y - (self.width - 2 * self.deltaHeight) / 2)
                    UIView.animate(withDuration: 0.2, animations: {
                        self.viewPreview?.frame.origin = point
                    }, completion: { _ in
                        self.doEnd(hintView: self.hintView)
                    })
                }
                
            default:
                break
            }
        }
        func doEnd(hintView: UIView){
            if self.rightLocation {
                QueueSoundPlayer.shared.play("effect/word puzzle drop")
                let blockView = SVGKFastImageView(svgkImage: nil)!
                blockView.image = (self.svgBlock)
                blockView.frame.size = CGSize(width: self.width, height: self.width)
                if let index = self.contentLayout.subviews.firstIndex(of: hintView) {
                    self.contentLayout.insertSubview(blockView, at: index + 1)
                }
                blockView.transform = CGAffineTransform(translationX: -self.deltaWidth * CGFloat(self.locationI) + self.deltaWidth * CGFloat(self.locationJ), y: self.deltaHeight * 0.5 + self.deltaHeight * CGFloat(self.locationI) + self.deltaHeight * CGFloat(self.locationJ) - CGFloat(self.mData!.currentData[self.locationI][self.locationJ]) * (self.width - 2 * self.deltaHeight))
                //blockView.contentScale = 0.98
                blockView.tag = RBox.id.block
                self.mData!.currentData[self.locationI][self.locationJ] += 1
                self.viewOnTop[self.locationI * 10 + self.locationJ] = blockView
                
                let finished = self.mData!.currentData == self.mData!.originData
                if finished {
                    game?.stopBackgroundMusic()
                    game?.animateCoinIfCorrect(view: self)
                    let delay = game?.playSound("effect/answer_end", (game?.getCorrectHumanSound())!, (game?.endGameSound())!)
                    game?.scheduler.schedule(delay: delay!) { [weak self] in
                        self?.game?.finishGame()
                    }
                    UIView.animate(withDuration: 0.6, delay: 1.0, animations: {
                        self.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
                    }, completion: { _ in
                        UIView.animate(withDuration: 0.5) {
                            self.transform = .identity
                        }
                    })
                } else {
                    QueueSoundPlayer.shared.play("effect/answer_correct")
                    game?.scheduler.schedule(delay: 0.5) { [weak self] in
                        self?.moveToCorner(newItem: true)
                        self?.game?.playSound("effect/slide2")
                        self?.game?.resumeGame()
                    }
                }
            } else {
                QueueSoundPlayer.shared.play("effect/answer_wrong")
                //viewPreview?.pivotX = viewPreview?.frame.width ?? 0 / 2
                //viewPreview?.pivotY = (viewPreview?.frame.height ?? 0) * 3 / 4
                viewPreview.layer.anchorPoint = CGPoint(x: 0.5, y: 0.75)
                UIView.animate(withDuration: 0.3, animations: {
                    //game?.viewPreview?.rotation = [0, -2, 0, 2, 0, -2, 0, 2, 0]
                }, completion: { _ in
                    self.game!.resumeGame()
                    self.moveToCorner(newItem: false)
                })
            }
        }
    }
}

extension SVGKImage {
    func withFillColor(_ color: UIColor) -> SVGKImage {
        self.fillColor(color: color, opacity: 1)
        return self
    }
}


struct RBox {
    struct id {
        static let block = -1
        static let item_content_layout = -2
        static let drag_view_container = -3
        static let background = -4
    }
}
