//
//  toancoban_list_pheptinhbongbay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_pheptinhbongbay: NhanBietGameFragment {
    // MARK: - Properties
    private var textCurrent: HeightRatioTextView?
    private var numpad: MathNumpad!
    private var a: Int = 0
    private var b: Int = 0
    private var itemContainer: UIView!
    private var textA: HeightRatioTextView!
    private var textB: HeightRatioTextView!
    private var textResult: HeightRatioTextView!
    private var svgView1: SVGKFastImageView!
    private var svgView2: SVGKFastImageView!
    private var zPos: CGFloat = 1000.0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#663535")
        
        let bgPark = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_park"))!
        bgPark.contentMode = .scaleAspectFill
        addSubview(bgPark)
        bgPark.makeViewCenterFillAndKeep(ratio: 2268.0 / 1236.2)
        self.bringSubviewToFront(view)
        
        let numpadContainer = UIView()
        view.addSubview(numpadContainer)
        numpadContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = false
        numpadContainer.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(numpadContainer.snp.left)
        }
        
        itemContainer = UIView()
        leftView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 3.0 / 2.0) // Ratio 3:2
        
        svgView1 = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "toan_pheptinhbongbay1"))!
        itemContainer.addSubview(svgView1)
        svgView1.snp.makeConstraints { make in
            make.width.equalTo(svgView1.snp.height).multipliedBy(399.0/667.0)
            make.height.equalTo(itemContainer).multipliedBy(0.6)
            make.bottom.equalToSuperview()
        }
        svgView1.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.svgView1.snapToHorizontalBias(horizontalBias: 0.9)
        }
        
        svgView2 = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "toan_pheptinhbongbay2"))!
        itemContainer.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalTo(itemContainer).multipliedBy(0.5)
            make.width.equalTo(svgView2.snp.height)
        }
        svgView2.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.svgView2.snapToHorizontalBias(horizontalBias: 0.92)
        }
        
        let equationContainer = UIView()
        itemContainer.addSubview(equationContainer)
        equationContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.65)
            make.width.equalTo(equationContainer.snp.height).multipliedBy(4.4) // Ratio 4.4:1
        }
        equationContainer.waitForLayout {
            equationContainer.snapToHorizontalBias(horizontalBias: 0.1)
            equationContainer.snapToVerticalBias(verticalBias: 0.95)
        }
        
        let textALayout = UIImageView(image: Utilities.SVGImage(named: "math_result_green"))
        equationContainer.addSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.height.equalTo(textALayout.snp.width)
            make.left.top.bottom.equalToSuperview()
        }
        
        textA = HeightRatioTextView()
        textA.text = "?"
        textA.setHeightRatio(0.7)
        textA.textColor = UIColor(hex: "#FFD31E")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textALayout.isUserInteractionEnabled = true
        textALayout.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextA)))
        
        let minusLayout = UIView()
        equationContainer.addSubview(minusLayout)
        minusLayout.snp.makeConstraints { make in
            make.height.equalTo(minusLayout.snp.width).dividedBy(0.6)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textALayout.snp.right)
        }
        
        let minusText = HeightRatioTextView()
        minusText.text = "-"
        minusText.setHeightRatio(0.7)
        minusText.textColor = UIColor(hex: "#FFD31E")
        minusText.font = .Freude(size: 20)
        minusText.textAlignment = .center
        minusText.adjustsFontSizeToFitWidth = true
        minusText.minimumScaleFactor = 0.1
        minusLayout.addSubview(minusText)
        minusText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_green"))
        equationContainer.addSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.height.equalTo(textBLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(minusLayout.snp.right)
        }
        
        textB = HeightRatioTextView()
        textB.text = "?"
        textB.setHeightRatio(0.7)
        textB.textColor = UIColor(hex: "#FFD31E")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textBLayout.isUserInteractionEnabled = true
        textBLayout.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextB)))
        
        let equalLayout = UIView()
        equationContainer.addSubview(equalLayout)
        equalLayout.snp.makeConstraints { make in
            make.height.equalTo(equalLayout.snp.width).dividedBy(0.6)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textBLayout.snp.right)
        }
        
        let equalText = HeightRatioTextView()
        equalText.text = "="
        equalText.setHeightRatio(0.7)
        equalText.textColor = UIColor(hex: "#FFD31E")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalLayout.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textResultLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_green"))
        equationContainer.addSubview(textResultLayout)
        textResultLayout.snp.makeConstraints { make in
            make.height.equalTo(textResultLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(equalLayout.snp.right)
        }
        
        textResult = HeightRatioTextView()
        textResult.text = "?"
        textResult.setHeightRatio(0.7)
        textResult.textColor = UIColor(hex: "#FFD31E")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        textResultLayout.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textResultLayout.isUserInteractionEnabled = true
        textResultLayout.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextResult)))
        
        itemContainer.layer.zPosition = zPos
    }
    
    // MARK: - Gesture Handlers
    @objc private func selectTextA() {
        select(view: textA)
    }
    
    @objc private func selectTextB() {
        select(view: textB)
    }
    
    @objc private func selectTextResult() {
        select(view: textResult)
    }
    
    // MARK: - Selection Logic
    private func select(view: HeightRatioTextView?) {
        if let current = textCurrent?.superview as? UIImageView {
            current.image = Utilities.SVGImage(named: "math_result_green")
        }
        if let view = view {
            numpad.isEnabled = true
            numpad.reset(value: 0)
            view.text = "?"
            if let parent = view.superview as? UIImageView {
                parent.image = Utilities.SVGImage(named: "math_result_green_focus")
            }
            view.textColor = UIColor(hex: "#FFD31E")
        } else {
            numpad.isEnabled = false
        }
        textCurrent = view
    }
    
    // MARK: - Game Logic
    private func checkFinish() {
        var aValue = 0
        var bValue = 0
        var resultValue = 0
        var filled = true
        
        if let aText = textA.text, !aText.isEmpty {
            if let value = Int(aText) {
                aValue = value
            } else {
                textA.textColor = UIColor(hex: "#FF7760")
                filled = false
            }
        }
        
        if let bText = textB.text, !bText.isEmpty {
            if let value = Int(bText) {
                bValue = value
            } else {
                textB.textColor = UIColor(hex: "#FF7760")
                filled = false
            }
        }
        
        if let resultText = textResult.text, !resultText.isEmpty {
            if let value = Int(resultText) {
                resultValue = value
            } else {
                textResult.textColor = UIColor(hex: "#FF7760")
                filled = false
            }
        }
        
        if a == aValue && b == bValue && a - b == resultValue {
            textA.textColor = UIColor(hex: "#FFD31E")
            textB.textColor = UIColor(hex: "#FFD31E")
            textResult.textColor = UIColor(hex: "#FFD31E")
            pauseGame()
            animateCoinIfCorrect(view: textResult)
            let delay = playSound(
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                "topics/Numbers/\(a)",
                "toan/trừ",
                "topics/Numbers/\(abs(b))",
                "toan/bằng",
                "topics/Numbers/\(resultValue)",
                endGameSound()
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else if filled {
            setGameWrong()
            pauseGame()
            animateCoinIfCorrect(view: textResult)
            textA.textColor = UIColor(hex: aValue == a ? "#FFD31E" : "#FF7761")
            textB.textColor = UIColor(hex: bValue == b ? "#FFD31E" : "#FF7761")
            textResult.textColor = UIColor(hex: resultValue == a - b ? "#FFD31E" : "#FF7761")
            var delay: TimeInterval = 0
            delay += playSound(name: "toan/toan_phep tinh bong bay 20_nham roi", delay: delay)
            scheduler.schedule(after: delay) { [weak self] in
                if aValue != self?.a {
                    self?.textA.text = String(self?.a ?? 0)
                    self?.textA.textColor = UIColor(hex: "#88D758")
                    self?.playSound("effect/answer_correct")
                }
            }
            delay += playSound(delay: delay, names: ["topics/Numbers/\(a)", "toan/toan_phep tinh bong bay 20_qua bong"])
            scheduler.schedule(after: delay) { [weak self] in
                if bValue != self?.b {
                    self?.textB.text = String(self?.b ?? 0)
                    self?.textB.textColor = UIColor(hex: "#88D758")
                    self?.playSound("effect/answer_correct")
                }
            }
            delay += playSound(delay: delay, names: ["topics/Numbers/\(b)", "toan/toan_phep tinh bong bay 20_qua bong bay mat"])
            scheduler.schedule(after: delay) { [weak self] in
                if resultValue != (self?.a ?? 0) - (self?.b ?? 0) {
                    self?.textResult.text = String((self?.a ?? 0) - (self?.b ?? 0))
                    self?.textResult.textColor = UIColor(hex: "#88D758")
                    self?.playSound("effect/answer_correct")
                }
            }
            delay += playSound(delay: delay, names: ["topics/Numbers/\((a - b))", "toan/toan_phep tinh bong bay 20_qua bong", endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    override func updateData() {
        super.updateData()
        while true {
            a = 10 + Int.random(in: 0..<10)
            b = 1 + Int.random(in: 0..<9)
            if a > b && a - b <= 9 {
                break
            }
        }
        
        // Giả lập SVGManager với SVGKFastImageView
        let svg1 = Utilities.GetSVGKImage(named: "toan_pheptinhbongbay1")
        let svg2 = Utilities.GetSVGKImage(named: "toan_pheptinhbongbay2")
        
        // Tắt opacity cho các nhóm SVG không cần hiển thị
        for i in 0..<(10 - (a - b)) {
            if let group = svg1.caLayerTree.sublayers?[safe: i] {
                group.opacity = 0
            }
        }
        svgView1.image = svg1
        
        for i in 0..<(10 - b) {
            if let group = svg2.caLayerTree.sublayers?[safe: 9 - i] {
                group.opacity = 0
            }
        }
        svgView2.image = svg2
        
        textA.text = "?"
        textB.text = "?"
        textResult.text = "?"
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_phep tinh bong bay 20")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_phep tinh bong bay 20")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_pheptinhbongbay: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        var adjustedValue = value
        if value >= 100 {
            adjustedValue = value % 10
        }
        if let current = textCurrent {
            current.text = String(adjustedValue)
        }
    }
    
    func onDelClick(value: Int) {
        if let current = textCurrent {
            current.text = ""
        }
    }
    
    func onCheckClick(value: Int) {
        select(view: nil)
        checkFinish()
    }
}

