<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="560:616"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.caverock.androidsvg.SVGImageView
            android:id="@+id/image_thumb"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/gallery_placeholder_thumb"
            app:layout_constraintDimensionRatio="515:616"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/image_lock"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/icon_album_lock"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.85"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.32"
            app:layout_constraintWidth_percent="0.3" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>