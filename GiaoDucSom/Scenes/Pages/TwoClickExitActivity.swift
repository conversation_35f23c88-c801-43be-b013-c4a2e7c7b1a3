//
//  TwoClickExitActivity.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/3/25.
//


import UIKit

class TwoClickExitActivity: BaseViewController {
    // MARK: - Properties
    var gameFragment: NhanBietGameFragment?
    var timeoutTimer: TimeoutTimer = TimeoutTimer()
    var btnHint: UIButton?
    var btnClose: UIButton?
    var gameView: UIControl?
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
    }

    // MARK: - Setup UI
        private func setupUI() {
            // Root view (PercentConstraintLayout)
            view.backgroundColor = .white
            view.clipsToBounds = false
            
            // Game view (ConstraintLayout)
            if gameView == nil {
                gameView = UIControl()
                gameView?.isUserInteractionEnabled = true // clickable="true"
                view.addSubview(gameView!)
                gameView?.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
            }
            // btn_close_game (AnimatedImageButton)
            if btnClose == nil {
                btnClose = KUButton(type: .custom)
                btnClose?.alpha = 0.6
                btnClose?.backgroundColor = .clear // #0000
                btnClose?.accessibilityLabel = "button close"
                btnClose?.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal) // Giả sử btn_close là tên asset
                btnClose?.contentMode = .scaleAspectFit // fitXY
                view.addSubview(btnClose!)
                btnClose!.snp.makeConstraints { make in
                    make.height.equalToSuperview().multipliedBy(0.1)
                    make.width.equalTo(btnClose!.snp.height)
                    make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
                }
                btnClose!.snpLeftTop(ratio: 1)
            }
            btnClose?.addTarget(self, action: #selector(onCloseTapped), for: .touchUpInside)

            // btn_hint (AnimatedImageButton)
            if btnHint == nil {
                btnHint = KUButton(type: .custom)
                btnHint?.alpha = 0
                btnHint?.backgroundColor = .clear // #0000
                btnHint?.accessibilityLabel = "button replay intro"
                btnHint?.setImage(Utilities.SVGImage(named: "btn_intro"), for: .normal) // Giả sử btn_intro là tên asset
                btnHint?.contentMode = .scaleAspectFit // fitXY
                btnHint?.isEnabled = false
                view.addSubview(btnHint!)
                btnHint!.snp.makeConstraints { make in
                    make.height.equalToSuperview().multipliedBy(0.1)
                    make.width.equalTo(btnHint!.snp.height)
                    make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.20)
                    make.left.equalTo(btnClose!)
                }
            }
            btnHint?.addTarget(self, action: #selector(onHintTapped), for: .touchUpInside)

            // Khởi tạo timeoutTimer
            timeoutTimer.duration = 2.0
            timeoutTimer.onActived = { [weak self] in
                guard let self = self else { return }
                self.hideClose()
            }
            gameView?.addTarget(self, action: #selector(onHintTapped), for: .touchUpInside)
        }

    // MARK: - Close/Hint Logic
    func showClose() {
        btnClose?.alpha = 1.0
        btnHint?.alpha = 1.0
        timeoutTimer.schedule()
        btnHint?.isEnabled = true
    }

    func hideClose() {
        btnClose?.alpha = 0.3
        btnHint?.alpha = 0
        btnHint?.isEnabled = false
        timeoutTimer.cancel()
    }

    // MARK: - Actions
    @objc private func onCloseTapped() {
        onBackPressed()
    }

    @objc private func onHintTapped() {
        gameFragment?.replayIntroSound()
        hideClose()
    }

    // MARK: - Back Pressed Logic
    func onBackPressed() {
        guard let btnClose = btnClose else { return }
        
        if btnClose.alpha < 1 {
            showClose()
            return
        }
        
        // Kiểm tra loại activity (giả sử dùng is để kiểm tra trong Swift)
        if self is SingleGameActivity || self is LanguageActivity || self is WritingVNActivity {
            onConfirmExit()
            return
        }
        
        playSound("vi/popup_quit_lesson")
        
        let dialog = MessageDialogView()
        dialog.setTitle("Thoát bài học")
            .setMessage("Bé có chắc chắn muốn thoát khỏi bài học này không?")
            .setButtonOkText("Học tiếp")
            .setButtonLaterText("Thoát")
            .setSvgLogoPath("icon_quit")
            .setSvgLogoColor(.red)
            .setShowLaterButton(true)
            .setShowOkButton(true)
            .setListener(onConfirm: {
                [weak self] in
                guard let self = self else { return }
                
            }, onClose: { buttonIndex in
                switch buttonIndex {
                case .ok: print("OK tapped")
                case .confirm: self.onConfirmExit()
                case .close: print("Close tapped")
                }
            })

        dialog.showIn(view)
    }

    func onConfirmExit() {
        self.gameFragment?.clipsToBounds = true
        UIView.animate(withDuration: 0.3, animations: {
            self.gameFragment?.alpha = 0
        })
        self.navigationController?.popViewController(animated: true)
        //dismiss(animated: true, completion: nil) // Tương đương finish() trong Android
    }
}


// Giả lập các class Activity khác
class LanguageActivity: TwoClickExitActivity {}

// Cập nhật MessageDialogFragment để có buttonConfirm
