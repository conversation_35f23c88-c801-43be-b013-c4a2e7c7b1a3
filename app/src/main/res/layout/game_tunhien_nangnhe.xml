<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EBFAFB"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/rotation_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            android:rotation="0"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2534:358"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9">

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/center_line"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:orientation="horizontal"
                app:layout_constraintGuide_percent="0.4" />

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/tunhien_nangnhe_bg" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/view_top_left"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                app:layout_constraintBottom_toBottomOf="@id/center_line"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.05"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintWidth_percent="0.3">

                <com.kidsup.giaoducsom.view.SVGAutosizeView
                    android:id="@+id/svg_view_top_left"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/view_top_right"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                app:layout_constraintBottom_toBottomOf="@id/center_line"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.95"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintWidth_percent="0.3">

                <com.kidsup.giaoducsom.view.SVGAutosizeView
                    android:id="@+id/svg_view_top_right"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tunhien_nangnhe_bg2"
            app:layout_constraintBottom_toBottomOf="@id/rotation_view"
            app:layout_constraintLeft_toLeftOf="@id/rotation_view"
            app:layout_constraintRight_toRightOf="@id/rotation_view"
            app:layout_constraintTop_toTopOf="@id/rotation_view">

            <ImageView
                android:id="@+id/rock_left"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tunhien_nangnhe_rock"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="432:348"
                app:layout_constraintHorizontal_bias="0.12"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <ImageView
                android:id="@+id/rock_right"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/tunhien_nangnhe_rock"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="432:348"
                app:layout_constraintHorizontal_bias="0.88"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/view_left"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.05"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.40" />

        <View
            android:id="@+id/view_right"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintHorizontal_bias="0.95"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.40" />

    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>