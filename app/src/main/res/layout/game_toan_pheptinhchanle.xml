<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/main_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercent="0.1">


            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/c_sign_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="3.3"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/text_left"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="8 + 8 = "
                    android:textColor="#74B6FF"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/number_c"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_diendau_selected"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/text_right"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text=""
                    android:textColor="#74B6FF"
                    app:autoSizeMaxTextSize="500dp"
                    app:autoSizeTextType="uniform"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintHeight_percent="0.8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#C5F7FF"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:id="@+id/drag_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>