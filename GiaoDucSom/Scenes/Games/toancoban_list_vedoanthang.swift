//
//  toancoban_list_vedoanthang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_vedoanthang: toancoban_list_thuocke {
    // MARK: - Game Logic
    override func updateData() {
        distance = 1 + Int.random(in: 0..<9) // 1 to 9
        while true {
            startPoint = Int.random(in: 0..<11) // 0 to 10
            if startPoint - distance >= 0 || startPoint + distance <= 10 {
                break
            }
        }
        let delay = 1.0 + playSound(delay: 1.0, names: ["toan/toan_ve doan thang", "topics/Numbers/\(distance)", "toan/toan_ve doan thang_cm"])
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_ve doan thang", "topics/Numbers/\(distance)", "toan/toan_ve doan thang_cm")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Snap Logic
    override func snap(index: Int) {
        guard let svg = svg else {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.snap(index: index)
            }
            return
        }
        
        if index != snapIndex || right != nil {
            //Utils.vibrate(context: self)
            let max = max(index, startPoint)
            let min = min(index, startPoint)
            for i in 0...10 {
                let color = (i >= min && i <= max && index != -1) ? (right == nil ? UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) : right! ? UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) : UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1)) : UIColor(red: 145/255, green: 162/255, blue: 174/255, alpha: 1) // #74B6FF, #87D657, #FF7760, #91A2AE
                (svg.caLayerTree.sublayers?[i + 2] as? CAShapeLayer)?.fillColor = color.cgColor
                svg.caLayerTree.sublayers?[i + 2].opacity = 1
            }
            svgRulerView.image = svg.uiImage
            snapIndex = index
        }
    }
}
