//
//  toancoban_list_congtrutronchuc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_congtrutronchuc
class toancoban_list_congtrutronchuc: NhanBietGameFragment {
    // MARK: - Properties
    private var a = 0
    private var b = 0
    private var sliderView: MySliderView!
    private var itemContainer: UIView!
    private var textResult: AutosizeLabel!
    private var textOperator: AutosizeLabel!
    private var operatorSymbol: String?

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")
        while true {
            a = 1 + Int.random(in: 0..<9)
            b = (1 + Int.random(in: 0..<9)) * (Int.random(in: 0...1) == 0 ? 1 : -1)
            if a + b >= 0 && a + b <= 10 {
                if a + b == 0 {
                    sliderView.setSelectedTick(10)
                }
                break
            }
        }
        print("\(a) + \(b) = \(a + b)")
        let topView = UIView()
        topView.stringTag = "top_view"
        view.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }

        itemContainer = UIView()
        itemContainer.stringTag = "item_container"
        topView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 5.4)
        itemContainer.transform = CGAffineTransformMakeScale(0.95, 0.95)

        let leftView = UIView()
        leftView.backgroundColor = .clear
        leftView.stringTag = "left_view"
        itemContainer.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        let rightView = UIView()
        rightView.backgroundColor = .clear
        rightView.stringTag = "right_view"
        itemContainer.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(leftView)
        }
        
        var leftAnchorView = leftView
        for _ in 0..<a {
            let imageView = UIImageView(image: Utilities.SVGImage(named: "toan_congtrutronchuc"))
            imageView.contentMode = .scaleAspectFit
            itemContainer.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.left.equalTo(leftAnchorView.snp.right)
                make.width.equalTo(imageView.snp.height).multipliedBy(0.15)
            }
            leftAnchorView = imageView
        }
        
        textOperator = AutosizeLabel()
        textOperator.stringTag = "text_operator"
        textOperator.text = "+"
        textOperator.textColor = UIColor(hex: "#87D657")
        textOperator.font = .Freude(size: 30)
        textOperator.textAlignment = .center
        itemContainer.addSubview(textOperator)
        
        textOperator.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(textOperator.snp.height)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.left.equalTo(leftAnchorView.snp.right)
        }
        
        leftAnchorView = textOperator
        
        for _ in 0..<abs(b) {
            let imageView = UIImageView(image: Utilities.SVGImage(named: "toan_congtrutronchuc"))
            imageView.contentMode = .scaleAspectFit
            itemContainer.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.top.bottom.equalToSuperview()
                make.left.equalTo(leftAnchorView.snp.right)
                make.width.equalTo(imageView.snp.height).multipliedBy(0.15)
            }
            leftAnchorView = imageView
        }
        

        let textEqual = AutosizeLabel()
        textEqual.stringTag = "text_equal"
        textEqual.text = "="
        textEqual.textColor = UIColor(hex: "#74B6FF")
        textEqual.font = .Freude(size: 30)
        textEqual.textAlignment = .center
        itemContainer.addSubview(textEqual)
        textEqual.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(textEqual.snp.height)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.left.equalTo(leftAnchorView.snp.right)
        }
                
        
        let resultBg = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        resultBg.stringTag = "result_bg"
        itemContainer.addSubview(resultBg)

        textResult = AutosizeLabel()
        textResult.stringTag = "text_result"
        textResult.text = "?"
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 30)
        textResult.textAlignment = .center
        textResult.transform = CGAffineTransformMakeScale(0.8, 0.8)
        itemContainer.addSubview(textResult)

        textResult.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.width.equalTo(textResult.snp.height)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.left.equalTo(textEqual.snp.right)
            make.right.equalTo(rightView.snp.left)
        }
        
        resultBg.snp.makeConstraints { make in
            make.left.right.bottom.equalTo(textResult)
            make.top.equalTo(textResult)
        }
        
        let bottomView = UIView()
        bottomView.stringTag = "bottom_view"
        bottomView.clipsToBounds = false
        bottomView.layer.masksToBounds = false
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(topView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        sliderView = MySliderView()
        sliderView.stringTag = "slider_view"
        bottomView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(sliderView.snp.width).dividedBy(6)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }
        sliderView.setMinValue(0)
        sliderView.setMaxValue(100)
        sliderView.setStep(10)
        sliderView.setSelectedTick(0)
        sliderView.setListener { [weak self] value in
            guard let self = self else { return }
            self.textResult.text = "\(value)"
            let correct = value == (self.a + self.b) * 10
            self.textResult.textColor = correct ? UIColor(hex: "#87D657") : UIColor(hex: "#FF7760")
            self.pauseGame()
            self.sliderView.setRight(correct)
            if correct {
                self.animateCoinIfCorrect(view: self.textResult)
                let delay = self.playSound(self.answerCorrect1EffectSound(), self.getCorrectHumanSound(), "topics/Numbers/\(10 * self.a)", self.operatorSymbol == "+" ? "toan/cộng" : "toan/trừ", "topics/Numbers/\(abs(10 * self.b))", "toan/bằng", "topics/Numbers/\(value)", self.endGameSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                self.setGameWrong()
                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.textResult.text = "?"
                    self.textResult.textColor = UIColor(hex: "#74B6FF")
                    self.sliderView.setRight(nil)
                    self.resumeGame()
                }
            }
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        operatorSymbol = b > 0 ? "+" : b < 0 ? "-" : ["+", "-"].randomElement()
        textOperator.text = operatorSymbol
        if operatorSymbol == "-" {
            textOperator.textColor = UIColor(hex: "#FF7760")
        }
        for i in a..<10 {
            itemContainer.viewWithStringTag("view\(32 + i)")?.isHidden = true
        }
        for i in 11 + abs(b)..<21 {
            itemContainer.viewWithStringTag("view\(32 + i)")?.isHidden = true
        }
        let delay = playSound(openGameSound(), "toan/toan_congtruphamvi5")
        scheduler.schedule(delay: Double(delay)) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_congtruphamvi5")
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}
