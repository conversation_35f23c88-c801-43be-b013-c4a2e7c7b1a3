//
//  ngonngu_list_fill.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/5/25.
//

import UIKit
import SnapKit
import SVGKit
import AVFAudio

class ngonngu_list_fill: NhanBietGameFragment {
    // MARK: - Properties
    var category: String?
    private var lines: [String] = []
    private var itemContainer: UIStackView!
    private var strokeSvg: SVGKImage?
    private var strokeIndex: Int = 0
    private var currentStroke: SVGImageView?
    private var optionContainer2: UIView!
    private var option21: KUButton!
    private var option22: KUButton!
    private var itemTypes: [String] = []
    private var option21SvgView: SVGImageView!
    private var option22SvgView: SVGImageView!
    private var option21Text: UILabel!
    private var option22Text: UILabel!
    private var lineIndex: Int = -1
    private var stickerItems: [StickerItem] = []
    private var nextReplay: UIButton!
    private var currentTextView: UILabel?
    private var currentSvgView: SVGImageView?
    private var currentImageView: UIImageView?
    private var isReplay: Bool = false
    private var items: [StickerItem] = []
    private var nextReplaySound: UIButton!
    private var wordList: [CsvWord] = []
    private var stopIndex: [Int] = []
    private var rightAnswerIndex: Int = 0
    private var questionIndex: Int = 0
    private var svgThumbnail: SVGImageView!
    private var answers: [[String]] = []
    var data: LanguageItem?
    private var player: AVAudioPlayer?
    private var wordIndex: Int = -1
    private var pauseTimer: Timer? // Thêm Timer cho pauseRunnable
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFFFFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        self.createItemContainer()
        
        optionContainer2 = UIView()
        optionContainer2.isHidden = true
        view.addSubview(optionContainer2)
        optionContainer2.makeViewCenterAndKeep(ratio: 1217.0/575.0)
        optionContainer2.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.5)
            make.centerY.equalToSuperview()
        }
        
        option21 = KUButton()
        option21.setImage(Utilities.SVGImage(named: "ngonngu_truyentranh_bg_option2"), for: .normal)
        option21.clipsToBounds = false
        optionContainer2.addSubview(option21)
        option21.makeViewCenterAndKeep(ratio: 1)
        option21.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.centerX.equalToSuperview().multipliedBy(0.34) // bias 0.17
            make.centerY.equalToSuperview().multipliedBy(0.6) // bias 0.3
        }
        
        option21SvgView = SVGImageView(frame: .zero)
        option21SvgView.stringTag = "option21_svg_view"
        option21.addSubview(option21SvgView)
        option21SvgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        option21Text = UILabel()
        option21Text.stringTag = "option21_text"
        option21Text.textColor = UIColor(hex: "#74B6FF")
        option21Text.textAlignment = .center
        option21Text.font = UIFont(name: "SVN-Freude", size: 100)
        option21Text.adjustsFontSizeToFitWidth = true
        option21Text.minimumScaleFactor = 0.1
        option21Text.numberOfLines = 1
        option21.addSubview(option21Text)
        option21Text.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.3)
            make.centerX.equalToSuperview()
            make.top.equalTo(option21SvgView.snp.bottom)
        }
        
        option22 = KUButton()
        option22.setImage(Utilities.SVGImage(named: "ngonngu_truyentranh_bg_option2"), for: .normal)
        option22.clipsToBounds = false
        optionContainer2.addSubview(option22)
        option22.makeViewCenterAndKeep(ratio: 1)
        option22.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.centerX.equalToSuperview().multipliedBy(1.66) // bias 0.83
            make.centerY.equalToSuperview().multipliedBy(0.6)
        }
        
        option22SvgView = SVGImageView(frame: .zero)
        option22SvgView.stringTag = "option22_svg_view"
        option22.addSubview(option22SvgView)
        option22SvgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        option22Text = UILabel()
        option22Text.stringTag = "option22_text"
        option22Text.textColor = UIColor(hex: "#74B6FF")
        option22Text.textAlignment = .center
        option22Text.font = UIFont(name: "SVN-Freude", size: 100)
        option22Text.adjustsFontSizeToFitWidth = true
        option22Text.minimumScaleFactor = 0.1
        option22Text.numberOfLines = 1
        option22.addSubview(option22Text)
        option22Text.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.3)
            make.centerX.equalToSuperview()
            make.top.equalTo(option22SvgView.snp.bottom)
        }
        
        nextReplay = UIButton()
        nextReplay.stringTag = "next_replay"
        nextReplay.setImage(Utilities.SVGImage(named: "btn_check"), for: .normal)
        nextReplay.isHidden = true
        view.addSubview(nextReplay)
        nextReplay.makeViewCenterAndKeep(ratio: 188.0/199.0)
        nextReplay.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        
        nextReplaySound = UIButton()
        nextReplaySound.stringTag = "next_replay_sound"
        nextReplaySound.setImage(Utilities.SVGImage(named: "ngonngu_truyentranh_replay"), for: .normal)
        nextReplaySound.isHidden = true
        view.addSubview(nextReplaySound)
        nextReplaySound.makeViewCenterAndKeep(ratio: 1)
        nextReplaySound.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.1)
            make.right.equalToSuperview()
            make.top.equalToSuperview().offset(view.frame.height * 0.05)
        }
        
        svgThumbnail = SVGImageView(frame: .zero)
        svgThumbnail.stringTag = "svg_thumbnail"
        view.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.7)
            make.center.equalToSuperview()
        }
        
        // Touch effects
        option21.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(option21Tapped)))
        option22.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(option22Tapped)))
        nextReplaySound.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(nextReplaySoundTapped)))
        
        // Start pause timer
        startPauseTimer()
    }
    func createItemContainer(){
        if itemContainer != nil {
            itemContainer.removeFromSuperview()
        }
        itemContainer = UIStackView()
        //itemContainer.backgroundColor = .red.withAlphaComponent(0.3)
        itemContainer.axis = .horizontal
        itemContainer.spacing = 0
        itemContainer.alignment = .center
        itemContainer.distribution = .equalSpacing
        itemContainer.clipsToBounds = false
        contentView?.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.height.equalTo(1000)
            make.center.equalToSuperview()
        }
    }
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        strokeSvg = Utilities.GetSVGKImage(named: "ngonngu_option")        
        scheduler.schedule(delay: 2) {
            [weak self] in
            guard let self = self else { return }
            //startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        if let category = category, data != nil {
            // Data already set
        } else if let category = category {
            guard let stickerSizeUrl = Bundle.main.url(forResource: "language_data", withExtension: "json"),
                  let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
                  var topics = try? JSONDecoder().decode([LanguageTopic].self, from: stickerSizeData) else {
                print("Error loading sticker_size.json")
                return
            }
            
            for topic in topics {
                let cat = topic.category == "Thơ" ? "tho" : topic.category == "Đồng dao" ? "dong dao" : topic.category == "Truyện" ? "truyen" : "cau do"
                if cat == category {
                    self.category = cat
                    data = topic.item.randomElement()
                    break
                }
            }
        } else {
            guard let stickerSizeUrl = Bundle.main.url(forResource: "language_data", withExtension: "json"),
                  let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
                  var topics = try? JSONDecoder().decode([LanguageTopic].self, from: stickerSizeData) else {
                print("Error loading sticker_size.json")
                return
            }
            
            var topic = topics.randomElement()
            data = topic?.item.randomElement()
            #if DEBUG
            //topic = topics.first
            //data = topic?.item.first
            #endif
            self.category = topic?.category == "Thơ" ? "tho" : topic?.category == "Đồng dao" ? "dong dao" : topic?.category == "Truyện" ? "truyen" : "cau do"
            
        }
        
        if let category = category, let data = data {
            let url = Utilities.SVGURL(of: "language/\(category)/\(data.name).svg")
            if url != nil {
                let image = SVGKImage(contentsOf: url)!
                svgThumbnail.contentMode = .scaleAspectFit
                svgThumbnail.image = image.uiImage
            }
            let delay = playSound(delay: 0, names: ["vi/ngonngu/\(category)/intro", "vi/ngonngu/\(category)/\(data.name)"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.svgThumbnail.isHidden = true
                self?.startReading()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let category = category, let data = data else { return }
            let delay = playSound(delay: 0, names: ["vi/ngonngu/\(category)/intro", "vi/ngonngu/\(category)/\(data.name)"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func option21Tapped() {
        if gameState != .playing { return }
        pauseGame(stopMusic: false)
        guard let tag = option21.tag as? Int else { return }
        var delay = playSound(getSoundPath(answers[tag]))
        if tag == rightAnswerIndex {
            delay += playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound()])
            animateCoinIfCorrect(view: option21)
            scheduler.schedule(after: delay) { [weak self] in
                self?.loadQuestion((self?.questionIndex ?? 0) + 1)
            }
        } else {
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            setGameWrong()
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    @objc private func option22Tapped() {
        if gameState != .playing { return }
        pauseGame(stopMusic: false)
        guard let tag = option22.tag as? Int else { return }
        var delay = playSound(getSoundPath(answers[tag]))
        if tag == rightAnswerIndex {
            delay += playSound(delay: delay, names: ["effect/answer_correct1", getCorrectHumanSound()])
            animateCoinIfCorrect(view: option22)
            scheduler.schedule(after: delay) { [weak self] in
                self?.loadQuestion((self?.questionIndex ?? 0) + 1)
            }
        } else {
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            setGameWrong()
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    @objc private func nextReplaySoundTapped() {
        lineIndex = 0
        isReplay = true
        itemContainer.isHidden = false
        nextReplay.isHidden = true
        optionContainer2.isHidden = true
        loadLine(lineIndex, showAll: false)
    }
    
    // MARK: - Helper Methods
    private func startReading() {
        guard let data = data else { return }
        lines = data.desc ?? []
        loadItems()
        lineIndex = 0
        isReplay = true
        loadLine(lineIndex, showAll: true)
        updateStroke()
    }
    
    private func loadItems() {
        stickerItems = []
        for line in lines {
            let words = splitString(line)
            for word in words {
                if word.contains("[") && word.contains("]") {
                    let cleanWord = word[word.index(after: word.firstIndex(of: "[")!)..<word.firstIndex(of: "]")!]
                    let items = cleanWord.components(separatedBy: ":")
                    if !stickerItems.contains(where: { $0.name == items[0] }) {
                        var item = StickerItem()
                        item.name = items[0]
                        item.path = "images/\(items[1]).svg"
                        stickerItems.append(item)
                    }
                }
            }
        }
    }
    
    private func loadLine(_ lineIndex: Int, showAll: Bool) {
        nextReplaySound.isHidden = true
        if lineIndex == 0 {
            itemContainer.transform = self.itemContainer.transform.translatedBy(x: -self.frame.width, y: 0)
        }
        UIView.animate(withDuration: 0.5, animations: {
            self.itemContainer.transform = self.itemContainer.transform.translatedBy(x: -self.frame.width, y: 0) //CGAffineTransform(translationX: -self.frame.width, y: 0)
            self.itemContainer.alpha = 0.01
        }) { _ in
            guard self.itemContainer != nil else { return }
            self.player = nil
            self.stopIndex = []
            self.wordIndex = -1
            
            guard let category = self.category, let data = self.data else { return }
            let path = "Sounds/vi/ngonngu/\(category)/\(data.name)_\(lineIndex + 1).mp3"
            if let url = Bundle.main.url(forResource: path, withExtension: nil) {
                do {
                    self.player = try AVAudioPlayer(contentsOf: url)
                    self.player?.prepareToPlay()
                } catch {
                    print(error)
                }
            }
                                   
            self.wordList = self.parse("Sounds/vi/ngonngu/\(category)/\(data.name)_\(lineIndex + 1)")
            self.itemContainer.arrangedSubviews.forEach { $0.removeFromSuperview() }
            self.createItemContainer()
            let line = self.lines[lineIndex]
            let words = self.splitString(line)
            self.itemTypes = []
            var afterPhoto = false
            
            for (i, word) in words.enumerated() {
                if word.contains("[") && word.contains("]") {
                    let cleanWord = word[word.index(after: word.firstIndex(of: "[")!)..<word.firstIndex(of: "]")!]
                    let items = cleanWord.components(separatedBy: ":")
                    let frameLayout = UIView()
                    frameLayout.clipsToBounds = false
                    
                    let backgroundImage = UIImageView()
                    backgroundImage.stringTag = "background_image"
                    backgroundImage.image = Utilities.SVGImage(named: "ngonngu_option")
                    frameLayout.addSubview(backgroundImage)
                    backgroundImage.snp.makeConstraints { make in
                        make.width.height.equalTo(400)
                        make.center.equalToSuperview()
                    }
                    
                    let svgView = SVGImageView(frame: .zero)
                    svgView.stringTag = "svg_view"
                    frameLayout.addSubview(svgView)
                    svgView.snp.makeConstraints { make in
                        make.width.height.equalTo(400)
                        make.center.equalToSuperview()
                    }
                    
                    let tv = UILabel()
                    tv.text = (afterPhoto ? " " : "") + items[0].replacingOccurrences(of: "_", with: " ") + " "
                    tv.textColor = UIColor(hex: "#565656")
                    tv.textAlignment = .center
                    tv.font = UIFont(name: "SVN-Freude", size: 200)
                    tv.numberOfLines = 0
                    frameLayout.addSubview(tv)
                    tv.snp.makeConstraints { make in
                        make.width.equalToSuperview()
                        make.height.equalTo(400)
                        make.center.equalToSuperview()
                    }
                    
                    self.itemContainer.addArrangedSubview(frameLayout)
                    afterPhoto = true
                    self.itemTypes.append("photo")
                    
                    if self.isReplay {
                        backgroundImage.isHidden = true
                        svgView.image = Utilities.GetSVGKImage(named: "\(items[1]).svg").uiImage
                        svgView.transform = CGAffineTransform(translationX: 0, y: -250)
                        tv.isHidden = false
                    } else {
                        self.stopIndex.append(i)
                        tv.isHidden = true
                    }
                    
                    let length = items[0].components(separatedBy: " ").count
                    if length > 1 {
                        for _ in 1..<length {
                            self.wordList.remove(at: i + 1)
                        }
                    }
                } else {
                    let tv = UILabel()
                    tv.text = (afterPhoto ? " " : "") + word.replacingOccurrences(of: "_", with: " ") + " "
                    tv.textColor = UIColor(hex: "#565656")
                    tv.textAlignment = .center
                    tv.font = UIFont(name: "SVN-Freude", size: 200)
                    tv.numberOfLines = 0
                    self.itemContainer.addArrangedSubview(tv)
                    afterPhoto = false
                    self.itemTypes.append("text")
                }
            }
            
            self.itemContainer.alpha = 0.01
            self.scheduler.schedule(delay: 0.1) {
                [weak self] in
                guard let self = self else { return }
                let scaleWidth = 0.9 / (self.itemContainer.frame.width / self.frame.width)
                let scaleHeight = 0.5 / (self.itemContainer.frame.height / self.frame.height)
                UIView.animate(withDuration: 0, animations: {
                    self.itemContainer.transform = CGAffineTransform(scaleX: min(scaleWidth, scaleHeight), y: min(scaleWidth, scaleHeight)).translatedBy(x: self.frame.width, y: 0)
                    self.itemContainer.alpha = 1
                })
                UIView.animate(withDuration: 0.5, delay: 0, options: .curveLinear, animations: {
                    self.itemContainer.transform = CGAffineTransform(scaleX: min(scaleWidth, scaleHeight), y: min(scaleWidth, scaleHeight))
                    self.itemContainer.alpha = 1
                })
                self.scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.player?.play()
                }
            }
            
            if self.stopIndex.isEmpty {
                self.scheduler.schedule(delay: (self.player?.duration ?? 0) + 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.lineIndex += 1
                    if self.lineIndex < self.lines.count {
                        self.loadLine(self.lineIndex, showAll: false)
                    } else {
                        self.itemContainer.isHidden = true
                        self.nextReplay.isHidden = true
                        self.loadQuestion(0)
                    }
                }
            }
        }
    }
    
    private func loadQuestion(_ questionIndex: Int) {
        nextReplaySound.isHidden = false
        guard let data = data, questionIndex <= data.question.count - 1 else {
            pauseGame(stopMusic: false)
            let delay = playSound(endGameSound())
            scheduler.schedule(after: delay + 2) { [weak self] in
                self?.finishGame()
            }
            return
        }
        
        resumeGame(startMusic: false)
        self.questionIndex = questionIndex
        optionContainer2.isHidden = false
        
        let askAnswer = data.question[questionIndex]
        let answers1 = askAnswer.answer[0].replacingOccurrences(of: "[", with: "").replacingOccurrences(of: "]", with: "").components(separatedBy: ":")
        let answers2 = askAnswer.answer[1].replacingOccurrences(of: "[", with: "").replacingOccurrences(of: "]", with: "").components(separatedBy: ":")
        answers = [answers1, answers2]
        
        let indexes = (0..<2).shuffled()
        rightAnswerIndex = 0
        option21.alpha = 0
        option21SvgView.image = Utilities.GetSVGKImage(named: "\(answers[indexes[0]][1]).svg").uiImage
        option21Text.text = answers[indexes[0]][0]
        option22.alpha = 0
        option22SvgView.image = Utilities.GetSVGKImage(named: "\(answers[indexes[1]][1]).svg").uiImage
        option22Text.text = answers[indexes[1]][0]
        option21.tag = indexes[0]
        option22.tag = indexes[1]
        
        let asks = askAnswer.ask.components(separatedBy: "#")
        var delay: TimeInterval = 0.1
        for ask in asks {
            if ask == "item" {
                delay += playSound(name: getSoundPath(answers[0]), delay: delay)
            } else {
                delay += playSound(name: "ngonngu/\(category ?? "")/\(ask.replacingOccurrences(of: "?", with: ""))", delay: delay)
            }
        }
        
        delay += 0.4
        playSound(name: "effect/bubble1", delay: delay)
        UIView.animate(withDuration: 0.8, delay: delay, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
            self.option21.transform = .identity
            self.option21.alpha = 1
        })
        
        delay += 0.4
        playSound(name: "effect/bubble2", delay: delay)
        UIView.animate(withDuration: 0.8, delay: delay, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
            self.option22.transform = .identity
            self.option22.alpha = 1
        })
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    
    private func getSoundPath(_ items: [String]) -> String {
        if items[1].contains("topics") {
            //return items[1]
        }
        return "ngonngu/\(items[0].lowercased())"
    }
    
    private func updateStroke() {
        guard !isReplay, let strokeSvg = strokeSvg, let currentStroke = currentStroke else {
            scheduler.schedule(delay: 0.03) { [weak self] in
                self?.updateStroke()
            }
            return
        }
        
        if let groups = strokeSvg.caLayerTree.sublayers, !groups.isEmpty {
            groups.forEach { layer in
                layer.isHidden = layer != groups[strokeIndex % groups.count]
            }
            currentStroke.image = strokeSvg.uiImage
            strokeIndex += 1
        }
        
        currentImageView?.alpha = strokeSvg != nil ? 0 : 1
        
        scheduler.schedule(delay: 0.03) { [weak self] in
            self?.updateStroke()
        }
    }
    
    private func highlightAt(_ wordIndex: Int) {
        guard wordIndex < itemContainer.arrangedSubviews.count else { return }
        let child = itemContainer.arrangedSubviews[wordIndex]
        //child.alpha = 0.5
        if child is UILabel {
            (child as? UILabel)?.textColor = UIColor(hex: "#74B6FF")
            currentStroke = nil
        } else if let frameLayout = child as? UIView {
            let list = frameLayout.findSubviews(ofType: UILabel.self)
            if let textView = list.first as? UILabel {
                currentTextView = textView
                if isReplay {
                    textView.textColor = UIColor(hex: "#74B6FF")
                }
            }
            if let svgView = frameLayout.subviews.first(where: { $0.stringTag == "svg_view" }) as? SVGImageView {
                currentSvgView = svgView
                currentStroke = svgView
            }
            if let imageView = frameLayout.subviews.first(where: { $0.stringTag == "background_image" }) as? UIImageView {
                currentImageView = imageView
            }
        }
    }
    
    private func showPopup() {
        guard let currentTextView = currentTextView else { return }
        let text = currentTextView.text?.trimmingCharacters(in: .whitespaces) ?? ""
        guard let stickerItem = stickerItems.first(where: { $0.name?.lowercased() == text.lowercased() }) else { return }
        
        while true {
            items = Array(stickerItems.shuffled().prefix(2))
            if items.contains(where: { $0.name == stickerItem.name }) {
                break
            }
        }
        
        UIView.animate(withDuration: 0.8) {
            self.itemContainer.transform = CGAffineTransform(translationX: 0, y: self.frame.height / 4)
        }
        nextReplaySound.isHidden = false
    }
    
    private func hidePopup() {
        UIView.animate(withDuration: 0.8, delay: 0.5) {
            self.itemContainer.transform = .identity
        }
        nextReplaySound.isHidden = true
    }
    
    private func splitString(_ text: String) -> [String] {
        let pattern = "\\[.*?\\]\\S*|\\S+"
        let regex = try? NSRegularExpression(pattern: pattern)
        let matches = regex?.matches(in: text, range: NSRange(text.startIndex..., in: text)) ?? []
        return matches.map { String(text[Range($0.range, in: text)!]) }
    }
    
    private func startPauseTimer() {
       pauseTimer = Timer.scheduledTimer(withTimeInterval: 0.01, repeats: true) { [weak self] _ in
           self?.checkPlayerPosition()
       }
   }
   
   private func checkPlayerPosition() {
       //print("\(Date())")
       guard let player = player, player.isPlaying, !wordList.isEmpty else { return }
       let currentPosition = player.currentTime
       //print("\(wordList)")
       print("\(Date()) - \(currentPosition) - \(wordIndex)")
       for (i, word) in wordList.enumerated() {
           if word.Start < currentPosition && wordIndex < i {
               if i == 2 {
                   let i = 2
               }
               wordIndex = i
               print("\(i)")
               if stopIndex.contains(i - 1) {
                   player.pause()
                   showPopup()
               } else {
                   highlightAt(i)
                   if i == wordList.count - 1, stopIndex.contains(i) {
                       let delay = player.duration - player.currentTime
                       scheduler.schedule(delay: delay) { [weak self] in
                           self?.wordIndex = self?.wordList.count ?? 0
                           self?.showPopup()
                       }
                   }
               }
               break
           }
       }
   }
    
    // MARK: - Cleanup
    deinit {
        pauseTimer?.invalidate()
        pauseTimer = nil
        player?.stop()
        player = nil
        stickerItems.removeAll()
        items.removeAll()
        itemContainer = nil
        optionContainer2 = nil
        option21 = nil
        option22 = nil
        nextReplay = nil
        nextReplaySound = nil
        svgThumbnail = nil
        strokeSvg = nil
        currentStroke = nil
        currentTextView = nil
        currentSvgView = nil
        currentImageView = nil
    }
    
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for i in 1..<lines.count {
                let line = lines[i]
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    words.append(word)
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        if texts.count == 3 {
            let minutes = Double(texts[0])!
            let seconds = Double(texts[1])!
            let miliseconds = Double(texts[2])!
            return minutes * 60.0 + seconds + miliseconds / 1000.0
        }
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: "\(name)", withExtension: "csv", subdirectory: nil, localization: "vi") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
}

// MARK: - Data Models
struct LanguageTopic: Codable {
    let category: String
    let item: [LanguageItem]
}

struct LanguageItem: Codable {
    let name: String
    let desc: [String]?
    let question: [AskAnswer]
        
    enum CodingKeys: String, CodingKey {
        case name
        case desc
        case question
    }
}

struct AskAnswer: Codable {
    let answer: [String]
    let ask: String
}

struct StickerItem {
    var name: String?
    var path: String?
}
