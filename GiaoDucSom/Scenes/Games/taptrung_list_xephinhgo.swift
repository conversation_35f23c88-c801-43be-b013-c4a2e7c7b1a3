//
//  taptrung_list_xephinhgo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_xephinhgo: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: SVGImageView!
    private var itemContainer: UIImageView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var Bounds: [[CGFloat]] = []
    private var views: [UIView] = []
    private var viewToPoints: [UIView: [CGPoint]] = [:]
    private var viewToOrigin: [UIView: CGPoint] = [:]
    private var svg: SVGKImage?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF2BB")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftContainer = UIView()
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        svgView = SVGImageView(frame: .zero)
        leftContainer.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
        
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        rightContainer.backgroundColor = .white
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        itemContainer = UIImageView()
        itemContainer.isUserInteractionEnabled = true
        itemContainer.clipsToBounds = false
        itemContainer.image = Utilities.SVGImage(named: "grid_puzzle_bg")
        rightContainer.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1)
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        // Logic handled in createGame
    }
    
    override func createGame() {
        super.createGame()
        
        let filename = "grid puzzle/\(Int.random(in: 2...25)*0+3).svg"
        svg = Utilities.GetSVGKImage(named: filename)
        loadData()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_grid puzzle")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.layer.zPosition = CGFloat.greatestFiniteMagnitude
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                playSound("effect/word puzzle drop")
                let stepX = itemContainer.frame.width / 7.0
                let stepY = itemContainer.frame.height / 7.0
                var frame = currentView.frame
                let minX = round(frame.minX / stepX) * stepX
                let minY = round(frame.minY / stepY) * stepY
                UIView.animate(withDuration: 0.2) {
                    currentView.frame = CGRectMake(minX, minY, frame.width, frame.height)
                } completion: { _ in
                    self.checkFinish()
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func loadData() {
        guard let svg = svg else { return }        
        
        let groups = svg.caLayerTree.sublayers ?? []
        let size = CGSize(width: itemContainer.frame.width, height: itemContainer.frame.height)
        let delays = (0..<groups.count).shuffled()
        var duration: TimeInterval = 0
        
        for gIndex in 0..<groups.count {
            let groupLayer = groups[gIndex]
            let groupView = SVGImageView(frame: .zero)
            var hidden:[Bool] = []
            for g in groups {
                hidden.append(g.isHidden)
                g.isHidden = g != groupLayer
                for i in 0..<g.sublayers!.count - 1 {
                    if let shapeLayer = g.sublayers?[i] as? CAShapeLayer {
                        shapeLayer.strokeColor = UIColor.clear.cgColor
                        shapeLayer.fillColor = UIColor.clear.cgColor
                    }
                }
            }
            groupView.image = svg.uiImage
            groupView.isUserInteractionEnabled = false
            itemContainer.addSubview(groupView)
            groupView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            for (i,g) in groups.enumerated() {
                g.isHidden = hidden[i]
            }
            var right = -CGFloat.greatestFiniteMagnitude
            var bottom = -CGFloat.greatestFiniteMagnitude
            var left = CGFloat.greatestFiniteMagnitude
            var top = CGFloat.greatestFiniteMagnitude
            
            for i in 1..<groupLayer.sublayers!.count - 1 {
                let bounds = (groupLayer.sublayers![i] as! CAShapeLayer).shapeContentBounds ?? CGRect.zero
                right = max(right, bounds.maxX)
                bottom = max(bottom, bounds.maxY)
                left = min(left, bounds.maxX)
                top = min(top, bounds.maxY)
            }
            
            let scale = itemContainer.frame.width / svg.size.width
            left *= scale
            right *= scale
            top *= scale
            bottom *= scale
            views.append(groupView)
            Bounds.append([left, top, right, bottom])
            let width = round((right - left) / (itemContainer.frame.width / 7.0))
            let height = round((bottom - top) / (itemContainer.frame.height / 7.0))
            
            let delay = TimeInterval(2.0 + 0.3 * CGFloat(delays[gIndex]))
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.viewToOrigin[groupView] = CGPointMake(groupView.frame.minX, groupView.frame.minY)
                UIView.animate(withDuration: 0.9, animations: {
                    groupView.transform = CGAffineTransform( translationX: -left + CGFloat(Int.random(in: 1...(6 - Int(width)))) * (self.itemContainer.frame.width / 7.0), y: -top + CGFloat(Int.random(in: 1...(6 - Int(height)))) * (self.itemContainer.frame.height / 7.0)
                    )
                    
                })
            }
            
            duration = max(duration, delay + 900)
            let offset = groupLayer.convert( CGRectMake(0, 0, 100, 100), to: svg.caLayerTree)
            let path = (groupLayer.sublayers![0] as? CAShapeLayer)?.path
            let points = getPoints(path: UIBezierPath(cgPath: path ?? CGPath(rect: .zero, transform: nil))).map{CGPointMake(($0.x+offset.minX) * scale, ($0.y+offset.minY) * scale)}
            viewToPoints[groupView] = points
        }
        loadHint()
        let delay: TimeInterval = playSound(delay: duration, names: [
            openGameSound(),
            "taptrung/taptrung_grid puzzle"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    private func loadHint() {
        guard let svg = svg else { return }
        let hintSVG = svg
        hintSVG.fillColor(color: .init(hex: "#CE8D60"), opacity: 1)
        hintSVG.caLayerTree.sublayers?.forEach { layer in
            layer.sublayers?.forEach { path in
                if let shapeLayer = path as? CAShapeLayer, shapeLayer == layer.sublayers?.last {
                    shapeLayer.strokeColor = UIColor.clear.cgColor
                    //shapeLayer.fillColor = UIColor.clear.cgColor
                }
                else {
                    path.isHidden = true
                }
            }
        }
        svgView.image = hintSVG.uiImage
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<itemContainer.subviews.count).reversed() {
            let view = itemContainer.subviews[i]
            let frame = view.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                if isPixelVisible(view: view, x: Int(x), y: Int(y)) {
                    return view
                }
            }
        }
        return nil
    }
    
    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard view.isHidden == false else { return false }
        return view.isPixelVisible(x: x, y: y)
    }
    
    private func checkFinish() {
        var listOrigin: [CGPoint] = []
        var listCurrent: [CGPoint] = []
        
        for view in views {
            viewToPoints.keys
            guard let points = viewToPoints[view] else { continue }
            listOrigin.append(contentsOf: points)
            listCurrent.append(contentsOf: points.map {
                CGPoint(x: view.frame.minX - viewToOrigin[view]!.x + $0.x, y: view.frame.minY - viewToOrigin[view]!.y + $0.y)
            })
        }
        
        var leftOrigin = CGFloat.greatestFiniteMagnitude
        var topOrigin = CGFloat.greatestFiniteMagnitude
        var leftCurrent = CGFloat.greatestFiniteMagnitude
        var topCurrent = CGFloat.greatestFiniteMagnitude
        
        for i in 0..<listOrigin.count {
            leftOrigin = min(leftOrigin, listOrigin[i].x)
            topOrigin = min(topOrigin, listOrigin[i].y)
            leftCurrent = min(leftCurrent, listCurrent[i].x)
            topCurrent = min(topCurrent, listCurrent[i].y)
        }
        
        let deltaX = leftOrigin - leftCurrent
        let deltaY = topOrigin - topCurrent
        let delta = itemContainer.frame.width / 50
        
        for i in 0..<listCurrent.count {
            listCurrent[i].x += deltaX
            listCurrent[i].y += deltaY
            var found = false
            for j in 0..<listOrigin.count {
                if abs(listCurrent[i].x - listOrigin[j].x) < delta && abs(listCurrent[i].y - listOrigin[j].y) < delta {
                    found = true
                    break
                }
            }
            if !found {
                return
            }
        }
        
        for i in 0..<listOrigin.count {
            var found = false
            for j in 0..<listCurrent.count {
                if abs(listOrigin[i].x - listCurrent[j].x) < delta && abs(listOrigin[i].y - listCurrent[j].y) < delta {
                    found = true
                    break
                }
            }
            if !found {
                return
            }
        }
        
        pauseGame(stopMusic: false)
        let delay: TimeInterval = playSound(delay: 0, names: [
            answerCorrect1EffectSound(),
            getCorrectHumanSound(),
            endGameSound()
        ])
        animateCoinIfCorrect(view: itemContainer)
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
    
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        let step = itemContainer.frame.width / 100
        return path.evenlySpacedPointsUsingDash(dashLength: step)
    }
}

