//
//  amnhac_list_nhaccu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/4/25.
//


import UIKit
import SnapKit
import SVGKit

class amnhac_list_nhaccu: MusicGameFragment {
    // MARK: - Properties
    private let folder = "musical intrusments"
    private var gridLayout: MyGridView!
    private var meIndex: Int = 0
    private var btnReplay: KUButton!
    private var items: [String] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 255/255, green: 221/255, blue: 253/255, alpha: 1) // #FDF
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "music_amsac_bg1"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let buttonContainer = UIView()
        buttonContainer.clipsToBounds = false
        view.addSubview(buttonContainer)
        buttonContainer.snp.makeConstraints { make in
            make.left.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.5)
            make.height.equalTo(view)
        }
        
        btnReplay = KUButton()
        btnReplay.alpha = 0.01
        btnReplay.setImage(Utilities.SVGImage(named: "music_amsac_btn1"), for: .normal)
        buttonContainer.addSubview(btnReplay)
        btnReplay.snp.makeConstraints { make in
            make.width.equalTo(buttonContainer).multipliedBy(0.6)
            make.height.equalTo(btnReplay.snp.width) // Ratio 1:1
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        btnReplay.addTarget(self, action: #selector(handleReplayTap(_:)), for: .touchUpInside)
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
    }
    
    override func createGame() {
        super.createGame()
        
        btnReplay.moveToCenter(of: self, duration: 0)
        btnReplay.alpha = 1
        
        items = StorageManager.manager.list(path: "music/\(folder)")
            .shuffled()
            .prefix(4)
            .map { $0 }
        meIndex = Int.random(in: 0..<items.count)
        
        var delay = playSound("music/am sac nhac cu", "effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let item = createItemWhiteCorner(svgPath: "music/\(folder)/\(items[i])")
            item.backgroundColor = .clear
            item.tag = i
            item.alpha = 0.001
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            item.addGestureRecognizer(tapGesture)
            item.isUserInteractionEnabled = true
            views.append(item)
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.1
        gridLayout.columns = 2
        gridLayout.reloadItemViews(views: views)
        
        scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.8) {
                self.btnReplay.transform = .identity
            }
            self.gridLayout.showItems(startDelay: 0.2)
        }
        
        delay += 2.0
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/am sac nhac cu", "effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleReplayTap(_ sender: UIButton) {
        pauseGame()
        let delay = playSound("effect/music/\(folder)/\(items[meIndex].replacingOccurrences(of: ".svg", with: ""))")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.resumeGame()
        }
    }
    
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let svgImage = view.subviews.first?.subviews.first(where: { $0 is UIImageView }) as? UIImageView else { return }
        
        pauseGame()
        let index = view.tag
        if index == meIndex {
            animateCoinIfCorrect(view: svgImage)
            let delay = playSound("effect/answer_end", getCorrectHumanSound(), "music/\(folder)/\(items[index].replacingOccurrences(of: ".svg", with: ""))")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemWhiteCorner(svgPath: String) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let svgImage = UIImageView()
        svgImage.accessibilityIdentifier = "svg_image"
        svgImage.image = Utilities.SVGImage(named: svgPath)
        svgImage.contentMode = .scaleAspectFit
        container.addSubview(svgImage)
        svgImage.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        
        return view
    }
}
