<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_boat"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/tuduy_bongnuoc_thuyen" />

        <ImageView
            android:id="@+id/image_flag"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/tuduy_bongnuoc_co" />

        <ImageView
            android:id="@+id/image_lunar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_bongnuoc_trang"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.3"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>