//
//  toancoban_list_solonnhat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_solonnhat: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var numbers: [Int] = []
    private var max: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.left.right.equalToSuperview()
            make.width.equalTo(gridLayout.snp.height).multipliedBy(2) // Ratio 2:1
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let value = 1000000 + Int.random(in: 0..<(Bool.random() ? 10000000 : 100000000))
        let value2 = Int(Double(value) * 0.1001)
        let value3 = value / (20 + Int.random(in: 0..<20))
        let value4 = value / (30 + Int.random(in: 0..<50))
        let value5 = value - 123
        
        numbers = [value2, value3, value4, value5]
        numbers = numbers.shuffled().take(count: 3)
        numbers.append(value)
        numbers = numbers.shuffled()
        max = numbers.max() ?? 0
        
        var views: [UIView] = []
        for i in 0..<numbers.count {
            let value = numbers[i]
            let view = createNumberItem(value: value)
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 4
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "toan/toan_so lon nhat")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so lon nhat")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid Item
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "math_solonnhat"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(400.0 / 107.0) // Ratio 400:107
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.85)
            make.height.equalTo(viewBackground).multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview() // Bias 0
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == max
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound(finishCorrect1Sounds())
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
