import UIKit
import SnapKit

class toancoban_list_caycaothap: NhanBietGameFragment {
    // MARK: - Properties
    private let ids1 = ["math_caycaothap_1_1", "math_caycaothap_1_2"]
    private let ids2 = ["math_caycaothap_2_1", "math_caycaothap_2_2"]
    private let ids3 = ["math_caycaothap_3_1", "math_caycaothap_3_2"]
    private var itemContainer: UIStackView!
    private var textName: UILabel!
    private var isTwoItems: Bool = false
    private var isHigh: Bool = false
    private var values: [Int] = []
    private var old1: Int = -1
    private var old2: Int = -1
    private var old3: Int = -1
    private var high: Int = 0
    private var most: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 197/255, green: 247/255, blue: 255/255, alpha: 1) // #C5F7FF
        
        let groundView = UIView()
        groundView.backgroundColor = UIColor(red: 159/255, green: 187/255, blue: 68/255, alpha: 1) // #9FBB44
        addSubview(groundView)
        groundView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.15)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let middleContainer = UIView()
        addSubview(middleContainer)
        middleContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.75)
            make.centerY.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        // Sử dụng UIStackView thay vì UIView để căn giữa các cây
        itemContainer = UIStackView()
        itemContainer.axis = .horizontal
        itemContainer.distribution = .equalSpacing
        itemContainer.alignment = .fill
        middleContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
            make.width.equalTo(itemContainer.snp.height).multipliedBy(3)
        }
        
        let image1 = UIImageView(image: Utilities.SVGImage(named: "math_caycaothap_3_1"))
        image1.contentMode = .scaleAspectFit
        image1.tag = 0
        itemContainer.addArrangedSubview(image1)
        image1.snp.makeConstraints { make in
            make.width.equalTo(image1.snp.height).multipliedBy(578.0 / 872.0) // Ratio 578:872
        }
        
        let image2 = UIImageView(image: Utilities.SVGImage(named: "math_caycaothap_2_1"))
        image2.contentMode = .scaleAspectFit
        image2.tag = 1
        itemContainer.addArrangedSubview(image2)
        image2.snp.makeConstraints { make in
            make.width.equalTo(image2.snp.height).multipliedBy(578.0 / 872.0) // Ratio 578:872
        }
        
        let image3 = UIImageView(image: Utilities.SVGImage(named: "math_caycaothap_2_1"))
        image3.contentMode = .scaleAspectFit
        image3.tag = 2
        itemContainer.addArrangedSubview(image3)
        image3.snp.makeConstraints { make in
            make.width.equalTo(image3.snp.height).multipliedBy(578.0 / 872.0) // Ratio 578:872
        }
        
        let image4 = UIImageView(image: Utilities.SVGImage(named: "math_caycaothap_2_1"))
        image4.contentMode = .scaleAspectFit
        image4.tag = 3
        itemContainer.addArrangedSubview(image4)
        image4.snp.makeConstraints { make in
            make.width.equalTo(image4.snp.height).multipliedBy(578.0 / 872.0) // Ratio 578:872
        }
        
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image1.addGestureRecognizer(tap1)
        image1.isUserInteractionEnabled = true
        
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image2.addGestureRecognizer(tap2)
        image2.isUserInteractionEnabled = true
        
        let tap3 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image3.addGestureRecognizer(tap3)
        image3.isUserInteractionEnabled = true
        
        let tap4 = UITapGestureRecognizer(target: self, action: #selector(onImageTapped(_:)))
        image4.addGestureRecognizer(tap4)
        image4.isUserInteractionEnabled = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        isTwoItems = Bool.random()
        isHigh = Bool.random()
        //textName.text = "Cây nào \(isHigh ? "cao" : "thấp") \(isTwoItems ? "hơn?" : "nhất?")"
        
        if isTwoItems {
            values = [0, 1, 2].shuffled().prefix(2).map { $0 }
        } else {
            values = [0, 1, 2].shuffled()
            if Bool.random() {
                if isHigh {
                    values.append(random.nextInt(bound:2))
                } else {
                    values.append(1 + random.nextInt(bound:2))
                }
                values.shuffle()
            }
        }
        
        let meValue = isHigh ? values.max()! : values.min()!
        let meIndex = values.firstIndex(of: meValue)!
        
        for i in 0..<itemContainer.subviews.count {
            guard let imageView = itemContainer.subviews[i] as? UIImageView else { continue }
            if i >= values.count {
                imageView.isHidden = true
            } else {
                imageView.isHidden = false
                let value = values[i]
                if value == 0 {
                    old1 = old1 == -1 ? random.nextInt(bound:ids1.count) : 1 - old1
                    imageView.image = Utilities.SVGImage(named: ids1[old1])
                } else if value == 1 {
                    old2 = old2 == -1 ? random.nextInt(bound:ids2.count) : 1 - old2
                    imageView.image = Utilities.SVGImage(named: ids2[old2])
                } else {
                    old3 = old3 == -1 ? random.nextInt(bound:ids3.count) : 1 - old3
                    imageView.image = Utilities.SVGImage(named: ids3[old3])
                }
            }
        }
        
        high = isHigh ? 0 : 1
        most = isTwoItems ? 2 : 0
        let delay = playSound(openGameSound(), "toan/toan_cay cao thap\(high + most + 1)")
        scheduler.schedule(delay: delay) { [weak self] in
            DispatchQueue.main.async {
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_cay cao thap\(isHigh ? 1 : 2)")
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.resumeGame()
                }
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func onImageTapped(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UIImageView else { return }
        pauseGame()
        let meValue = isHigh ? values.max()! : values.min()!
        let meIndex = values.firstIndex(of: meValue)!
        let correct = view.tag == meIndex
        
        if correct {
            animateCoinIfCorrect(view: view)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.finishGame()
                }
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.resumeGame()
                }
            }
        }
    }
}
