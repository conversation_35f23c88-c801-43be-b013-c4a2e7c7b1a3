<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/option_bg_white_shadow"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="346:373"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:id="@+id/text_number"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeMaxTextSize="500dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="1"
            android:textColor="#74B6FF"
            app:layout_constraintHeight_percent="0.7"
            app:layout_constraintWidth_percent="0.8"
            app:layout_constraintVertical_bias="0.45"
            android:textSize="24sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>