<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="3:4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/xucxac1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintWidth_percent="0.333" />

        <ImageView
            android:id="@+id/xucxac3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.333"
            app:layout_constraintWidth_percent="0.333" />

        <ImageView
            android:id="@+id/xucxac5"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.333"
            app:layout_constraintWidth_percent="0.333" />

        <ImageView
            android:id="@+id/xucxac6"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg4"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.333"
            app:layout_constraintWidth_percent="0.333" />

        <ImageView
            android:id="@+id/xucxac2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.667"
            app:layout_constraintWidth_percent="0.333" />

        <ImageView
            android:id="@+id/xucxac4"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xucxac_bg5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.25"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.333" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>