//
//  toancoban_list_ongroito.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_ongroito: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var a: Int = 0
    private var b: Int = 0
    private var itemContainer: UIView!
    private var bees1: [UIImageView] = []
    private var bees2: [UIImageView] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#663535")
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_garden"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        itemContainer = UIView()
        leftView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 3.0/2.0)
        
        let hiveContainer = UIView()
        itemContainer.addSubview(hiveContainer)
        hiveContainer.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.7)
            make.width.equalTo(hiveContainer.snp.height).multipliedBy(197.0 / 245.0) // Ratio 197:245
            make.bottom.equalToSuperview().multipliedBy(0.8)
            make.left.equalToSuperview()
        }
        
        let hiveImage = UIImageView(image: Utilities.SVGImage(named: "math_ongroito_hive"))
        hiveContainer.addSubview(hiveImage)
        hiveImage.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(hiveContainer.frame.width * 0.1)
        }
        
        bees1 = [
            createBee(hiveContainer, tag: "1", biasX: 0.5, biasY: 0.0, widthPercent: 0.35),
            createBee(hiveContainer, tag: "1", biasX: 0.85, biasY: 0.15, widthPercent: 0.35, scaleX: -1),
            createBee(hiveContainer, tag: "1", biasX: 0.15, biasY: 0.2, widthPercent: 0.35, scaleX: -1),
            createBee(hiveContainer, tag: "1", biasX: 0.8, biasY: 0.4, widthPercent: 0.35),
            createBee(hiveContainer, tag: "1", biasX: 0.15, biasY: 0.5, widthPercent: 0.35),
            createBee(hiveContainer, tag: "1", biasX: 0.8, biasY: 0.7, widthPercent: 0.35),
            createBee(hiveContainer, tag: "1", biasX: 0.4, biasY: 0.85, widthPercent: 0.35, scaleX: -1)
        ]
        
        let flowerContainer = UIImageView(image: Utilities.SVGImage(named: "math_ongroito_flower"))
        itemContainer.addSubview(flowerContainer)
        flowerContainer.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.5)
            make.width.equalTo(flowerContainer.snp.height).multipliedBy(197.0 / 245.0) // Ratio 197:245
            make.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        bees2 = [
            createBee(itemContainer, tag: "2", biasX: 0.7, biasY: 0.7, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 0.8, biasY: 0.5, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 1.0, biasY: 0.4, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 0.6, biasY: 0.4, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 0.6, biasY: 0.8, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 0.9, biasY: 0.25, widthPercent: 0.13),
            createBee(itemContainer, tag: "2", biasX: 0.7, biasY: 0.25, widthPercent: 0.13)
        ]
    }
    
    private func createBee(_ container: UIView, tag: String, biasX: CGFloat, biasY: CGFloat, widthPercent: CGFloat, scaleX: CGFloat = 1) -> UIImageView {
        let bee = UIImageView(image: Utilities.SVGImage(named: "math_ongroito_bee"))
        bee.contentMode = .scaleAspectFit
        bee.tag = tag == "1" ? 1 : 2
        bee.transform = CGAffineTransform(scaleX: scaleX, y: 1)
        container.addSubview(bee)
        bee.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(widthPercent)
            make.height.equalTo(bee.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            bee.snapToHorizontalBias(horizontalBias: biasX)
            bee.snapToVerticalBias(verticalBias: biasY)
        }
        return bee
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 2 + Int.random(in: 0..<10)
            b = 1 + Int.random(in: 0..<7)
            if a > b && a - b <= 7 && a <= 9 {
                break
            }
        }
        
        let show1 = Utils.generatePermutation(a - b, size: 7)
        let show2 = Utils.generatePermutation(b, size: 7)
        for i in 0..<7 {
            bees1[i].isHidden = !show1.contains(i)
            bees2[i].isHidden = !show2.contains(i)
        }
        
        buildGrid(grid: gridLayout, count: a - b)
    }
    
    override func createGame() {
        super.createGame()
        var delay: TimeInterval = 1.0
        delay += playSound(delay: delay, names: [
            "toan/toan_ong roi to1",
            "topics/Numbers/\(a)",
            "toan/toan_ong roi to2",
            "topics/Numbers/\(b)",
            "toan/toan_ong roi to3"
        ])
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(
                "toan/toan_ong roi to1",
                "topics/Numbers/\(a)",
                "toan/toan_ong roi to2",
                "topics/Numbers/\(b)",
                "toan/toan_ong roi to3"
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 346.0/373.0
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == a - b
        var delay = playSound("topics/Numbers/\(value)")
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            delay += playSound(delay: delay, names: finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}

