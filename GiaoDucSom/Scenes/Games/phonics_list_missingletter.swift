//
//  phonics_list_missingletter.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_missingletter: GameFragment {
    private var values : [String] = []
    private var values1 : [String] = []
    var answerLayout = MyGridView()
    var bottomView = SVGImageView(SVGName: "btn bg yellow").then {
        $0.isUserInteractionEnabled = true
        $0.alpha = 0
    }
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#99B6C1")
    }
    var svgView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    var question = ""
    var questions : [String] = []
    var rightAns = ""
    override func configureLayout(_ view: UIView) {
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#FFF5CC")
        view.addSubview(leftView)
        let leftBg = UIView()
        leftBg.backgroundColor = .color(hex: "#FFF5CC")
        leftView.addSubview(leftBg)
        leftBg.snp.makeConstraints{ make in
            make.top.left.bottom.equalTo(self)
            make.right.equalToSuperview()
        }
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        let leftInnerView = UIView()
        leftView.addSubview(leftInnerView)
        leftInnerView.makeViewCenterAndKeep(ratio: 1.5)
            
        bottomView.isUserInteractionEnabled = true
        leftInnerView.addSubview(textName)
        leftInnerView.addSubview(bottomView)
        bottomView.snp.makeConstraints{ make in
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.width.equalToSuperview().multipliedBy(0.9)
            make.centerX.equalToSuperview()
            make.height.equalTo(bottomView.snp.width).multipliedBy(151.0/400.0)
        }
        bottomView.addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(1)
            make.height.equalToSuperview().multipliedBy(1)
            make.center.equalToSuperview()
        }
        textName.snp.makeConstraints{ make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.35)
        }
        let rightView = UIView()
        rightView.backgroundColor = .color(hex: "#849BFD")
        view.addSubview(rightView)
        let rightBgx = UIView()
        rightBgx.backgroundColor = .color(hex: "#849BFD")
        rightView.addSubview(rightBgx)
        rightBgx.snp.makeConstraints{ make in
            make.top.right.bottom.equalTo(self)
            make.left.equalToSuperview()
        }
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(leftView.snp.right)
        }
        let rightBg = SVGImageView(SVGName: "option_bg_white_shadow")
        rightView.addSubview(rightBg)
        rightBg.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalTo(rightBg.snp.width)
            make.center.equalToSuperview()
        }
        rightView.addSubview(svgView)
        svgView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgView.snp.width)
        }
        svgView.alpha = 0
        textName.alpha = 0
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = self.playSound(delay: 0, names: self.parseIntroText()!)
            delay += self.playSound(name: tapdoc || isVocab() ? "" : answer, delay: delay)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        values1 = (game.values1?.compactMap { $0.value as? String })!.map{ $0.hasSuffix("1") ? String($0.dropLast(1)) : $0 }.randomOrder()
        loadNextStep()
    }
    var step = 0
    var answer = ""
    func loadNextStep(){
        CoinAnimationUtils.shared.removeList()
        if step >= 1 {
            var delay = 0.5
            delay += playSound(name: endGameSound(), delay: delay)
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
#if DEBUG
        //answer = "nhẫn"
        //values1 = ["a","ă","â"]
#endif
        step += 1
        var delay = playSound(openGameSound(), delay: 0)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.showImage()
        })
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: tapdoc || isVocab() ? "" : answer, delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
            self.showBottom()
        })
        var nosign = vietnamese.removeSign(answer)
        svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(answer)))!] : "english phonics/\(game.level!)/\(answer).svg"
        
        
        let randomOrder = values1.randomOrder()

        for s in randomOrder {
            if nosign.contains(s) {
                if let index = nosign.range(of: s)?.lowerBound {
                    if tapdoc && s.count == 1 {
                        rightAns = s
                        let text = answer
                        let nosign = vietnamese.removeSign(text)
                        let index = nosign.firstIndex(of: s.first!)?.utf16Offset(in: nosign) ?? 0
                        let startIndex = text.index(text.startIndex, offsetBy: index)
                        let endIndex = text.index(startIndex, offsetBy: 1)
                        let spanText = getTextWithHiddenChar(text: String(text[startIndex..<endIndex]), baseCharToHide: s.first!)
                        
                        let attributedText = NSMutableAttributedString(string: String(text[..<startIndex]))
                        attributedText.append(spanText)
                        attributedText.append(NSAttributedString(string: String(text[endIndex...])))
                        let highlightRange = NSRange(location: index, length: 1)
                        attributedText.addAttribute(.foregroundColor, value: UIColor.color(hex: "#B7E6EA"), range: highlightRange)
                        textName.attributedText = attributedText
                    } else {
                        rightAns = s
                        let missingWord = answer.prefix(upTo: index) + String(repeating: "_", count: 1) + answer.suffix(from: answer.index(index, offsetBy: s.count))
                        let spannable = NSMutableAttributedString(string: String(missingWord))
                        spannable.addAttribute(.foregroundColor, value: UIColor(red: 1.0, green: 0.465, blue: 0.380, alpha: 1.0), range: NSRange(location: index.utf16Offset(in: missingWord), length: 1))
                        textName.attributedText = spannable
                    }
                }
            }
        }

        var choose: [String] = []
        while true {
            choose = values1.randomOrder().prefix(3).map { $0 }
            if choose.contains(rightAns) {
                break
            }
        }
                
        
        var listViews : [UIView] = []
        for i in 0..<choose.count {
            let view = createItem(text: choose[i])
            view.tag = 100 + i
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 1
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.03
        answerLayout.columns = 3
        answerLayout.reloadItemViews(views: listViews)
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: answer, delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg yellow")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#68C1FF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        var nosign = vietnamese.removeSign(answer)
        if rightAns == label.text! {
            pauseGame()
            animateCoinIfCorrect(view: sender)
            label.textColor = .color(hex: "#73D048")
            textName.textColor = .color(hex: "#73D048")
            textName.text = answer
            var delay = 0.5
            delay += playSound(name: answerCorrect1EffectSound(), delay: delay)
            delay += playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer, delay: delay)
            delay += playSound(name: getCorrectHumanSound(), delay: delay)
            delay += playSound(name: endGameSound(), delay: delay)
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.hideAll()
            })
            scheduler.schedule(delay: 3, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
                self.textName.textColor = .color(hex: "#99B6C1")
                self.resumeGame()
                self.loadNextStep()
            })
        } else {
            setGameWrong()
            incorrect += 1
            label.textColor = .color(hex: "#FF7761")
            var delay = 0.3
            delay += playSound(name: answerWrongEffectSound(), delay: delay)
            delay += playSound(name: label.text!, delay: delay)
            scheduler.schedule(delay: 0.7, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
            })
        }
    }
    func showBottom(){
        let delta = bottomView.bounds.height * 1.5
        bottomView.transform = CGAffineTransformMakeTranslation(0, delta)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [delta, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue)
            self.bottomView.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func hideAll(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -svgView.bounds.width * 1.2]
        let animValues2: [Double] = [0, self.bounds.height * 0.6 + 40.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.svgView.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            self.svgView.alpha = 1 - value
            self.textName.alpha = 1 - value
            self.textName.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue2)
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showImage(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [svgView.bounds.width * 1.2, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.svgView.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            self.textName.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            self.svgView.alpha = value
            self.textName.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}


