//
//  phonics_list_reading2.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 04/08/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import Speech
import Reachability

class phonics_list_reading2: GameFragment {
    private var colorRead = UIColor(hexString: "#032cfc")
    private var colorReading = UIColor(hexString: "#032cfc")
    private var colorNormal = UIColor(hexString: "#0394fc")
    private var colorBackground = UIColor(hexString: "#7197CF")
    private var contentLayout = UIView()
    private var topView = UIView()
    var textContainer = UIView()
    var textBottomContainer = UIView()
    private var values: [String] = []
    private var values2: [String] = []
    private var items: [String] = []
    private var step: Int = 0
    var chooseIndexes : [Int] = []
    private var dragViews : [UIView] = []
    var answer = ""
    var topIntroView: UIView?
    var leftContainer = UIView()
    var leftButton = SVGButton(SVGIcon: "bot btn record")
    var rightContainer = UIView()
    var rightButton = SVGButton(SVGIcon: "bot btn next")
    var topContainer = UIView()
    var readSlowButton = SVGButton(SVGIcon: "btn white bg green read1")
    var readButton = SVGButton(SVGIcon: "btn white bg green read2")
    let reachability = try! Reachability()
    var internetConnected = false
    override func configureLayout(_ view: UIView) {
        topIntroView = createIntroButton()
        addSubview(textBottomContainer)
        addSubview(topView)
        addSubview(textContainer)
        contentLayout.alpha = 0
        textBottomContainer.alpha = 0
        topView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(topIntroView!.snp.bottom)
            make.bottom.equalTo(textBottomContainer.snp.top)
        }
        topView.addSubview(contentLayout)
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
        
        addSubview(leftContainer)
        addSubview(rightContainer)
        let leftBackground = SVGImageView(SVGName: "bot bg left")
        let rightBackground = SVGImageView(SVGName: "bot bg right")
        leftContainer.addSubview(leftBackground)
        rightContainer.addSubview(rightBackground)
        leftContainer.addSubview(leftButton)
        rightContainer.addSubview(rightButton)
        leftContainer.snp.makeConstraints{ make in
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        rightContainer.snp.makeConstraints{ make in
            make.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        leftBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.1)
            make.width.equalTo(leftBackground.snp.height).multipliedBy(628.0/401.0)
        }
        rightBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.85)
            make.width.equalTo(rightBackground.snp.height).multipliedBy(628.0/401.0)
        }
        leftButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        
        leftButton.addTarget(self, action: #selector(record), for: .touchUpInside)
        rightButton.addTarget(self, action: #selector(loadNext), for: .touchUpInside)
        
        addSubview(topContainer)
        topContainer.alpha = 0
        let topBackground = SVGImageView(SVGName: "btn bg green")
        topContainer.addSubviewWithInset(subview: topBackground, inset: 0)
        topContainer.addSubview(readSlowButton)
        topContainer.addSubview(readButton)
        topContainer.snp.makeConstraints{ make in
            make.height.equalTo(leftContainer)
            make.centerX.equalToSuperview()
            make.top.equalTo(topIntroView!.snp.bottom).offset(20)
            make.width.equalTo(topContainer.snp.height).multipliedBy(2)
        }
        readSlowButton.snp.makeConstraints{ make in
            make.width.equalTo(readSlowButton.snp.height)
            make.height.equalToSuperview().multipliedBy(0.75)
            make.centerX.equalToSuperview().multipliedBy(0.55)
            make.centerY.equalToSuperview()
        }
        readButton.snp.makeConstraints{ make in
            make.width.equalTo(readButton.snp.height)
            make.height.equalToSuperview().multipliedBy(0.75)
            make.centerX.equalToSuperview().multipliedBy(1.45)
            make.centerY.equalToSuperview()
        }
        
        readSlowButton.addTarget(self, action: #selector(readSlowClick), for: .touchUpInside)
        readButton.addTarget(self, action: #selector(readNormalClick), for: .touchUpInside)
        initRead()
        reachability.whenReachable = { [weak self] reachability in
            guard let self = self else { return }
            if reachability.connection == .wifi {
                print("Reachable via WiFi")
                self.internetConnected = true
                self.reloadRecordButton()
            } else {
                print("Reachable via Cellular")
                self.internetConnected = true
                self.reloadRecordButton()
            }
        }
        reachability.whenUnreachable = {[weak self] _ in
            guard let self = self else { return }
            print("Not reachable")
            self.internetConnected = false
            self.reloadRecordButton()
        }

        do {
            try reachability.startNotifier()
        } catch {
            print("Unable to start notifier")
        }
    }
    func reloadRecordButton(){
        var enableSpeech = false
        if #available(iOS 13, *) {
            enableSpeech = speechRecognizer?.supportsOnDeviceRecognition ?? false || internetConnected
        } else {
            enableSpeech = internetConnected
        }
        //leftButton.alpha = enableSpeech ? 1 : 0.5
        leftButton.set(image: SVGKImage(contentsOf: Utilities.SVGURL(of: enableSpeech ? "bot btn record" :"bot btn record2")).uiImage)
    }
    override func layoutSubviews() {
        super.layoutSubviews()
        textContainer.flex.layout(mode: .adjustHeight)
        textContainer.pin.left().vCenter().right()
        textBottomContainer.flex.layout(mode: .adjustHeight)
        textBottomContainer.pin.left().bottom(bounds.width/10).right()
        contentLayout.flex.layout(mode: .adjustHeight)
        contentLayout.pin.left().vCenter().right()
    }
    func hintButtonTapped() {
        let vc = MessagePopupViewController()
        vc.applyPopPresenter()
        vc.hideIcon = false
        vc.popupTitle = "Mẹo"
        vc.popupDescription = ""
        parentViewController?.navigationController?.present(vc, animated: true)
    }
    override func createGame() {
        super.createGame()
        leftContainer.alpha = 0
        rightContainer.alpha = 0
        topContainer.isHidden = true
        hideMenu()
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.leftContainer.alpha = 1
            self.rightContainer.alpha = 1
        })
        let values = (game.values1?.compactMap { $0.value as? String })!
        let values2 = (game.values2?.compactMap { $0.value as? String })!
        let randomOrder = [Int](0..<values.count).randomOrder()
        self.values = randomOrder.map{values[$0]}
        self.values2 = randomOrder.map{values2[$0]}
        
        //values.insert("co co co co", at: 0)
        loadNextStep()
    }
    var isNeedShowHintButton: (Bool){false}
    func loadNextStep(){
        resumeGame()
        if step >= values.count {
            pauseGame()
            var delay = 0.5
            delay += self.playSound(name: "effects/cheer\(Int.random(in: 1...4))", delay: delay)
            delay += self.playSound(name: "effects/end game", delay: delay)
            delay += 1
            self.scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        readSlow = false
        answer = values[step]
        var delay = 0.5
        if game.nomatching ?? false {
            
        } else {
            if step == 0 {
                delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{ $0 == "@" ? "\(game.path != nil && game.path != "" ? game.path! : "sentence")/\($0)".replacingOccurrences(of: "@", with: answer) : $0})
            } else {
                delay += self.playSound(name: game.path != nil && game.path != "" ? "\(game.path!)/\(answer)" : "sentence/\(answer)", delay: delay)
            }
        }
        let answer2 = values2[step]
        step += 1
        let labelIntro = topIntroView?.viewWithTag(1) as! UILabel
        labelIntro.text = answer2
        label2.text = answer2
        let text = "\(answer.prefix(1).uppercased())\(answer.dropFirst(1))\(game.question_mark ?? false ? "?":".")"
        var itemIndex = Int.random(in: 0..<4)
        itemIndex = 1
        
        colorRead = UIColor(hexString: ["#EC6149","#4C9743","#FFFFFF","#FFFFFF"][itemIndex]) // done
        colorReading = colorRead
        colorNormal = UIColor(hexString: ["#587AC2","#D9FF4D","#6695FF","#8ACE7C"][itemIndex])
        colorBackground = UIColor(hexString: ["#7197CF","#D9FF4D","#6695FF","#8ACE7C"][itemIndex])
        self.backgroundColor = .color(hex: "#88D35A")
                
        
        items = text.split(separator: " ").map{$0.string}
        var count = items.count
        chooseIndexes = [Int](0..<items.count).randomOrder().take(count: count)
        textContainer.subviews.forEach{$0.removeFromSuperview()}
        textBottomContainer.subviews.forEach{$0.removeFromSuperview()}
        contentLayout.subviews.forEach{$0.removeFromSuperview()}
        textBottomContainer.alpha = 0
        contentLayout.alpha = 0
        textContainer.alpha = 1
        dragViews.removeAll()
        textContainer.alpha = 0
        let fontSize = getTextSize(text: text)
        
        textContainer.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.center).alignItems(.start).define { flex in
            for item in items {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = item
                label.textColor = colorRead
                //label.backgroundColor = .green
                flex.addItem(label).marginBottom(fontSize/4)
                
                label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = " "
                label.textColor = .red
                //label.backgroundColor = .green
                flex.addItem(label)
            }
        }
        textBottomContainer.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.center).alignItems(.start).define { flex in
            var index = 0
            for item in items {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = item
                label.textColor = chooseIndexes.contains(index) ? .clear : colorRead
                label.backgroundColor = chooseIndexes.contains(index) ? colorBackground : .clear
                label.layer.cornerRadius = chooseIndexes.contains(index) ? fontSize/10 : 0
                label.clipsToBounds = chooseIndexes.contains(index)
                flex.addItem(label).marginBottom(fontSize/4)
                
                label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = " "
                label.textColor = .red
                //label.backgroundColor = .green
                flex.addItem(label)
                index += 1
            }
        }
        contentLayout.flex.padding(0).wrap(.wrap).direction(.row).justifyContent(.spaceEvenly).define { flex in
            for index in chooseIndexes {
                var label = UILabel()
                label.font = .Freude(size: fontSize)
                label.text = " \(items[index]) "
                label.textColor = colorNormal
                //label.backgroundColor = .green
                flex.addItem(label).marginBottom(fontSize/4)
            }
        }
        if game.nomatching ?? false {
            textContainer.alpha = 1
            scheduler.schedule(delay: 2, execute: {
                [weak self] in
                guard let self = self else { return }
                self.jumpAnimation()
            })
            return
        }
        showBottom()
        //container.flex.layout()
        setNeedsLayout()
    }
    func showBottom(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeInOut
        let animValues: [Double] = [-bounds.width,0]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textBottomContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
            self?.textBottomContainer.alpha = value
            self?.contentLayout.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            self.resumeGame()
        }
    }
    var swingId = -1
    
    func showCenter(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeInOut
        let animValues: [Double] = [-bounds.width,0]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.textContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    var readSlow = false
    func jumpAnimation(){
        var delay : Double = 0.5
        let file = readSlow ? "\(answer)2" : answer
        let words = parse(file)
        playSound(name: game.path != nil && game.path != "" ? "\(game.path!)/\(file)" : "sentence/\(file)", delay: 0.5)
        for i in 0..<words.count {
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                let label = self.textContainer.subviews[i*2] as? UILabel
                label?.textColor = colorNormal
            })
            delay += words[i].Duration
        }
        
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.finishAnimation()
            }
        }
    }
    func MoveToTopAndStartJumpAnimation(){
        playSound(name: "effects/slide", delay: 0)
        for i in 0..<items.count {
            let dst = textContainer.subviews[2*i]
            let src = textBottomContainer.subviews[2*i]
            let d = src.convert(CGPoint.zero, to: dst)
            UIView.animate(withDuration: 0.3) {
                src.transform = CGAffineTransformMakeTranslation(-d.x, -d.y+dst.bounds.height/2-src.bounds.height/2)
            }
        }
        scheduler.schedule(delay: 0.31) {
            [weak self] in
            guard let self = self else { return }
            self.textContainer.alpha = 1
            self.textBottomContainer.alpha = 0
            self.jumpAnimation()
        }
    }
    
    func getTextSize(text:String)->CGFloat{
        var size = min(bounds.width/5,sqrt(bounds.width*bounds.height) / CGFloat(text.count) * 3)
        var size2 = calculateFontSize(for: text.components(separatedBy: " "), maxWidth: bounds.width)
        if text == "Why should I care?" {
            size = size * 0.9
            size2 = size2 * 0.9
        }
        return min(size2 * 0.9,Utilities.isIPad ? size * 0.7 : size)
    }
    private var selectedView: UIView?
    private var zPosition: CGFloat = 1
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            for view in contentLayout.subviews {
                if dragViews.contains(view) {continue}
                if sender.placeInView(view: view){
                    selectedView = view
                    zPosition += 1
                    view.layer.zPosition = zPosition
                    UIView.animate(withDuration: 0.3) {
                        view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                    }
                    if let label = view as? UILabel {
                        label.textColor = colorRead
                    }
                    return;
                }
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if state == .ended || state == .cancelled || state == .failed {
            if let selectedView = selectedView as? UILabel {
                let textBottoms = textBottomContainer.subviews.map{$0 as! UILabel}
                var closedView: UILabel?
                var distance : Double = 1000000
                for textBottom in textBottoms {
                    var p = textBottom.convert( CGPointMake(textBottom.bounds.width / 2, textBottom.bounds.height / 2), to: selectedView)
                    p.x -= selectedView.bounds.width / 2
                    p.y -= selectedView.bounds.height / 2
                    let d = sqrt(p.x * p.x + p.y * p.y)
                    if d < distance {
                        distance = d
                        closedView = textBottom
                    }
                }
                if distance < selectedView.bounds.height / 2 {
                    if selectedView.text?.replacingOccurrences(of: " ", with: "") == closedView?.text?.replacingOccurrences(of: " ", with: "") && closedView?.textColor != colorRead {
                        playSound(answerCorrect1EffectSound(), )
                        correct += 1
                        processRightDrag(closedView: closedView!, label: selectedView)
                    } else {
                        playSound(answerWrongEffectSound(), )
                        incorrect += 1
                        UIView.animate(withDuration: 0.2) {
                            selectedView.transform = .identity
                        }
                        selectedView.textColor = colorNormal
                    }
                } else {
                    playSound(name: answerWrongEffectSound(), )
                    UIView.animate(withDuration: 0.2) {
                        selectedView.transform = .identity
                    }
                    selectedView.textColor = colorNormal
                }
            }
        }
    }
    func processRightDrag(closedView:UILabel, label: UILabel){
        dragViews.append(label)
        playSound(name: "effects/true2")
        closedView.backgroundColor = .clear
        var p = closedView.convert( CGPointMake(closedView.bounds.width / 2, closedView.bounds.height / 2), to: label)
        p.x -= label.bounds.width / 2
        p.y -= label.bounds.height / 2
        UIView.animate(withDuration: 0.1) {
            var tran = label.transform
            tran.tx += p.x
            tran.ty += p.y
            label.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
        } completion: { done in
            label.textColor = .clear
            closedView.textColor = self.colorRead
        }
        if dragViews.count == chooseIndexes.count {
            pauseGame()
            scheduler.schedule(delay: 0.3, execute: {
                [weak self] in
                guard let self = self else { return }
                CoinAnimationUtils.shared.removeList()
                self.textBottomContainer.animateCoin(answer: true)                
            })
            scheduler.schedule(delay: 1.5) {
                [weak self] in
                guard let self = self else { return }
                self.MoveToTopAndStartJumpAnimation()
            }
        }
    }
    func finishAnimation(){
        var delay = 1.0
        //delay += self.playSound(name: "effects/cheer\(Int.random(in: 1...4))", delay: delay)
        showMenu()
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    var correct = 0
    override func getScore()->Float{
        return Float(correct) / Float(correct + incorrect)
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: game.path != nil && game.path != "" ? "Sounds/\(game.path!)/\(name)" : "Sounds/sentence/\(name)", withExtension: "csv") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for line in lines {
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    if word.Duration != 0 {
                        words.append(word)
                    }
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
    func createIntroButton()->UIControl{
        let view = SVGButton()
        let image1 = SVGImageView(SVGName: "btn sound bg green1")
        let image2 = SVGImageView(SVGName: "btn sound bg green2")
        let image3 = SVGImageView(SVGName: "btn sound bg green3")
        view.addSubview(image1)
        view.addSubview(image2)
        view.addSubview(image3)
        image1.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(image1.snp.height)
        }
        image2.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(image1.snp.right).offset(-5)
            make.right.equalTo(image3.snp.left).offset(5)
        }
        image3.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(image3.snp.height)
        }
        addSubview(view)
        view.snp.makeConstraints{ make in
            make.top.equalTo(20)
            make.height.equalTo(70)
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9).priority(.low)
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.9)
        }
        let label = AutosizeLabel()
        label.tag = 1
        view.addSubview(label)
        label.snp.makeConstraints{ make in
            make.left.equalToSuperview().inset(70)
            make.right.equalToSuperview().inset(20)
            make.centerY.equalToSuperview().multipliedBy(0.85)
            make.width.equalToSuperview().offset(-100)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        label.text = "Listen for"
        label.textColor = .color(hex: "#99B6C1")
        view.addTarget(self, action: #selector(playIntro), for: .touchUpInside)
        
        label2 = UILabel()
        label2.font = .Freude(size: 25)
        label2.alpha = 0
        label2.backgroundColor = .red.withAlphaComponent(0.3)
        label2.setContentHuggingPriority(.required, for: .horizontal)
        view.addSubview(label2)
        label2.snp.makeConstraints{ make in
            make.center.equalToSuperview()
        }
        view.snp.makeConstraints{ make in
            make.width.lessThanOrEqualTo(label2).offset(100)
        }
        return view
    }
    var label2 : UILabel = UILabel()
    @objc func playIntro(){
        pauseGame()
        if isShowMenu && (game.noslow ?? false){
            readSlow = false
            hideMenu()
            resetAndRead()
            return
        }
        var delay = 0.0
        delay += self.playSound(name: game.path != nil && game.path != "" ? "\(game.path!)/\(answer)" : "sentence/\(answer)", delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    private var isShowMenu = false
    func hideMenu(){
        isShowMenu = false
        playSound(name: "effects/slide1", delay: 0)
        leftContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -leftContainer.bounds.width * 1.2 - 20.0]
        let animValues2: [Double] = [0, rightContainer.bounds.width * 1.2 + 20.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
            if self.game.noslow ?? false {
                self.topContainer.alpha = 0
            } else {
                self.topContainer.alpha = 1.0 - value
            }
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showMenu(){
        isShowMenu = true
        startGame()
        playSound(name: "effects/slide1", delay: 0)
        topContainer.isHidden = false
        leftContainer.transform = CGAffineTransformMakeTranslation(-leftContainer.bounds.width, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(rightContainer.bounds.width, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [-leftContainer.bounds.width, 0]
        let animValues2: [Double] = [rightContainer.bounds.width, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
            if self.game.noslow ?? false {
                self.topContainer.alpha = 0
            } else {
                self.topContainer.alpha = value
            }
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    @objc func loadNext(){
        hideMenu()
        pauseGame()
        var delay = 0.3
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            let easy = BackEaseInterpolater()
            easy.mode = .easeIn
            let animValues: [Double] = [0,bounds.width]
            let timeChange = Interpolate(values: [0,1],
            apply: { [weak self] (value) in
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                self?.textContainer.transform = CGAffineTransformMakeTranslation(finalValue,0)
            })
            timeChange.animate(1, duration: 0.5){
                self.textContainer.transform = .identity
                self.textContainer.alpha = 0
                self.loadNextStep()
            }
        })
    }
    @objc func record(){
        hideMenu()
        prepareRead()
    }
    @objc func readSlowClick(){
        readSlow = true
        hideMenu()
        resetAndRead()
    }
    @objc func readNormalClick(){
        readSlow = false
        hideMenu()
        resetAndRead()
    }
    func resetAndRead(){
        for i in 0..<self.textContainer.subviews.count/2 {
            let label = self.textContainer.subviews[i*2] as? UILabel
            label?.textColor = colorRead
        }
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.jumpAnimation()
        })
    }
    
    var xamlWave = XAMLAnimationView()
    var xamlLoad = XAMLAnimationView()
    var waveData: XAMLModel.UserControl?
    var loadData: XAMLModel.UserControl?
    var centerView = UIView()
    private var speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: "en-US")) //1
    private var recognitionRequest: SFSpeechAudioBufferRecognitionRequest?
    private var recognitionTask: SFSpeechRecognitionTask?
    private var audioEngine = AVAudioEngine()
    var lang: String = "en-US"
    var listening = false
    var timertimeout = TimeoutTimer()
    var timerLongtimeout = TimeoutTimer()
    var texts : [String] = []
    var topLabel = AutosizeLabel()
    var bottomLabel = AutosizeLabel()
    func initRead(){
        addSubview(centerView)
        centerView.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.1)
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        //centerView.backgroundColor = .red
        var recordingBg = SVGImageView(SVGName: "recording bg2").then{
            $0.contentMode = .scaleAspectFit
        }
        centerView.alpha = 0
        centerView.addSubviewWithInset(subview: recordingBg, inset: 0)
        centerView.addSubview(xamlWave)
        centerView.addSubview(xamlLoad)
        xamlWave.makeViewCenterAndKeep(ratio: 1)
        xamlLoad.makeViewCenterAndKeep(ratio: 1)
        xamlLoad.isHidden = true
        do {
            waveData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "recording wave2")!)
            loadData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "recording load2")!)
            xamlWave.loadView(from: waveData!)
            xamlLoad.loadView(from: loadData!)
        } catch {}
        timertimeout.duration = 1
        timertimeout.onActived = {
            [weak self] in
            guard let self = self else { return }
            if !self.listening {
                return
            }
            self.xamlWave.isHidden = true
            self.xamlLoad.isHidden = false;
            self.listening = false
            self.toggleStartStop()
            self.scheduler.schedule(delay: 2, execute: {
                [weak self] in
                guard let self = self else { return }
                self.xamlLoad.isHidden = true
                let text = self.texts[self.texts.count-1]
                bottomLabel.text = text
                self.scheduler.schedule(delay: 3, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    self.topLabel.text = ""
                    self.bottomLabel.text = ""
                    UIView.animate(withDuration: 0.3, animations: {
                        self.textContainer.alpha = 1
                        self.centerView.alpha = 0
                    })
                    self.showMenu()
                })
                if topLabel.text!.lowercased().replacingOccurrences(of: "?", with: "").replacingOccurrences(of: ".", with: "") == bottomLabel.text!.lowercased().replacingOccurrences(of: "?", with: "").replacingOccurrences(of: ".", with: "") {
                    CoinAnimationUtils.shared.removeList()
                    textContainer.animateCoin(answer: true)
                }
                UIView.animate(withDuration: 0.3, animations: {
                    self.topLabel.transform = CGAffineTransformMakeTranslation(0, self.xamlLoad.bounds.height/2)
                    self.bottomLabel.transform = CGAffineTransformMakeTranslation(0, -self.xamlLoad.bounds.height/2)
                    self.centerView.alpha = 0
                })
                //self.processData(text: self.texts[self.texts.count-1])
            })
        }
        timerLongtimeout.duration = 5
        timerLongtimeout.onActived = {
            [weak self] in
            guard let self = self else { return }
            if !self.listening {
                return
            }
            self.texts.append("")
            self.timertimeout.schedule()
        }
        addSubview(topLabel)
        addSubview(bottomLabel)
        topLabel.textColor = colorNormal
        bottomLabel.textColor = colorRead
        topLabel.textColor = .color(hex: "#D9FF4D")
        bottomLabel.textColor = .color(hex: "#4C9743")
        
        topLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalTo(topLabel.snp.width).multipliedBy(0.3)
            make.bottom.equalTo(xamlLoad.snp.top).offset(-20)
        }
        bottomLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalTo(topLabel.snp.width).multipliedBy(0.3)
            make.top.equalTo(xamlLoad.snp.bottom).offset(20)
        }
    }
    func prepareRead(){
        speechRecognizer?.delegate = self as? SFSpeechRecognizerDelegate  //3
        speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: lang))
        SFSpeechRecognizer.requestAuthorization {[weak self] (authStatus) in  //4
            guard let self = self else { return }
            var isButtonEnabled = false
            
            switch authStatus {  //5
            case .authorized:
                isButtonEnabled = true
                
            case .denied:
                isButtonEnabled = false
                print("User denied access to speech recognition")
                
            case .restricted:
                isButtonEnabled = false
                print("Speech recognition restricted on this device")
                
            case .notDetermined:
                isButtonEnabled = false
                print("Speech recognition not yet authorized")
            }
            
            OperationQueue.main.addOperation() {
                [weak self] in
                guard let self = self else { return }
                if !isButtonEnabled {
                    // chưa sẳn sàng về quyền
                    self.finishGame()
                } else {
                    var delay = 0.0
                    UIView.animate(withDuration: 0.3, animations: {
                        self.textContainer.alpha = 0
                        self.centerView.alpha = 1
                    })
                    self.scheduler.schedule(delay: delay, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.xamlWave.startAnimation()
                        self.xamlLoad.startAnimation()
                        self.xamlWave.isHidden = false
                        listening = true
                        topLabel.text = "\(answer)\(game.question_mark ?? false ? "?" : ".")"
                        UIView.animate(withDuration: 0.3, animations: {
                            self.topLabel.transform = .identity
                            self.bottomLabel.transform = .identity
                            self.centerView.alpha = 1
                        })
                        self.toggleStartStop()
                    })
                }
            }
        }
    }
    func toggleStartStop() {
        
        speechRecognizer = SFSpeechRecognizer(locale: Locale.init(identifier: lang))
        
        if !listening {
            timerLongtimeout.cancel()
            audioEngine.stop()
            recognitionRequest?.endAudio()
            let audioSession = AVAudioSession.sharedInstance()
            do {
                try audioSession.setCategory(AVAudioSession.Category.playback)
                try audioSession.setMode(AVAudioSession.Mode.default)
                try audioSession.setActive(true)
            } catch {
                print("audioSession properties weren't set because of an error.")
            }
            //startStopBtn.isEnabled = false
            //startStopBtn.setTitle("Start Recording", for: .normal)
        } else {
            timerLongtimeout.schedule()
            self.startRecording()
            //self.playSound(name: "effects/score", delay: 0)
            scheduler.schedule(delay: 1, execute: {
                [weak self] in
                guard let self = self else { return }
                
            })
            
            //startStopBtn.setTitle("Stop Recording", for: .normal)
        }
    }
    
    
    func startRecording() {
        
        texts = []
        if recognitionTask != nil {
            recognitionTask?.cancel()
            recognitionTask = nil
        }
        let audioSession = AVAudioSession.sharedInstance()
        do {
            try audioSession.setCategory(AVAudioSession.Category.record)
            try audioSession.setMode(AVAudioSession.Mode.measurement)
            try audioSession.setActive(true, options: .notifyOthersOnDeactivation)
        } catch {
            print("audioSession properties weren't set because of an error.")
        }
        
        recognitionRequest = SFSpeechAudioBufferRecognitionRequest()
        
        let inputNode = audioEngine.inputNode
        
        guard let recognitionRequest = recognitionRequest else {
            fatalError("Unable to create an SFSpeechAudioBufferRecognitionRequest object")
        }
        
        recognitionRequest.shouldReportPartialResults = true
        
        recognitionTask = speechRecognizer?.recognitionTask(with: recognitionRequest, resultHandler: { [weak self] (result, error) in
            guard let self = self else { return }
            var isFinal = false
            
            if result != nil {
                var text = result?.bestTranscription.formattedString
                if text != nil && listening {
                    self.texts.append(text!)
                    self.timertimeout.schedule()
                }
                isFinal = (result?.isFinal)!
            }
            
            if error != nil || isFinal {
                self.audioEngine.stop()
                self.recognitionRequest?.endAudio()
                inputNode.removeTap(onBus: 0)
                
                self.recognitionRequest = nil
                self.recognitionTask = nil
                
                //self.startStopBtn.isEnabled = true
            }
        })
        
        let recordingFormat = inputNode.outputFormat(forBus: 0)
        audioEngine.inputNode.removeTap(onBus: 0)
        inputNode.installTap(onBus: 0, bufferSize: 1024, format: recordingFormat) { (buffer, when) in
            self.recognitionRequest?.append(buffer)
        }
        audioEngine.prepare()
        
        do {
            try audioEngine.start()
        } catch {
            print("audioEngine couldn't start because of an error.")
        }
        
        //textView.text = "Say something, I'm listening!"
        
    }
    // Override the willMove(toSuperview:) method
    override func willMove(toSuperview newSuperview: UIView?) {
        super.willMove(toSuperview: newSuperview)
        
        if newSuperview == nil {
            // The view is being removed from the view hierarchy, remove the tap from the input node.
            if listening {
                audioEngine.stop()
                recognitionRequest?.endAudio()
                audioEngine.inputNode.removeTap(onBus: 0)
                let audioSession = AVAudioSession.sharedInstance()
                do {
                    try audioSession.setCategory(AVAudioSession.Category.playback)
                    try audioSession.setMode(AVAudioSession.Mode.default)
                    try audioSession.setActive(true)
                } catch {
                    print("audioSession properties weren't set because of an error.")
                }
                timerLongtimeout.cancel()
                timertimeout.cancel()
            }
            reachability.stopNotifier()
        }
    }

    func calculateFontSize(for text: String, maxWidth: CGFloat) -> CGFloat {
        var fontSize: CGFloat = 0
        var minFontSize: CGFloat = 0
        var maxFontSize: CGFloat = 1000 // You can adjust this to limit the maximum font size
        
        while minFontSize <= maxFontSize {
            fontSize = (minFontSize + maxFontSize) / 2
            let attributes: [NSAttributedString.Key: Any] = [
                NSAttributedString.Key.font: UIFont.Freude(size: fontSize)
            ]
            
            let textRect = NSString(string: text).boundingRect(
                with: CGSize(width: maxWidth * 2, height: maxWidth * 2),
                options: .usesLineFragmentOrigin,
                attributes: attributes,
                context: nil
            )
            
            if textRect.width <= maxWidth {
                minFontSize = fontSize + 1
            } else {
                maxFontSize = fontSize - 1
            }
        }
        
        return fontSize
    }
    func calculateFontSize(for texts: [String], maxWidth: CGFloat) -> CGFloat{
        var size = 1000.0
        for text in texts {
            let s = calculateFontSize(for: text, maxWidth: maxWidth)
            if s < size {
                size = s
            }
        }
        return size
    }
}
