//
//  taptrung_list_bimtocmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 22/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_bimtocmau: NhanBietGameFragment {
    // MARK: - Properties
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F9FF03"),
        UIColor.color(hex: "#F45D73"),
        UIColor.color(hex: "#5CFF1F")
    ].shuffled()
    private var svgView: SVGImageView!
    private var dest1View: UIView!
    private var dest2View: UIView!
    private var dest3View: UIView!
    private var circle1View: UIView!
    private var circle2View: UIView!
    private var circle3View: UIView!
    private var itemContainer: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var doneViews: [UIView] = []
    private var doneTags: [Int] = []
    private var svg: SVGKImage!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#24669F")
        
        let paddingView = UIView()
        view.addSubviewWithPercentInset(subview: paddingView, percentInset: 0.05)
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        //mainContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        paddingView.addSubview(mainContainer)
        mainContainer.makeViewCenterAndKeep(ratio: 3)
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        mainContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.height).multipliedBy(400.0 / 105.5) // Ratio 400:105.5
            make.width.equalTo(mainContainer).multipliedBy(0.84)
            make.centerY.equalToSuperview()
            make.left.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        mainContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.left.equalTo(svgView.snp.right)
            make.right.equalToSuperview()
            make.top.bottom.equalTo(svgView)
        }
        
        circle1View = UIView()
        circle1View.stringTag = "circle_1_view"
        itemContainer.addSubview(circle1View)
        circle1View.snp.makeConstraints { make in
            make.width.equalTo(circle1View.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.28)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        circle2View = UIView()
        circle2View.stringTag = "circle_2_view"
        itemContainer.addSubview(circle2View)
        circle2View.snp.makeConstraints { make in
            make.width.equalTo(circle2View.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.28)
            make.center.equalToSuperview()
        }
        
        circle3View = UIView()
        circle3View.stringTag = "circle_3_view"
        itemContainer.addSubview(circle3View)
        circle3View.snp.makeConstraints { make in
            make.width.equalTo(circle3View.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.28)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        dest1View = UIView()
        dest1View.stringTag = "dest_1_view"
        dest1View.isHidden = true
        mainContainer.addSubview(dest1View)
        dest1View.snp.makeConstraints { make in
            make.width.equalTo(dest1View.snp.height) // Ratio 1:1
            make.height.equalTo(mainContainer).multipliedBy(0.185)
            make.right.equalTo(svgView)
            make.top.equalTo(svgView)
        }
        
        dest2View = UIView()
        dest2View.stringTag = "dest_2_view"
        dest2View.isHidden = true
        mainContainer.addSubview(dest2View)
        dest2View.snp.makeConstraints { make in
            make.width.equalTo(dest2View.snp.height) // Ratio 1:1
            make.height.equalTo(mainContainer).multipliedBy(0.185)
            make.right.equalTo(svgView)
            make.centerY.equalTo(mainContainer)
        }
        
        dest3View = UIView()
        dest3View.stringTag = "dest_3_view"
        dest3View.isHidden = true
        mainContainer.addSubview(dest3View)
        dest3View.snp.makeConstraints { make in
            make.width.equalTo(dest3View.snp.height) // Ratio 1:1
            make.height.equalTo(mainContainer).multipliedBy(0.185)
            make.right.equalTo(svgView)
            make.bottom.equalTo(svgView)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let number = Int.random(in: 1...3)        
        svg = Utilities.GetSVGKImage(named: "taptrung_bimtoc\(number)")
        for i in 0..<3 {
            svg.caLayerTree.sublayers![i].sublayers![0].setFillColor(color: .white)
            svg.caLayerTree.sublayers![i].sublayers![1].setFillColor(color: self.colors[i])
        }
        self.svgView.image = svg.uiImage
        
        let tags = (0..<3).shuffled()
        circle1View.stringTag = "\(tags[0])"
        circle2View.stringTag = "\(tags[1])"
        circle3View.stringTag = "\(tags[2])"
        dest1View.stringTag = "\(0)"
        dest2View.stringTag = "\(1)"
        dest3View.stringTag = "\(2)"
        
        let views = [circle1View, circle2View, circle3View]
        for (i, circleView) in views.enumerated() {
            circleView?.layer.cornerRadius = circleView!.frame.width / 2
            circleView?.backgroundColor = colors[tags[i]]
            //circleView?.transform = CGAffineTransform(scaleX: 0, y: 0)
            circleView?.alpha = 0.01
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                circleView?.layer.cornerRadius = circleView!.frame.height / 2
            }
        }
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_bim toc"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6, delay: 0, options: .curveEaseOut) {
                self.circle1View.alpha = 1
                self.circle1View.transform = .identity
            }
            UIView.animate(withDuration: 0.6, delay: 0.4, options: .curveEaseOut) {
                self.circle2View.alpha = 1
                self.circle2View.transform = .identity
            }
            UIView.animate(withDuration: 0.6, delay: 0.8, options: .curveEaseOut) {
                self.circle3View.alpha = 1
                self.circle3View.transform = .identity
            }
        }
        
        playSound(name: "effect/bubble1", delay: delay)
        playSound(name: "effect/bubble2", delay: delay + 0.4)
        playSound(name: "effect/bubble3", delay: delay + 0.8)
        
        delay += 1.5
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_bim toc")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    var originX = 0.0, originY = 0.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.layer.zPosition = CGFloat.greatestFiniteMagnitude
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                originX = currentView.frame.minX
                originY = currentView.frame.minY
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if let currentView = currentView {
                let dests = [dest1View, dest2View, dest3View]
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for dest in dests {
                    guard let dest = dest else { continue }
                    let vector = currentView.distanceFromCenterToCenter(to: dest)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = dest
                    }
                }
                
                if let targetView = targetView, minDistance < targetView.frame.height / 2 {
                    guard let targetTag = Int(targetView.stringTag ?? "0"),
                          let tag = Int(currentView.stringTag ?? "0") else { return }
                    if tag == targetTag && !doneTags.contains(targetTag) {
                        playSound("effect/word puzzle drop")
                        if !doneViews.contains(currentView) {
                            doneViews.append(currentView)
                        }
                        doneTags.append(targetTag)
                        currentView.moveToCenter(of: targetView, duration: 0.2)
                        if let svg = svg {
                            svg.caLayerTree.sublayers![tag].sublayers![0].setFillColor(color: colors[tag])
                            svgView.image = svg.uiImage
                        }
                        if doneViews.count == 3 {
                            pauseGame(stopMusic: false)
                            animateCoinIfCorrect(view: svgView)
                            let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
                            scheduler.schedule(after: delay) { [weak self] in
                                self?.finishGame()
                            }
                        }
                        return
                    } else {
                        setGameWrong()
                    }
                }
                UIView.animate(withDuration: 0.5) {
                    currentView.transform = .identity
                    currentView.frame.origin = CGPointMake(self.originX, self.originY)
                }
                playSound("effect/slide2")
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<itemContainer.subviews.count).reversed() {
            let child = itemContainer.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
}
