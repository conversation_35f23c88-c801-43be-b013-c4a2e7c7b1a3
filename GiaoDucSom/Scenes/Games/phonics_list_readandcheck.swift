//
//  phonics_list_readandcheck.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_readandcheck: GameFragment {
    var bottomGrid = MyGridView()
    var centerView = SVGImageView(SVGName: "bg obj white").then{
        $0.contentMode = .scaleAspectFit
    }
    var imageView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    private var values : [String] = []
    var meIndex = 0
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        let rightView = UIView()
        addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        rightView.addSubview(centerView)
        centerView.makeViewCenterAndKeep(ratio: 1)
        centerView.transform = CGAffineTransformMakeScale(0.8, 0.8)
        centerView.addSubview(imageView)
        imageView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#849BFD")
        addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(centerView.snp.left)
        }
        
        leftView.addSubviewWithPercentInset(subview: bottomGrid, percentInset: 0.1)
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let texts = parseIntroText() ?? []
            var delay: TimeInterval = 0.5
            for text in texts {
                delay += playSound(text, delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 4)
        meIndex = Int.random(in: 0..<values.count)
        imageView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : "english phonics/\(game.level!)/\(values[meIndex]).svg"
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        let values1 = values.randomOrder()
        var listViews : [UIView] = []
        for value in values1 {
            let view = createGridItem()
            let label: AutosizeLabel = view.viewWithTag(1) as! AutosizeLabel
            label.text = value
            view.tag = 100 + values.firstIndex(of: value)!
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        bottomGrid.columns = 1
        bottomGrid.itemRatio = 2.3
        bottomGrid.itemSpacingRatio = 0.05
        bottomGrid.insetRatio = 0.05
        bottomGrid.reloadItemViews(views: listViews)
        delay += 0.2
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.bottomGrid.playSound = false
            self.bottomGrid.step = 0.5
            self.bottomGrid.showItems()
            for i in 0..<values1.count {
                self.playSound(name: "effect/bubble\(i+1)", delay: 0.5*Double(i))
            }
        })
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    @objc func itemClick(_ sender: UIControl){
        let label = sender.viewWithTag(1) as! AutosizeLabel
        if sender.tag == meIndex + 100 {
            pauseGame()
            //sender.animateCoin(answer: true)
            animateCoinIfCorrect(view: sender)
            var delay = 0.0
            delay += self.playSound(name: answerCorrect1EffectSound(), delay: delay)
            delay += self.playSound(name: getCorrectHumanSound(), delay: delay)
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : values[meIndex], delay: delay)
            delay += self.playSound(name: endGameSound(), delay: delay)
            delay += 1
            label.textColor = .color(hex: "73D048")
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
        } else {
            pauseGame()            
            //sender.animateCoin(answer: false)
            var delay = 0.0
            delay += playSound(delay: delay, names:[answerWrongEffectSound(), getWrongHumanSound()])
            setGameWrong()
            incorrect += 1
            label.textColor = .color(hex: "FF7761")
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "68C1FF")
                self.resumeGame()
            })
        }
    }
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "option_bg_white_long_shadow")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 2.3)
        let label = AutosizeLabel()
        label.textColor = .color(hex: "#68C1FF")
        label.tag = 1
        background.addSubview(label)
        label.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}
