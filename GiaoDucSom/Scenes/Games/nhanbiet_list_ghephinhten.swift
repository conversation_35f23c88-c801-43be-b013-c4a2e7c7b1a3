//
//  nhanbiet_list_ghephinhten.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_ghephinhten: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var svgImage: SVGImageView!
    private var textName: UILabel!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    private var viewLeft: UIImageView!
    private var viewRight: UIImageView!
    private var centerView: UIView!
    private var viewCompleted: UIImageView!
    var centerLeft, centerRight: CGPoint!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_puzzle2"))!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        centerView = UIView()
        itemContainer.addSubview(centerView)
        centerView.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalTo(centerView.snp.width).multipliedBy(1/5.2) // Ratio 5.2:1
            make.center.equalToSuperview()
        }
        
        viewLeft = UIImageView()
        viewLeft.image = Utilities.SVGImage(named: "nhanbiet_btn_puzzle1")
        viewLeft.stringTag = "view_left"
        itemContainer.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            make.width.equalTo(centerView.snp.height).multipliedBy(779.1/595.9) // Ratio 779.1:595.9
            make.height.equalTo(centerView)
            //make.centerX.equalToSuperview().multipliedBy(0.4) // Horizontal bias 0.2
            make.centerY.equalTo(centerView)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.viewLeft.snapToHorizontalBias(horizontalBias: 0.2)
        }
        
        svgImage = SVGImageView(frame: .zero)
        svgImage.stringTag = "svg_image"
        viewLeft.addSubview(svgImage)
        svgImage.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.8)
            //make.centerX.equalToSuperview().multipliedBy(0.5) // Horizontal bias 0.25
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.svgImage.snapToHorizontalBias(horizontalBias: 0.25)
        }
        
        viewRight = UIImageView()
        viewRight.image = Utilities.SVGImage(named: "nhanbiet_btn_puzzle2")
        viewRight.stringTag = "view_right"
        itemContainer.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            make.width.equalTo(centerView.snp.height).multipliedBy(1074.2/595.9) // Ratio 1074.2:595.9
            make.height.equalTo(centerView)
            //make.centerX.equalToSuperview().multipliedBy(1.6) // Horizontal bias 0.8
            make.centerY.equalTo(centerView)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.viewRight.snapToHorizontalBias(horizontalBias: 0.8)
        }
        
        viewCompleted = UIImageView()
        viewCompleted.stringTag = "view_completed"
        viewCompleted.image = Utilities.SVGImage(named: "nhanbiet_btn_puzzle2_completed")
        viewCompleted.alpha = 1
        viewRight.addSubview(viewCompleted)
        viewCompleted.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        textName = AutosizeLabel()
        textName.stringTag = "text_name"
        textName.textColor = UIColor.color(hex: "#FF7761")
        textName.font = .Freude(size: 20)
        viewRight.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.5)
            //make.centerX.equalToSuperview().multipliedBy(1.6) // Horizontal bias 0.8
            make.centerY.equalToSuperview().multipliedBy(0.86) // Vertical bias 0.43
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.textName.snapToHorizontalBias(horizontalBias: 0.8)
        }
        
        // Add pan gesture for drag
        //let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        //itemContainer.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let item = getItem(), let folder = getFolder(), let path = item.path, !path.isEmpty else { return }
        
        textName.text = getLanguage() == "vi" ? item.name.vi : item.name.en
        svgImage.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(path)").uiImage
        
        let delay = playSound(delay: 0, names: [openGameSound(), "\(getLanguage())/nhanbiet/nhanbiet_puzzle2"])
        scheduler.schedule(after: delay) {
            [weak self] in
            guard let self = self else { return }
            self.centerLeft = viewLeft.center
            self.centerRight = viewRight.center
            self.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_puzzle2")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    var zPosition = 10.0
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        let location = gesture.location(in: view)
        latestPoint = location
        deltaX = centerView.frame.minX
        deltaY = centerView.frame.minY
        currentView = gesture.placeInView(view: viewLeft) ? viewLeft :
                    gesture.placeInView(view: viewRight) ? viewRight : nil
        if let currentView = currentView {
            dX = currentView.frame.minX - location.x
            dY = currentView.frame.minY - location.y
            currentView.layer.zPosition = zPosition
            zPosition += 1
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            Utils.vibrate()
        }
    }
    var latestPoint = CGPoint.zero
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        let location = gesture.location(in: view)
        if let currentView = currentView {
            let newX = currentView.center.x + location.x - latestPoint.x
            let newY = currentView.center.y + location.y - latestPoint.y
            currentView.center = CGPoint(x: newX, y: newY)
            latestPoint = location
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count > 1 { return }
        let gesture = touches.first!
        let view = itemContainer
        if let currentView = currentView {
            Utils.vibrate()
            playSound("effect/word puzzle drop")
            Utils.vibrate()
            if let viewLeft = viewLeft, let viewRight = viewRight {
                if viewsAreClosed(viewLeft: viewLeft, viewRight: viewRight) {
                    moveViewsToCenter()
                }
            }
        }
        currentView = nil
    }
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for child in itemContainer.subviews.reversed() {
            if x >= child.frame.minX && x <= child.frame.maxX &&
               y >= child.frame.minY && y <= child.frame.maxY {
                if child.stringTag == "view_left" || child.stringTag == "view_right" {
                    return child
                }
            }
        }
        return nil
    }
    
    private func viewsAreClosed(viewLeft: UIView, viewRight: UIView) -> Bool {
        let x1 = viewLeft.frame.midX
        let y1 = viewLeft.frame.midY
        let x2 = viewRight.frame.midX
        let y2 = viewRight.frame.midY
        let ratioY = abs(y2 - y1) / viewLeft.frame.height
        let ratioX = abs(x2 - x1) / viewLeft.frame.width
        return ratioY < 0.1 && ratioX > 0.85 && ratioX < 1.05
    }
    
    private func moveViewsToCenter() {
        if let completedView = viewRight.viewWithStringTag("view_completed") {
            UIView.animate(withDuration: 0.1, animations: {
                completedView.alpha = 0
            }, completion: { _ in
                self.viewRight.layer.zPosition = 1
            })
        }
        
        let distance = viewLeft.frame.width * 0.7
        
        UIView.animate(withDuration: 0.5, animations: {
            self.viewLeft.center = CGPoint(x: self.centerLeft.x + distance / 2, y: self.centerLeft.y)
            self.viewRight.center = CGPoint(x: self.centerRight.x - distance / 2, y: self.centerRight.y)
        })
        
        pauseGame(stopMusic: false)
        var delay: TimeInterval = 0
        if let item = self.getItem(), let folder = self.getFolder() {
            delay += self.playSound(delay: delay, names: [                
                "effect/answer_end",
                self.getCorrectHumanSound(),
                "\(self.getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))",
                endGameSound()
            ])
        }
        //animateCoinIfCorrect(view: centerView)
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
}
