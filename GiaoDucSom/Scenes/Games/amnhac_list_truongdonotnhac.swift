//
//  amnhac_list_truongdonotnhac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFoundation

class amnhac_list_truongdonotnhac: MusicGameFragment {
    // MARK: - Properties
    private var svgPhimDanView: SVGKFastImageView!
    private var phimDanSVG: SVGKImage?
    private var btnC1: UIView!
    private var btnD1: UIView!
    private var btnE1: UIView!
    private var btnF1: UIView!
    private var btnG1: UIView!
    private var btnA1: UIView!
    private var btnB1: UIView!
    private var btnC2: UIView!
    private var gridLayout: UIView!
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    private var musicStreamIdMap: [String: Bool] = [:]
    private var textColorMap: [String: UIColor] = [:]
    private var bgColorMap: [String: UIColor] = [:]
    private var bg2ColorMap: [String: UIColor] = [:]
    private let notes: [String] = ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "c2"]
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F73535"),
        UIColor.color(hex: "#FF8700"),
        UIColor.color(hex: "#FFD400"),
        UIColor.color(hex: "#05BD34"),
        UIColor.color(hex: "#00CFFF"),
        UIColor.color(hex: "#1B7CCC"),
        UIColor.color(hex: "#BF04BB"),
        UIColor.color(hex: "#F73535")
    ]
    private var busy: Bool = false
    private var animationValue: CGFloat = 0
    private var tappedTime: CGFloat = 0
    private var noteIndex: Int = 0
    private var musicLength: Int = 8
    private var viewBg: UIView!
    private var viewBg2: UIView!
    private var viewRight: UIView!
    private var viewWrong: UIView!
    private var viewWrong2: UIView!
    private var textRight: HeightRatioTextView!
    private var textWrong: HeightRatioTextView!
    private var animator: UIViewPropertyAnimator?
    private var correct: Bool = false
    let innerContainer = UIImageView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        
        setupAppStateObservers()
        
        
        noteIndex = Int.random(in: 0..<notes.count)
        //noteIndex = 1
        musicLength = [1, 2, 4, 6, 8].randomElement() ?? 8
        view.backgroundColor = .white // #FFF
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
        
        innerContainer.isUserInteractionEnabled = true
        innerContainer.image = Utilities.SVGImage(named: "music_phimdan2")
        innerContainer.clipsToBounds = false
        itemContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 1840.0 / 1241.0)
        
        svgPhimDanView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "music_phimdan2"))
        svgPhimDanView.contentMode = .scaleToFill
        svgPhimDanView.stringTag = "svg_phimdan_view"
        innerContainer.addSubview(svgPhimDanView)
        svgPhimDanView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let buttons: [(id: String, bias: CGFloat, width: CGFloat)] = [
            ("c1", 0.041, 0.10),
            ("d1", 0.173, 0.10),
            ("e1", 0.305, 0.10),
            ("f1", 0.435, 0.10),
            ("g1", 0.567, 0.10),
            ("a1", 0.698, 0.10),
            ("b1", 0.83, 0.10),
            ("c2", 0.96, 0.10)
        ]
        
        for (id, bias, width) in buttons {
            let btn = UIView()
            btn.stringTag = id
            btn.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
            btn.alpha = 0.1
            innerContainer.addSubview(btn)
            btn.snp.makeConstraints { make in
                make.width.equalTo(innerContainer).multipliedBy(width)
                make.height.equalTo(innerContainer).multipliedBy(0.41)
                make.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                btn.snapToHorizontalBias(horizontalBias: bias)
            }
            btn.isUserInteractionEnabled = true
            
            switch id {
            case "c1": btnC1 = btn
            case "d1": btnD1 = btn
            case "e1": btnE1 = btn
            case "f1": btnF1 = btn
            case "g1": btnG1 = btn
            case "a1": btnA1 = btn
            case "b1": btnB1 = btn
            case "c2": btnC2 = btn
            default: break
            }
        }
        
        gridLayout = UIView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        gridLayout.clipsToBounds = false
        innerContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.8 * Float(musicLength) / 8)
            make.height.equalTo(innerContainer).multipliedBy(0.55)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.591) // verticalBias=0.08
        }
        
        viewBg = UIView()
        viewBg.backgroundColor = colors[noteIndex]
        viewBg.alpha = 0.1
        gridLayout.addSubview(viewBg)
        viewBg.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewBg2 = UIView()
        viewBg2.backgroundColor = colors[noteIndex]
        viewBg2.alpha = 0.3
        viewBg2.isHidden = true
        gridLayout.addSubview(viewBg2)
        viewBg2.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.left.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        
        viewRight = UIView()
        viewRight.backgroundColor = colors[noteIndex]
        gridLayout.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            make.height.equalTo(gridLayout).multipliedBy(0.16)
            make.left.right.equalToSuperview()
        }
        let verticalBias = notes[noteIndex] == "c1" ? 1.0 : notes[noteIndex] == "d1" ? 0.9 : notes[noteIndex] == "e1" ? 0.8 : notes[noteIndex] == "f1" ? 0.71 : notes[noteIndex] == "g1" ? 0.61 : notes[noteIndex] == "a1" ? 0.517 : notes[noteIndex] == "b1" ? 0.42 : 0.325
        addActionOnLayoutSubviews{
            [weak self] in
            guard let self = self else { return }
            self.viewRight.snapToVerticalBias(verticalBias: verticalBias)
        }
        
        let rightInner = UIView()
        viewRight.addSubview(rightInner)
        rightInner.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(rightInner.snp.height)
        }
        
        textRight = HeightRatioTextView()
        textRight.setHeightRatio(0.7)
        textRight.text = notes[noteIndex].prefix(1).uppercased()
        textRight.font = .Freude(size: 20)
        textRight.textColor = .white
        textRight.textAlignment = .center
        textRight.backgroundColor = .clear
        rightInner.addSubview(textRight)
        textRight.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewWrong = UIView()
        viewWrong.backgroundColor = .green.withAlphaComponent(0.1)
        viewWrong.isHidden = true
        gridLayout.addSubview(viewWrong)
        viewWrong.snp.makeConstraints { make in
            make.height.equalTo(gridLayout).multipliedBy(0.16)
            make.left.right.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(0.8) // Default verticalBias=0.4
        }
        
        viewWrong2 = UIView()
        viewWrong2.backgroundColor = .red
        viewWrong.addSubview(viewWrong2)
        viewWrong2.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(viewWrong2.snp.height)
        }
        
        textWrong = HeightRatioTextView()
        textWrong.setHeightRatio(0.7)
        textWrong.text = "A"
        textWrong.font = .Freude(size: 20)
        textWrong.textColor = .white
        textWrong.textAlignment = .center
        textWrong.backgroundColor = .clear
        viewWrong2.addSubview(textWrong)
        textWrong.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Comment mask as per your style
        /*
        let mask = PassthroughImageView()
        mask.image = Utilities.SVGImage(named: "music_phimdan2_mask")
        innerContainer.addSubview(mask)
        mask.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        */
        
        loadPiano()
    }
    var loadedPiano: Bool = false
    func loadPiano() {
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in notes {
                if let url = Utilities.url(soundPath: "effect/music/piano_8\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
            loadedPiano = true
        }
    }
    func tryStartGame(){
        if !loadedPiano {
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                tryStartGame()
            }
            return
        }
        self.startGame()
    }
    var touchNote = ""
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count == 1 {
            let touch = touches.first!
            for i in 0..<innerContainer.subviews.count {
                let view = innerContainer.subviews[i]
                if touch.placeInView(view: view) {
                    if let note = view.stringTag as? String, note != "svg_phimdan_view" {
                        touchNote = note
                        handleButtonTap(note: note)
                    }
                }
            }
        }
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing { return }
        if touches.count == 1 {
            handleButtonTapUp()
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        startTimeLoop()
        let delay = playSound(openGameSound(), "vi/music/truong do")
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.tryStartGame()
        }
    }
    
    private func setupAppStateObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(pauseAnimation),
            name: UIApplication.didEnterBackgroundNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(resumeAnimation),
            name: UIApplication.willEnterForegroundNotification,
            object: nil
        )
    }

    @objc private func pauseAnimation() {
        animator?.pauseAnimation()
    }

    @objc private func resumeAnimation() {
        animator?.startAnimation()
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    override func didMoveToSuperview() {
        super.didMoveToSuperview()
        if superview == nil {
            // View bị xóa khỏi view tree, tương đương onPause()
            animator?.pauseAnimation()
        } else {
            // View được thêm vào view tree, tương đương onResume()
            animator?.startAnimation()
        }
    }
    
    override func removeFromSuperview() {
        super.removeFromSuperview()
        animator?.stopAnimation(true)
        //animator?.finishAnimation(at: .current)
    }
    func resetGame(){
        noteIndex = Int.random(in: 0..<notes.count)
        //noteIndex = 1
        musicLength = [1, 2, 4, 6, 8].randomElement() ?? 8
        touchNote = ""
        correct = false
        viewBg.backgroundColor = colors[noteIndex]
        viewBg2.backgroundColor = colors[noteIndex]
        gridLayout.stopColor()
        gridLayout.clearColor()
        viewWrong.stopColor()
        viewWrong.clearColor()
        gridLayout.snp.remakeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.8 * Float(musicLength) / 8)
            make.height.equalTo(innerContainer).multipliedBy(0.546)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.585) // verticalBias=0.08
        }
        viewRight.backgroundColor = colors[noteIndex]
        let verticalBias = notes[noteIndex] == "c1" ? 1.0 : notes[noteIndex] == "d1" ? 0.9 : notes[noteIndex] == "e1" ? 0.8 : notes[noteIndex] == "f1" ? 0.71 : notes[noteIndex] == "g1" ? 0.61 : notes[noteIndex] == "a1" ? 0.517 : notes[noteIndex] == "b1" ? 0.42 : 0.325
        self.viewRight.snapToVerticalBias(verticalBias: verticalBias)
        textRight.text = notes[noteIndex].prefix(1).uppercased()
    }
    // MARK: - Touch Handling
    private func handleButtonTapUp(){
        let note = touchNote
        correct = note == notes[noteIndex]
        if let player = noteToMusicId[note] {
            if correct {
                let delta = abs(gridLayout.colorWidth()-gridLayout.frame.width) / gridLayout.frame.height
                player.pause()
                gridLayout.stopColor()
                if delta < 0.1 {                    
                    playSound(answerCorrect1EffectSound())
                    self.resetGame()
                }
            }
            else {
                player.pause()
                viewWrong.stopColor()
            }
            self.busy = false
            
        }
    }
    private func handleButtonTap(note: String) {
        if busy { return }
        busy = true
        
        correct = note == notes[noteIndex]
        tappedTime = animationValue
        
        if let player = noteToMusicId[note] {
            player.currentTime = 0
            player.play()
            musicStreamIdMap[note] = true
        }
        
        // Placeholder for SVG path manipulation
        textColorMap[note] = .color(hex: "#B8C8D3")
        bgColorMap[note] = .color(hex: "#DCE4EA")
        bg2ColorMap[note] = .color(hex: "#DCE4EA")
        svgPhimDanView.setNeedsDisplay()
        let duration = 3.0
        if correct {
            viewWrong.clearColor()
            viewWrong.isHidden = true
            viewBg2.isHidden = false
            /*
            viewBg2.snp.updateConstraints { make in
                make.width.equalTo(1)
            }
             */
            gridLayout.startColor(duration: duration, color: colors[noteIndex].withAlphaComponent(0.5), width: gridLayout.frame.height*3)
        } else {
            viewWrong.startColor(duration: duration, color: colors[notes.firstIndex(of: note)!],width: gridLayout.frame.height*3)
            viewWrong2.backgroundColor = viewWrong.backgroundColor
            viewWrong.isHidden = false
            let verticalBias = note == "c1" ? 1.0 : note == "d1" ? 0.9 : note == "e1" ? 0.8 : note == "f1" ? 0.71 : note == "g1" ? 0.61 : note == "a1" ? 0.517 : note == "b1" ? 0.42 : 0.325
            viewWrong.snapToVerticalBias(verticalBias: verticalBias)
            textWrong.text = note.prefix(1).uppercased()
            gridLayout.clearColor()
        }
        scheduler.schedule(after: duration) { [weak self] in
            self?.busy = false
        }
        /*
        scheduler.schedule(after: 0.1) { [weak self] in
            self?.busy = false
            self?.viewWrong.isHidden = true
            self?.viewBg2.isHidden = true
            
            if self?.correct == true {
                let delta = abs((self?.viewBg2.frame.width ?? 0) - (self?.viewRight.frame.width ?? 0))
                let percent = delta / (self?.viewRight.frame.height ?? 1)
                if percent < 0.5 {
                    self?.noteIndex = Int.random(in: 0..<self!.notes.count)
                    self?.musicLength = [1, 2, 4, 6, 8].randomElement() ?? 8
                    self?.gridLayout.snp.updateConstraints { make in
                        make.width.equalTo(self!.innerContainer).multipliedBy(0.8 * Float(self!.musicLength) / 8)
                    }
                    let note2 = self!.notes[self!.noteIndex]
                    let verticalBias = note2 == "c1" ? 1.0 : note2 == "d1" ? 0.9 : note2 == "e1" ? 0.8 : note2 == "f1" ? 0.71 : note2 == "g1" ? 0.61 : note2 == "a1" ? 0.517 : note2 == "b1" ? 0.42 : 0.325
                    self?.viewRight.backgroundColor = self!.colors[self!.noteIndex]
                    self?.viewRight.snp.updateConstraints { make in
                        make.centerY.equalToSuperview().multipliedBy(verticalBias * 2)
                    }
                    self?.textRight.text = note2.prefix(1).uppercased()
                    self?.viewBg.backgroundColor = self!.colors[self!.noteIndex]
                    self?.viewBg.alpha = 0.1
                    self?.viewBg2.backgroundColor = self!.colors[self!.noteIndex]
                    self?.viewBg2.alpha = 0.3
                    self?.animator?.stopAnimation(true)
                    self?.scheduler.schedule(after: 0.1) { [weak self] in
                        self?.startTimeLoop()
                    }
                }
            }
            
            // Placeholder for SVG path manipulation
            self?.textColorMap.removeValue(forKey: note)
            self?.bgColorMap.removeValue(forKey: note)
            self?.bg2ColorMap.removeValue(forKey: note)
            self?.svgPhimDanView.setNeedsDisplay()
            self?.musicStreamIdMap[note] = false
        }*/
    }
    
    // MARK: - Animation
    private func startTimeLoop() {
        if 1==1 { return }
        let multiple: CGFloat = 10000
        animator?.stopAnimation(true)
        animator = UIViewPropertyAnimator(duration: 300 * Double(musicLength) * Double(multiple) / 1000, curve: .linear) { [weak self] in
            guard let self = self else { return }
            //self.gridLayout.transform = CGAffineTransform(translationX: -self.gridLayout.frame.width * multiple, y: 0)
        }
        animator?.addAnimations { [weak self] in
            guard let self = self else { return }
            if self.busy {
                let targetView = self.correct ? self.viewBg2 : self.viewWrong
                targetView?.snp.updateConstraints { make in
                    make.width.equalTo(-(self.animationValue - self.tappedTime))
                }
            }
        }
        animator?.addCompletion { [weak self] _ in
            self?.startTimeLoop()
        }
        animator?.startAnimation()
        
        // Update animationValue
        let valueAnimator = UIViewPropertyAnimator(duration: 300 * Double(musicLength) * Double(multiple) / 1000, curve: .linear) { [weak self] in
            self?.animationValue = -self!.gridLayout.frame.width * multiple
        }
        valueAnimator.startAnimation()
    }
    
    // MARK: - Supporting Structures
    
  
}

struct R18 {
    struct drawable {
        static let music_phimdan2 = 1
        static let music_phimdan2_mask = 2
        static let music_truongdo = 3
    }
}

extension Int {
    func toDrawableNameTDNN() -> String? {
        switch self {
        case R18.drawable.music_phimdan2:
            return "music_phimdan2"
        case R18.drawable.music_phimdan2_mask:
            return "music_phimdan2_mask"
        case R18.drawable.music_truongdo:
            return "music_truongdo"
        default:
            return "empty"
        }
    }
}

import UIKit

extension UIView {
    // Associated object keys để lưu trữ layer và animation
    private struct AssociatedKeys {
        static var redRectangleLayerKey = "redRectangleLayerKey"
        static var widthAnimationKey = "widthAnimationKey"
    }
    
    // Lưu trữ redRectangleLayer
    private var redRectangleLayer: CALayer? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.redRectangleLayerKey) as? CALayer
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.redRectangleLayerKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // Lưu trữ animation
    private var widthAnimation: CABasicAnimation? {
        get {
            return objc_getAssociatedObject(self, &AssociatedKeys.widthAnimationKey) as? CABasicAnimation
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.widthAnimationKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // Hàm bắt đầu hoạt hình với tham số màu
    func startColor(duration: TimeInterval, color: UIColor = .red, width: CGFloat = 100) {
        // Xóa layer cũ nếu có
        redRectangleLayer?.removeFromSuperlayer()
        redRectangleLayer = nil
        widthAnimation = nil
        
        // Tạo CALayer cho hình chữ nhật
        let rectangleLayer = CALayer()
        rectangleLayer.backgroundColor = color.cgColor
        // Đặt anchorPoint thành (0, 0) để mở rộng từ mép trái, trên
        rectangleLayer.anchorPoint = CGPoint(x: 0, y: 0)
        // Đặt frame ban đầu: mép trái, trên, dưới dính vào view, chiều rộng = 0
        rectangleLayer.frame = CGRect(x: 0, y: 0, width: 0, height: bounds.height)
        // Chèn layer vào vị trí thấp nhất (dưới các subview)
        layer.insertSublayer(rectangleLayer, at: 0)
        redRectangleLayer = rectangleLayer
        
        // Tạo animation để tăng chiều rộng
        let animation = CABasicAnimation(keyPath: "bounds.size.width")
        animation.fromValue = 0
        animation.toValue = width
        animation.duration = duration
        animation.timingFunction = CAMediaTimingFunction(name: .linear)
        animation.fillMode = .forwards
        animation.isRemovedOnCompletion = false
        
        // Lưu animation
        widthAnimation = animation
        
        // Áp dụng animation
        rectangleLayer.add(animation, forKey: "widthAnimation")
    }
    
    // Hàm dừng hoạt hình
    func stopColor() {
        guard let rectangleLayer = redRectangleLayer else { return }
        
        // Lấy giá trị hiện tại của chiều rộng
        if let presentationLayer = rectangleLayer.presentation() {
            let currentWidth = presentationLayer.bounds.size.width
            // Cập nhật frame của layer để giữ trạng thái hiện tại
            rectangleLayer.frame = CGRect(x: 0, y: 0, width: currentWidth, height: bounds.height)
        }
        
        // Xóa animation
        rectangleLayer.removeAnimation(forKey: "widthAnimation")
        widthAnimation = nil
    }
    func clearColor() {
        redRectangleLayer?.removeFromSuperlayer()
        redRectangleLayer = nil
    }
    func colorWidth() -> CGFloat {
        guard let rectangleLayer = redRectangleLayer else {
            return 0 // Trả về 0 nếu layer không tồn tại
        }
        
        // Nếu layer đang animating, lấy chiều rộng từ presentation layer
        if rectangleLayer.animation(forKey: "widthAnimation") != nil {
            return rectangleLayer.presentation()?.bounds.size.width ?? rectangleLayer.bounds.size.width
        }
        
        // Nếu không animating, trả về chiều rộng của model layer
        return rectangleLayer.bounds.size.width
    }
}
