//
//  phonics_list_tapviet.swift
//  KidsUPTiengViet
//
//  Created by <PERSON><PERSON> on 31/01/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit

final class phonics_list_tapviet:GameFragment, TapVietViewDelegate{
    func tapvietDragBegin(_ tv: TapVietView) {
        
    }
    
    func tapvietDragEnd(_ tv: TapVietView) {
        move += 1
    }
    
    
    
    private let waitingColor = UIColor.color(hex: "#71BD51")
    private let tagStart = 1000
    private var itemContainer = UIView()
    private let itemContainerParent = UIView()
    private var drawContainer = UIView()
    private var writingContainer : UIView { drawContainer }
    private var values: [String] = []
    let topContainer = UIView()
    let labelTop = AutosizeLabel()
    override func configureLayout(_ view: UIView) {
        clipsToBounds = true
        backgroundColor = .color(hex: "#88D35A")
        itemContainerParent.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 0.8)
        addSubview(itemContainerParent)
        addSubview(drawContainer)
        
        addSubview(topContainer)
        topContainer.snp.makeConstraints{ make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.2)
        }
        topContainer.addSubview(labelTop)
        labelTop.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
        }
     
        itemContainerParent.snp.makeConstraints { make in
            make.top.equalTo(topContainer.snp.bottom)
            make.left.bottom.right.equalToSuperview()
        }
        drawContainer.snp.makeConstraints { make in
            make.top.bottom.equalTo(itemContainer)
            make.width.equalTo(drawContainer.snp.height)
            make.centerX.equalToSuperview()
        }
    }
    
    
    override func createGame() {
        super.createGame()
        
        values = (game.values?.compactMap { $0.value as? String })!
        
        var delay = playSound(name:values[0].split(separator: " ").count == 2 ? "games/tapviet_tu" : "games/tapviet_cau", delay: 0)
        
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
            self.loadNextStep()
        }
    }
    private var letters : [String] = []
    private var letterIndex = 0
    private var svgLetterList : [SVG] = []
    private var step = 0
    private var answer = ""
    private var words : [String] = []
    private var wordIndex = 0
    private var fileToSVG : [String : SVG] = [:]
    private var letterToSVG : [String : SVG] = [:]
    private var svgToView : [SVG : UIView] = [:]
    private var viewToSVG : [UIView : SVG] = [:]
    func loadNextStep(){
        if step >= values.count {
            var delay = 0.5
            if game.path != nil && game.path != nil {
                delay += self.playSound(name:"\(game.path!)/\(answer)", delay: delay)
                delay += 1
            }
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
        step += 1
        //answer = "n2_h_ị nh_ị n2_a v_à_ng b_ô_ng tr_ắ_ng";
        #if DEBUG
        //answer = "oa xin chao"
        #endif
        labelTop.text = answer
        words = answer.split(separator: " ").map{$0.string}
        var svgPaths : [String] = []
        var svgLetters : [String] = []
        for word in words {
            let letters = Array(word).map{String($0)}
            for letter in letters {
                svgPaths.append(letter == letter.lowercased() ? "writing/\(letter)1.svg" : "writing/\(letter.lowercased() )2.svg")
                svgLetters.append(letter)
            }
        }
        itemContainer.removeAllSubviews()
        drawContainer.removeAllSubviews()
        for i in 0..<svgPaths.count {
            let svgPath = svgPaths[i]
            let svg = SVG(image: SVGKImage(contentsOf: Utilities.SVGURL(of: svgPath)))
            fileToSVG[svgPath] = svg
            letterToSVG[svgLetters[i]] = svg
        }
        wordIndex = 0
        loadNextWord()
        if game.text == "spell" || game.text == "spell2" {
            var delay = 0.5
            delay += self.playSound(name:game.text == "spell" ? "vocab/" + answer : answer, delay: delay)
            delay += self.playSound(name:"is spell", delay: delay)
            for i in 0..<answer.count {
                delay += self.playSound(name:answer[i].string + "1", delay: delay)
            }            
        }
    }
    var isNeedShowHintButton: (Bool){false}
    func loadNextWord(){
        if wordIndex >= 1 {
            var delay = 0.0
            for wIndex in 0..<words.count {
                let word = words[wIndex]
            }
            scheduler.schedule(delay: 1+delay) {
                [weak self] in
                guard let self = self else { return }
                self.loadNextStep()
            }
            return
        }
        itemContainer.removeAllSubviews()
        wordIndex += 1
        var delta = 0.0
        svgLetterList = []
        svgToView = [:]
        var firstSVG: SVG?
        for wIndex in 0..<words.count {
            let word = words[wIndex]
            letters = Array(word).map{String($0)}
            var extraDraw : [SVG] = []
            for i in 0..<letters.count {
                let svg = letterToSVG[letters[i]]!.deepClone()
                var extraStroke : SVG?
                var extraSign: SVG?
                var extraDrawTemp: [SVG] = []
                if extraDrawTemp.count > 0 {
                    for temp in extraDrawTemp {
                        extraDraw.append(temp)
                    }
                }
                
                var autosizeView = SVGImageView(frame: CGRect.zero)
                if i==0 {
                    autosizeView.tag = wIndex + tagStart
                }
                
                itemContainer.addSubview(autosizeView)
                autosizeView.contentMode = .scaleAspectFit
                autosizeView.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                
                let ratio = itemContainer.bounds.height / svg.size.height
                if firstSVG == nil {
                    firstSVG = svg
                } else {
                    delta += svg.size.width * ratio
                }
                let tx = delta - svg.size.width / 2 * ratio + firstSVG!.size.width / 2 * ratio
                autosizeView.transform = CGAffineTransformMakeTranslation(tx, 0)
                svgToView[svg] = autosizeView
                viewToSVG[autosizeView] = svg
                setFillLetter(view: autosizeView, write: false)
                svgLetterList.append(svg)
                if letters[i] == "n2" {
                    if i < letters.count - 1 && letters[i + 1] == "h" {
                        delta -= itemContainer.bounds.height / 50
                    } else {
                        delta -= itemContainer.bounds.height / 7
                    }
                } else if letters[i] == "v2" {
                    delta -= itemContainer.bounds.height / 7
                } else if letters[i] == "s2" {
                    delta -= itemContainer.bounds.height / 9
                } else  { delta -= itemContainer.bounds.height / 50 }
            }
            for temp in extraDraw {
                svgLetterList.append(temp)
            }
            delta += itemContainer.bounds.height / 6
        }
        //itemContainer.removeAllSubviews()
        letterIndex = 0
        loadNextLetter()
    }
    func loadNextLetter(){
        if letterIndex > svgLetterList.count - 1 {
            labelTop.text = answer
            labelTop.textColor = .white
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.loadNextWord()
            }
            return
        }
        let svg = svgLetterList[letterIndex]
        letterIndex += 1
        let spannable = NSMutableAttributedString(string: answer)
        spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#FFFFFF"), range: NSRange(location: 0, length: letterIndex-1))
        spannable.addAttribute(.foregroundColor, value: UIColor.color(hex: "#4C9743"), range: NSRange(location: letterIndex-1, length: 1))
        labelTop.textColor = waitingColor
        labelTop.attributedText = spannable
        writingContainer.removeAllSubviews()
        var duraion = 0.3
        let nextView = svgToView[svg]
        if nextView!.tag >= tagStart {
            let wIndex = nextView!.tag - tagStart
            
        }
        if letterIndex == 1 {
            UIView.animate(withDuration: 0.3) {
                self.itemContainer.transform = CGAffineTransformMakeTranslation(0, 0)
            }
        } else {
            let distanceCenterTwoViews = writingContainer.distanceFromCenterToCenter(to: nextView!)
            var tx = self.itemContainer.transform.tx
            tx += distanceCenterTwoViews.x
            duraion = 0.5
            UIView.animate(withDuration: duraion) {
                self.itemContainer.transform = CGAffineTransformMakeTranslation(tx, 0)
            }
            if abs(distanceCenterTwoViews.x) > 0 {
                playSound(name:"slide")
            }
        }
        
        scheduler.schedule(delay: duraion) {
            [weak self] in
            guard let self = self else { return }
            let tapviet = TapVietView()
            tapviet.delegate = self
            self.writingContainer.addSubview(tapviet)
            tapviet.makeViewCenterAndKeep(ratio: Float(svg.image.caLayerTree.bounds.width / svg.image.caLayerTree.bounds.height))
            tapviet.svg = svg
            tapviet.snap = true
            tapviet.setup()
            self.resumeGame()
        }
    }
    func setFillLetter(view:SVGImageView, write:Bool){
        let svg = viewToSVG[view]
        view.image = svg?.uiImage.tinted(withColor: write ? UIColor.white : UIColor.color(hex: "#71BD51"))
    }
    func tapvietOnCompleted(_ tv: TapVietView) {
        pauseGame()
        playSound(name:"\(letters[letterIndex-1])1", delay: 0.3)
        scheduler.schedule(delay: 0.8) {
            [weak self] in
            guard let self = self else { return }
            self.loadNextLetter()
        }
    }
    func tapvietOnStrokeCompleted(_ tv: TapVietView, _ strokeIndex: Int) {
        let svg = svgLetterList[letterIndex-1]
        let nextView = svgToView[svg] as! SVGImageView
        nextView.image = svg.uiImage.tinted(withColor: .white)
        playSound(name:"effects/true2")
        correct += 1
    }
    deinit {
        print(#function, self)
        NotificationCenter.default.removeObserver(self)
    }
    override func getSkills()->[GameSkill]{
        return [.GameWriting]
    }
    var correct = 0
    var move = 0
    override func getScore()->Float{
        return Float(correct) / Float(move)
    }
}
extension UIView {
    func distanceFromCenterToCenter(to other:UIView)->CGPoint{
        let d = self.convert(CGPoint.zero, to: other)
        return CGPoint(x: d.x + self.bounds.width/2 - other.bounds.width/2, y: d.y + self.bounds.height/2 - other.bounds.height/2)
    }
    func distanceFromLeftToLeft(to other:UIView)->CGPoint{
        let d = self.convert(CGPoint.zero, to: other)
        return d
    }
    func nearestView(views:[UIView])->UIView{
        var min = views.min { a, b in
            let pa = distanceFromCenterToCenter(to: a)
            let pb = distanceFromCenterToCenter(to: b)
            return pa.x * pa.x + pa.y * pa.y < pb.x * pb.x + pb.y * pb.y
        }
        return min!
    }
}
class SVG : Hashable{
    static var index = 0
    var id = 0
    static func == (lhs: SVG, rhs: SVG) -> Bool {
        lhs.id == rhs.id
    }
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
        hasher.combine(layers)
        hasher.combine(image)
    }
    
    var image: SVGKImage
    var layers: [Int] = []
    var uiImage : UIImage {
        for i in 0..<image.caLayerTree.sublayers!.count {
            if let layer = image.caLayerTree.sublayers![i] as? CALayer {
                layer.isHidden = !layers.contains(i)
            }
        }
        return image.uiImage
    }
    func uiImage(paths:[Int])->UIImage{
        for i in 0..<image.caLayerTree.sublayers!.count {
            if let layer = image.caLayerTree.sublayers![i] as? CALayer {
                layer.isHidden = !layers.contains(i)
                if !layer.isHidden {
                    for j in 0..<layer.sublayers!.count {
                        layer.sublayers![j].isHidden = !paths.contains(j)
                    }
                }
            }
        }
        return image.uiImage
    }
    var size : CGSize {image.size}
    init(image: SVGKImage) {
        let source = image.source as! SVGKSourceURL
        let URL = source.url
        self.image = SVGKImage(contentsOf: URL)
        self.layers = [Int](0..<image.caLayerTree.sublayers!.count)
        SVG.index += 1
        self.id = SVG.index
    }
    func clone(pathIndex:Int)->SVG{
        let svg = SVG(image: image)
        svg.layers = [self.layers[pathIndex]]
        return svg
    }
    func clone(pathIndexs:[Int])->SVG{
        let svg = SVG(image: image)
        svg.layers = pathIndexs.map{self.layers[$0]}
        return svg
    }
    func deepClone()->SVG{
        let svg = SVG(image: image)
        svg.layers = self.layers.map{$0}
        return svg
    }
    func removePathAt(pathIndex:Int){
        layers.remove(at: pathIndex)
    }
    func getPath(pathIndex:Int)->CAShapeLayer{
        return image.caLayerTree.sublayers![layers[pathIndex]] as! CAShapeLayer
    }    
    func updateFillColor(color: UIColor){
        for i in 0..<layers.count {
            let path = getPath(pathIndex: i)
            path.fillColor = color.cgColor
        }
    }
    func updateColor(color: UIColor){
        for i in 0..<layers.count {
            let path = getPath(pathIndex: i)
            path.fillColor = color.cgColor
            path.strokeColor = color.cgColor
        }
    }
}

