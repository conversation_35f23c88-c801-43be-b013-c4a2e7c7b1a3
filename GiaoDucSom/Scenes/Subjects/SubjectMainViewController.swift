//
//  SubjectMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit
import SnapKit
import SVGKit

class SubjectMainViewController: BaseSubjectViewController {
    
    
    var sections: [SectionData] = []
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupCollectionView()
        setupData()
        collectionView.reloadData()
    }
    
    
    
    // MARK: - Setup CollectionView
    private func setupCollectionView() {
        collectionView.delegate = self
        collectionView.dataSource = self
        collectionView.register(HeaderCell.self, forSupplementaryViewOfKind: UICollectionView.elementKindSectionHeader, withReuseIdentifier: HeaderCell.reuseIdentifier)
        collectionView.register(LyricCell.self, forCellWithReuseIdentifier: LyricCell.reuseIdentifier)
        collectionView.register(TapDocCell.self, forCellWithReuseIdentifier: TapDocCell.reuseIdentifier)
        collectionView.register(TapVietCell.self, forCellWithReuseIdentifier: TapVietCell.reuseIdentifier)
        collectionView.register(TroChoiNgonNguCell.self, forCellWithReuseIdentifier: TroChoiNgonNguCell.reuseIdentifier)
        collectionView.register(PhonicsCell.self, forCellWithReuseIdentifier: PhonicsCell.reuseIdentifier) // Đăng ký PhonicsCell
        collectionView.register(TruyenThoTiemThucCell.self, forCellWithReuseIdentifier: TruyenThoTiemThucCell.reuseIdentifier)
    }
    
    // MARK: - Setup Data (Abstract Method)
    func setupData() {
        fatalError("Subclasses must implement setupData()")
    }
}

struct SectionData {
    var header: String
    var items: [Any]
}

struct LyricItem {
    let title: String
    let imageName: String
}

struct TapDocItem {
    let name: String
    let thumb: String
    let kind: String
    let letters: [String]
    let isFreeToPlay: Bool
}

struct TapVietItem {
    let type: WriteItemType
    let text: String
    let data: String
    let isFreeToPlay: Bool
}

struct TroChoiNgonNguItem {
    let data: String
    let isTapDocGame: Bool
    init(data: String, isTapDocGame: Bool = false) {
        self.data = data
        self.isTapDocGame = isTapDocGame
    }
}
struct TruyenThoTiemThucItem {
    let data: LanguageItem
    let category: String
    init(data: LanguageItem, category: String) {
        self.data = data
        self.category = category
    }
}

enum WriteItemType {
    case write
    case print
    case separator
    case space
    case thai
}

// MARK: - Cells
class LyricCell: UICollectionViewCell {
    static let reuseIdentifier = "LyricCell"
    
    private let imageView: SVGImageView = {
        let imageView = SVGImageView(frame: .zero)
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 10
        return imageView
    }()
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 16, weight: .bold)
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(titleLabel)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(10)
        }
    }
    
    func configure(with item: LyricItem) {
        imageView.SVGName = item.imageName
        titleLabel.text = item.title
    }
}

class TapDocCell: UICollectionViewCell {
    static let reuseIdentifier = "TapDocCell"
    
    private let textLabel: HeightRatioTextView = {
        let label = HeightRatioTextView()
        label.setHeightRatio(0.6)
        label.numberOfLines = 3
        label.lineBreakMode = .byWordWrapping
        label.textAlignment = .left
        label.textColor = .white
        label.font = .Freude(size: 20)
        return label
    }()
    
    private let thumbLabel: HeightRatioTextView = {
        let label = HeightRatioTextView()
        label.setHeightRatio(0.55)
        label.textAlignment = .right
        label.textColor = .init(hex: "#849BFD")
        label.font = .Freude(size: 20)
        return label
    }()
    
    private let backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let checkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(backgroundImageView)
        contentView.addSubview(textLabel)
        contentView.addSubview(thumbLabel)
        contentView.addSubview(checkImageView)
        
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        textLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.right.equalToSuperview().multipliedBy(0.8)
            make.top.equalTo(self.snp.bottom).multipliedBy(0.20)
        }
        
        thumbLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.right.equalToSuperview().multipliedBy(0.93)
        }
        
        checkImageView.snp.makeConstraints { make in
            make.edges.equalTo(backgroundImageView)
        }
    }
    static var ngonngu_list_tapdoc: UIImage? = Utilities.SVGImage(named: "ngonngu_list_tapdoc")
    static var english_list_bg3: UIImage? = Utilities.SVGImage(named: "english_list_bg3")
    static var english_list_check_1: UIImage? = Utilities.SVGImage(named: "english_list_check_1")
    static var english_list_check_2: UIImage? = Utilities.SVGImage(named: "english_list_check_2")
    static var english_list_check_3: UIImage? = Utilities.SVGImage(named: "english_list_check_3")
    
    func configure(with item: TapDocItem) {
        textLabel.text = item.name
        thumbLabel.text = item.thumb
        backgroundImageView.image = item.isFreeToPlay ? TapDocCell.ngonngu_list_tapdoc : TapDocCell.english_list_bg3
        thumbLabel.isHidden = !item.isFreeToPlay
    }
}

class HeaderCell: UICollectionReusableView {
    static let reuseIdentifier = "HeaderCell"
    
    private let titleLabel: UILabel = {
        let label = UILabel()
        label.textAlignment = .center
        label.textColor = .white
        label.font = UIFont.systemFont(ofSize: 18, weight: .bold)
        label.backgroundColor = UIColor(hex: "#74B6FF")
        label.layer.cornerRadius = 10
        label.clipsToBounds = true
        return label
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(titleLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(30)
        }
    }
    
    func configure(with title: String) {
        titleLabel.text = title
    }
}
// MARK: - Cells
class TapVietCell: UICollectionViewCell {
    static let reuseIdentifier = "TapVietCell"
    static let bgImage = Utilities.GetSVGKImage(named: "ngonngu_list_tapviet")
    let svgThumbnailView = SVGKFastImageView(svgkImage: nil)!
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        let bgView = SVGKFastImageView(svgkImage: TapVietCell.bgImage)!
        bgView.isUserInteractionEnabled = true
        contentView.addSubview(bgView)
        bgView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.width.equalTo(bgView.snp.height).multipliedBy(554.0/505.0)
        }
        bgView.addSubview(svgThumbnailView)
        svgThumbnailView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalTo(svgThumbnailView.snp.width)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
        }
    }
    
    func configure(with item: TapVietItem) {
        // Giả định bạn có hình ảnh SVG hoặc PNG cho thumbnail
        svgThumbnailView.image = Utilities.GetSVGKImage(named: "tap viet/thumb/\(item.data).svg")
        alpha = item.isFreeToPlay ? 1.0 : 0.5
    }
}

class TroChoiNgonNguCell: UICollectionViewCell {
    static let reuseIdentifier = "TroChoiNgonNguCell"
    
    private let imageView: SVGKFastImageView = {
        let imageView = SVGKFastImageView(frame: .zero)
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 10
        return imageView
    }()
    
    private let levelMark: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let freeMark: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(levelMark)
        contentView.addSubview(freeMark)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        levelMark.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(5)
            make.width.height.equalTo(20)
        }
        
        freeMark.snp.makeConstraints { make in
            make.bottom.trailing.equalToSuperview().inset(5)
            make.width.height.equalTo(20)
        }
    }
    var item: TroChoiNgonNguItem?
    func configure(with item: TroChoiNgonNguItem) {
        self.item = item
        //imageView.image = Utilities.GetSVGKImage(named: item.data)
        
        imageView.alpha = 0
        DispatchQueue.global(qos: .default).async {
            // Load SVGKImage ở background thread
            let image = Utilities.GetSVGKImage(named: item.data)
            
            // Cập nhật UI trên main thread
            DispatchQueue.main.async {
                [weak self] in
                guard let self = self else { return }
                guard self.item?.data == item.data else { return }
                imageView.image = image // Gán hình ảnh mới
                UIView.animate(withDuration: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    imageView.alpha = 1
                }
            }
        }
         
        // Logic cho levelMark và freeMark cần thêm (dựa vào game level và freetoplay)
        // Ví dụ:
        let isFree = false // Thay bằng logic thực tế
        freeMark.isHidden = !isFree
        //alpha = isFree ? 1.0 : 0.5
        let moduleName = Utils.getAppModuleName()
        let classType = NSClassFromString(moduleName! + "." + item.data) as? BaseFragmentView.Type
        contentView.alpha = classType != nil ? 1 : 0.5
    }
}

class TruyenThoTiemThucCell: UICollectionViewCell {
    static let reuseIdentifier = "TruyenThoTiemThucCell"
    
    private let imageView: SVGKFastImageView = {
        let imageView = SVGKFastImageView(frame: .zero)
        imageView.contentMode = .scaleAspectFit
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 10
        return imageView
    }()
    
    private let levelMark: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let freeMark: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(levelMark)
        contentView.addSubview(freeMark)
        
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        levelMark.snp.makeConstraints { make in
            make.top.trailing.equalToSuperview().inset(5)
            make.width.height.equalTo(20)
        }
        
        freeMark.snp.makeConstraints { make in
            make.bottom.trailing.equalToSuperview().inset(5)
            make.width.height.equalTo(20)
        }
    }
    var item: TruyenThoTiemThucItem?
    var category: String?
    func configure(with item: TruyenThoTiemThucItem) {
        self.item = item
        imageView.alpha = 0
        DispatchQueue.global(qos: .background).async {
            // Load SVGKImage ở background thread
            let category = item.category.lowercased()
            let folder = category == "thơ" ? "tho" :
                category == "đồng dao" ? "dong dao" :
                category == "truyện" ? "truyen" :
                category == "câu đố" ? "cau do" :
                "cau do"
            let url = Utilities.SVGURL(of: "language/\(folder)/\(item.data.name).svg")
            let image = url == nil ? nil : SVGKImage(contentsOf: url) ?? nil
            // Cập nhật UI trên main thread
            DispatchQueue.main.async {
                [weak self] in
                guard let self = self else { return }
                guard self.item?.data.name == item.data.name else { return }
                imageView.image = image // Gán hình ảnh mới
                UIView.animate(withDuration: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    imageView.alpha = 1
                }
            }
        }
        // Logic cho levelMark và freeMark cần thêm (dựa vào game level và freetoplay)
        // Ví dụ:
        let isFree = false // Thay bằng logic thực tế
        freeMark.isHidden = !isFree
        alpha = isFree ? 1.0 : 0.5
        contentView.alpha = alpha
    }
}

// MARK: - CollectionView DataSource & Delegate
extension SubjectMainViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func numberOfSections(in collectionView: UICollectionView) -> Int {
        return sections.count
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return sections[section].items.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let section = sections[indexPath.section]
        
        if let phonicsItem = section.items[indexPath.item] as? PhonicsItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: PhonicsCell.reuseIdentifier, for: indexPath) as! PhonicsCell
            cell.configure(with: phonicsItem)
            return cell
        } else if let lyricItem = section.items[indexPath.item] as? LyricItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: LyricCell.reuseIdentifier, for: indexPath) as! LyricCell
            cell.configure(with: lyricItem)
            return cell
        } else if let tapDocItem = section.items[indexPath.item] as? TapDocItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: TapDocCell.reuseIdentifier, for: indexPath) as! TapDocCell
            cell.configure(with: tapDocItem)
            return cell
        } else if let tapVietItem = section.items[indexPath.item] as? TapVietItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: TapVietCell.reuseIdentifier, for: indexPath) as! TapVietCell
            cell.configure(with: tapVietItem)
            return cell
        } else if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: TroChoiNgonNguCell.reuseIdentifier, for: indexPath) as! TroChoiNgonNguCell
            cell.configure(with: troChoiItem)
            return cell
        } else if let truyentroItem = section.items[indexPath.item] as? TruyenThoTiemThucItem {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: TruyenThoTiemThucCell.reuseIdentifier, for: indexPath) as! TruyenThoTiemThucCell
            cell.configure(with: truyentroItem)
            return cell
        }
        return UICollectionViewCell()
    }
    
    func collectionView(_ collectionView: UICollectionView, viewForSupplementaryElementOfKind kind: String, at indexPath: IndexPath) -> UICollectionReusableView {
        let header = collectionView.dequeueReusableSupplementaryView(ofKind: kind, withReuseIdentifier: HeaderCell.reuseIdentifier, for: indexPath) as! HeaderCell
        header.configure(with: sections[indexPath.section].header)
        return header
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let section = sections[indexPath.section]
        let width = (collectionView.frame.width - 20) / 3 // 3 items per row
        if section.items[indexPath.item] is PhonicsItem {
            return CGSize(width: width, height: width * 0.76)
        } else if section.items[indexPath.item] is LyricItem {
            return CGSize(width: width, height: width * 0.76)
        } else if section.items[indexPath.item] is TapDocItem {
            return CGSize(width: width, height: width * 0.76)
        } else if section.items[indexPath.item] is TapVietItem {
            return CGSize(width: width, height: width * 1.0)
        } else if section.items[indexPath.item] is TroChoiNgonNguItem {
            return CGSize(width: width, height: width * 0.76)
        } else if section.items[indexPath.item] is TruyenThoTiemThucItem {
            return CGSize(width: width, height: width * 0.76)
        }
        return CGSize(width: width, height: width)
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, referenceSizeForHeaderInSection section: Int) -> CGSize {
        return CGSize(width: collectionView.frame.width, height: 50)
    }
    
    // Thêm phương thức này để các class con có thể override
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        // Để trống, để các class con override và xử lý logic riêng
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            runPremiumFeatures({
                [weak self] in
                guard let self = self else { return }
                let vc = SingleGameActivity()
                vc.game = troChoiItem.data
                vc.tapdoc = troChoiItem.isTapDocGame
                vc.phonics = troChoiItem.data.contains("phonics_list")
                self.navigationController?.pushViewController(vc, animated: true)
            }, false)
        }
        if let tapDocItem = section.items[indexPath.item] as? TapDocItem {
            runPremiumFeatures({
                [weak self] in
                guard let self = self else { return }
                let vc = TapDocViewController()
                vc.name = tapDocItem.name
                vc.thumb = tapDocItem.thumb
                vc.kind = tapDocItem.kind
                vc.letters = tapDocItem.letters
                self.navigationController?.pushViewController(vc, animated: true)
            }, false)
        }
        if let lyricItem = section.items[indexPath.item] as? LyricItem {
            runPremiumFeatures({
                [weak self] in
                guard let self = self else { return }
                if lyricItem.title != "Câu đố" {
                    let vc = LanguageListViewController()
                    vc.category = lyricItem.title
                    self.navigationController?.pushViewController(vc, animated: true)
                } else {
                    runPremiumFeatures({
                        let vc = LanguageGameActivity()
                        vc.category = "cau do"
                        self.navigationController?.pushViewController(vc, animated: true)
                    }, false)
                }
                
            }, false)
        }
        if let truyenThoItem = section.items[indexPath.item] as? TruyenThoTiemThucItem {
            runPremiumFeatures({
                let vc = LanguageGameActivity()
                vc.category = truyenThoItem.category
                vc.data = truyenThoItem.data
                self.navigationController?.pushViewController(vc, animated: true)
            }, false)
        }
        if let tapvietItem = section.items[indexPath.item] as? TapVietItem {
            runPremiumFeatures({
                let vc = WritingVNActivity()
                vc.data = tapvietItem.data
                self.navigationController?.pushViewController(vc, animated: true)
            }, false)
        }
    }
}
