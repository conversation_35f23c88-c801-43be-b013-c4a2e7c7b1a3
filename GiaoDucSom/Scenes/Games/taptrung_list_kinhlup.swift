//
//  taptrung_list_kinhlup.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//

class taptrung_list_kinhlup: nhanbiet_list_kinhlup {
    // MARK: - Game Logic
    override func updateData() {
        let folder = FlashcardsManager.shared.getPacks().shuffled().first!
        let items = folder.items.shuffled().prefix(3).map { $0 }
        setFolder(folder.folder)
        setItem(items.randomElement()!)
        setListItems(items)
        super.updateData()
    }
}
