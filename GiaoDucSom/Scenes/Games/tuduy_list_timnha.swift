//
//  tuduy_list_timnha.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_timnha: NhanBietGameFragment {
    // MARK: - Properties
    private let colorNames = ["red", "blue", "green", "yellow", "purple"]
    private let colors = [
        UIColor(red: 255/255, green: 0/255, blue: 0/255, alpha: 1), // #FF0000
        UIColor(red: 0/255, green: 0/255, blue: 255/255, alpha: 1), // #0000FF
        UIColor(red: 0/255, green: 255/255, blue: 0/255, alpha: 1), // #00FF00
        UIColor(red: 255/255, green: 255/255, blue: 0/255, alpha: 1), // #FFFF00
        UIColor(red: 128/255, green: 0/255, blue: 128/255, alpha: 1) // #800080
    ]
    private var animalGridLayout: MyGridView!
    private var houseGridLayout: MyGridView!
    private var items: [Item] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var used: [Bool] = []
    private var colorIndexs: [Int] = []
    private var textHint: UITextView!
    private var correctCount: Int = 0
    private var animals: [String] = []
    private var hints: [String] = []
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        
        houseGridLayout = MyGridView()
        houseGridLayout.backgroundColor = UIColor(red: 227/255, green: 222/255, blue: 255/255, alpha: 1) // #E3DEFF
        view.addSubview(houseGridLayout)
        houseGridLayout.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.6)
        }
        
        animalGridLayout = MyGridView()
        view.addSubview(animalGridLayout)
        animalGridLayout.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.4)
        }
        
        textHint = UITextView()
        textHint.isEditable = false
        textHint.backgroundColor = .clear
        view.addSubview(textHint)
        textHint.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        coinView = UIView() // Giả lập từ item_coin_view.xml
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.height.equalTo(50) // Giả định kích thước
            make.center.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        animalGridLayout.addGestureRecognizer(panGesture)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        animalGridLayout.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        colorIndexs = [0, 1, 2, 3, 4].shuffled()
        items = FlashcardsManager.shared.getPacks().first(where: { $0.folder.lowercased() == "farm animals" })?.items.shuffled().prefix(5).map { $0 } ?? []
        animals = items.compactMap { $0.name.vi }
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = createItemTimNhaTop(item: items[i])
            view.tag = i
            views.append(view)
        }
        animalGridLayout.columns = items.count
        animalGridLayout.itemRatio = 1
        animalGridLayout.itemSpacingRatio = 0.02
        animalGridLayout.insetRatio = 0.02
        let itemViewList = views.shuffled()
        animalGridLayout.reloadItemViews(views: itemViewList)
        
        used = Array(repeating: false, count: items.count)
        hints = generationHint()
        let hintText = animals.map { animal in
            hints.first { $0.starts(with: "item:\(animal)") } ?? ""
        }.joined(separator: "\n")
        // textHint.text = hintText // Tạm ẩn vì XML không hiển thị rõ ràng
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_doan nha")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_doan nha")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = createItemTimNhaBottom(colorIndex: colorIndexs[i])
            view.tag = i
            views.append(view)
        }
        houseGridLayout.columns = items.count
        houseGridLayout.itemRatio = Float(houseGridLayout.frame.width / houseGridLayout.frame.height) / 5
        houseGridLayout.itemSpacingRatio = 0
        houseGridLayout.insetRatio = 0
        houseGridLayout.reloadItemViews(views: views)
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: animalGridLayout)
        currentView = findViewUnder(x: location.x, y: location.y)
        if let currentView = currentView {
            playSound("effect/cungchoi_pick\(random(1,2))")
            dX = currentView.frame.minX - location.x
            dY = currentView.frame.minY - location.y
            currentView.superview?.bringSubviewToFront(currentView)
            
            let index = currentView.tag
            let item = items[index]
            guard let hint = hints.first(where: { $0.starts(with: "item:\(item.name.vi!)") }) else { return }
            let words = hint.split(separator: "|")
            var delay: TimeInterval = 0
            scheduler.clearAll()
            // SoundManager.stopAllScheduled() // Giả lập
            for word in words {
                var sound = String(word)
                if sound.hasPrefix("item:") {
                    let name = sound.dropFirst(5)
                    if let item = items.first(where: { $0.name.vi! == name }) {
                        sound = "topics/Farm Animals/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
                    }
                } else if sound.hasPrefix("color:") {
                    sound = "topics/Colors/\(sound.dropFirst(6))"
                } else if sound.hasPrefix("number:") {
                    sound = "topics/Numbers/\(sound.dropFirst(7))"
                } else {
                    sound = "tuduy/doannha_\(sound)"
                }
                delay += playSound(name: sound, delay: delay)
            }
        }
    }
            
    var originX = 0.0, originY = 0.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: animalGridLayout)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                playSound("effect/cungchoi_pick\(random(1,2))")
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                originX = currentView.frame.minX
                originY = currentView.frame.minY
                currentView.superview?.bringSubviewToFront(currentView)
                
                let index = currentView.tag
                let item = items[index]
                guard let hint = hints.first(where: { $0.starts(with: "item:\(item.name.vi!)") }) else { return }
                let words = hint.split(separator: "|")
                var delay: TimeInterval = 0
                scheduler.clearAll()
                // SoundManager.stopAllScheduled() // Giả lập
                for word in words {
                    var sound = String(word)
                    if sound.hasPrefix("item:") {
                        let name = sound.dropFirst(5)
                        if let item = items.first(where: { $0.name.vi! == name }) {
                            sound = "topics/Farm Animals/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
                        }
                    } else if sound.hasPrefix("color:") {
                        sound = "topics/Colors/\(sound.dropFirst(6))"
                    } else if sound.hasPrefix("number:") {
                        sound = "topics/Numbers/\(sound.dropFirst(7))"
                    } else {
                        sound = "tuduy/doannha_\(sound)"
                    }
                    delay += playSound(name: sound, delay: delay)
                }
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = location.x + dX
                let newY = location.y + dY
                currentView.frame.origin = CGPoint(x: newX, y: newY)
            }
            
        case .ended:
            if let currentView = currentView {
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for child in houseGridLayout.subviews {
                    let dvector = currentView.distanceFromCenterToCenter(to: child)
                    let distance = hypot(dvector.x, dvector.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = child
                    }
                }
                
                if let targetView = targetView, minDistance < targetView.frame.height / 2 {
                    playSound("effect/word puzzle drop")
                    scheduler.clearAll()
                    // SoundManager.stopAllScheduled() // Giả lập
                    
                    let tag = currentView.tag
                    let targetTag = targetView.tag
                    if tag == targetTag {
                        correctCount += 1
                        playSound(correctCount == 5 ? "effect/answer_end" : "effect/answer_correct")
                        if let bgView = currentView.viewWithStringTag("view_background"){
                            UIView.animate(withDuration: 0.2) {
                                bgView.alpha = 0
                            }
                        }
                        if let target = targetView.viewWithStringTag("view_target") {
                            currentView.moveToCenter(of: target, duration: 0.2)
                            if correctCount == 5 {
                                pauseGame()
                                animateCoinIfCorrect(view: coinView)
                                let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
                                scheduler.schedule(delay: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            }
                        }
                    } else {
                        setGameWrong()
                        playSound("effect/slide2")
                        UIView.animate(withDuration: 0.8) {
                            self.currentView?.transform = .identity
                            self.currentView?.frame = CGRectMake(self.originX, self.originY, self.currentView!.frame.width, self.currentView!.frame.height)
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.8) {
                        self.currentView?.transform = .identity
                        self.currentView?.frame = CGRectMake(self.originX, self.originY, self.currentView!.frame.width, self.currentView!.frame.height)
                    }
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<animalGridLayout.subviews.count).reversed() {
            let child = animalGridLayout.subviews[i]
            if x >= child.frame.minX && x <= child.frame.maxX &&
               y >= child.frame.minY && y <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func createItemTimNhaTop(item: Item) -> UIView {
        let view = UIView()
        let container = UIView()
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height) // Ratio 1:1
        }
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        viewBackground.stringTag = "view_background"
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let svgView = UIImageView()
        svgView.image = Utilities.SVGImage(named: "topics/Farm Animals/\(item.path!)")
        container.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(svgView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return view
    }
    
    private func createItemTimNhaBottom(colorIndex: Int) -> UIView {
        let view = UIView()
        let viewBackground = UIView()
        viewBackground.backgroundColor = colors[colorIndex].withAlphaComponent(0.1)
        viewBackground.stringTag = "view_background"
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let container = UIView()
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height).multipliedBy(0.7) // Ratio 0.7:1
        }
        
        let svgView = UIImageView()
        svgView.image = Utilities.SVGImage(named: "tuduy_doannha_\(colorNames[colorIndex].lowercased())")
        container.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(container)
            make.height.equalTo(svgView.snp.width).multipliedBy(326.0 / 518.0) // Ratio 518:326
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().inset(16)
        }
        
        let viewTarget = UIView()
        viewTarget.stringTag = "view_target"
        container.addSubview(viewTarget)
        viewTarget.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(viewTarget.snp.width) // Ratio 1:1
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        return view
    }
    
    private func generationHint() -> [String] {
        used = Array(repeating: false, count: items.count)
        var hints: [String] = []
        
        for _ in 0..<5 {
            while true {
                let pos = getFreePosition()
                let hint = Int.random(in: 1...13)
                var found = false
                
                if hint == 1 {
                    let hangxom = [pos - 2, pos + 2].shuffled()
                    for p in hangxom {
                        let center = (p + pos) / 2
                        if center >= 0 && center < items.count {
                            if p < 0 || p >= items.count || used[p] {
                                found = true
                                used[pos] = true
                                hints.append("item:\(items[pos].name.vi!)|sống bên cạnh nhà có mái|color:\(colorNames[colorIndexs[center]])")
                                break
                            }
                        }
                    }
                } else if hint == 2 {
                    let hangxom = [pos - 1, pos + 1].shuffled()
                    for p in hangxom {
                        if p >= 0 && p < items.count {
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống ngay bên \(p < pos ? "phải" : "trái") nhà có mái|color:\(colorNames[colorIndexs[p]])")
                            break
                        }
                    }
                } else if hint == 3 {
                    let hangxom = [pos - 2, pos + 2].shuffled()
                    for p in hangxom {
                        let center = (p + pos) / 2
                        if center >= 0 && center < items.count && used[center] {
                            if p < 0 || p >= items.count || used[p] {
                                found = true
                                used[pos] = true
                                hints.append("item:\(items[pos].name.vi!)|sống bên cạnh nhà của|item:\(items[center].name.vi!)")
                                break
                            }
                        }
                    }
                } else if hint == 4 {
                    let hangxom = [pos - 1, pos + 1].shuffled()
                    for p in hangxom {
                        if p >= 0 && p < items.count && used[p] {
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống ngay bên \(p < pos ? "phải" : "trái") nhà của|item:\(items[p].name.vi!)")
                            break
                        }
                    }
                } else if hint == 5 {
                    if pos < 4 && used[pos + 1] {
                        let ok = (0..<pos).allSatisfy { used[$0] }
                        if ok {
                            var hangxom: [Int] = []
                            var index = pos + 1
                            while index < items.count && used[index] {
                                hangxom.append(index)
                                index += 1
                            }
                            let p = hangxom.randomElement()!
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống về phía bên trái nhà của|item:\(items[p].name.vi!)")
                            break
                        }
                    }
                    if pos > 1 && used[pos - 1] {
                        let ok = ((pos + 1)..<items.count).allSatisfy { used[$0] }
                        if ok {
                            var hangxom: [Int] = []
                            var index = pos - 1
                            while index >= 0 && used[index] {
                                hangxom.append(index)
                                index -= 1
                            }
                            let p = hangxom.randomElement()!
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống về phía bên phải nhà của|item:\(items[p].name.vi!)")
                            break
                        }
                    }
                } else if hint == 6 {
                    if pos < 4 {
                        let ok = (0..<pos).allSatisfy { used[$0] }
                        if ok {
                            var hangxom: [Int] = []
                            var index = pos + 1
                            while index < items.count {
                                hangxom.append(index)
                                if !used[index] { break }
                                index += 1
                            }
                            let p = hangxom.randomElement()!
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống về phía bên trái nhà có mái|color:\(colorNames[colorIndexs[p]])")
                            break
                        }
                    }
                    if pos > 1 {
                        let ok = ((pos + 1)..<items.count).allSatisfy { used[$0] }
                        if ok {
                            var hangxom: [Int] = []
                            var index = pos - 1
                            while index >= 0 {
                                hangxom.append(index)
                                if !used[index] { break }
                                index -= 1
                            }
                            let p = hangxom.randomElement()!
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|sống về phía bên phải nhà có mái|color:\(colorNames[colorIndexs[p]])")
                            break
                        }
                    }
                } else if hint == 7 {
                    if pos > 0 && pos < 4 && used[pos - 1] && used[pos + 1] {
                        found = true
                        used[pos] = true
                        hints.append("item:\(items[pos].name.vi!)|sống ở giữa nhà của|item:\(items[pos - 1].name.vi!)|và|item:\(items[pos + 1].name.vi!)")
                        break
                    }
                } else if hint == 8 {
                    if pos > 0 && pos < 4 {
                        found = true
                        used[pos] = true
                        hints.append("item:\(items[pos].name.vi!)|sống ở giữa nhà có mái|color:\(colorNames[colorIndexs[pos - 1]])|và|color:\(colorNames[colorIndexs[pos + 1]])")
                        break
                    }
                } else if hint == 9 {
                    let hangxom = [0, 1, 2, 3, 4].shuffled()
                    for p in hangxom {
                        if pos != p - 1 && pos != p + 1 {
                            let count = (0..<used.count).filter { !used[$0] && ($0 != p - 1 && $0 != p + 1) }.count
                            if count == 1 {
                                found = true
                                used[pos] = true
                                hints.append("item:\(items[pos].name.vi!)|không sống cạnh nhà có mái|color:\(colorNames[colorIndexs[p]])")
                                break
                            }
                        }
                    }
                    if found { break }
                } else if hint == 10 {
                    let hangxom = [0, 1, 2, 3, 4].shuffled()
                    for p in hangxom where used[p] && (pos < p - 1 || pos > p + 1) {
                        let count = (0..<used.count).filter { !used[$0] && ($0 < p - 1 || $0 > p + 1) }.count
                        if count == 1 {
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|không sống cạnh nhà của|item:\(items[p].name.vi!)")
                            break
                        }
                    }
                    if found { break }
                } else if hint == 11 {
                    let hangxom = [0, 1, 2, 3, 4].shuffled()
                    for p in hangxom where p != pos && !used[p] {
                        let count = (0..<used.count).filter { !used[$0] && $0 != p }.count
                        if count == 1 {
                            found = true
                            used[pos] = true
                            hints.append("item:\(items[pos].name.vi!)|không sống ở nhà có mái|color:\(colorNames[colorIndexs[p]])")
                            break
                        }
                    }
                    if found { break }
                } else if hint == 12 {
                    found = true
                    used[pos] = true
                    hints.append("item:\(items[pos].name.vi!)|\(Bool.random() ? "sống ở nhà thứ \(pos + 1) từ trái qua" : "sống ở nhà thứ \(5 - pos) từ phải qua")")
                    break
                } else if hint == 13 {
                    found = true
                    used[pos] = true
                    hints.append("item:\(items[pos].name.vi!)|sống ở nhà có mái|color:\(colorNames[colorIndexs[pos]])")
                    break
                }
                if found { break }
            }
        }
        return hints
    }
    
    private func getFreePosition() -> Int {
        while true {
            let i = Int.random(in: 0..<items.count)
            if !used[i] {
                return i
            }
        }
    }
}

