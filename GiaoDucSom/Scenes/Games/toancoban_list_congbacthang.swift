//
//  toancoban_list_congbacthang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit

class toancoban_list_congbacthang: NhanBietGameFragment {
    // MARK: - Properties
    private var textA: HeightRatioTextView!
    private var textB: HeightRatioTextView!
    private var textC: HeightRatioTextView!
    private var textD: HeightRatioTextView!
    private var textE: HeightRatioTextView!
    private var textF: HeightRatioTextView!
    private var textCurrent: HeightRatioTextView?
    private var numpad: MathNumpad!
    private var a: Int = 0
    private var b: Int = 0
    private var c: Int = 0
    private var d: Int = 0
    private var e: Int = 0
    private var f: Int = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#E9FDFF")
        
        let skyView = UIView()
        skyView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(skyView)
        skyView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = false
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let gridContainer = UIView()
        view.addSubview(gridContainer)
        gridContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        let innerGridContainer = UIView()
        gridContainer.addSubview(innerGridContainer)
        innerGridContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
            make.width.equalTo(innerGridContainer.snp.height) // Ratio 1:1
        }
        
        let line1 = UIImageView(image: Utilities.SVGImage(named: "image_congbacthang"))
        innerGridContainer.addSubview(line1)
        let line2Left = UIImageView(image: Utilities.SVGImage(named: "image_congbacthang"))
        innerGridContainer.addSubview(line2Left)
        let line2Right = UIImageView(image: Utilities.SVGImage(named: "image_congbacthang"))
        innerGridContainer.addSubview(line2Right)
        
        let textALayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        innerGridContainer.addSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textALayout.snp.height) // Ratio 1:1
            make.left.equalToSuperview()
        }
        textALayout.waitForLayout {
            textALayout.snapToVerticalBias(verticalBias: 1.0)
        }
        
        textA = HeightRatioTextView()
        textA.text = "?"
        textA.setHeightRatio(0.7)
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        innerGridContainer.addSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textBLayout.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        textBLayout.waitForLayout {
            textBLayout.snapToVerticalBias(verticalBias: 1.0)
        }
        
        textB = HeightRatioTextView()
        textB.text = "?"
        textB.setHeightRatio(0.7)
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textCLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        innerGridContainer.addSubview(textCLayout)
        textCLayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textCLayout.snp.height) // Ratio 1:1
            make.right.equalToSuperview()
        }
        textCLayout.waitForLayout {
            textCLayout.snapToVerticalBias(verticalBias: 1.0)
        }
        
        textC = HeightRatioTextView()
        textC.text = "?"
        textC.setHeightRatio(0.7)
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 20)
        textC.textAlignment = .center
        textC.adjustsFontSizeToFitWidth = true
        textC.minimumScaleFactor = 0.1
        textCLayout.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textDLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        textDLayout.isUserInteractionEnabled = true
        innerGridContainer.addSubview(textDLayout)
        textDLayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textDLayout.snp.height) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(0.52)
        }
        textDLayout.waitForLayout {
            textDLayout.snapToVerticalBias(verticalBias: 0.5)
            textDLayout.snapToHorizontalBias(horizontalBias: 0.26)
        }
        
        textD = HeightRatioTextView()
        textD.text = "?"
        textD.setHeightRatio(0.7)
        textD.textColor = UIColor(hex: "#74B6FF")
        textD.font = .Freude(size: 20)
        textD.textAlignment = .center
        textD.adjustsFontSizeToFitWidth = true
        textD.minimumScaleFactor = 0.1
        textD.isUserInteractionEnabled = true
        textDLayout.addSubview(textD)
        textD.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textD.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextD)))
        
        let textELayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        textELayout.isUserInteractionEnabled = true
        innerGridContainer.addSubview(textELayout)
        textELayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textELayout.snp.height) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(1.48)
        }
        textELayout.waitForLayout {
            textELayout.snapToVerticalBias(verticalBias: 0.5)
            textELayout.snapToHorizontalBias(horizontalBias: 0.74)
        }
        
        textE = HeightRatioTextView()
        textE.text = "?"
        textE.setHeightRatio(0.7)
        textE.textColor = UIColor(hex: "#74B6FF")
        textE.font = .Freude(size: 20)
        textE.textAlignment = .center
        textE.adjustsFontSizeToFitWidth = true
        textE.minimumScaleFactor = 0.1
        textE.isUserInteractionEnabled = true
        textELayout.addSubview(textE)
        textE.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textE.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextE)))
        
        let textFLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        textFLayout.isUserInteractionEnabled = true
        innerGridContainer.addSubview(textFLayout)
        textFLayout.snp.makeConstraints { make in
            make.height.equalTo(innerGridContainer).multipliedBy(0.28)
            make.width.equalTo(textFLayout.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
        }
        textFLayout.waitForLayout {
            textFLayout.snapToVerticalBias(verticalBias: 0.0)
        }
        
        textF = HeightRatioTextView()
        textF.text = "?"
        textF.setHeightRatio(0.7)
        textF.textColor = UIColor(hex: "#74B6FF")
        textF.font = .Freude(size: 20)
        textF.textAlignment = .center
        textF.adjustsFontSizeToFitWidth = true
        textF.minimumScaleFactor = 0.1
        textF.isUserInteractionEnabled = true
        textFLayout.addSubview(textF)
        textF.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textF.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextF)))
        
        
        line1.snp.makeConstraints { make in
            make.left.equalTo(textDLayout.snp.centerX)
            make.right.equalTo(textELayout.snp.centerX)
            make.top.equalTo(textFLayout.snp.bottom)
            make.bottom.equalTo(textDLayout.snp.top)
        }
        
                
        line2Left.snp.makeConstraints { make in
            make.left.equalTo(textALayout.snp.centerX)
            make.right.equalTo(textBLayout.snp.centerX)
            make.top.equalTo(textDLayout.snp.bottom)
            make.bottom.equalTo(textALayout.snp.top)
        }
        
        
        line2Right.snp.makeConstraints { make in
            make.left.equalTo(textBLayout.snp.centerX)
            make.right.equalTo(textCLayout.snp.centerX)
            make.top.equalTo(textELayout.snp.bottom)
            make.bottom.equalTo(textBLayout.snp.top)
        }
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Selection Logic
    @objc private func selectTextD() { select(view: textD) }
    @objc private func selectTextE() { select(view: textE) }
    @objc private func selectTextF() { select(view: textF) }
    
    private func select(view: HeightRatioTextView?) {
        if let current = textCurrent, let parent = current.superview as? UIImageView {
            parent.image = Utilities.SVGImage(named: "math_result_bg")
        }
        if let view = view, let parent = view.superview as? UIImageView {
            numpad.isEnabled = true
            parent.image = Utilities.SVGImage(named: "math_result_bg_focus")
            view.textColor = UIColor(hex: "#74B6FF")
        } else {
            numpad.isEnabled = false
        }
        textCurrent = view
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 1 + Int.random(in: 0..<9)
            b = 1 + Int.random(in: 0..<9)
            c = 1 + Int.random(in: 0..<9)
            if a + b + b + c <= 9 {
                break
            }
        }
        
        textA.text = String(a)
        textB.text = String(b)
        textC.text = String(c)
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_cong bac thang")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_cong bac thang")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func checkFinish() {
        guard let dText = textD.text, !dText.isEmpty,
              let eText = textE.text, !eText.isEmpty,
              let fText = textF.text, !fText.isEmpty else {
            return
        }
        
        let dOK = StringUtils.equalsIgnoreCase(dText, String(a + b))
        let eOK = StringUtils.equalsIgnoreCase(eText, String(b + c))
        let fOK = StringUtils.equalsIgnoreCase(fText, String(a + b + b + c))
        
        if dOK && eOK && fOK {
            coinView.moveToCenter(of: textB, duration: 0)
            animateCoinIfCorrect(view: coinView)
            textD.textColor = UIColor(hex: "#74B6FF")
            textE.textColor = UIColor(hex: "#74B6FF")
            textF.textColor = UIColor(hex: "#74B6FF")
            pauseGame()
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            textD.textColor = UIColor(hex: dOK ? "#74B6FF" : "#FF7760")
            textE.textColor = UIColor(hex: eOK ? "#74B6FF" : "#FF7760")
            textF.textColor = UIColor(hex: fOK ? "#74B6FF" : "#FF7760")
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_congbacthang: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        let adjustedValue = value >= 10 ? value % 10 : value
        textCurrent?.text = String(adjustedValue)
    }
    
    func onDelClick(value: Int) {
        textCurrent?.text = ""
    }
    
    func onCheckClick(value: Int) {
        select(view: nil)
        checkFinish()
    }
}


