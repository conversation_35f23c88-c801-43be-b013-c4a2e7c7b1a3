<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#426697"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/grid_layout"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/top_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#1B385A"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintHeight_percent="0.6"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/boat_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:alpha="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.8"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1">

                <include
                    android:id="@+id/boat1"
                    layout="@layout/item_bongnuoc"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/shadow_view"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/tuduy_bongnuoc_thuyen2"
                    app:layout_constraintDimensionRatio="1.2"
                    app:layout_constraintHeight_percent="0.8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toBottomOf="@+id/boat1"
                    app:layout_constraintVertical_bias="1">

                    <ImageView
                        android:id="@+id/image_lunar2"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:src="@drawable/tuduy_bongnuoc_trang2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="0.4"
                        app:layout_constraintHorizontal_bias="1"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleY="0.7"
            android:transformPivotY="0dp"
            app:layout_constraintHeight_percent="0.48"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toBottomOf="@id/top_view"
            android:layout_marginTop="1dp"
            app:layout_constraintVertical_bias="1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/boat_bottom_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0"
                android:scaleY="-1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <include
                    layout="@layout/item_bongnuoc"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="1" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#649FE4"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>