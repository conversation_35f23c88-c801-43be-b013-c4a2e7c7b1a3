<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#27394B"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="11:5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.05">

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/lights_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toLeftOf="@id/right_view"
            app:layout_constraintTop_toTopOf="parent">


        </com.kidsup.giaoducsom.view.MyGridLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/right_view"
            android:layout_width="wrap_content"
            android:layout_height="0dp"
            android:background="#2B79B9"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.kidsup.giaoducsom.view.MyGridLayout
                android:id="@+id/grid_layout"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/paths_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="6:5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>