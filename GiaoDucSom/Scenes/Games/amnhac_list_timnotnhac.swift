//
//  amnhac_list_timnotnhac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/4/25.
//


import UIKit
import SnapKit
import SVGKit

class amnhac_list_timnotnhac: MusicGameFragment {
    // MARK: - Properties
    private var btnPlay: KUButton!
    private var gridLayout: MyGridView!
    private var meIndex: Int = 0
    private let notes: [Character] = ["C", "D", "E", "F", "G", "A", "B"]
    private let notes2: [Int] = [0, 1, 2, 3, 4, 5, 6]
    private let ids: [Int] = [
        R13.drawable.ic_8c1,
        R13.drawable.ic_8d1,
        R13.drawable.ic_8e1,
        R13.drawable.ic_8f1,
        R13.drawable.ic_8g1,
        R13.drawable.ic_8a1,
        R13.drawable.ic_8b1
    ]
    private var items: [Int] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_cloud"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 4.5)
        
        
        btnPlay = KUButton()
        btnPlay.alpha = 0.01
        btnPlay.setImage(Utilities.SVGImage(named: "music_btn_playback"), for: .normal)
        view.addSubview(btnPlay)
        btnPlay.snp.makeConstraints { make in
            make.width.equalTo(btnPlay.snp.height) // Ratio 1:1
            make.height.equalTo(view).multipliedBy(0.2)
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.btnPlay.snp.makeConstraints { make in
                make.top.right.equalTo(view.frame.height * 0.07)
            }
        }
        btnPlay.addTarget(self, action: #selector(handlePlayTap(_:)), for: .touchUpInside)
        btnPlay.isHidden = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        items = notes2.shuffled().prefix(4).map { $0 }
        meIndex = Int.random(in: 0..<items.count)
        
        let showText = Bool.random()
        var views: [UIView] = []
        for i in 0..<items.count {
            let item = createItemGhepCap(note: notes[items[i]], imageRes: ids[items[i]], showText: showText)
            item.tag = i
            //item.alpha = 0.001
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            item.addGestureRecognizer(tapGesture)
            item.isUserInteractionEnabled = true
            views.append(item)
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.columns = 4
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "music/timnotnhac", "topics/Music Notes/\(notes[items[meIndex]].lowercased())")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/timnotnhac", "topics/Music Notes/\(notes[items[meIndex]].lowercased())")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePlayTap(_ sender: UIButton) {
        pauseGame()
        let delay = playSound("music/timnotnhac", "topics/Music Notes/\(notes[items[meIndex]].lowercased())")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.resumeGame()
        }
    }
    
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard gesture.numberOfTouches == 1 else { return }
        if gameState != .playing { return }
        guard let view = gesture.view,
              let viewText = view.viewWithStringTag("view_text") as? UILabel else { return }
        
        pauseGame()
        let index = view.tag
        var delay = playSound("effect/music/piano_2\(notes[items[index]].lowercased())1")
        if index == meIndex {
            animateCoinIfCorrect(view: view)
            delay += playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names:[ answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemGhepCap(note: Character, imageRes: Int, showText: Bool) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_card1")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let bgWhite = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_card2"))
        bgWhite.isUserInteractionEnabled = true
        container.addSubview(bgWhite)
        bgWhite.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let innerContainer = UIView()
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height).multipliedBy(1.5) // Ratio 1.5:1
            make.width.equalTo(container).multipliedBy(0.95)
            make.center.equalToSuperview()
        }
        
        let viewImage = UIView()
        viewImage.stringTag = "view_image"
        viewImage.isHidden = showText
        innerContainer.addSubview(viewImage)
        viewImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        viewImage.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(viewImage).multipliedBy(0.94)
            make.height.equalTo(viewImage)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg"))
        barline.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg"))
        viewImage.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let imageNote = UIImageView(image: Utilities.SVGImage(named: imageRes.toDrawableNameTNN() != nil ? "music/note/\(imageRes.toDrawableNameTNN()!).svg"  : "empty").withRenderingMode(.alwaysTemplate))
        imageNote.stringTag = "image_note"
        imageNote.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.width.equalTo(imageNote.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(1.3) // Bias 0.65
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg"))
        clef.contentMode = .scaleAspectFill
        clef.tintColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewImage.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(0.35) // Bias 0.15
            make.centerY.equalToSuperview()
        }
        
        let viewText = HeightRatioTextView()
        viewText.setHeightRatio(0.7)
        viewText.stringTag = "view_text"
        viewText.text = String(note)
        viewText.font = .Freude(size: 20)
        viewText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        viewText.textAlignment = .center
        viewText.backgroundColor = .clear
        viewText.isHidden = !showText
        container.addSubview(viewText)
        viewText.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(container).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        return view
    }
}

struct R13 {
    struct drawable {
        static let nhanbiet_bg_cloud = 1
        static let music_btn_playback = 2
        static let nhanbiet_bg_card1 = 3
        static let nhanbiet_bg_card2 = 4
        static let music_bg = 5
        static let ic_barline = 6
        static let endline = 7
        static let ic_8c1 = 8
        static let ic_8d1 = 9
        static let ic_8e1 = 10
        static let ic_8f1 = 11
        static let ic_8g1 = 12
        static let ic_8a1 = 13
        static let ic_8b1 = 14
        static let music_clef = 15
        static let music_timnotnhac = 16
        static let music_item_ghepcap = 17
    }
}

extension Int {
    func toDrawableNameTNN() -> String? {
        switch self {
        case R13.drawable.nhanbiet_bg_cloud:
            return "nhanbiet_bg_cloud"
        case R13.drawable.music_btn_playback:
            return "music_btn_playback"
        case R13.drawable.nhanbiet_bg_card1:
            return "nhanbiet_bg_card1"
        case R13.drawable.nhanbiet_bg_card2:
            return "nhanbiet_bg_card2"
        case R13.drawable.music_bg:
            return "music_bg"
        case R13.drawable.ic_barline:
            return "ic_barline"
        case R13.drawable.endline:
            return "endline"
        case R13.drawable.ic_8c1:
            return "8c1"
        case R13.drawable.ic_8d1:
            return "8d1"
        case R13.drawable.ic_8e1:
            return "8e1"
        case R13.drawable.ic_8f1:
            return "8f1"
        case R13.drawable.ic_8g1:
            return "8g1"
        case R13.drawable.ic_8a1:
            return "8a1"
        case R13.drawable.ic_8b1:
            return "8b1"
        case R13.drawable.music_clef:
            return "music_clef"
        case R13.drawable.music_timnotnhac:
            return "music_timnotnhac"
        case R13.drawable.music_item_ghepcap:
            return "music_item_ghepcap"
        default:
            return "empty"
        }
    }
}
