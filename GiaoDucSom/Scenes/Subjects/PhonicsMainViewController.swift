//
//  PhonicsMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//
import UIKit

class PhonicsMainViewController: SubjectMainViewController {
    private var chapters: [GroupLesson] = []
    private var selectedLesson: Lesson?
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Phonics")
        titleColor = UIColor(hex: "#849BFD")
        backgroundColor = UIColor(hex: "#F1E2FF")
        initializePhonicsManager()
    }
    
    override var backgroundMusicFile: String? {
        return "bg_list9"
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if let selectedLesson = selectedLesson {
            // Cập nhật trạng thái hiển thị của bài học đã chọn
            for section in sections {
                for (index, item) in section.items.enumerated() {
                    if let phonicsItem = item as? PhonicsItem, phonicsItem.lesson == selectedLesson {
                        /*
                        if let indexPath = collectionView.indexPath(for: item) {
                            collectionView.reloadItems(at: [indexPath])
                        }
                         */
                        break
                    }
                }
            }
            // Xóa trạng thái bài học đã chọn
            self.selectedLesson = nil
        }
    }
    
    private func initializePhonicsManager() {
        // Giả lập PhonicsManager để lấy dữ liệu chapters
        // Trong thực tế, bạn cần implement PhonicsManager để lấy dữ liệu thực
        PhonicsManager.shared.initialize()
        chapters = PhonicsManager.shared.getChapters()
        setupData()
    }
    
    override func setupData() {
        var tempSections: [SectionData] = []
        
        // Tạo sections cho các chapters và lessons
        for (chapterIndex, chapter) in chapters.enumerated() {
            let lessons = chapter.lessons.map { lesson -> PhonicsItem in
                let lessonIndex = chapter.lessons.firstIndex(where: { $0.getId() == lesson.getId() })!
                let trialLesson = chapterIndex == 0 && lessonIndex <= 1
                return PhonicsItem(lesson: lesson, trialLesson: trialLesson)
            }
            let section = SectionData(header: chapter.name, items: lessons)
            tempSections.append(section)
        }
        
        // Section: Phonic Games
        let phonicGamesSection = SectionData(
            header: "Phonic Games",
            items: [
                TroChoiNgonNguItem(data: "phonics_list_balloon"),
                TroChoiNgonNguItem(data: "phonics_list_bee"),
                TroChoiNgonNguItem(data: "phonics_list_crab"),
                TroChoiNgonNguItem(data: "phonics_list_chantbeat1"),
                TroChoiNgonNguItem(data: "phonics_list_stickers"),
                TroChoiNgonNguItem(data: "phonics_list_spelltheword"),
                TroChoiNgonNguItem(data: "phonics_list_wordpuzzle"),
                TroChoiNgonNguItem(data: "phonics_list_unscramble"),
                TroChoiNgonNguItem(data: "phonics_list_readtheword"),
                TroChoiNgonNguItem(data: "phonics_list_memory"),
                TroChoiNgonNguItem(data: "phonics_list_listenandcheck"),
                TroChoiNgonNguItem(data: "phonics_list_chant"),
                TroChoiNgonNguItem(data: "phonics_list_readandcheck"),
                TroChoiNgonNguItem(data: "phonics_list_read"),
                TroChoiNgonNguItem(data: "phonics_list_whatletteristhis"),
                TroChoiNgonNguItem(data: "phonics_list_basketball"),
                TroChoiNgonNguItem(data: "phonics_list_letterandsound"),
                TroChoiNgonNguItem(data: "phonics_list_samebeginningsound"),
                TroChoiNgonNguItem(data: "phonics_list_beginningsound"),
                TroChoiNgonNguItem(data: "phonics_list_whatsoundisthis"),
                TroChoiNgonNguItem(data: "phonics_list_missingletter"),
                TroChoiNgonNguItem(data: "phonics_list_canyouhear"),
                TroChoiNgonNguItem(data: "phonics_list_listenfor1"),
                TroChoiNgonNguItem(data: "phonics_list_listenfor2"),
                TroChoiNgonNguItem(data: "phonics_list_listening"),
                TroChoiNgonNguItem(data: "phonics_list_reading2"),
                TroChoiNgonNguItem(data: "phonics_list_rhyme"),
                TroChoiNgonNguItem(data: "phonics_list_samerhyme"),
                TroChoiNgonNguItem(data: "phonics_list_writing")
            ]
        )
        
        // Add all sections
        sections = [phonicGamesSection] + tempSections + [phonicGamesSection]
    }
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let phonicsItem = section.items[indexPath.item] as? PhonicsItem {
            let lesson = phonicsItem.lesson
            let chapterIndex = chapters.firstIndex { chapter in
                chapter.lessons.contains { $0 == lesson }
            } ?? 0
            let lessonIndex = chapters[chapterIndex].lessons.firstIndex(of: lesson) ?? 0
            
            // Mở PhonicsViewController
            let phonicsVC = PhonicsActivity()
            phonicsVC.chapterIndex = chapterIndex
            phonicsVC.lessonIndex = lessonIndex
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let playable = (phonicsItem.trialLesson && inTrialTime) || PaymentHelper.isFullVersion()
            if playable {
                navigationController?.pushViewController(phonicsVC, animated: true)
                selectedLesson = lesson
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this lesson")
            }
        } else if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            super.collectionView(collectionView, didSelectItemAt: indexPath)
            /*
            // Mở SingleGameViewController với game tương ứng
            let singleGameVC = SingleGameViewController()
            singleGameVC.game = getGameByThumb(troChoiItem.data)
            singleGameVC.isPhonics = true
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = checkFreeGameByThumbnail(troChoiItem.data)
            let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
            if playable {
                navigationController?.pushViewController(singleGameVC, animated: true)
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this game")
            }*/
        }
    }

    
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự PhonicsGameNameHelper.getGameByThumb
        return thumbnail
    }
}

// MARK: - PhonicsItem
struct PhonicsItem {
    let lesson: Lesson
    let trialLesson: Bool
}



// MARK: - PhonicsCell
class PhonicsCell: UICollectionViewCell {
    static let reuseIdentifier = "PhonicsCell"
    private let textLabel: UILabel = {
        let label = UILabel()
        label.numberOfLines = 3
        label.lineBreakMode = .byWordWrapping
        label.textAlignment = .left
        label.textColor = .white
        label.font = .Freude(size: 20)
        return label
    }()
    
    private let thumbLabel: HeightRatioTextView = {
        let label = HeightRatioTextView()
        label.setHeightRatio(0.55)
        label.textAlignment = .right
        label.textColor = .init(hex: "#849BFD")
        label.font = .Freude(size: 20)
        return label
    }()
    
    private let backgroundImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private let checkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.addSubview(backgroundImageView)
        contentView.addSubview(textLabel)
        contentView.addSubview(thumbLabel)
        contentView.addSubview(checkImageView)
        
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        textLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.7)
            //make.height.equalToSuperview().multipliedBy(0.5)
            make.right.equalToSuperview().multipliedBy(0.75)
            make.top.equalTo(self.snp.bottom).multipliedBy(0.25)
        }
        
        thumbLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.bottom.equalToSuperview().multipliedBy(0.9)
            make.right.equalToSuperview().multipliedBy(0.93)
        }
        
        checkImageView.snp.makeConstraints { make in
            make.edges.equalTo(backgroundImageView)
        }
    }
    static var english_list_bg1: UIImage? = Utilities.SVGImage(named: "english_list_bg1")
    static var english_list_bg3: UIImage? = Utilities.SVGImage(named: "english_list_bg3")
    static var english_list_check_1: UIImage? = Utilities.SVGImage(named: "english_list_check_1")
    static var english_list_check_2: UIImage? = Utilities.SVGImage(named: "english_list_check_2")
    static var english_list_check_3: UIImage? = Utilities.SVGImage(named: "english_list_check_3")
    func configure(with item: PhonicsItem) {
        let lesson = item.lesson
        let lessonComplete = PhonicsManager.shared.isLessonComplete(lesson: lesson)
        var counted = PhonicsManager.shared.countLessonComplete(lesson: lesson)
        #if DEBUG
        //counted = Int64(arc4random_uniform(4))
        #endif
        let inTrialTime = (ActivityTracker.getCurrentViewController() as! BaseViewController).installedTimeInDays() < 2
        let playable = (item.trialLesson && inTrialTime) || PaymentHelper.isFullVersion()
        
        backgroundImageView.image = playable ? PhonicsCell.english_list_bg1 : PhonicsCell.english_list_bg3
        checkImageView.image = counted == 0 ? nil : counted == 1 ? PhonicsCell.english_list_check_1 : counted == 2 ? PhonicsCell.english_list_check_2 : PhonicsCell.english_list_check_3
        textLabel.text = removeLastDigitIfExists(lesson.name)
        thumbLabel.text = lesson.thumb
        thumbLabel.setHeightRatio(lesson.thumb?.lowercased().contains("blends") == true ? 0.34 : 0.55)
        
        thumbLabel.isHidden = !playable
    }
    
    private func removeLastDigitIfExists(_ text: String) -> String {
        guard !text.isEmpty, text.last?.isNumber == true else { return text }
        return String(text.dropLast())
    }
    override func layoutSubviews() {
        //textLabel.backgroundColor = .green.withAlphaComponent(0.4)
        textLabel.font = .Freude(size: self.bounds.height * 0.12)
    }
}

// MARK: - PaymentHelper (Giả lập)
class PaymentHelper {
    static func isFullVersion() -> Bool {
        // Trong thực tế, bạn cần implement logic để kiểm tra trạng thái premium
        return true
    }
}

