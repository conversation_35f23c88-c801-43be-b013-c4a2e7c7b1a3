<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="737:559"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintDimensionRatio="737:559"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/background_image"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/music_list_giaidieu" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:background="#0f00"
                android:id="@+id/text_name"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="bottom|right"
                android:text="Cả nhà thương nhau"
                android:textColor="#71BD51"
                app:heightRatio="0.2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.8"
                app:layout_constraintWidth_percent="0.8" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>