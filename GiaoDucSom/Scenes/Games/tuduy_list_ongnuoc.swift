//
//  tuduy_list_ongnuoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 10/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_ongnuoc: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private let generator = PipeNetworkGenerator()
    private var viewStart: UIView!
    private var viewEnd: UIImageView!
    private var viewWater: UIView!
    private var imageWater: UIImageView!
    private var waterDirection: String = "right"
    private var waterX: Int = -1
    private var waterY: Int = -1
    private var foundWater: Bool = false
    private var watersvg: SVGKImage?
    private var startButton: UIImageView!
    private var waterOpen: Bool = false
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 8.0 / 6.0)
        
        let subContainer = UIView()
        subContainer.backgroundColor = .white
        itemContainer.addSubview(subContainer)
        subContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.75)
            make.height.equalTo(itemContainer).multipliedBy(0.667)
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        subContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        viewStart = UIView()
        itemContainer.addSubview(viewStart)
        viewStart.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.16666)
            make.width.equalTo(viewStart.snp.height) // Ratio 1:1
            make.left.equalToSuperview()
        }
        
        let startInnerContainer = UIView()
        startInnerContainer.transform = CGAffineTransform(scaleX: 10, y: 1)
        viewStart.addSubview(startInnerContainer)
        startInnerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let pipe4 = UIImageView()
        pipe4.image = Utilities.SVGImage(named: "tuduy_pipe_4")
        startInnerContainer.addSubview(pipe4)
        pipe4.snp.makeConstraints { make in
            make.width.equalTo(startInnerContainer).multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        let pipe3 = UIImageView()
        pipe3.image = Utilities.SVGImage(named: "tuduy_pipe_3")
        viewStart.addSubview(pipe3)
        pipe3.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        startButton = UIImageView()
        startButton.image = Utilities.SVGImage(named: "tuduy_pipe_6")
        viewStart.addSubview(startButton)
        startButton.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleStartTap(_:)))
        startButton.addGestureRecognizer(tapGesture)
        startButton.isUserInteractionEnabled = true
        
        viewEnd = UIImageView(image: Utilities.SVGImage(named: "tuduy_pipe_5"))
        viewEnd.contentMode = .scaleAspectFit
        itemContainer.addSubview(viewEnd)
        viewEnd.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.16666)
            make.width.equalTo(viewEnd.snp.height) // Ratio 1:1
            make.right.equalToSuperview()
        }
        
        viewWater = UIView()
        viewWater.isUserInteractionEnabled = false
        itemContainer.addSubview(viewWater)
        viewWater.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.16666)
            make.width.equalTo(viewWater.snp.height) // Ratio 1:1
        }
        
        imageWater = UIImageView()
        imageWater.backgroundColor = .red.withAlphaComponent(0.5)
        imageWater.alpha = waterOpen ? 1 : 0
        imageWater.contentMode = .scaleAspectFit
        viewWater.addSubview(imageWater)
        imageWater.snp.makeConstraints { make in
            make.width.equalTo(viewWater).multipliedBy(0.7)
            make.width.equalTo(imageWater.snp.height).multipliedBy(1011.0 / 1401.0) // Ratio 1011:1401
            make.center.equalToSuperview()
        }
        
        addActionOnLayoutSubviews {
            self.viewStart.snapToVerticalBias(verticalBias: 0.2 * (1.0 + CGFloat(self.generator.startPoint[1])))
            self.viewEnd.snapToVerticalBias(verticalBias: 0.2 * (1.0 + CGFloat(self.generator.endPoint[1])))
            self.viewWater.snapToHorizontalBias(horizontalBias: 0.14285714285 * (CGFloat(self.waterX) + 1.0))
            self.viewWater.snapToVerticalBias(verticalBias: 0.2 * (CGFloat(self.waterY) + 1.0))
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        generator.generate()
        waterY = generator.startPoint[1]       
        
        let pipes = generator.pipes
        let grid = generator.grid
        var views: [UIView] = []
        for i in 0..<grid.count {
            for j in 0..<grid[i].count {
                let svgAutosizeView = UIImageView()
                let inAnswer = grid[i][j]
                var leftArrow = generator.getArrowName(x: j - 1, y: i)
                var rightArrow = generator.getArrowName(x: j + 1, y: i)
                var topArrow = generator.getArrowName(x: j, y: i - 1)
                var bottomArrow = generator.getArrowName(x: j, y: i + 1)
                var arrow = generator.getArrowName(x: j, y: i)
                
                if j == 0 && i == generator.startPoint[1] {
                    leftArrow = "right"
                }
                if j == grid[i].count - 1 && i == generator.endPoint[1] {
                    rightArrow = "right"
                }
                
                if inAnswer {
                    if leftArrow == "right" {
                        if arrow == "right" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_thang")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_thang
                        } else if arrow == "down" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                        } else if arrow == "up" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi / 2)
                        }
                    } else if rightArrow == "left" {
                        if arrow == "left" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_thang")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_thang
                        } else if arrow == "down" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: -.pi / 2)
                        } else if arrow == "up" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi)
                        }
                    } else if topArrow == "down" {
                        if arrow == "down" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_thang")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_thang
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi / 2)
                        } else if arrow == "right" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi)
                        } else if arrow == "left" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi / 2)
                        }
                    } else if bottomArrow == "up" {
                        if arrow == "up" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_thang")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_thang
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: .pi / 2)
                        } else if arrow == "right" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                            svgAutosizeView.transform = CGAffineTransform(rotationAngle: -.pi / 2)
                        } else if arrow == "left" {
                            svgAutosizeView.image = Utilities.SVGImage(named: "tuduy_pipe_di_xuong")
                            svgAutosizeView.tag = R8.drawable.tuduy_pipe_di_xuong
                        }
                    }
                } else {
                    let id = Bool.random() ? R8.drawable.tuduy_pipe_thang : R8.drawable.tuduy_pipe_di_xuong
                    svgAutosizeView.image = Utilities.SVGImage(named: id == R8.drawable.tuduy_pipe_thang ? "tuduy_pipe_thang" : "tuduy_pipe_di_xuong")
                    svgAutosizeView.tag = id
                    svgAutosizeView.transform = CGAffineTransform(rotationAngle: CGFloat(random(0, 1, 2, 3)) * .pi / 2)
                }
                svgAutosizeView.transform = CGAffineTransform(rotationAngle: CGFloat(random(0, 1, 2, 3)) * .pi / 2)
                views.append(svgAutosizeView)
                
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleCellTap(_:)))
                svgAutosizeView.addGestureRecognizer(tapGesture)
                svgAutosizeView.isUserInteractionEnabled = true
            }
        }
        gridLayout.columns = grid[0].count
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0
        gridLayout.insetRatio = 0
        gridLayout.reloadItemViews(views: views)
        updateWater()
        let delay = playSound(openGameSound(), "tuduy/ong nuoc")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
            self?.updateWaterFrame(frame: 0)
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/ong nuoc")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        watersvg = Utilities.GetSVGKImage(named: "animations/pipe_water.svg")
    }
    
    override func BackgroundMusicName() -> String? {
        return waterOpen ? "bg_water_stream" : nil
    }
    
    // MARK: - Touch Handling
    @objc private func handleStartTap(_ gesture: UITapGestureRecognizer) {
        playSound("effect/pipe_lock")
        if waterOpen {
            closeWater()
        } else {
            openWater()
            scheduler.schedule(after: 0.6) { [weak self] in
                self?.updateWater()
            }
        }
    }
    
    @objc private func handleCellTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        playSound("effect/pipe_spin")
        let rotation = view.transform.rotationAngle()
        let roundedRotation = round(rotation / (.pi / 2)) * (.pi / 2)
        UIView.animate(withDuration: 0.1, delay: 0, options: .curveLinear) {
            view.transform = CGAffineTransform(rotationAngle: roundedRotation + .pi / 2)
        } completion: { _ in
            self.updateWater()
        }
    }
    
    // MARK: - Water Logic
    private func openWater() {
        waterOpen = true
        UIView.animate(withDuration: 0.6) {
            self.startButton.transform = CGAffineTransform(rotationAngle: -.pi / 2)
        }
        playSound("effect/pipe_lock")
        startBackgroundMusic()
        scheduler.schedule(after: 0.6) {
            self.imageWater.alpha = 1
        }
    }
    
    private func closeWater() {
        waterOpen = false
        UIView.animate(withDuration: 0.6) {
            self.startButton.transform = CGAffineTransform(rotationAngle: .pi / 2)
        }
        playSound("effect/pipe_lock")
        stopBackgroundMusic()
        scheduler.schedule(after: 0.6) {
            self.imageWater.alpha = 0
        }
    }
    
    private func viewAt(x: Int, y: Int) -> UIImageView? {
        let index = y * generator.GRID_WIDTH + x
        return gridLayout.subviews[index] as? UIImageView
    }
    
    private func angleAt(x: Int, y: Int) -> Int {
        guard let view = viewAt(x: x, y: y) else { return 0 }
        let rotation = view.transform.rotationAngle()
        let value = (rotation + 6 * .pi) / (.pi / 2)
        return Int(value.rounded()) % 4 * 90
    }
    
    private func waterAt(x: Int, y: Int) -> [String] {
        if x < 0 || x >= generator.GRID_WIDTH || y < 0 || y >= generator.GRID_HEIGHT {
            return []
        }
        guard let view = viewAt(x: x, y: y) else { return [] }
        let id = view.tag
        let angle = angleAt(x: x, y: y)
        
        if id == R8.drawable.tuduy_pipe_thang {
            return angle % 180 == 0 ? ["right", "left"] : ["up", "down"]
        } else {
            switch angle % 360 {
            case 0: return ["left", "down"]
            case 90: return ["left", "up"]
            case 180: return ["right", "up"]
            default: return ["right", "down"]
            }
        }
    }
    
    private func findWaterPosition(x: Int, y: Int) {
        var nextX = x
        var nextY = y
        var waters = waterAt(x: x, y: y)
        if waters.isEmpty {
            waterX = x
            waterY = y
            foundWater = true
            if waterX == -1 { waterDirection = "left" }
            if waterX == generator.GRID_WIDTH { waterDirection = "right" }
            if waterY == -1 { waterDirection = "up" }
            if waterY == generator.GRID_HEIGHT { waterDirection = "down" }
            return
        }
        
        if x == waterX + 1 && y == waterY {
            if waters.contains("left") {
                waters.removeAll { $0 == "left" }
                if waters.contains("right") {
                    nextX = x + 1
                } else if waters.contains("up") {
                    nextY = y - 1
                } else if waters.contains("down") {
                    nextY = y + 1
                } else {
                    return
                }
                waterX = x
                waterY = y
                findWaterPosition(x: nextX, y: nextY)
            } else {
                waterX = x
                waterY = y
                foundWater = true
                waterDirection = "right"
            }
        }
        if foundWater { return }
        
        if x == waterX - 1 && y == waterY {
            if waters.contains("right") {
                waters.removeAll { $0 == "right" }
                if waters.contains("left") {
                    nextX = x - 1
                } else if waters.contains("up") {
                    nextY = y - 1
                } else if waters.contains("down") {
                    nextY = y + 1
                } else {
                    return
                }
                waterX = x
                waterY = y
                findWaterPosition(x: nextX, y: nextY)
            } else {
                waterX = x
                waterY = y
                foundWater = true
                waterDirection = "left"
            }
        }
        if foundWater { return }
        
        if x == waterX && y == waterY + 1 {
            if waters.contains("up") {
                waters.removeAll { $0 == "up" }
                if waters.contains("left") {
                    nextX = x - 1
                } else if waters.contains("right") {
                    nextX = x + 1
                } else if waters.contains("down") {
                    nextY = y + 1
                } else {
                    return
                }
                waterX = x
                waterY = y
                findWaterPosition(x: nextX, y: nextY)
            } else {
                waterX = x
                waterY = y
                foundWater = true
                waterDirection = "down"
            }
        }
        if foundWater { return }
        
        if x == waterX && y == waterY - 1 {
            if waters.contains("down") {
                waters.removeAll { $0 == "down" }
                if waters.contains("left") {
                    nextX = x - 1
                } else if waters.contains("right") {
                    nextX = x + 1
                } else if waters.contains("up") {
                    nextY = y - 1
                } else {
                    return
                }
                waterX = x
                waterY = y
                findWaterPosition(x: nextX, y: nextY)
            } else {
                waterX = x
                waterY = y
                foundWater = true
                waterDirection = "up"
            }
        }
    }
    
    private func updateWaterFrame(frame: Int) {
        guard let watersvg = watersvg else { return
        }
        /*
        var nextFrame = frame + 1
        nextFrame %= watersvg.groups.count
        imageWater.image = watersvg.groups[nextFrame].uiImage
        scheduler.schedule(after: 0.2) { [weak self] in
            self?.updateWaterFrame(frame: nextFrame)
        }*/
    }
    
    func updateWater() {
        waterX = -1
        waterY = generator.startPoint[1]
        foundWater = false
        findWaterPosition(x: generator.startPoint[0], y: generator.startPoint[1])
        
        viewWater.snapToHorizontalBias(horizontalBias: 0.14285714285 * (CGFloat(waterX) + 1))
        viewWater.snapToVerticalBias(verticalBias: 0.2 * (CGFloat(waterY) + 1))
        viewWater.transform = CGAffineTransform(rotationAngle: waterDirection == "right" ? 0 : waterDirection == "down" ? .pi / 2 : waterDirection == "left" ? .pi : 3 * .pi / 2)
        
        let endPoint = generator.endPoint
        if waterX == endPoint[0] + 1 && waterY == endPoint[1] {
            if waterOpen {
                pauseGame()
                animateCoinIfCorrect(view: viewEnd)
                startBackgroundMusic()
                let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                playSound("tuduy/ong nuoc2")
            }
        }
    }
}

// MARK: - Supporting Structures
class PipeNetworkGenerator {
    var foundAnswer: Bool = false
    var GRID_WIDTH = 6
    var GRID_HEIGHT = 4
    private var MIN_PIPES = 15
    public var grid: [[Bool]] = []
    public var pipes: [[Int]] = []
    public var startPoint: [Int] = [0, Int.random(in: 0..<4)]
    public var endPoint: [Int] = [5, Int.random(in: 0..<4)]
    
    func generate(cols: Int = 6, rows: Int = 4, steps: Int = 15) {
        GRID_HEIGHT = rows
        GRID_WIDTH = cols
        MIN_PIPES = steps
        resetGrid()
        generatePipes(x: startPoint[0], y: startPoint[1], pipeCount: 0)
    }
    
    private func generatePipes(x: Int, y: Int, pipeCount: Int) {
        if pipeCount >= MIN_PIPES {
            return
        }
        
        grid[y][x] = true
        if x == endPoint[0] && y == endPoint[1] {
            foundAnswer = true
            return
        }
        
        let directions = [3, 0, 1, 2].shuffled() // Phải, lên, xuống, trái
        var found = false
        for dir in directions {
            var newX = x
            var newY = y
            switch dir {
            case 0: newY -= 1
            case 1: newY += 1
            case 2: newX -= 1
            case 3: newX += 1
            default: break
            }
            
            if isValid(x: newX, y: newY) {
                found = true
                let pipe = [x, y, newX, newY]
                pipes.append(pipe)
                generatePipes(x: newX, y: newY, pipeCount: pipeCount + 1)
                if foundAnswer { return }
                pipes.removeLast()
            }
        }
        grid[y][x] = false
    }
    
    private func isValid(x: Int, y: Int) -> Bool {
        return x >= 0 && x < GRID_WIDTH && y >= 0 && y < GRID_HEIGHT && !grid[y][x]
    }
    
    func isValidIndex(x: Int, y: Int) -> Bool {
        return x >= 0 && x < GRID_WIDTH && y >= 0 && y < GRID_HEIGHT
    }
    
    private func resetGrid() {
        foundAnswer = false
        grid = Array(repeating: Array(repeating: false, count: GRID_WIDTH), count: GRID_HEIGHT)
        pipes = []
        startPoint = [0, Int.random(in: 0..<GRID_HEIGHT)]
        endPoint = [GRID_WIDTH - 1, Int.random(in: 0..<GRID_HEIGHT)]
    }
    
    private func getArrow(x: Int, y: Int) -> String {
        if x == endPoint[0] && y == endPoint[1] { return "→" }
        for pipe in pipes {
            if pipe[0] == x && pipe[1] == y {
                if pipe[2] == x + 1 { return "→" }
                if pipe[2] == x - 1 { return "←" }
                if pipe[3] == y + 1 { return "↓" }
                if pipe[3] == y - 1 { return "↑" }
            }
        }
        return "?"
    }
    
    func getArrowName(x: Int, y: Int) -> String {
        let arrow = getArrow(x: x, y: y)
        switch arrow {
        case "→": return "right"
        case "←": return "left"
        case "↓": return "down"
        case "↑": return "up"
        default: return ""
        }
    }
}

extension CGAffineTransform {
    func rotationAngle() -> CGFloat {
        return atan2(b, a)
    }
    
    func rotationAngleInDegress() -> CGFloat {
        return rotationAngle() * 180 / .pi
    }
}

struct R8 {
    struct drawable {
        static let tuduy_pipe_thang = 1
        static let tuduy_pipe_di_xuong = 2
    }
}
