//
//  phonics_list_chantbeat1.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 1/5/25.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AVFAudio

class phonics_list_chantbeat1 : GameFragment {
    var textName = AutosizeLabel()
    var svgView = SVGImageView(frame: CGRectZero)
    var svgView2 = SVGImageView(frame: CGRectZero)
    var values: [String] = []
    var words: [CsvWord] = []
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = .color(hex: "#F5FAFE")
        let container = UIView()
        self.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 0.5)
        container.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview().inset(20)
            make.height.equalToSuperview().multipliedBy(0.25)
        }
        textName.textColor = .color(hex: "#FF7761")
        textName.text = "Text"
        textName.isHidden = true
        container.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(textName.snp.bottom)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        container.addSubview(svgView2)
        svgView2.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(svgView.snp.bottom)
        }
        svgView2.contentMode = .scaleAspectFit
        svgView2.transform = CGAffineTransformMakeScale(2, 2)
    }
    
    var startTime: Date = Date()
    var backgroundName: String = ""
    override func createGame() {
        super.createGame()
        values = (data!.values?.compactMap { $0.value as? String })!
        textName.isHidden = false
        self.svgView.SVGName = "flashcards/\(self.data!.level!)/\(self.values[0]).svg"
        self.svgView2.SVGName = "flashcards/\(self.data!.level!)/\(self.values[0])2.svg"
        backgroundName = "en/english phonics/effects/chant_beat1_\(values.count)"
        SoundPreloadManager.shared.loadSound(named: backgroundName)
        for value in values {
            SoundPreloadManager.shared.loadSound(named: "en/english phonics/chant/\(value)")
        }
        SoundPreloadManager.shared.loadSound(named: "en/english phonics/chant/\(self.data!.value1 ?? "\(self.values[0][0])")")
        textName.text = data!.value1 ?? "\(values[0][0])"
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            SoundPreloadManager.shared.playSound(named: backgroundName)
            startTime = Date()
            scheduler.schedule(delay: 4) {
                [weak self] in
                guard let self = self else { return }
                var delta = Date().timeIntervalSince(startTime)
                var delta2 = SoundPreloadManager.shared.getCurrentTime(named:  backgroundName)
                var delta3 = 2
            }
            for i in 0..<values.count {
                let finalI = i
                playItem(delay: 0, index: i)
            }
            self.updateTime()
            scheduler.schedule(delay: SoundPreloadManager.shared.getDuration(named: backgroundName)) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
        }
        words = parse("effects/chant_beat1_\(values.count)")
        var soundIndex = 0
    }
    func updateTime(){
        var pos = SoundPreloadManager.shared.getCurrentTime(named: backgroundName)
        if self.gameState != .finished {
            //print(pos)
            scheduler.schedule(delay: 0.05) {
                [weak self] in
                guard let self = self else { return }
                self.updateTime()
            }
        }
    }
    func playItem(delay: Double, index: Int){
        let IMAGE_SOON_TIME = 0.9
        scheduler.schedule(delay: delay + words[index*8].Start - 0.1 - IMAGE_SOON_TIME) {
            [weak self] in
            guard let self = self else { return }
            self.textName.isHidden = false
            self.svgView.SVGName = "flashcards/\(self.data!.level!)/\(self.values[index]).svg"
            self.svgView2.SVGName = "flashcards/\(self.data!.level!)/\(self.values[index])2.svg"
            self.scheduler.schedule(delay: 0.001 + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.values[index])")
                zoomItem()
            }
            self.scheduler.schedule(delay: words[index*8+1].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let delta = Date().timeIntervalSince(startTime)
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+1].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.data!.value1 ?? "\(self.values[0][0])")")
                zoomLetter()
            }
            self.scheduler.schedule(delay: words[index*8+2].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+2].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.data!.value1 ?? "\(self.values[0][0])")")
                zoomLetter()
            }
            self.scheduler.schedule(delay: words[index*8+3].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+3].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named:  "en/english phonics/chant/\(self.values[index])")
                zoomItem()
            }
            self.scheduler.schedule(delay: words[index*8+4].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+4].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named:  "en/english phonics/chant/\(self.values[index])")
                zoomItem()
            }
            self.scheduler.schedule(delay: words[index*8+5].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+5].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.game.value1 ?? "\(self.values[0][0])")")
                zoomLetter()
            }
            self.scheduler.schedule(delay: words[index*8+6].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+6].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.game.value1 ?? "\(self.values[0][0])")")
                zoomLetter()
            }
            self.scheduler.schedule(delay: words[index*8+7].Start-words[index*8].Start + IMAGE_SOON_TIME) {
                [weak self] in
                guard let self = self else { return }
                #if DEBUG
                let pos = SoundPreloadManager.shared.getCurrentTime(named: self.backgroundName)
                let pos2 = words[index*8+7].Start
                print("pos: \(pos)  \(pos2)")
                #endif
                SoundPreloadManager.shared.playSound(named: "en/english phonics/chant/\(self.values[index])")
                zoomItem()
            }
        }
    }
    func zoomItem(){
        UIView.animate(withDuration: 0.1) {
            self.svgView2.transform = CGAffineTransformMakeScale(2.2, 2.2)
        } completion: { ok in
            self.scheduler.schedule(delay: 0.2) {
                [weak self] in
                guard let self = self else { return }
                UIView.animate(withDuration: 0.1) {
                    self.svgView2.transform = CGAffineTransformMakeScale(2.0, 2.0)
                }
            }
        }
    }
    func zoomLetter(){
        UIView.animate(withDuration: 0.05) {
            self.textName.transform = CGAffineTransformMakeScale(1.1, 1.1)
        } completion: { _ in
            UIView.animate(withDuration: 0.05) {
                self.textName.transform = CGAffineTransformMakeScale(1.0, 1.0)
            }
        }
    }
    func printTime(index:Int){
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "yyyy-MM-dd HH:mm:ss.SSS" // Customize the date format as needed
        let currentTime = dateFormatter.string(from: Date())
        print("Current time is \(index): \(currentTime)")
    }
    func readText( _ name: String) ->String?{
        if let fileURL = Bundle.main.url(forResource: "Sounds/en/english phonics/\(name)", withExtension: "csv") {
            do {
                let text = try String(contentsOf: fileURL, encoding: .utf8)
                return text
            } catch {
                // Handle error if reading the file fails
                print("Error reading file:", error.localizedDescription)
            }
        } else {
            // Handle the case when the file is not found
            print("Resource file not found.")
        }
        return nil
    }
    func parse(_ name: String)->[CsvWord] {
        var words = [CsvWord]() // Assuming Word is a custom struct or class representing the data structure for a word
        
        if let text = readText(name) {
            var fps = 1000.0
            
            if text.contains("59.94 fps") {
                fps = 60.0
            }
            
            if text.contains("30 fps") {
                fps = 30.0
            }
            
            let lines = text.components(separatedBy: "\n")
            for line in lines {
                let trimmedLine = line.trimmingCharacters(in: .whitespacesAndNewlines)
                let w = trimmedLine.split(separator: "\t").map { String($0) }
                
                if w.count >= 3 && w[1] != "Start" {
                    var word = CsvWord()
                    word.Text = w[0]
                    word.Start = parse(w[1], fps)
                    word.Duration = parse(w[2], fps)
                    words.append(word)
                }
            }
        }
        return words
    }
    func parse(_ text: String, _ fps: Double) -> Double {
        let texts = text.components(separatedBy: CharacterSet(charactersIn: ":."))
        if texts.count == 3 {
            let minutes = Double(texts[0])!
            let seconds = Double(texts[1])!
            let miliseconds = Double(texts[2])!
            return minutes * 60.0 + seconds + miliseconds / 1000.0
        }
        guard texts.count >= 2,
              let minutes = Int(texts[texts.count - 2]),
              let seconds = Int(texts[texts.count - 1]) else {
            // Return a default value or handle the error case as needed
            return 0.0
        }
        
        return Double(minutes + seconds) / fps
    }
    deinit{
        SoundPreloadManager.shared.stopAll()
    }
}
