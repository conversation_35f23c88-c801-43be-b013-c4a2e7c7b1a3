//
//  toancoban_list_xephang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/4/25.
//


import UIKit
import SnapKit

class toancoban_list_xephang: NhanBietGameFragment {
    // MARK: - Properties
    private var gridBottomLayout: UIView!
    private var gridBottom2Layout: UIView!
    private var listViews2: [UIView] = []
    private var originListViews: [UIView] = []
    private var move: Int = 0
    private var zIndex: Int = 10
    var busy = false
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 245/255, green: 255/255, blue: 184/255, alpha: 1) // #F5FFB8
        
        let groundView = UIView()
        groundView.backgroundColor = UIColor(red: 185/255, green: 162/255, blue: 69/255, alpha: 1) // #B9A245
        addSubview(groundView)
        groundView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let marginLayout = UIView()
        addSubviewWithPercentInset(subview: marginLayout, percentInset: 5)
        gridBottomLayout = UIView()
        gridBottomLayout.alpha = 0.01
        marginLayout.addSubview(gridBottomLayout)
        gridBottomLayout.makeViewCenterAndKeep(ratio: 2.3)
        
        let biases = [0.0, 0.25, 0.5, 0.75, 1.0]
        for i in 0..<5 {
            let cell = UIView()
            gridBottomLayout.addSubview(cell)
            cell.snp.makeConstraints { make in
                make.width.equalTo(cell.snp.height).multipliedBy(240.0 / 400.0) // Ratio 240:400
                make.height.equalToSuperview()
                make.top.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                cell.snapToHorizontalBias(horizontalBias: biases[i])
            }
            
            let image = UIImageView()
            image.contentMode = .scaleAspectFit
            cell.addSubview(image)
            image.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
        
        let margin2Layout = UIView()
        addSubviewWithPercentInset(subview: margin2Layout, percentInset: 5)
        gridBottom2Layout = UIView()
        margin2Layout.addSubview(gridBottom2Layout)
        gridBottom2Layout.makeViewCenterAndKeep(ratio: 2.3)
        for i in 0..<5 {
            let cell = UIView()
            gridBottom2Layout.addSubview(cell)
            cell.snp.makeConstraints { make in
                make.width.equalTo(cell.snp.height).multipliedBy(240.0 / 400.0) // Ratio 240:400
                make.height.equalToSuperview()
                make.top.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                cell.snapToHorizontalBias(horizontalBias: biases[i])
            }
            
            let image = SVGImageView(frame: .zero)
            image.contentMode = .scaleAspectFit
            cell.addSubview(image)
            image.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "toan/toan_xep hang cao thap")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_xep hang cao thap")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        
        let answer = "12345"
        let chars = Array(answer)
        originListViews = gridBottomLayout.subviews
        for i in 0..<originListViews.count {
            originListViews[i].tag = Int(String(chars[i]))!
        }
        
        var randomIndexes: [Int]
        while true {
            randomIndexes = Utils.generatePermutation(answer.count)
            let foundAnswer = zip(chars, randomIndexes).contains { $0 != chars[$1] }
            if foundAnswer { break }
        }
        
        let ids = ["toan_xephang1", "toan_xephang2", "toan_xephang3", "toan_xephang4", "toan_xephang5"]
        listViews2 = gridBottom2Layout.subviews
        for i in 0..<listViews2.count {
            listViews2[i].tag = Int(String(chars[randomIndexes[i]]))!
            if let imageView = listViews2[i].subviews.first as? SVGImageView {
                imageView.SVGName = ids[randomIndexes[i]]
            }
            
            let pan = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
            listViews2[i].addGestureRecognizer(pan)
            listViews2[i].isUserInteractionEnabled = true
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        var dX: CGFloat = 0
        var dY: CGFloat = 0
        var busy = false
        
        let translation = gesture.translation(in: view.superview)
        
        switch gesture.state {
        case .began:
            dX = view.frame.minX - translation.x
            dY = view.frame.minY - translation.y
            view.layer.zPosition = CGFloat(zIndex)
            zIndex += 1
            //Utils.vibrate(context: self)
            
        case .changed:
            
            let translation = gesture.translation(in: self)
            gesture.setTranslation(CGPointZero, in: self)
            var transform = view.transform
            transform.tx += translation.x
            transform.ty += translation.y
            view.transform = transform
            
            if !busy {
                let oldIndex = listViews2.firstIndex(of: view)!
                for i in 0..<listViews2.count where listViews2[i] != view {
                    let view2 = listViews2[i]
                    let distance = view.distanceFromCenterToCenter(to: view2)
                    if hypot(distance.x, distance.y) < view.frame.width * 0.4 {
                        playSound("effect/bubble\(1 + random.nextInt(bound: 4))")
                        //Utils.vibrate(context: self)
                        busy = true
                        scheduler.schedule(delay: 0.3) { [weak self] in
                            self?.busy = false
                        }
                        
                        let index = i
                        let delta = index > oldIndex ? 1 : -1
                        var start = oldIndex
                        while start != index {
                            let viewTMP = listViews2[start]
                            listViews2[start] = listViews2[start + delta]
                            listViews2[start + delta] = viewTMP
                            start += delta
                        }
                        for j in 0..<listViews2.count where listViews2[j] != view {
                            UIView.animate(withDuration: 0.2) {
                                self.listViews2[j].frame = self.originListViews[j].frame
                            }
                        }
                        break
                    }
                }
            }
            
        case .ended, .cancelled:
            let index = listViews2.firstIndex(of: view)!
            UIView.animate(withDuration: 0.2) {
                view.frame = self.originListViews[index].frame
            }
            move += 1
            checkFinish()
            
        default:
            break
        }
        
        for v in listViews2 {
            zIndex += 1
            v.layer.zPosition = CGFloat(zIndex)
        }
    }
    
    private func checkFinish() {
        let finish = listViews2.enumerated().allSatisfy { $0.element.tag == originListViews[$0.offset].tag }
        if finish {
            animateCoinIfCorrect(view: gridBottom2Layout)
            pauseGame()
            let delay = 1.0 + playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.finishGame()
                }
            }
        }
    }
}

