//
//  phonics_list_writing.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

final class phonics_list_writing : GameFragment, TapVietViewDelegate {
    private var values : [String] = []
    var step = 0
    
    func tapvietDragBegin(_ tv: TapVietView) {
        
    }
    
    func tapvietDragEnd(_ tv: TapVietView) {
        move += 1
    }
    
    func tapvietOnCompleted(_ tv: TapVietView) {
        playSound(name: "true")
        drawCompleted = true
        pauseGame()
        var delay = 0.5
        delay += self.playSound(delay: delay, names: [answer.hasSuffix("2") ? "this is capital" : "this is lowercase", answer.replacingOccurrences(of: "2", with: "1")])
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
        }
    }
    func tapvietOnStrokeCompleted(_ tv: TapVietView, _ strokeIndex: Int) {
        correct += 1
        self.strokeIndex = strokeIndex
        playSound(name: "effects/true2")
    }
    var strokeIndex = 0
    var answer : String = ""
    var count = 0
    var drawCompleted = false
    private var backImage : SVGImageView = SVGImageView(frame: CGRect.zero)
    private var svgBackground : SVG?
    private var topView : UIView!
    private var bottomContainer : UIView!
    private var writeContainer : UIView!
    
   
    
    func loadViews(){
        self.removeAllSubviews()
        backImage = SVGImageView(frame: CGRect.zero)
        topView = UIView()
        bottomContainer = UIView()
        writeContainer = UIView()
        addSubview(topView)
        var img = SVGKImage(contentsOf: Utilities.SVGURL(of: "writing/\(answer).svg"))!
        backgroundColor = .color(hex: "#88D35A")
        addSubview(bottomContainer)
        bottomContainer.addSubview(backImage)
        let ratio = Float(img.size.width/img.size.height)
        backImage.makeViewCenterAndKeep(ratio: ratio)
       
        bottomContainer.addSubview(writeContainer)
        writeContainer.makeViewCenterAndKeep(ratio: ratio)
        
        topView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(100)
        }
        bottomContainer.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
            make.top.equalTo(topView.snp.bottom)
        }
        self.svg = SVG(image: img)
    }
    var isNeedShowHintButton: (Bool){false}
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        loadNextStep()
        startGame()        
    }
    var svg : SVG?
    func loadNextStep(){
        resumeGame()
        if step >= values.count {
            var delay = 0.5
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            delay += 0.5
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            return
        }
        answer = values[step]
        step += 1
        var delay = 0.5
        delay += self.playSound(delay: delay, names: [answer.hasSuffix("2") ? "write a capital" : "write a lowercase"])
        delay += 0.3
        delay += self.playSound(delay: delay, names: [answer.replacingOccurrences(of: "2", with: "1")])
        loadViews()
        writeContainer.removeAllSubviews()
        let tapviet = TapVietView()
        writeContainer.addSubviewWithInset(subview: tapviet, inset: 0)
        tapviet.delegate = self
        
        svgBackground = svg!.deepClone()
        tapviet.svg = svg!.clone(pathIndexs: [Int](0..<svg!.layers.count))
        tapviet.hideDotHint = true
        tapviet.hideLineHint = true
        tapviet.snap = true
        tapviet.setup()
        strokeIndex = 0
        scheduler.schedule(delay: 0.1) {
            [weak self] in
            guard let self = self else { return }
            self.drawCompleted = false
        }
    }
    deinit {
        print(#function, self)
        NotificationCenter.default.removeObserver(self)
    }
    override func getSkills()->[GameSkill]{
        return [.GameWriting]
    }
    var correct = 0
    var move = 0
    override func getScore()->Float{
        return Float(correct) / Float(move)
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay: TimeInterval = 0.5
            delay += self.playSound(delay: delay, names: [answer.hasSuffix("2") ? "write a capital" : "write a lowercase"])
            delay += 0.3
            delay += self.playSound(delay: delay, names: [answer.replacingOccurrences(of: "2", with: "1")])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}

