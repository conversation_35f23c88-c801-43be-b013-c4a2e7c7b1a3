//
//  BaseFragmentView.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/3/25.
//


import UIKit
import SnapKit

public class BaseFragmentView: UIView {
    // MARK: - Properties
    private var logTag: String {
        return String(describing: Self.self)
    }
    // MARK: - GameFragment conformance
    static func create(frame: CGRect) -> Self {
        let instance = Self.init(frame: frame)
        return instance
    }
    private var language: String = "vi"
    open func getLanguage() -> String {
        return language
    }
    @discardableResult
    open func setLanguage(_ language: String) -> BaseFragmentView {
        self.language = language
        return self
    }
    var scheduler = Scheduler()
    var score: Float = 0
    weak var onGameFragmentListener: OnGameFragmentListener?
    private var containerLayoutWidth: CGFloat = 0
    private var containerLayoutHeight: CGFloat = 0
    private var soundPlayerKeyList: [String] = []
    let random = Random()
    var gameState: GameState = .waiting
    private var backgroundMusicName: String?
    private var scores: [Float] = []
    private var busy = false
    private var backgroundMusicId: String?
    private var bgMusic: String?
    private var answerCorrect = true
    private var overlayView: UIView?
    private var gameCreated = false
    private var startTime: Int64 = 0
    var contentView: UIView?

    // MARK: - Initialization
    required override init(frame: CGRect) {
        super.init(frame: frame)
        setupBase()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupBase()
    }
    
    var skipView: KUButton?
    private func setupBase() {
        contentView = UIView()
        addSubview(contentView!)
        contentView!.snp.makeConstraints { make in
            make.edges.equalTo(safeAreaLayoutGuide)
        }
        scheduler = Scheduler()
        soundPlayerKeyList = []
        gameCreated = true
        StopwatchUtils.getManager().start()
        startTime = Int64(StopwatchUtils.getManager().getElapsedTime())
        scheduler.schedule(delay: 5) { [weak self] in
            guard let self = self else { return }
            StopwatchUtils.getManager().stop()
            let time = Int64(StopwatchUtils.getManager().getElapsedTime())
            let skipView = KUButton(type: .custom)
            skipView.setImage(Utilities.SVGImage(named: "btn_skip_top"), for: .normal)
            skipView.addTarget(self, action: #selector(skipTapped), for: .touchUpInside)
            self.addSubview(skipView)
            skipView.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.1)
                make.width.equalTo(skipView.snp.height)
                make.top.equalToSuperview { $0.snp.bottom }.multipliedBy(0.05)
            }
            skipView.snpRightTop(ratio: 1)
            self.skipView = skipView
        }
        layoutTimer.duration = 0.1
        layoutTimer.onActived = { [weak self] in            
            guard let self = self else { return }
            handleContainerLayout()
        }
#if DEBUG
        scheduler.schedule(delay: 120) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
#endif
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            self.removeAllLayoutActions()
        }
    }

    // MARK: - Lifecycle
    public override func didMoveToSuperview() {
        super.didMoveToSuperview()
        guard let superview = superview else { return }
        configureLayout(contentView!)
        updateData()
        setupOverlay()
    }

    var layoutTimer = TimeoutTimer()
    public override func layoutSubviews() {
        super.layoutSubviews()
        layoutTimer.schedule()
    }

    // MARK: - Setup
    private func setupOverlay() {
        overlayView = UIView()
        overlayView?.backgroundColor = .blue
        overlayView?.alpha = 0.1
        overlayView?.isUserInteractionEnabled = true // Chặn tương tác
        self.addSubview(self.overlayView ?? UIView())
        self.overlayView?.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        self.overlayView?.isHidden = gameState == .playing
    }

    func configureLayout(_ view: UIView) {
        
    }

    func updateData() {
        // Để trống, cho subclass override nếu cần
    }

    func createGame() {
        // Để trống, cho subclass override nếu cần
    }

    // MARK: - Game Control
    func startGame() {
        setGameState(.playing)
        startBackgroundMusic()
    }

    func pauseGame(stopMusic: Bool = true) {
        setGameState(.paused)
        if stopMusic {
            stopBackgroundMusic()
        }
    }

    func resumeGame(startMusic: Bool = true) {
        setGameState(.playing)
        if startMusic {
            startBackgroundMusic()
        }
    }

    func finishGame() {
        self.clipsToBounds = true
        self.layer.masksToBounds = true
        setGameState(.finished)
        stopBackgroundMusic()
        GameHelper.shared.logGameComplete(className: String(describing: Self.self))
        let sum = scores.reduce(0, +)
        score = scores.isEmpty ? 1 : sum / Float(scores.count)
        let stopTime = Int64(StopwatchUtils.getManager().getElapsedTime())
        let gameDuration = max(stopTime - startTime, 0)
        ReportManager.shared.logGameFinish(game: self.className, score: score, gameDuration: gameDuration)
        if score > 0.5 {
            GameUnlockUtils.shared.logGameFinish(className: self.className, success: true)
        } else {
            GameUnlockUtils.shared.logGameFinish(className: self.className, success: false)
        }
        let gameLevelUp = GameUnlockUtils.shared.getGameLevelUp(className: self.className)
        onGameFragmentListener?.onGameFinish(self)
        // Xử lý ReviewHelper nếu cần trong Activity
    }

    func setGameState(_ state: GameState) {
        self.gameState = state
        isUserInteractionEnabled = state == .playing
        overlayView?.isHidden = state == .playing
        self.bringSubviewToFront(overlayView!)
    }

    // MARK: - Sound
    
    @discardableResult func playSound(_ names: [String]) -> TimeInterval {
        playSound(delay: 0, names: names)
    }
    
    @discardableResult func playSound(_ names: String...) -> TimeInterval {
        playSound(delay: 0, names: names)
    }

    @discardableResult func playSound(delay: TimeInterval, names: [String]) -> TimeInterval {
        var d = delay
        for name in names where !name.isEmpty {
            d += playSound(name: name, delay: d)
        }
        return d - delay
    }
    @discardableResult func playSound(name: String) -> TimeInterval{
        return playSound(name: name, delay: 0)
    }
    @discardableResult func playSound(name: String, delay: TimeInterval) -> TimeInterval {
        var newName = name
        if(!(newName.starts(with: "effect/") || newName.starts(with: "effects/") || !newName.contains("/") || newName.starts(with: "vi/") || newName.starts(with: "en/"))){
            newName = getLanguage() + "/" + newName
        }
        /*
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            let soundKey = SoundManager.shared.play(name)
            self.soundPlayerKeyList.append(soundKey)
        }
        let duration = soundDuration(name: name)
        #if DEBUG
        return duration == 0 ? 2 : duration
        #else
        return duration
        #endif
         */
        scheduler.schedule(delay: delay) { [weak self] in
            guard self != nil else { return }
            let id : String = QueueSoundPlayer.shared.play(sound: newName, delay: 0)
            self?.soundPlayerKeyList.append(id)
        }
        return QueueSoundPlayer.shared.calcDuration(sound: newName)
    }

    func soundDuration(name: String) -> TimeInterval {
        let filepath = "vi/\(name).mp3" // Giả định ngôn ngữ mặc định là "vi"
        var duration = QueueSoundPlayer.shared.play(sound: filepath, delay: 0, play: false)
        if duration <= 0 {
            duration =  QueueSoundPlayer.shared.play(sound: "\(name).mp3", delay: 0, play: false)
        }
        return TimeInterval(duration / 1000) // Chuyển từ ms sang seconds
    }

    func startBackgroundMusic() {
        stopBackgroundMusic()
        backgroundMusicName = BackgroundMusicName()
        if let music = backgroundMusicName, AppSettings.musicOn {
            backgroundMusicId = QueueSoundPlayer.shared.play(sound: music, delay: 0)
        }
    }

    func stopBackgroundMusic() {
        if let id = backgroundMusicId {
            QueueSoundPlayer.shared.stop(id: id)
            backgroundMusicId = nil
        }
    }

    func BackgroundMusicName() -> String? {
        bgMusic = bgMusic ?? "bg_lesson\(random.nextInt(bound: 3) + 1)"
        return bgMusic
    }

    // MARK: - Helpers
    func inflate(resource: String) -> UIView {
        // Giả định bạn có cách load view từ resource (Storyboard/XIB)
        // Đây là placeholder, cần triển khai thực tế
        return UIView()
    }

    @objc private func skipTapped() {
        playSound("vi/popup_quit_game")
        let dialog = MessageDialogView()
        dialog.setTitle("Bỏ qua trò này")
            .setMessage("Bé có chắc chắn muốn bỏ qua câu này không?")
            .setButtonOkText("Học tiếp")
            .setButtonLaterText("Thoát")
            .setSvgLogoPath("btn_skip_top")
            .setSvgLogoColor(.red)
            .setShowLaterButton(true)
            .setShowOkButton(true)
            .setListener(onConfirm: {
                [weak self] in
                guard let self = self else { return }
                self.skipView!.isEnabled = false
                self.scheduler.clearAll()
                QueueSoundPlayer.shared.clearAll()
                self.pauseGame()
                let delay = self.playSound("effect/end game2") ?? 0
                self.scheduler.schedule(delay: TimeInterval(delay + 0.2)) {
                    [weak self] in
                    guard let self = self else { return }
                    self.skip()
                }
            }, onClose: { buttonIndex in
                switch buttonIndex {
                case .ok: print("OK tapped")
                case .confirm: print("Later tapped")
                case .close: print("Close tapped")
                }
            })

        dialog.showIn(ActivityTracker.getCurrentViewController()?.view ?? self)
    }

    func skip() {
        setGameWrong()
        animateCoinIfCorrect(view: self)
        finishGame()
    }

    func setGameWrong() {
        answerCorrect = false
    }

    func isGameCorrected() -> Bool {
        return answerCorrect
    }

    func animateCoinIfCorrect(view: UIView) {
        let gameCorrected = isGameCorrected()
        if gameCorrected {
            if let viewController = self.parentViewController {
                // For long lessons, don't limit coin rewards
                var gameReward = !(viewController is SingleGameActivity)
                               && !(viewController is LanguageActivity)
                               //&& !(viewController is WritingVNViewController)
                
                if !gameReward {
                    // Otherwise, each game rewards coins max 3 times by gameName
                    var gameName = self.className
                    /*
                    if let writingGame = self as? WritingOlyGameViewController {
                        gameName += "_\(writingGame.getLetter())"
                    }
                    
                    if let languageGame = self as? LanguageFillGameViewController {
                        if let data = languageGame.getData() {
                            gameName += "_\(data.name)"
                        }
                    }
                    */
                    gameReward = CoinGameRewardUtils.shared.gameReward(gameName: gameName)
                }
                
                if gameReward && !(self is phonics_list_intro) {
                    CoinAnimationUtils.shared.removeList()
                    CoinAnimationUtils.shared.animate(view: view, answer: true)
                }
            }
        }
        
        scores.append(gameCorrected ? 1.0 : 0.0)
        answerCorrect = true
    }
    func animateCoinIfCorrect(view: UIView?) {
        let gameCorrected = isGameCorrected()
        if gameCorrected {
            // Xử lý coin animation dựa trên logic Activity
            // Giả định dùng CoinAnimationUtils tương tự AnimateCoin
            view!.animateCoin(answer: true)
        }
        scores.append(gameCorrected ? 1 : 0)
        answerCorrect = true
    }

    private func handleContainerLayout() {
        let container = self
        let width = container.bounds.width
        let height = container.bounds.height
        if width <= 0 || height <= 0 { return }
        if width == containerLayoutWidth && height == containerLayoutHeight { return }
        containerLayoutWidth = width
        containerLayoutHeight = height
        if gameCreated {
            gameCreated = false
            createGame()
        } else {
            updateLayout()
        }
        for action in layoutActions {
            action()
        }
    }

    func updateLayout() {
        #if DEBUG
        print("\(logTag) - width: \(bounds.width ?? 0); height: \(bounds.height ?? 0)")
        #endif
    }

    // MARK: - Deinit
    deinit {
        scheduler.clearAll()
        QueueSoundPlayer.shared.stop(ids: soundPlayerKeyList)
        stopBackgroundMusic()
        QueueSoundPlayer.shared.clearAll()
    }
    open func replayIntroSound(){
        
    }
    public override func hitTest(_ point: CGPoint, with event: UIEvent?) -> UIView? {
        if let overlay = overlayView, !overlay.isHidden, overlay.frame.contains(point) {
            return overlay // Trả về overlayView để chặn sự kiện
        }
        return super.hitTest(point, with: event) // Để mặc định cho các trường hợp khác
    }
    
    private var layoutActions: [Action] = []
        
    func addActionOnLayoutSubviews(action: @escaping Action) {
        layoutActions.append(action)
    }
    
    func removeAllLayoutActions() {
        layoutActions.removeAll()
    }
    func isRunningOnIphone() -> Bool {
        return UIDevice.current.userInterfaceIdiom == .phone
    }

    func isRunningOnIpad() -> Bool {
        return UIDevice.current.userInterfaceIdiom == .pad
    }
}

// MARK: - Game State
enum GameState {
    case waiting, playing, paused, finished
}

// MARK: - Listener Protocol
protocol OnGameFragmentListener : AnyObject {
    func onGameFinish(_ gameFragment: BaseFragmentView)
    func onUpdateGameProgress(_ progress: Float)
}

// MARK: - Placeholder Classes (Giả định)


class GameHelper {
    static let shared = GameHelper()
    func logGameComplete(className: String) {}
    func gameCompletedByGameId(id:String)->Bool{
        return false
    }
}

class GameUnlockUtils {
    static let shared = GameUnlockUtils()
    func logGameFinish(className: String, success: Bool) {}
    func getGameLevelUp(className: String) -> Int { return 0 }
}


class Random {
    func nextInt(bound: Int) -> Int { return Int.random(in: 0..<bound) }
}

