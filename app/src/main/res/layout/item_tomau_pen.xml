<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"

        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_fill"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/bg_tomautheoso_1"
            app:tint="#ff0" />

        <ImageView
            android:id="@+id/image_stroke"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:src="@drawable/bg_tomautheoso_2"
            app:tint="#f00" />

        <TextView
            android:id="@+id/text_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="1"
            android:textColor="#ffffff"
            app:autoSizeMaxTextSize="500dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.7" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>