//
//  amnhac_list_xuongamtrenphimdan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFoundation

class amnhac_list_xuongamtrenphimdan: MusicGameFragment {
    // MARK: - Properties
    private var svgPhimDanView: SVGKFastImageView!
    private var phimDanSVG: SVGKImage?
    private var btnC1: UIView!
    private var btnD1: UIView!
    private var btnE1: UIView!
    private var btnF1: UIView!
    private var btnG1: UIView!
    private var btnA1: UIView!
    private var btnB1: UIView!
    private var btnC2: UIView!
    private var gridLayout: UIView!
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    private var musicStreamIdMap: [String: Bool] = [:]
    private var textColorMap: [String: UIColor] = [:]
    private var bgColorMap: [String: UIColor] = [:]
    private var bg2ColorMap: [String: UIColor] = [:]
    private let notes: [String] = ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "c2"]
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F73535"),
        UIColor.color(hex: "#FF8700"),
        UIColor.color(hex: "#FFD400"),
        UIColor.color(hex: "#05BD34"),
        UIColor.color(hex: "#00CFFF"),
        UIColor.color(hex: "#1B7CCC"),
        UIColor.color(hex: "#BF04BB"),
        UIColor.color(hex: "#F73535")
    ]
    private var busy: Bool = true
    private var tappedTime: TimeInterval = 0
    private var sampleNotes: [Int] = []
    private var pressedNotes: [Int] = []
    private var currentIndex: Int = -1
    private var newIndex: Int = -1
    let innerContainer = UIImageView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
        
        innerContainer.isUserInteractionEnabled = true
        innerContainer.image = Utilities.SVGImage(named: "music_phimdan3")
        innerContainer.clipsToBounds = false
        itemContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 1840.0 / 1241.0)
        
        svgPhimDanView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "music_phimdan3"))
        svgPhimDanView.contentMode = .scaleToFill
        svgPhimDanView.stringTag = "svg_phimdan_view"
        innerContainer.addSubview(svgPhimDanView)
        svgPhimDanView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let buttons: [(id: String, bias: CGFloat, width: CGFloat)] = [
            ("c1", 0.041, 0.10),
            ("d1", 0.173, 0.10),
            ("e1", 0.305, 0.10),
            ("f1", 0.435, 0.10),
            ("g1", 0.567, 0.10),
            ("a1", 0.698, 0.10),
            ("b1", 0.83, 0.10),
            ("c2", 0.96, 0.10)
        ]
        
        for (id, bias, width) in buttons {
            let btn = UIView()
            btn.stringTag = id
            btn.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
            btn.alpha = 0.1
            innerContainer.addSubview(btn)
            btn.snp.makeConstraints { make in
                make.width.equalTo(innerContainer).multipliedBy(width)
                make.height.equalTo(innerContainer).multipliedBy(0.41)
                make.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                btn.snapToHorizontalBias(horizontalBias: bias)
            }
            btn.isUserInteractionEnabled = true
            
            switch id {
            case "c1": btnC1 = btn
            case "d1": btnD1 = btn
            case "e1": btnE1 = btn
            case "f1": btnF1 = btn
            case "g1": btnG1 = btn
            case "a1": btnA1 = btn
            case "b1": btnB1 = btn
            case "c2": btnC2 = btn
            default: break
            }
        }
        
        let mask = PassthroughImageView()
        mask.image = Utilities.SVGImage(named: "music_phimdan2_mask")
        innerContainer.addSubview(mask)
        mask.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        let musicBgView = UIImageView()
        musicBgView.image = Utilities.SVGImage(named: "music/note/music_bg.svg")
        innerContainer.addSubview(musicBgView)
        gridLayout = UIView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        gridLayout.clipsToBounds = false
        innerContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.96)
            make.height.equalTo(innerContainer).multipliedBy(0.56)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.58) // verticalBias=0.08
        }
        
        musicBgView.snp.makeConstraints { make in
            make.edges.equalTo(gridLayout)
        }
        
        let noteIds = ["note_1", "note_2", "note_3", "note_4", "note_5"]
        var i = 0.0
        for (index, id) in noteIds.enumerated() {
            let noteContainer = UIView()
            //noteContainer.stringTag = id
            noteContainer.backgroundColor = UIColor.black.withAlphaComponent(0.18) // #2f00
            gridLayout.addSubview(noteContainer)
            noteContainer.snp.makeConstraints { make in
                make.width.equalTo(gridLayout).multipliedBy(0.18)
                make.top.bottom.equalToSuperview()
                make.right.equalToSuperview().multipliedBy((i+1.0)*0.18 + 0.05)
            }
            i += 1
            let noteImage = UIImageView()
            noteImage.stringTag = id
            noteImage.contentMode = .scaleAspectFit
            noteContainer.addSubview(noteImage)
            noteImage.makeViewCenterAndKeep(ratio: 50.0/130.0)
        }
        
        loadPiano()
        
    }
    
    func loadPiano() {
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in notes {
                if let url = Utilities.url(soundPath: "effect/music/piano_2\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if touches.count == 1 {
            let touch = touches.first!
            for i in 0..<innerContainer.subviews.count {
                let view = innerContainer.subviews[i]
                if touch.placeInView(view: view) {
                    if let note = view.stringTag as? String, note != "svg_phimdan_view" {
                        handleButtonTap(note: note)
                    }
                }
            }
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        
        sampleNotes = []
        for _ in 0..<5 {
            let noteIndex = Int.random(in: 0..<notes.count)
            sampleNotes.append(noteIndex)
        }
        
        let noteIds = ["note_1", "note_2", "note_3", "note_4", "note_5"]
        var delay: TimeInterval = 0.5
        delay += playSound("music/xuongamtrendan")
        
        for i in 0..<sampleNotes.count {
            let noteIndex = sampleNotes[i]
            let note = notes[noteIndex]
            let noteView = gridLayout.viewWithStringTag(noteIds[i]) as? UIImageView
            noteView?.image = Utilities.SVGImage(named: "music/note/2\(note).svg").withRenderingMode(.alwaysTemplate)
            let noteContainer = noteView?.superview
            noteContainer?.backgroundColor = UIColor.color(hex: "#3C3C3C").withAlphaComponent(0.196) // #3C3C3C, alpha 50
            noteView?.tintColor = UIColor.color(hex: "#3C3C3C")
            noteContainer?.isHidden = true
            
            scheduler.schedule(after: delay + 1.0 + Double(i) * 0.8) { [weak self] in
                noteContainer?.isHidden = false
                self?.playSound("effect/music/man_2\(note)\(note == "b1" ? "_vi" : "")")
            }
        }
        
        scheduler.schedule(after: delay + 1.0 + Double(sampleNotes.count) * 0.8) { [weak self] in
            self?.busy = false
            self?.newIndex = 0
            self?.startGame()
        }
        
        updateCurrentIndex()
         
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            var delay: TimeInterval = playSound("music/xuongamtrendan")
            for i in 0..<sampleNotes.count {
                let noteIndex = sampleNotes[i]
                let note = notes[noteIndex]
                scheduler.schedule(after: delay + 1.0 + Double(i) * 0.8) { [weak self] in
                    self?.playSound("effect/music/man_2\(note)\(note == "b1" ? "_vi" : "")")
                }
            }
            scheduler.schedule(after: delay + 1.0 + Double(sampleNotes.count) * 0.8) { [weak self] in
                self?.busy = false
                self?.newIndex = 0
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    private func handleButtonTap(note: String) {
        if busy || currentIndex >= sampleNotes.count || currentIndex < 0 { return }
        busy = true
        
        if let player = noteToMusicId[note] {
            player.currentTime = 0
            player.play()
            musicStreamIdMap[note] = true
        }
        
        // Placeholder for SVG path manipulation
        textColorMap[note] = .color(hex: "#B8C8D3")
        bgColorMap[note] = .color(hex: "#DCE4EA")
        bg2ColorMap[note] = .color(hex: "#DCE4EA")
        svgPhimDanView.setNeedsDisplay()
        
        let correct = note == notes[sampleNotes[currentIndex]]
        let noteIds = ["note_1", "note_2", "note_3", "note_4", "note_5"]
        let noteView = gridLayout.viewWithStringTag(noteIds[currentIndex]) as? UIImageView
        let noteContainer = noteView?.superview
        
        noteView?.image = Utilities.SVGImage(named: "music/note/2\(note).svg").withRenderingMode(.alwaysTemplate)
        
        if correct {
            noteContainer?.backgroundColor = colors[sampleNotes[currentIndex]].withAlphaComponent(0.196) // alpha 50
            noteView?.tintColor = colors[sampleNotes[currentIndex]]
            pressedNotes.append(sampleNotes[currentIndex])
            newIndex = currentIndex + 1
            
            if newIndex >= sampleNotes.count {
                pauseGame()
                animateCoinIfCorrect(view: svgPhimDanView)
                var delay: TimeInterval = playSound(delay: 0, names: ["effect_correct1", getCorrectHumanSound()])
                for i in 0..<sampleNotes.count {
                    let noteIndex = sampleNotes[i]
                    let note2 = notes[noteIndex]
                    scheduler.schedule(after: delay) { [weak self] in
                        self?.playSound("effect/music/man_2\(note2)\(note2 == "b1" ? "_vi" : "")")
                    }
                    delay += 0.8
                }
                delay += playSound(name: endGameSound(), delay: delay)
                scheduler.schedule(after: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                busy = false
            }
        } else {
            setGameWrong()
            noteContainer?.backgroundColor = UIColor.color(hex: "#3C3C3C").withAlphaComponent(0.196)
            noteView?.tintColor = UIColor.color(hex: "#3C3C3C")
            scheduler.schedule(after: 0.2) { [weak self] in
                guard let self = self else { return }
                noteView?.image = Utilities.SVGImage(named: "music/note/2\(self.notes[self.sampleNotes[self.currentIndex]]).svg").withRenderingMode(.alwaysTemplate)
                self.busy = false
            }
        }
        
        scheduler.schedule(after: 0.1) { [weak self] in
            //self?.releaseNote(note: note)
        }
    }
    /*
    private func releaseNote(note: String) {
        if currentNote == note {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.releaseNote(note: note)
            }
            return
        }
                
        
        // Placeholder for SVG path manipulation
        textColorMap.removeValue(forKey: note)
        bgColorMap.removeValue(forKey: note)
        bg2ColorMap.removeValue(forKey: note)
        svgPhimDanView.setNeedsDisplay()
        musicStreamIdMap[note] = false
    }
    */
    private func updateCurrentIndex() {
        if currentIndex >= 0 {
            let noteView = gridLayout.viewWithStringTag("note_\(currentIndex + 1)")
            noteView?.alpha = 1
        }
        
        currentIndex = newIndex
        if currentIndex >= 0 {
            let noteView = gridLayout.viewWithStringTag("note_\(currentIndex + 1)") as? UIImageView
            noteView?.alpha = 0.6
            scheduler.schedule(after: 0.4) { [weak noteView] in
                noteView?.alpha = 1
            }
        }
        
        scheduler.schedule(after: 0.8) { [weak self] in
            self?.updateCurrentIndex()
        }
    }
    
    // MARK: - Supporting Structures
    struct VisualTree {
        static func moveElement(viewA: UIView, viewB: UIView, duration: TimeInterval, scale: CGFloat = 1, alpha: CGFloat = 1) {
            // Giả lập, cần thay bằng implement thực tế
            UIView.animate(withDuration: duration) {
                viewA.center = viewB.center
                viewA.transform = CGAffineTransform(scaleX: scale, y: scale)
                viewA.alpha = alpha
            }
        }
    }
}


struct R17 {
    struct drawable {
        static let music_phimdan3 = 1
        static let music_phimdan2_mask = 2
        static let music_bg = 3
        static let _c1 = 4
        static let _d1 = 5
        static let _e1 = 6
        static let _f1 = 7
        static let _g1 = 8
        static let _a1 = 9
        static let _b1 = 10
        static let _c2 = 11
        static let music_xuongamtrendan = 12
    }
}

extension Int {
    func toDrawableNameXATD() -> String? {
        switch self {
        case R17.drawable.music_phimdan3:
            return "music_phimdan3"
        case R17.drawable.music_phimdan2_mask:
            return "music_phimdan2_mask"
        case R17.drawable.music_bg:
            return "music_bg"
        case R17.drawable._c1:
            return "c1"
        case R17.drawable._d1:
            return "d1"
        case R17.drawable._e1:
            return "e1"
        case R17.drawable._f1:
            return "f1"
        case R17.drawable._g1:
            return "g1"
        case R17.drawable._a1:
            return "a1"
        case R17.drawable._b1:
            return "b1"
        case R17.drawable._c2:
            return "c2"
        case R17.drawable.music_xuongamtrendan:
            return "music_xuongamtrendan"
        default:
            return "empty"
        }
    }
}
