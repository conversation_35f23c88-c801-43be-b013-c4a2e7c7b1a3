//
//  ngonngu_list_timdau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class ngonngu_list_timdau: NhanBietGameFragment {
    // MARK: - Properties
    private var items: [Item] = []
    private var folders: [String] = []
    private var meIndex: Int = 0
    private var gridLayout: MyGridView!
    private var indexes: [Int] = []
    private var tones: [String] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#EBFBFC")
               
        gridLayout = MyGridView()
        gridLayout.columns = 4
        gridLayout.itemRatio = 0.7
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            for item in pack.items {
                if let letterVi = item.letterVi, !letterVi.isEmpty, !item.name.vi!.contains(" ") {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
        }
        
        meIndex = Int.random(in: 0..<4)
        while true {
            indexes = (0..<items.count).shuffled().prefix(4).map { $0 }
            tones = []
            for index in indexes {
                let words = vietnamese.extractWord(items[index].name.vi!)
                if words.contains("sắc") {
                    tones.append("sắc")
                } else if words.contains("huyền") {
                    tones.append("huyền")
                } else if words.contains("hỏi") {
                    tones.append("hỏi")
                } else if words.contains("ngã") {
                    tones.append("ngã")
                } else if words.contains("nặng") {
                    tones.append("nặng")
                } else {
                    tones.append("không")
                }
            }
            let meTone = tones[meIndex]
            let size = tones.filter { $0 == meTone }.count
            if size == 1 {
                break
            }
        }
        
        let tone = tones[meIndex]
        var delay: TimeInterval = 0.5
        if tone == "ngang" || tone == "không" {
            delay += playSound(delay: delay, names: ["ngonngu/ngonngu_tim dau_ngang"])
        } else {
            let toneSound = tone == "sắc" ? "sac" :
                            tone == "huyền" ? "huyen" :
                            tone == "hỏi" ? "hoi" :
                            tone == "ngã" ? "nga" :
                            tone == "nặng" ? "nang" : "ngang"
            delay += playSound(delay: delay, names: [
                "ngonngu/ngonngu_tim dau1",
                "ngonngu/ngonngu_tim dau_\(toneSound)"
            ])
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        
        var views: [UIView] = []
        for i in 0..<indexes.count {
            let item = items[indexes[i]]
            let folder = folders[indexes[i]]
            
            let view = UIView()
            view.clipsToBounds = false
            view.isUserInteractionEnabled = true
            addActionOnLayoutSubviews {
                view.transform = CGAffineTransformMakeTranslation(0, view.frame.height * (i%2==0 ?0.25 : -0.25))
            }
            
            let bgView = UIImageView()
            bgView.image = Utilities.SVGImage(named: "option_bg_white_shadow")
            view.addSubview(bgView)
            bgView.snp.makeConstraints { make in
                make.left.top.right.equalToSuperview()
                make.height.equalTo(bgView.snp.width) // Ratio 1:1
            }

            
            let svgView = SVGImageView(frame: .zero)
            svgView.accessibilityIdentifier = "svg_view"
            svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item.path!)").uiImage
            bgView.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.width.equalToSuperview()
                make.height.equalTo(svgView.snp.width) // Ratio 1:1
                make.center.equalToSuperview()
            }
            
            let textName = HeightRatioTextView()
            textName.setHeightRatio(0.8)
            textName.textAlignment = .center
            textName.textColor = UIColor.color(hex: "#74B6FF")
            textName.font = .Freude(size: 20)
            textName.text = item.name.vi
            view.addSubview(textName)
            textName.snp.makeConstraints { make in
                make.height.equalToSuperview().multipliedBy(0.23)
                make.bottom.left.right.equalToSuperview()
            }
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.stringTag = "\(i)"
            
            views.append(view)
        }
        
        gridLayout.reloadItemViews(views: views)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let tone = tones[meIndex]
            var delay: TimeInterval = 0.5
            if tone == "ngang" || tone == "không" {
                delay += playSound(delay: delay, names: ["ngonngu/ngonngu_tim dau_ngang"])
            } else {
                let toneSound = tone == "sắc" ? "sac" :
                                tone == "huyền" ? "huyen" :
                                tone == "hỏi" ? "hoi" :
                                tone == "ngã" ? "nga" :
                                tone == "nặng" ? "nang" : "ngang"
                delay += playSound(delay: delay, names: [
                    "ngonngu/ngonngu_tim dau1",
                    "ngonngu/ngonngu_tim dau_\(toneSound)"
                ])
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let index = Int(view.stringTag ?? "0"),
              index < indexes.count else { return }
        
        pauseGame(stopMusic: false)
        let item = items[indexes[index]]
        let folder = folders[indexes[index]]
        let sound = "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
        if index == meIndex {
            let svgView = view.findSubviews(ofType: SVGImageView.self).first!
            let delay = playSound(delay: 0, names: [
                sound,
                "effect/answer_correct1",
                getCorrectHumanSound(),
                endGameSound()
            ])
            animateCoinIfCorrect(view: svgView)
            scheduler.schedule(after: delay + 1) { [weak self] in
                self?.finishGame()
            }
        } else {
            let delay = playSound(delay: 0, names: [
                sound,
                answerWrongEffectSound(),
                getWrongHumanSound()
            ])
            setGameWrong()
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}
