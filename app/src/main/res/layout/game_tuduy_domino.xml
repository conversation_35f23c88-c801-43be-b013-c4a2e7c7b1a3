<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#F9E3C2"
    android:clipChildren="false"
    android:clipToPadding="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercent="0.05">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/items_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_domino_bg"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="2335:575"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0">

            <View
                android:id="@+id/constraintLayout10"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout9"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

            <View
                android:id="@+id/constraintLayout9"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout11"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout10"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

            <View
                android:id="@+id/constraintLayout11"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout12"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout9"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

            <View
                android:id="@+id/constraintLayout12"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout11"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/items_place_holder"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout42"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout32"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout32"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout52"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout42"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout52"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout62"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout32"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout62"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout72"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout52"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout72"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout82"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout62"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout82"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout72"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16">


            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/items_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="10"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1">

            <include
                android:id="@+id/constraintLayout4"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout3"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="spread_inside"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

            <include
                android:id="@+id/constraintLayout3"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout5"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout4"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />


            <include
                android:id="@+id/constraintLayout5"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout6"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout3"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />


            <include
                android:id="@+id/constraintLayout6"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout7"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout5"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />


            <include
                android:id="@+id/constraintLayout7"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout8"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout6"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

            <include
                android:id="@+id/constraintLayout8"
                layout="@layout/item_domino"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/tuduy_domino_piece_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="373.6:209.8"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout7"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.16" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>