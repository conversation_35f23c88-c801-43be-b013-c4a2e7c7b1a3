//
//  tuduy_list_domino.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_domino: NhanBietGameFragment {
    // MARK: - Properties
    private var numbers: [Int] = []
    private var itemsBottom: UIView!
    private var itemsTop: UIImageView!
    private var topViews: [UIView] = []
    private var bottomViews: [UIView] = []
    private var dragViews: [UIView] = []
    private var itemContainer: UIView!
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    private var itemsPlaceHolder: UIView!
    private var leftViews: [UIView] = []
    private var snapViews: [UIView: UIView] = [:]
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 249/255, green: 227/255, blue: 194/255, alpha: 1) // #F9E3C2
        
        let view5 = UIView()
        view.addSubviewWithPercentInset(subview: view5, percentInset: 5)
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view5.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 2.5)
                
        
        itemsTop = UIImageView()
        itemsTop.isUserInteractionEnabled = true
        itemsTop.image = Utilities.SVGImage(named: "tuduy_domino_bg")
        itemContainer.addSubview(itemsTop)
        itemsTop.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(itemsTop.snp.width).multipliedBy(575.0 / 2335.0) // Ratio 2335:575
        }
        
        itemsPlaceHolder = UIView()
        itemContainer.addSubview(itemsPlaceHolder)
        itemsPlaceHolder.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(itemsPlaceHolder.snp.width).multipliedBy(0.1) // Ratio 10:1
        }
        
        itemsBottom = UIView()
        itemContainer.addSubview(itemsBottom)
        itemsBottom.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalTo(itemsBottom.snp.width).multipliedBy(0.1) // Ratio 10:1
        }
        
        let topPlaceholders = ["constraintLayout10", "constraintLayout9", "constraintLayout11", "constraintLayout12"]
        var index = 0
        for id in topPlaceholders {
            let placeholder = UIImageView()
            placeholder.isUserInteractionEnabled = true
            placeholder.image = Utilities.SVGImage(named: "tuduy_domino_piece_bg")
            itemsTop.addSubview(placeholder)
            
            placeholder.snp.makeConstraints { make in
                make.width.equalTo(itemsTop).multipliedBy(0.16)
                make.height.equalTo(placeholder.snp.width).multipliedBy(209.8 / 373.6) // Ratio 373.6:209.8
                make.centerY.equalToSuperview()
                make.right.equalToSuperview().multipliedBy((0.072+0.16)*CGFloat(index+1))
            }
            placeholder.stringTag = id
            index += 1
        }
        
        let placeholders = ["constraintLayout42", "constraintLayout32", "constraintLayout52", "constraintLayout62", "constraintLayout72", "constraintLayout82"]
        index = 0
        for id in placeholders {
            let placeholder = UIImageView()
            placeholder.image = Utilities.SVGImage(named: "tuduy_domino_piece_bg")
            itemsPlaceHolder.addSubview(placeholder)
            
            placeholder.snp.makeConstraints { make in
                make.width.equalTo(itemsPlaceHolder).multipliedBy(0.16)
                make.height.equalTo(placeholder.snp.width).multipliedBy(209.8 / 373.6) // Ratio 373.6:209.8
                make.centerY.equalToSuperview()
                make.right.equalToSuperview().multipliedBy(0.008 * CGFloat(index) + 0.16 * CGFloat(index+1))
            }
            placeholder.stringTag = id
            index += 1
        }
        
        let bottomItems = ["constraintLayout4", "constraintLayout3", "constraintLayout5", "constraintLayout6", "constraintLayout7", "constraintLayout8"]
        index = 0
        for id in bottomItems {
            let item = createItemDomino()
            itemsBottom.addSubview(item)
            item.snp.makeConstraints { make in
                make.width.equalTo(itemsBottom).multipliedBy(0.16)
                make.height.equalTo(item.snp.width).multipliedBy(209.8 / 373.6) // Ratio 373.6:209.8
                make.centerY.equalToSuperview()
                make.right.equalToSuperview().multipliedBy(0.008 * CGFloat(index) + 0.16 * CGFloat(index+1))
            }
            item.stringTag = id
            bottomViews.append(item)
            index += 1
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer.addGestureRecognizer(panGesture)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        itemContainer.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        numbers = [2, 3, 4, 5, 6].shuffled() + [1, 0]
        for i in 0..<6 {
            let item = itemsBottom.subviews[i]
            guard let imageLeft = item.viewWithStringTag("image_left") as? UIImageView,
                  let imageRight = item.viewWithStringTag("image_right") as? UIImageView else { continue }
            imageLeft.image = Utilities.SVGImage(named: numbers[i] == 0 ? "empty": "tuduy_domino_piece_\(numbers[i])")
            imageRight.image = Utilities.SVGImage(named: numbers[i+1] == 0 ? "empty": "tuduy_domino_piece_\(numbers[i + 1])")
        }
        
        topViews = []
        for i in 0..<4 {
            let topView = itemsTop.subviews[i]
            topView.isHidden = false
            topView.alpha = 0.01
            topViews.append(topView)
        }
        topViews.shuffle()
        
        let delay = playSound(openGameSound(), "tuduy/domino")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/domino")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    var frames : [CGRect] = []
    override func createGame() {
        super.createGame()
        dragViews = []
        for i in 0..<4 {
            let item = itemsBottom.subviews[i+2]
            let viewTo = topViews[i]
            dragViews.append(item)
            frames.append(item.frame)
            item.moveToCenter(of: viewTo, duration:0)
            if Bool.random() {
                item.subviews.first?.transform = CGAffineTransform(rotationAngle: .pi)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        let location = gesture.location(in: itemContainer)
        let containerLocation = itemContainer.convert(CGPoint.zero, to: nil)
        let bottomLocation = itemsBottom.convert(CGPoint.zero, to: nil)
        deltaX = bottomLocation.x - containerLocation.x
        deltaY = bottomLocation.y - containerLocation.y
        let currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
        if let currentView = currentView {
            if let child = currentView.subviews.first {
                UIView.animate(withDuration: 0.2) {
                    let currentRotation = child.transform.rotationAngle()
                    let targetRotation = round((.pi - currentRotation) / .pi) * .pi
                    child.transform = CGAffineTransform(rotationAngle: targetRotation)
                }
            }
        }
    }
    var zPos = 100.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: itemContainer)
        
        switch gesture.state {
        case .began:
            let containerLocation = itemContainer.convert(CGPoint.zero, to: nil)
            let bottomLocation = itemsBottom.convert(CGPoint.zero, to: nil)
            deltaX = bottomLocation.x - containerLocation.x
            deltaY = bottomLocation.y - containerLocation.y
            
            currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                zPos += 1
                currentView.layer.zPosition = zPos
                for placeholder in itemsPlaceHolder.subviews {
                    if snapViews[placeholder] == currentView {
                        snapViews[placeholder] = nil
                    }
                }
                playSound("effect/slide1")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: self)
                gesture.setTranslation(CGPointZero, in: self)
                var transform = currentView.transform
                transform.tx += translation.x
                transform.ty += translation.y
                currentView.transform = transform
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                
                var minDistance = CGFloat.greatestFiniteMagnitude
                var minView: UIView?
                for placeholder in itemsPlaceHolder.subviews {
                    let distance = currentView.distanceFromCenterToCenter(to: placeholder)
                    let dist = sqrt(distance.x * distance.x + distance.y * distance.y)
                    if dist < minDistance {
                        minDistance = dist
                        minView = placeholder
                    }
                }
                
                if let minView = minView, minDistance < currentView.frame.height {
                    if let oldView = snapViews[minView] {
                        if let index = dragViews.firstIndex(of: oldView), index < topViews.count {
                            oldView.moveToCenter(of: topViews[index], duration: 0.2)
                        }
                    }
                    snapViews[minView] = currentView
                    playSound("effect/word puzzle drop")
                    self.currentView?.moveToCenter(of: minView, duration: 0.2) {
                        [weak self] _ in
                        guard let self = self else { return }
                        if self.snapViews.compactMap({$0.value}).count == 4 {
                            var win = true
                            for i in 0..<self.dragViews.count {
                                let view = self.dragViews[i]
                                let dx = abs(view.frame.midX - frames[i].midX)
                                let dy = abs(view.frame.midY - frames[i].midY)
                                if dx > view.frame.height / 3 || dy > view.frame.height / 3 || abs(view.subviews.first?.transform.rotationAngle() ?? 0) > 0.1 {
                                    win = false
                                    break
                                }
                            }
                            
                            if win {
                                self.pauseGame()
                                self.animateCoinIfCorrect(view: self.itemsBottom)
                                let delay = self.playSound("effect/answer_end", self.getCorrectHumanSound(), self.endGameSound())
                                self.scheduler.schedule(delay: delay) { [weak self] in
                                    self?.finishGame()
                                }
                            } else {
                                self.setGameWrong()
                                self.itemsBottom.alpha = 1
                                for i in 0..<self.dragViews.count {
                                    let item = self.dragViews[i]
                                    let viewTo = self.topViews[i]
                                    self.scheduler.schedule(after: TimeInterval(i) * 0.04) { [weak self] in
                                        guard self != nil else { return }
                                        item.moveToCenter(of: viewTo, duration: 0.8)
                                        UIView.animate(withDuration: 0.8) {
                                            item.subviews.first?.transform = CGAffineTransform(rotationAngle: Bool.random() ? .pi : 0)
                                        }
                                    }
                                }
                                self.snapViews = [:]
                            }
                        }
                    }
                } else if let index = dragViews.firstIndex(of: currentView) {
                    currentView.moveToCenter(of: topViews[index], duration: 0.2)
                }
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func createItemDomino() -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "tuduy_domino_piece")
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height).multipliedBy(373.6 / 209.8) // Ratio 373.6:209.8
        }
        
        let imageLeft = UIImageView()
        imageLeft.stringTag = "image_left"
        container.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        let imageRight = UIImageView()
        imageRight.stringTag = "image_right"
        container.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        return view
    }
    
    private func findViewUnder(x: Float, y: Float) -> UIView? {
        let adjustedX = CGFloat(x) - deltaX
        let adjustedY = CGFloat(y) - deltaY
        for view in dragViews {
            if adjustedX >= CGFloat(view.frame.minX) && adjustedX <= CGFloat(view.frame.maxX) &&
               adjustedY >= CGFloat(view.frame.minY) && adjustedY <= CGFloat(view.frame.maxY) {
                return view
            }
        }
        return nil
    }
}

