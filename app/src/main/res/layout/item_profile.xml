<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:id="@+id/background_item"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginVertical="15dp"
        android:background="@drawable/btn_account_name"
        android:orientation="vertical"
        app:layout_constraintDimensionRatio="1148:176"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/image_avatar"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:padding="5dp"
            android:src="@drawable/cat"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/text_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:fontFamily="@font/svn_freude"
            android:gravity="left|center_vertical"
            android:paddingVertical="0dp"
            android:text="Bony"
            android:textColor="#565656"
            android:textSize="16dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintLeft_toRightOf="@id/image_avatar"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.8" />

        <ImageView
            android:id="@+id/image_check"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/bg_flashcards_card_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:pcl_layout_marginPercentPerHeight="0.15"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
