//
//  tuduy_list_chonggiaymau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 17/4/25.
//


import UIKit
import SnapKit

class tuduy_list_chonggiaymau: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var gridLayout: MyGridView!
    private var viewTopLeft: UIView!
    private var viewTopRight: UIView!
    private var viewBottomLeft: UIView!
    private var viewBottomRight: UIView!
    private var viewCenter: UIView!
    private var selectedViews: [UIView] = []
    private let colors = [
        UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1), // #87D657
        UIColor(red: 255/255, green: 247/255, blue: 0/255, alpha: 1),  // #FFF700
        UIColor(red: 184/255, green: 86/255, blue: 255/255, alpha: 1), // #B856FF
        UIColor(red: 90/255, green: 184/255, blue: 255/255, alpha: 1), // #5AB8FF
        UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1)  // #FF7760
    ]
    private var meIndex: Int = 0
    private var itemSample: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .white // #FFFFFF
        
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        let leftPaddingView = UIView()
        leftView.addSubviewWithPercentInset(subview: leftPaddingView, percentInset: 10)
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        leftPaddingView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 2)
        
        itemSample = createItemXepChongGiay()
        itemSample.isHidden = true
        itemContainer.addSubview(itemSample)
        itemSample.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let innerContainer = UIView()
        //innerContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        itemContainer.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height) // Ratio 1:1
            make.height.equalTo(itemContainer).multipliedBy(0.82)
            make.center.equalToSuperview()
        }
        
        viewTopLeft = UIView()
        viewTopLeft.backgroundColor = .yellow // #ff0
        innerContainer.addSubview(viewTopLeft)
        viewTopLeft.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.66)
            make.height.equalTo(viewTopLeft.snp.width) // Ratio 1:1
            make.left.top.equalToSuperview()
        }
        
        viewBottomLeft = UIView()
        viewBottomLeft.backgroundColor = .cyan // #0ff
        innerContainer.addSubview(viewBottomLeft)
        viewBottomLeft.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.66)
            make.height.equalTo(viewBottomLeft.snp.width) // Ratio 1:1
            make.left.bottom.equalToSuperview()
        }
        
        viewCenter = UIView()
        viewCenter.backgroundColor = .red // #f00
        innerContainer.addSubview(viewCenter)
        viewCenter.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.66)
            make.height.equalTo(viewCenter.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        viewTopRight = UIView()
        viewTopRight.backgroundColor = .green // #0f0
        innerContainer.addSubview(viewTopRight)
        viewTopRight.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.66)
            make.height.equalTo(viewTopRight.snp.width) // Ratio 1:1
            make.right.top.equalToSuperview()
        }
        
        viewBottomRight = UIView()
        viewBottomRight.backgroundColor = .blue // #00f
        innerContainer.addSubview(viewBottomRight)
        viewBottomRight.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.66)
            make.height.equalTo(viewBottomRight.snp.width) // Ratio 1:1
            make.right.bottom.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        resetZ()
        let count = 3
        let shuffledColors = colors.shuffled()
        meIndex = 0
        
        for i in 0..<selectedViews.count {
            selectedViews[i].backgroundColor = shuffledColors[i]
        }
        
        var views: [UIView] = []
        for i in 0..<count {
            let item = createItemXepChongGiay()
            guard let item1 = item.viewWithStringTag("item_1" ),
                  let item2 = item.viewWithStringTag("item_2"),
                  let item3 = item.viewWithStringTag("item_3"),
                  let item4 = item.viewWithStringTag("item_4") else { continue }
            
            if i == 0 {
                item1.backgroundColor = shuffledColors[0]
                item2.backgroundColor = shuffledColors[1]
                item3.backgroundColor = shuffledColors[2]
                item4.backgroundColor = shuffledColors[3]
            } else if i == 1 {
                item1.backgroundColor = shuffledColors[1]
                item2.backgroundColor = shuffledColors[0]
                item3.backgroundColor = shuffledColors[2]
                item4.backgroundColor = shuffledColors[3]
            } else {
                item1.backgroundColor = shuffledColors[0]
                item2.backgroundColor = shuffledColors[2]
                item3.backgroundColor = shuffledColors[1]
                item4.backgroundColor = shuffledColors[3]
            }
            
            item.tag = i
            //AnimationUtils.setTouchEffect(view: item)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            item.addGestureRecognizer(tapGesture)
            item.isUserInteractionEnabled = true
            item.transform = CGAffineTransform(scaleX: 0, y: 0)
            item.alpha = 0
            views.append(item)
        }
        
        gridLayout.columns = 1
        gridLayout.itemRatio = 2
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views.shuffled())
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_xep chong giay")
        gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_xep chong giay")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        pauseGame()
        let index = view.tag
        if index == meIndex {
            if let firstChild = view.subviews.first?.subviews.first {
                animateCoinIfCorrect(view: firstChild)
            }
            var delay = playSound("effect/answer_correct1")
            
            scheduler.schedule(after: delay) {
                guard let sample1 = self.itemSample.viewWithStringTag("item_1"),
                      let sample2 = self.itemSample.viewWithStringTag("item_2"),
                      let sample3 = self.itemSample.viewWithStringTag("item_3"),
                      let sample4 = self.itemSample.viewWithStringTag("item_4") else { return }
                
                let samples = [sample1, sample2, sample3, sample4]
                for j in 0..<self.selectedViews.count {
                    let selectedView = self.selectedViews[j]
                    let sample = samples[j]
                    selectedView.moveToCenter(of: sample, duration: 0.5)
                }
                self.playSound("effect/slide2")
            }
            
            delay += 1.0
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func resetZ() {
        while true {
            let views = [viewTopLeft, viewTopRight, viewBottomLeft, viewBottomRight, viewCenter].shuffled()
            let indexTopLeft = views.firstIndex(of: viewTopLeft)!
            let indexTopRight = views.firstIndex(of: viewTopRight)!
            let indexBottomLeft = views.firstIndex(of: viewBottomLeft)!
            let indexBottomRight = views.firstIndex(of: viewBottomRight)!
            
            if abs(indexTopLeft - indexBottomRight) == 1 || abs(indexTopRight - indexBottomLeft) == 1 {
                continue
            }
            
            views[0]!.isHidden = true
            for i in 1..<views.count {
                let item = views[i]!
                item.isHidden = false
                item.layer.zPosition = CGFloat(100 + i)
            }
            selectedViews = Array(views.dropFirst()).compactMap{ $0}
            break
        }
    }
    
    private func createItemXepChongGiay() -> UIView {
        let view = UIView()
        let container = UIView()
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 2)
        
        let item1 = UIView()
        item1.backgroundColor = .red // #f00
        item1.stringTag = "item_1"
        container.addSubview(item1)
        item1.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.6)
            make.height.equalTo(item1.snp.width) // Ratio 1:1
            make.left.bottom.equalToSuperview()
        }
        
        let item2 = UIView()
        item2.backgroundColor = .green // #0f0
        item2.stringTag = "item_2"
        container.addSubview(item2)
        item2.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.6)
            make.height.equalTo(item2.snp.width) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(0.66) // Bias 0.33
            //make.centerY.equalToSuperview().multipliedBy(1.32) // Bias 0.66
        }
        
        let item3 = UIView()
        item3.backgroundColor = .blue // #00f
        item3.stringTag = "item_3"
        container.addSubview(item3)
        item3.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.6)
            make.height.equalTo(item3.snp.width) // Ratio 1:1
            //make.centerX.equalToSuperview().multipliedBy(1.32) // Bias 0.66
            //make.centerY.equalToSuperview().multipliedBy(0.66) // Bias 0.33
        }
        
        addActionOnLayoutSubviews {
            item2.snapToHorizontalBias(horizontalBias: 0.33)
            item2.snapToVerticalBias(verticalBias: 0.66)
            
            item3.snapToHorizontalBias(horizontalBias: 0.66)
            item3.snapToVerticalBias(verticalBias: 0.33)
        }
        
        let item4 = UIView()
        item4.backgroundColor = .yellow // #ff0
        item4.stringTag = "item_4"
        container.addSubview(item4)
        item4.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(0.6)
            make.height.equalTo(item4.snp.width) // Ratio 1:1
            make.right.top.equalToSuperview()
        }
        
        return view
    }
}

