//
//  phonics_list_basketball.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 19/10/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_basketball: GameFragment {
    var answer: String = ""
    var finished : Bool = false
    var xamlBall = XAMLAnimationView()
    var bottomGrid = MyGridView()
    private var values : [String] = []
    var basketControl : XAMLModel.UserControl?
    var ballView = SVGImageView(SVGName: "basketball_ball")
    var topValues: [String] = []
    override func configureLayout(_ view: UIView) {
        isMultipleTouchEnabled = false
        clipsToBounds = true
        backgroundColor = .color(hex: "#FFEA94")
        var bottomView = UIView()
        addSubview(bottomView)
        addSubview(bottomGrid)
        bottomView.addSubview(xamlBall)
        bottomGrid.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        bottomView.snp.makeConstraints{ make in
            make.left.bottom.right.equalToSuperview()
            make.top.equalTo(bottomGrid.snp.bottom)
        }
        xamlBall.makeViewCenterAndKeep(ratio: 1)
        topValues = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        for topValue in topValues {
            values.append(topValue)
            values.append(topValue)
        }
        do {
            basketControl = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "basketball_basket")!)
        }catch{}
        values = values.randomOrder()
        var listViews : [UIView] = []
        for i in 0..<topValues.count {
            let view = createGridItem()
            let label = view.viewWithTag(3) as! AutosizeLabel
            label.text = topValues[i]
            view.tag = 100 + i
            listViews.append(view)
        }
        bottomGrid.itemRatio = 1
        bottomGrid.itemSpacingRatio = 0.01
        bottomGrid.insetRatio = 0.03
        bottomGrid.columns = 0
        bottomGrid.reloadItemViews(views: listViews)
        
        do {
            var xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "basketball_ball")!)
            xamlBall.loadView(from: xamlData)
            scheduler.schedule(delay: 1, execute: {
                [weak self] in
                guard let self = self else { return }
                
            })
        } catch {}
                
        addSubview(ballView)
        ballView.snp.makeConstraints{ make in
            make.left.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.18)
            make.width.equalTo(ballView.snp.height)
        }
        ballView.alpha = 0
    }
    
    func createGridItem()->UIView{
        var view = UIView()
        var xamlView = XAMLAnimationView();
        xamlView.loadView(from: basketControl!)
        xamlView.tag = 2
        let label = AutosizeLabel()
        label.tag = 3
        label.font = .UTMAvo(size: 17)
        label.textColor = .color(hex: "#FFD910")
        label.text = "a"
        
        let box = xamlView.findGridviewByName(name: "box1")
        box?.backgroundColor = .clear
        box?.subviews.forEach{ $0.isHidden = true }
        box?.addSubview(label)
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        view.addSubview(xamlView)
        xamlView.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        return view
    }
    func playBallSound(){
        if finished {
            xamlBall.alpha = 0
            return
        }
        if xamlBall.alpha == 1 {
            playSound(name: answer)
        }
        scheduler.schedule(delay: 1.2, execute: {
            [weak self] in
            guard let self = self else { return }
            self.playBallSound()
        })
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
            self.startGame()
            xamlBall.startAnimation()
            self.scheduler.schedule(delay: 0.2, execute: {
                [weak self] in
                guard let self = self else { return }
                self.playBallSound()
            })
        })
    }
    var touchInsideBall = false
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing {
            return
        }
        if let touch = touches.first{
            touchInsideBall = touch.placeInView(view: xamlBall)
            if touchInsideBall {
                
                let touchPoint = touch.location(in: self)
                
                // Calculate the new position for the custom view to center around the touch point
                let newCenterX = touchPoint.x //- ballView.frame.width / 2
                let newCenterY = touchPoint.y //- ballView.frame.height / 2
                
                // Update the custom view's center position
                ballView.center = CGPoint(x: newCenterX, y: newCenterY)
                ballView.alpha = 1
                xamlBall.alpha = 0
            }
        }
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        checkGame()
    }
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        checkGame()
    }
    func checkGame(){
        if !touchInsideBall || gameState != .playing {
            return
        }
        var distance = 1000000.0
        var closedView : UIView? = nil
        for view in bottomGrid.subviews {
            let p = ballView.distanceFromCenterToCenter(to: view)
            let d = sqrt(p.x * p.x + p.y * p.y)
            if d < distance {
                distance = d
                closedView = view
            }
        }
        if distance > ballView.bounds.width {
            ballView.alpha = 0
            xamlBall.alpha = 1
            return
        }
        let index = closedView!.tag - 100
        let value = topValues[index]
        let xamlView = closedView?.viewWithTag(2) as? XAMLAnimationView
        pauseGame()
        if value == answer {
            //CoinAnimationUtils.shared.animate(view: closedView!, answer: true)
            playSound("en/english phonics/effects/basketball_true")
            animateCoinIfCorrect(view: closedView!)
            xamlView?.startAnimationStoryboard(with: "sb_true")
            scheduler.schedule(delay: 1.5, execute: {
                [weak self] in
                guard let self = self else { return }
                self.xamlBall.alpha = 1
                loadNextStep()
                resumeGame()
            })
        } else {
            //CoinAnimationUtils.shared.animate(view: closedView!, answer: false)
            setGameWrong()
            playSound("en/english phonics/effects/basketball_fail")
            xamlView?.startAnimationStoryboard(with: "sb_fail")
            scheduler.schedule(delay: 1, execute: {
                [weak self] in
                guard let self = self else { return }
                resumeGame()
                self.xamlBall.alpha = 1
            })
        }
        ballView.alpha = 0
    }
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        if gameState != .playing {
            return
        }
        if let touch = touches.first {
            let touchPoint = touch.location(in: self)

            // Calculate the new position for the custom view to center around the touch point
            let newCenterX = touchPoint.x //- ballView.frame.width / 2
            let newCenterY = touchPoint.y //- ballView.frame.height / 2

            // Update the custom view's center position
            ballView.center = CGPoint(x: newCenterX, y: newCenterY)
        }
    }
    var step = 0
    func loadNextStep(){
        if step >= values.count {
            self.playSound(name: endGameSound())
            DictionaryManager.shared.unlock(names: values)
            scheduler.schedule(delay: 1.5, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
        step += 1
        CoinAnimationUtils.shared.removeList()
    }
}
