//
//  GalleryViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 21/5/25.
//


import UIKit
import SnapKit
import SVGKit
import MBProgressHUD

class GalleryViewController: BaseViewController {
    // MARK: - Properties
    var map: String?
    var mapColor: String?
    private var language: String = "vi"
    var unUsedStickers: [RealmSticker] = []
    private var usedStickers: [RealmSticker] = []
    var uniqueStickers: [RealmSticker] = []
    private var allItems: [Item] = []
    var pack: Folder?
    private var packWithCurrentMap: Folder?
    private var mapData: MapData?
    private var itemRatio: CGFloat = 0
    private var itemCount: Int = 0
    private var selectedView: UIView?
    private var isShowMenu: Bool = false
    private var isShowBottom: Bool = true
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    
    let scrollView = UIScrollView()
    private let viewStickerContainer = UIView()
    private let imageContainer = UIStackView()
    private let imageShadow = SVGKFastImageView(svgkImage: nil)!
    private let btnClose = KUButton()
    private let viewBottomList = UIView()
    private let bgBarLeft = SVGImageView(frame: .zero)
    private let bgBarRight = SVGImageView(frame: .zero)
    private let btnPrev = KUButton()
    private let btnNext = KUButton()
    private let imageNext = SVGImageView(frame: .zero)
    private let recyclerView = UICollectionView(frame: .zero, collectionViewLayout: UICollectionViewFlowLayout())
    private let btnRemove = KUButton()
    private let btnFlip = KUButton()
    
    override var backgroundMusicFile: String? {
        "bg_other"
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .clear
        // backgroundMusicName = "bg_other"
        
        if let intentMap = navigationController?.topViewController?.presentedViewController?.value(forKey: "map") as? String,
           let intentMapColor = navigationController?.topViewController?.presentedViewController?.value(forKey: "mapColor") as? String {
            map = intentMap
            mapColor = intentMapColor
        }
        if let intentLanguage = navigationController?.topViewController?.presentedViewController?.value(forKey: "language") as? String {
            language = intentLanguage
        }
        
        guard let map = map else {
            let dialog = MessageDialogView()
                .setTitle("Lỗi bản đồ")
                .setMessage("Không tìm thấy bản đồ")
                .setListener(onClose:{ [weak self] _ in
                    self?.dismiss(animated: true, completion: nil)
                    self?.navigationController?.popViewController(animated: true)
                })
                .showIn(view)
            return
        }
        
        let packs = FlashcardsManager.shared.getPacks()
        pack = packs.first { $0.maps?.contains(map) ?? false }
        guard let pack = pack else {
            let dialog = MessageDialogView()
                .setTitle("Lỗi bản đồ")
                .setMessage("Không tìm thấy bản đồ \(map)")
                .setListener(onClose: { [weak self] _ in
                    self?.dismiss(animated: true, completion: nil)
                    self?.navigationController?.popViewController(animated: true)
                })
                .showIn(view)
            return
        }
        guard let stickerSizeUrl = Bundle.main.url(forResource: "sticker_size", withExtension: "json"),
              let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
              let stickerSizes = try? JSONDecoder().decode([MapData].self, from: stickerSizeData) else {
            print("Error loading sticker_size.json")
            return
        }
        mapData = stickerSizes.first { $0.Map.replacingOccurrences(of: ".svg", with: "") == map.replacingOccurrences(of: "2", with: "") }
        
        packWithCurrentMap = getPackWithCurrentMap(pack: pack)
        reloadAllStickers()
        loadItems()
        scheduler.schedule(after: 0.1) { [weak self] in
            self?.loadStickers()
        }
        setupUI()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        ReviewHelper(context: self).onExitStickerAlbum()
    }
    
    // MARK: - Setup UI
    private func setupUI() {
        view.removeAllSubviews()
        
        // scrollView
        scrollView.isScrollEnabled = true
        scrollView.bounces = false
        scrollView.showsHorizontalScrollIndicator = false
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .never
        //scrollView.alwaysBounceHorizontal = true
        //scrollView.alwaysBounceVertical = false
        view.addSubview(scrollView)
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // viewStickerContainer
        viewStickerContainer.translatesAutoresizingMaskIntoConstraints = false
        viewStickerContainer.backgroundColor = .clear
        scrollView.addSubview(viewStickerContainer)
        viewStickerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            //make.width.equalTo(scrollView).multipliedBy(2) // Giả định kích thước lớn hơn
            make.height.equalTo(scrollView)
        }
        
        // imageContainer
        imageContainer.axis = .horizontal
        imageContainer.spacing = 0
        imageContainer.isUserInteractionEnabled = false
        viewStickerContainer.addSubview(imageContainer)
        imageContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // imageShadow
        imageShadow.isHidden = true
        viewStickerContainer.addSubview(imageShadow)
        
        // btnClose
        btnClose.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal)
        btnClose.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.width.equalTo(btnClose.snp.height)
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
        }
        btnClose.snpLeftTop(ratio: 1)
        
        // viewBottomList
        viewBottomList.backgroundColor = .clear
        view.addSubview(viewBottomList)
        viewBottomList.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalTo(view.snp.height).multipliedBy(0.2)
        }
        viewBottomList.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.viewBottomList.snapToVerticalBias(verticalBias: 0.95)
        }
        
        // bgBarLeft
        bgBarLeft.SVGName = "gallery_bg_bar_left"
        viewBottomList.addSubview(bgBarLeft)
        bgBarLeft.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(bgBarLeft.snp.height).multipliedBy(606.1/309.0)
        }
        
        // bgBarRight
        bgBarRight.SVGName = "gallery_bg_bar_right"
        viewBottomList.addSubview(bgBarRight)
        bgBarRight.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(bgBarRight.snp.height).multipliedBy(606.1/309.0)
        }
        
        // Background bar
        let bgBar = UIView()
        bgBar.backgroundColor = UIColor(hex: "#DBF1F8")
        viewBottomList.addSubview(bgBar)
        bgBar.snp.makeConstraints { make in
            make.left.equalTo(bgBarLeft.snp.right).offset(-2)
            make.right.equalTo(bgBarRight.snp.left).offset(2)
            make.top.bottom.equalToSuperview()
        }
        
        // btnPrev
        var btn_back = Utilities.GetSVGKImage(named: "btn_back")
        btn_back.caLayerTree.sublayers![0].setFillColor(color: UIColor.color(hex: mapColor!))
        btnPrev.setImage(btn_back.uiImage, for: .normal)
        btnPrev.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(loadPrevStickerPage)))
        viewBottomList.addSubview(btnPrev)
        btnPrev.snp.makeConstraints { make in
            make.width.equalTo(viewBottomList.snp.height).multipliedBy(0.45)
            make.height.equalTo(btnPrev.snp.width)
            make.centerY.equalToSuperview()
        }
        btnPrev.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.btnPrev.snp.makeConstraints { make in
                make.left.equalToSuperview().inset(self.viewBottomList.bounds.height * 0.1)
            }
        }
        
        // btnNext
        btnNext.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(loadNextStickerPage)))
        viewBottomList.addSubview(btnNext)
        btnNext.snp.makeConstraints { make in
            make.width.equalTo(viewBottomList.snp.height).multipliedBy(0.45)
            make.height.equalTo(btnNext.snp.width)
            make.centerY.equalToSuperview()
        }
        btnNext.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.btnNext.snp.makeConstraints { make in
                make.right.equalToSuperview().inset(self.viewBottomList.bounds.height * 0.1)
            }
        }
        
        // imageNext
        imageNext.image = btn_back.uiImage
        imageNext.transform = CGAffineTransform(scaleX: -1, y: 1)
        btnNext.addSubview(imageNext)
        imageNext.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // recyclerView
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        recyclerView.collectionViewLayout = layout
        recyclerView.isScrollEnabled = true
        recyclerView.backgroundColor = .clear
        recyclerView.showsHorizontalScrollIndicator = false
        
        viewBottomList.addSubview(recyclerView)
        recyclerView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(viewBottomList.snp.height).multipliedBy(0.8)
        }
        recyclerView.snpLeftTop(ratio: 6)
        
        // btnRemove
        btnRemove.setImage(Utilities.SVGImage(named: "gallery_btn_remove"), for: .normal)
        btnRemove.isHidden = true
        btnRemove.addTarget(self, action: #selector(removeTapped), for: .touchUpInside)
        view.addSubview(btnRemove)
        btnRemove.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.12)
            make.width.equalTo(btnRemove.snp.height)
        }
        btnRemove.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            btnRemove.snp.makeConstraints { make in
                make.right.bottom.equalToSuperview().inset(self.viewBottomList.bounds.height * 0.05)
            }
        }
        
        // btnFlip
        btnFlip.setImage(Utilities.SVGImage(named: "gallery_btn_flip"), for: .normal)
        btnFlip.isHidden = true
        btnFlip.addTarget(self, action: #selector(flipTapped), for: .touchUpInside)
        view.addSubview(btnFlip)
        btnFlip.snp.makeConstraints { make in
            make.width.height.right.equalTo(btnRemove)
        }
        btnFlip.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            btnFlip.snp.makeConstraints { make in
                make.bottom.equalTo(self.btnRemove.snp.top).offset(-self.btnFlip.bounds.height * 0.05)
            }
        }
                
        
        // Configure drag and drop
        viewStickerContainer.addInteraction(UIDragInteraction(delegate: self))
        viewStickerContainer.addInteraction(UIDropInteraction(delegate: self))
        
        // Configure touch
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        panGesture.delegate = self
        viewStickerContainer.addGestureRecognizer(panGesture)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        tapGesture.delegate = self
        viewStickerContainer.addGestureRecognizer(tapGesture)
       
        
        
        // Load background images
        var lastBg: UIView?
        for i in 1..<20 {
            //let svg = Utilities.SVGImage(named: "gallery/\(map ?? "")_\(i).svg")
            if let url = Utilities.SVGURL(of: "gallery/\(map ?? "")_\(i).svg"), let svg = SVGKImage(contentsOf: url) {
                let imageView = SVGKFastImageView(svgkImage: svg)!
                imageContainer.addArrangedSubview(imageView)
                imageView.snp.makeConstraints { make in
                    make.width.equalTo(imageView.snp.height).multipliedBy(1) // Giả định tỷ lệ
                }
                lastBg = imageView
            } else {
                viewStickerContainer.snp.makeConstraints { make in
                    make.right.equalTo(lastBg!)
                }
                break
            }
        }
        
        // Load map color
        if let mapColor = mapColor {
            let svg = Utilities.SVGImage(named: "btn_back")
            // Note: SVGKImage doesn't support direct path color changes; may need custom logic
            //self.btnPrev.image = Utilities.SVGImage(named: "btn_back")
            //self.imageNext.image = Utilities.SVGImage(named: "btn_back")
        }
        
        let coinView = CoinView()
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.top.bottom.equalTo(btnClose)
        }
        coinView.snpRightTop(ratio: 1)        
    }
    
    // MARK: - Data Management
    func reloadAllStickers() {
        let stickers = GalleryManager.shared.getStickers(map: map ?? "")
#if DEBUG
        DataManager.shared.runRealmTransaction { _ in
            stickers.forEach { item in
                //item.used = false
            }
        }
#endif
        unUsedStickers = stickers.filter { !$0.used }
        usedStickers = stickers.filter { $0.used }
        uniqueStickers = []
        
        for sticker in stickers {
            if !uniqueStickers.contains(where: { $0.sticker == sticker.sticker }) {
                uniqueStickers.append(sticker)
            }
        }
        
#if DEBUG
        DataManager.shared.runRealmTransaction { _ in
            // Debug: Reset used state if needed
        }
#endif
    }
    
    private func loadStickers() {
        for sticker in usedStickers {
            guard let mapData = mapData else { continue }
            let mapItem = mapData.Items.first { sticker.sticker.lowercased().replacingOccurrences(of: " ", with: "_") == $0.Id.lowercased() }
            let svg = Utilities.GetSVGKImage(named: "topics/\(pack?.folder ?? "")/\(sticker.sticker).svg")
            let svgImageView = SVGImageView(frame: .zero)
            svgImageView.translatesAutoresizingMaskIntoConstraints = false
            svgImageView.image = svg.uiImage
            let bounds = svg.caLayerTree.shapeContentBounds!
            self.viewStickerContainer.addSubview(svgImageView)
            
            let scale = mapItem != nil ? mapItem!.H / mapData.H : 0.5
            let locationX = sticker.locationX * Float(self.viewStickerContainer.bounds.width)
            let locationY = sticker.locationY * Float(self.viewStickerContainer.bounds.height)
            
            // Set constraints for size and position
            svgImageView.snp.makeConstraints { make in
                make.width.equalTo(svgImageView.snp.height)
                make.height.equalTo(self.viewStickerContainer.snp.height).multipliedBy(CGFloat(scale) * svg.size.height / bounds.height)
                // Center the view at the desired position
                make.centerX.equalToSuperview().offset(locationX - Float(self.viewStickerContainer.bounds.width) / 2)
                make.centerY.equalToSuperview().offset(locationY - Float(self.viewStickerContainer.bounds.height) / 2)
            }
            
            // Apply transform for scaling and flipping
            var itemScale = sticker.locationY - 0.5
            itemScale = max(-0.3, min(0.3, itemScale))
            itemScale = 0.7 + (itemScale + 0.3) * 0.5
            svgImageView.transform = CGAffineTransform(scaleX: CGFloat(itemScale) * (sticker.flipX ? -1 : 1), y: CGFloat(itemScale))
            svgImageView.tag = sticker.hashValue
        }
    }
    
    private func loadItems() {
        recyclerView.register(ItemStickerCell.self, forCellWithReuseIdentifier: "ItemStickerCell")
        recyclerView.collectionViewLayout.invalidateLayout()
        
        recyclerView.layoutIfNeeded()
        recyclerView.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            itemCount = Int(recyclerView.bounds.width / recyclerView.bounds.height / 1.1)
            itemRatio = recyclerView.bounds.width / recyclerView.bounds.height / CGFloat(itemCount) + 0.005
            
            recyclerView.dataSource = self
            recyclerView.delegate = self
            
            //recyclerView.addObserver(self, forKeyPath: "contentOffset", options: [.new], context: nil)
            reloadData()
        }
    }
    
    private func getPackWithCurrentMap(pack: Folder) -> Folder {
        if packWithCurrentMap == nil {
            guard let mapIndex = pack.maps!.firstIndex(of: map ?? "") else { return pack }
            packWithCurrentMap = Folder(
                name: pack.name,
                folder: pack.folder,
                onlyPhoto: pack.onlyPhoto,
                items: pack.items.enumerated().filter { index, _ in
                    let mapIndex2 = convertItemIndexToMapIndex(index: index)
                    return mapIndex2 == mapIndex
                }.map { $0.element },
                thumb: pack.thumb,
                recognize: pack.recognize,
                freetoplay: pack.freetoplay,
                maps: [map ?? ""],
                mapColor: pack.mapColor
            )
        }
        return packWithCurrentMap!
    }
    
    private func convertItemIndexToMapIndex(index: Int) -> Int {
        guard let pack = pack, !pack.items.isEmpty, !pack.maps!.isEmpty else { return 0 }
        var mapIndex = index / (pack.items.count / pack.maps!.count + 1)
        mapIndex = min(mapIndex, pack.maps!.count - 1)
        return mapIndex
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func removeTapped() {
        guard let selectedView = selectedView, let tag = selectedView.tag as? Int,
              let sticker = usedStickers.first(where: { $0.hashValue == tag }) else { return }

        DataManager.shared.runRealmTransaction { _ in
            sticker.used = false
        }
        usedStickers.removeAll { $0.hashValue == tag }
        unUsedStickers.append(sticker)
        selectedView.removeFromSuperview()
        imageShadow.isHidden = true
        reloadData()
        hideMenu()
    }
    
    @objc private func flipTapped() {
        guard let selectedView = selectedView, let tag = selectedView.tag as? Int,
              let sticker = usedStickers.first(where: { $0.hashValue == tag }) else { return }
        
        DataManager.shared.runRealmTransaction { _ in
            sticker.flipX = !sticker.flipX
        }
        let scaleX = abs(selectedView.transform.a)
        selectedView.transform = CGAffineTransformMakeScale(sticker.flipX ? -scaleX : scaleX, scaleX)
        imageShadow.transform = selectedView.transform
    }
    
    @objc private func loadPrevStickerPage() {
        let itemCount = Int(recyclerView.bounds.width / recyclerView.bounds.height)
        guard let layout = recyclerView.collectionViewLayout as? UICollectionViewFlowLayout else { return }
        let firstVisible = recyclerView.indexPathsForVisibleItems.map{$0.item}.min() ?? 0
        let lastVisible = recyclerView.indexPathsForVisibleItems.map{$0.item}.max() ?? 0
        recyclerView.isScrollEnabled = true
        recyclerView.scrollToItem(at: IndexPath(item: firstVisible, section: 0), at: .right, animated: true)
        scheduler.schedule(after: 0.5) { [weak self] in
            //self?.recyclerView.isScrollEnabled = false
        }
    }
    
    @objc private func loadNextStickerPage() {
        let itemCount = Int(recyclerView.bounds.width / recyclerView.bounds.height)
        guard let layout = recyclerView.collectionViewLayout as? UICollectionViewFlowLayout,
              let packWithCurrentMap = packWithCurrentMap else { return }
        let firstVisible = recyclerView.indexPathsForVisibleItems.map{$0.item}.min() ?? 0
        let lastVisible = recyclerView.indexPathsForVisibleItems.map{$0.item}.max() ?? 0
        recyclerView.isScrollEnabled = true
        recyclerView.scrollToItem(at: IndexPath(item: lastVisible, section: 0), at: .left, animated: true)
        scheduler.schedule(after: 0.5) { [weak self] in
            //self?.recyclerView.isScrollEnabled = false
        }
    }
    
    // MARK: - KVO
    override func observeValue(forKeyPath keyPath: String?, of object: Any?, change: [NSKeyValueChangeKey : Any]?, context: UnsafeMutableRawPointer?) {
        if keyPath == "contentOffset" {
            let firstVisible = recyclerView.indexPathsForVisibleItems.first?.item ?? 0
            let lastVisible = recyclerView.indexPathsForVisibleItems.last?.item ?? 0
            btnPrev.alpha = firstVisible > 0 ? 1 : 0.5
            btnNext.alpha = lastVisible < (packWithCurrentMap?.items.count ?? 0) - 1 ? 1 : 0.5
            btnPrev.isUserInteractionEnabled = firstVisible > 0
            btnNext.isUserInteractionEnabled = lastVisible < (packWithCurrentMap?.items.count ?? 0) - 1
        }
    }
    
    // MARK: - Touch Handling
    var zPos = 100.0
    var originX = 0.0
    var originY = 0.0
    private func deselectView(){
        selectedView = nil
        hideMenu()
    }
    private func selectView(view: UIView){
        selectedView = view
        zPos += 1
        imageShadow.layer.zPosition = zPos
        zPos += 1
        view.layer.zPosition = zPos
        imageShadow.snp.remakeConstraints { make in
            make.edges.equalTo(view)
        }
        imageShadow.transform = view.transform
        if let sticker = usedStickers.first(where: { $0.hashValue == view.tag }) {
            let svg = Utilities.GetSVGKImage(named: "topics/\(pack?.folder ?? "")/shadow/\(sticker.sticker).svg")
            if let layer = svg.caLayerTree.sublayers?[0] as? CAShapeLayer {
                layer.strokeColor = UIColor.black.withAlphaComponent(0.3).cgColor
                layer.lineWidth = viewStickerContainer.bounds.height / 20
                layer.lineCap = .round
                layer.lineJoin = .round
            }
            self.imageShadow.image = svg
            self.imageShadow.isHidden = false
            playSound("\(self.language)/topics/\(self.pack?.folder ?? "")/\(sticker.sticker)")
        }
        showMenu()
    }
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)

        imageShadow.isHidden = true
        currentView = findViewUnder(x: location.x, y: location.y)
        selectedView = currentView

        if let currentView = currentView {
            selectView(view: currentView)
        } else {
            hideMenu()
            if isShowBottom {
                hideBottom()
            } else {
                showBottom()
            }
        }
    }
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)

        switch gesture.state {
        case .began:
            imageShadow.isHidden = true
            currentView = findViewUnder(x: location.x, y: location.y)

            if let currentView = currentView {
                selectView(view: currentView)
            } else {
                if isShowMenu {
                    hideMenu()
                }
            }
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: viewStickerContainer)
                let newCenterX = currentView.center.x + translation.x
                let newCenterY = currentView.center.y + translation.y

                // Update constraints
                currentView.snp.updateConstraints { make in
                    make.centerX.equalToSuperview().offset(newCenterX - viewStickerContainer.bounds.width / 2)
                    make.centerY.equalToSuperview().offset(newCenterY - viewStickerContainer.bounds.height / 2)
                }
                viewStickerContainer.layoutIfNeeded()

                let y = currentView.center.y - viewStickerContainer.bounds.height / 2
                var scale = y / viewStickerContainer.bounds.height
                scale = max(-0.3, min(0.3, scale))
                scale = 0.7 + (scale + 0.3) * 0.5
                if let sticker = usedStickers.first(where: { $0.hashValue == currentView.tag }) {
                    currentView.transform = CGAffineTransform(scaleX: scale * (sticker.flipX ? -1 : 1), y: scale)
                    imageShadow.transform = currentView.transform
                }
                gesture.setTranslation(.zero, in: viewStickerContainer)
            }
        case .ended, .cancelled:
            if currentView == nil {
                isShowBottom ? hideBottom() : showBottom()
            } else {
                if let sticker = usedStickers.first(where: { $0.hashValue == currentView?.tag }) {
                    DataManager.shared.runRealmTransaction { [weak self] _ in
                        guard let self = self else { return }
                        sticker.locationX = Float(currentView!.center.x / self.viewStickerContainer.bounds.width)
                        sticker.locationY = Float(currentView!.center.y / self.viewStickerContainer.bounds.height)
                    }
                }
            }
            scrollView.isScrollEnabled = true
            currentView = nil
        default:
            break
        }
    }
    
    
    // MARK: - Animation
    private func showMenu() {
        isShowMenu = true
        btnRemove.alpha = 0
        btnFlip.alpha = 0
        btnRemove.isHidden = false
        btnFlip.isHidden = false
        UIView.animate(withDuration: 0.5) {
            self.btnRemove.alpha = 1
            self.btnFlip.alpha = 1
        }
        hideBottom()
    }
    
    private func hideMenu() {
        isShowMenu = false
        UIView.animate(withDuration: 0.5, animations: {
            self.btnRemove.alpha = 0
            self.btnFlip.alpha = 0
        }, completion: { _ in
            self.btnRemove.isHidden = true
            self.btnFlip.isHidden = true
        })
    }
    
    private func showBottom() {
        isShowBottom = true
        UIView.animate(withDuration: 0.5) {
            self.viewBottomList.transform = .identity
            self.viewBottomList.alpha = 1
        }
    }
    
    private func hideBottom() {
        isShowBottom = false
        UIView.animate(withDuration: 0.5) {
            self.viewBottomList.transform = CGAffineTransform(translationX: 0, y: self.viewBottomList.bounds.height)
            self.viewBottomList.alpha = 0
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for subview in viewStickerContainer.subviews.reversed() where subview != imageContainer && subview != imageShadow {
            if x >= subview.frame.minX && x <= subview.frame.maxX &&
                y >= subview.frame.minY && y <= subview.frame.maxY {
                return subview
            }
        }
        return nil
    }
    
    func getAvailableSticker(item: Item) -> RealmSticker? {
        guard let index = unUsedStickers.firstIndex(where: { "\($0.sticker).svg" == item.path }) else { return nil }
        let sticker = unUsedStickers[index]
        DataManager.shared.runRealmTransaction { _ in
            sticker.used = true
        }
        usedStickers.append(sticker)
        unUsedStickers.remove(at: index)
        return sticker
    }
    
    func addItemAtPosition(sticker: RealmSticker, item: Item, x: CGFloat, y: CGFloat) {
        guard let pack = pack else { return }
        let mapItem = mapData?.Items.first { sticker.sticker.lowercased().replacingOccurrences(of: " ", with: "_") == $0.Id.lowercased() }
        
        let svg = Utilities.GetSVGKImage(named: "topics/\(pack.folder)/\(sticker.sticker).svg")
        let svgImageView = SVGImageView(frame: .zero)
        svgImageView.translatesAutoresizingMaskIntoConstraints = false
        svgImageView.image = svg.uiImage
        let bounds = svg.caLayerTree.shapeContentBounds!
        self.viewStickerContainer.addSubview(svgImageView)
        
        let scale = mapItem != nil ? mapItem!.H / (self.mapData?.H ?? 1) : 0.5
        svgImageView.snp.makeConstraints { make in
            make.width.equalTo(svgImageView.snp.height)
            make.height.equalTo(self.viewStickerContainer.snp.height).multipliedBy(CGFloat(scale) * svg.size.height / bounds.height)
            make.centerX.equalToSuperview().offset(x - self.viewStickerContainer.bounds.width / 2)
            make.centerY.equalToSuperview().offset(y - self.viewStickerContainer.bounds.height / 2)
        }
        
        let itemScale: CGFloat = 0.85
        svgImageView.transform = CGAffineTransform(scaleX: itemScale, y: itemScale)
        
        DataManager.shared.runRealmTransaction { _ in
            sticker.locationX = Float(x / self.imageContainer.bounds.width)
            sticker.locationY = Float(y / self.imageContainer.bounds.height)
        }
        svgImageView.tag = sticker.hashValue
    }
}

// MARK: - UIDragInteractionDelegate
extension GalleryViewController: UIDragInteractionDelegate {
    func dragInteraction(_ interaction: UIDragInteraction, itemsForBeginning session: UIDragSession) -> [UIDragItem] {
        guard let view = interaction.view else { return [] }
        let item = UIDragItem(itemProvider: NSItemProvider(object: NSString(string: "\(pack?.folder ?? "")/\(map ?? "")")))
        item.localObject = view
        return [item]
    }
}

// MARK: - UIDropInteractionDelegate
extension GalleryViewController: UIDropInteractionDelegate {
    func dropInteraction(_ interaction: UIDropInteraction, canHandle session: UIDropSession) -> Bool {
        return session.canLoadObjects(ofClass: NSString.self)
    }
    
    func dropInteraction(_ interaction: UIDropInteraction, sessionDidUpdate session: UIDropSession) -> UIDropProposal {
        return UIDropProposal(operation: .copy)
    }
    
    func dropInteraction(_ interaction: UIDropInteraction, performDrop session: UIDropSession) {
        guard let view = session.localDragSession?.localContext as? UIView,
              let item = view.tag as? Item else { return }
        let location = session.location(in: viewStickerContainer)
        if let sticker = getAvailableSticker(item: item) {
            addItemAtPosition(sticker: sticker, item: item, x: location.x, y: location.y)
            reloadData()
        }
    }
}

// MARK: - ItemHomeAdapter
extension GalleryViewController: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    
    
    func reloadData() {
        recyclerView.reloadData()
    }
    
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return packWithCurrentMap?.items.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ItemStickerCell", for: indexPath) as! ItemStickerCell
        if let item = packWithCurrentMap?.items[indexPath.item] {
            cell.mapColor = mapColor
            cell.configure(with: item, viewController: self)
        }
        return cell
    }
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let height = collectionView.frame.height
        let width = height
        return CGSize(width: width, height: height)
    }
}

// MARK: - ItemStickerCell
class ItemStickerCell: UICollectionViewCell {
    private let viewRatio = UIView()
    private let imageView = SVGImageView(frame: .zero)
    private let viewStickerNumber = UIImageView()
    private let textStickerNumber = AutosizeLabel()
    private var item: Item?
    private var unUsedCount: Int = 0
    private var unlocked: Bool = false
    var mapColor: String?
    private weak var viewController: GalleryViewController?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    static var gallery_bg_sticker_number = Utilities.SVGImage(named: "gallery_bg_sticker_number").withRenderingMode(.alwaysTemplate)
    private func setupUI() {
        contentView.backgroundColor = UIColor(hex: "#F00")
        
        // viewRatio
        
        
        contentView.addSubview(viewRatio)
        contentView.backgroundColor = .clear
        viewRatio.makeViewCenterAndKeep(ratio: 1.1)
        
        
        // Background
        let backgroundView = UIImageView()
        backgroundView.isUserInteractionEnabled = true
        backgroundView.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        viewRatio.addSubview(backgroundView)
        backgroundView.makeViewCenterAndKeep(ratio: 1)
        
        
        // imageView
        imageView.contentMode = .scaleAspectFit
        viewRatio.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.height.equalTo(viewRatio.snp.height).multipliedBy(0.9)
            make.width.equalTo(imageView.snp.height)
            make.center.equalToSuperview()
        }
        
        // viewStickerNumber
        viewStickerNumber.image = Self.gallery_bg_sticker_number
        viewRatio.addSubview(viewStickerNumber)
        viewStickerNumber.snp.makeConstraints { make in
            make.height.equalTo(viewRatio.snp.height).multipliedBy(0.3)
            make.width.equalTo(viewStickerNumber.snp.height)
            make.right.top.equalToSuperview()
        }
        
        // textStickerNumber
        textStickerNumber.text = "2"
        textStickerNumber.font = .Freude(size: 20)
        textStickerNumber.textColor = .white
        textStickerNumber.textAlignment = .center
        viewStickerNumber.addSubview(textStickerNumber)
        textStickerNumber.snp.makeConstraints { make in
            make.left.right.centerY.equalToSuperview().inset(5)
            make.height.equalToSuperview().multipliedBy(0.7)
        }
        
        // Tap and long press
        let tap = UITapGestureRecognizer(target: self, action: #selector(itemTapped))
        contentView.addGestureRecognizer(tap)
        let longPress = UILongPressGestureRecognizer(target: self, action: #selector(startDrag))
        contentView.addGestureRecognizer(longPress)
        contentView.isUserInteractionEnabled = true
    }
    
    func configure(with item: Item, viewController: GalleryViewController?) {
        self.item = item
        self.viewController = viewController
        update()
        
        guard let pack = viewController?.pack else { return }
        if unlocked {
            imageView.customTintColor = nil
        } else {
            let mapColor = UIColor(hex: pack.mapColor!)
            imageView.customTintColor = mapColor
        }
        imageView.SVGName = unlocked ? "topics/\(pack.folder)/\(item.path!)" : "topics/\(pack.folder)/shadow/\(item.path!)"
        if let mapColor = mapColor {
            viewStickerNumber.tintColor = .color(hex: mapColor)
        }
        textStickerNumber.text = "\(unUsedCount)"
        viewStickerNumber.isHidden = unUsedCount == 0
        contentView.alpha = unlocked && unUsedCount == 0 ? 0.5 : 1
        contentView.tag = item.hashValue
        #if DEBUG
        //viewStickerNumber.isHidden = false
        #endif
    }
    
    private func update() {
        unUsedCount = 0
        unlocked = false
        guard let item = item, let uniqueStickers = viewController?.uniqueStickers else { return }
        if uniqueStickers.contains(where: { "\($0.sticker).svg" == item.path }) {
            unlocked = true
        }
        unUsedCount = viewController?.unUsedStickers.filter { "\($0.sticker).svg" == item.path }.count ?? 0
        #if DEBUG
        //unlocked = false
        #endif
    }
    
    @objc private func itemTapped() {
        guard let item = item, let viewController = viewController else { return }
        if let sticker = viewController.getAvailableSticker(item: item) {
            textStickerNumber.text = "\(unUsedCount - 1)"
            viewStickerNumber.isHidden = (unUsedCount - 1) == 0
            contentView.alpha = (unUsedCount - 1) == 0 ? 0.5 : 1
            unUsedCount -= 1
            let center = viewController.scrollView.center
            viewController.addItemAtPosition(sticker: sticker, item: item, x: center.x + viewController.scrollView.contentOffset.x, y: center.y)
        } else if unlocked && unUsedCount == 0 {
            viewController.playSound("vi/popup_becodongydung", "vi/topics/Numbers/\(CoinManager.PRICE_STICKER)", "vi/popup_xu_sticker")
            let dialog = MessageDialogView()
                .setTitle("Mua sticker")
                .setMessage("Bé có đồng ý dùng \(CoinManager.PRICE_STICKER) xu để mua sticker này không?")
                .setSvgLogoPath("topics/\(viewController.pack?.folder ?? "")/\(item.path!)")
                .setButtonLaterText("Để sau")
                .setButtonOkText("Mua")
                .setShowLaterButton(true)
                .setShowOkButton(true)
                .setListener { buttonIndex in
                    if buttonIndex == .ok {
                        viewController.scheduler.clearAll()
                        if CoinManager.shared.useCoin(CoinManager.PRICE_STICKER) {
                            DataManager.shared.runRealmTransaction { _ in
                                if let map = viewController.map {
                                    GalleryManager.shared.addStickerToMap(map: map, folder: viewController.pack?.folder ?? "", sticker: item.name.en!, vocab: false)
                                }
                            }
                            viewController.reloadAllStickers()
                            self.unUsedCount += 1
                            viewController.reloadData()
                            viewController.playSound("effect/unlock item")
                            viewController.scheduler.schedule(after: 1) {
                                // Animate coin removal
                            }
                        } else {
                            viewController.playSound("vi/popup_khongdu_sticker")
                            MessageDialogView()
                                .setTitle("Không đủ xu")
                                .setMessage("Bé không đủ xu để mua sticker này")
                                .setSvgLogoPath("topics/\(viewController.pack?.folder ?? "")/\(item.path!)")
                                .showIn(viewController.view)
                        }
                    }
                }
                .showIn(viewController.view)
        } else if !unlocked {
            viewController.playSound("vi/popup_besenhanstickerkhi")
            MessageDialogView()
                .setTitle("Chưa mở khóa")
                .setMessage("Bé sẽ nhận được sticker này khi hoàn thành bài nhận biết")
                .setSvgLogoPath("topics/\(viewController.pack?.folder ?? "")/shadow/\(item.path!)")
                .setSvgLogoColor(UIColor(hex: viewController.pack?.mapColor ?? "#000000"))
                .showIn(viewController.view)
        }
    }
    
    @objc private func startDrag(_ gesture: UILongPressGestureRecognizer) {
        guard gesture.state == .began, unlocked, unUsedCount > 0 else { return }
        viewStickerNumber.alpha = 0
        UIView.animate(withDuration: 0, animations: {
            self.imageView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }, completion: { _ in
            // Start drag
            let dragInteraction = UIDragInteraction(delegate: self.viewController!)
            self.contentView.addInteraction(dragInteraction)
            self.imageView.transform = .identity
            self.viewStickerNumber.alpha = 1
        })
    }
}

import StoreKit
import UIKit

class ReviewHelper {
    // MARK: - Constants
    private static let PREF_NAME = "review_prefs"
    private static let KEY_REVIEWED = "is_reviewed"
    private static let KEY_GAME_COUNT = "game_count"
    
    // MARK: - Properties
    private let defaults: UserDefaults
    private weak var context: UIViewController?
    
    // MARK: - Initialization
    init(context: UIViewController) {
        self.context = context
        self.defaults = UserDefaults(suiteName: ReviewHelper.PREF_NAME) ?? UserDefaults.standard
    }
    
    // MARK: - Public Methods
    func isStoreReviewDone() -> Bool {
        return defaults.bool(forKey: ReviewHelper.KEY_REVIEWED)
    }
    
    func showReviewIfNeeded() {
        guard let profile = DataManager.shared.currentProfile,
              profile.reviewed5star != true else { return }
        
        if AppSettings.isUserInVietnam {
            DispatchQueue.main.asyncAfter(deadline: .now() + 2) { [weak self] in
                guard let self = self, let context = self.context else { return }
                let dialog = RateDialogViewController()
                //dialog.show(context)
            }
        }
    }
    
    func showAndroidReview(actionDone: @escaping () -> Void) {
        guard !isStoreReviewDone(), let context = context else {
            actionDone()
            return
        }
        
        if #available(iOS 14.0, *) {
            if let windowScene = UIApplication.shared.connectedScenes
                .filter({ $0.activationState == .foregroundActive })
                .compactMap({ $0 as? UIWindowScene })
                .first {
                SKStoreReviewController.requestReview(in: windowScene)
                markReviewed()
                actionDone()
            } else {
                actionDone()
            }
        } else {
            SKStoreReviewController.requestReview()
            markReviewed()
            actionDone()
        }
    }
    
    func onExitStickerAlbum() {
        showReviewIfNeeded()
    }
    
    func onChallengeCompleted() {
        showReviewIfNeeded()
    }
    
    func onGameEnd() {
        let count = defaults.integer(forKey: ReviewHelper.KEY_GAME_COUNT) + 1
        defaults.set(count, forKey: ReviewHelper.KEY_GAME_COUNT)
        if count == 4 {
            showReviewIfNeeded()
        }
    }
    
    func onAppStart() {
        defaults.set(0, forKey: ReviewHelper.KEY_GAME_COUNT)
    }
    
    func markReviewed() {
        defaults.set(true, forKey: ReviewHelper.KEY_REVIEWED)
    }
    
    func markReviewedForProfile() {
        DataManager.shared.runRealmTransaction { _ in
            DataManager.shared.currentProfile?.reviewed5star = true
            FirebaseUtils.shared.updateReviewed5star()
        }
    }
}

extension GalleryViewController : UIGestureRecognizerDelegate {
    // UIGestureRecognizerDelegate
    func gestureRecognizerShouldBegin(_ gestureRecognizer: UIGestureRecognizer) -> Bool {
        guard gestureRecognizer is UIPanGestureRecognizer else { return true }
        let location = gestureRecognizer.location(in: viewStickerContainer)
        return findViewUnder(x: location.x, y: location.y) != nil
    }
    
    func gestureRecognizer(_ gestureRecognizer: UIGestureRecognizer, shouldRecognizeSimultaneouslyWith otherGestureRecognizer: UIGestureRecognizer) -> Bool {
        // Không cho phép pan gesture và scroll view gesture đồng thời
        return false
    }
}
