//
//  tuduy_list_manhtamgiac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_manhtamgiac: NhanBietGameFragment {
    // MARK: - Properties
    private var tamgiac1: UIImageView!
    private var tamgiac2: UIImageView!
    private var tamgiac3: UIImageView!
    private var tamgiac4: UIImageView!
    private var tamgiac5: UIImageView!
    private let colors: [UIColor] = [
        UIColor(red: 255/255, green: 119/255, blue: 97/255, alpha: 1), // #FF7761
        UIColor(red: 136/255, green: 215/255, blue: 88/255, alpha: 1), // #88D758
        UIColor(red: 255/255, green: 186/255, blue: 0/255, alpha: 1) // #FFBA00
    ]
    private var meIndex: Int = 0
    private var viewCenter: UIView!
    private var viewLeft: UIView!
    private var rotation: Int = 0
    private var tamgiac60: UIImageView!
    private var tamgiac180: UIImageView!
    private var tamgiac300: UIImageView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        
        let viewPadding = UIView()
        view.addSubviewWithPercentInset(subview: viewPadding, percentInset: 5)
        viewCenter = UIView()
        viewCenter.clipsToBounds = false
        viewPadding.addSubview(viewCenter)
        viewCenter.makeViewCenterAndKeep(ratio: 2.2)
        
        viewLeft = UIView()
        viewLeft.clipsToBounds = false
        viewCenter.addSubview(viewLeft)
        viewLeft.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(viewLeft.snp.height)
        }
        
        tamgiac1 = UIImageView()
        tamgiac1.contentMode = .scaleAspectFit
        tamgiac1.backgroundColor = .clear
        viewLeft.addSubview(tamgiac1)
        tamgiac1.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.tamgiac1.snapToVerticalBias(verticalBias: 0.07)
        }
        
        tamgiac2 = UIImageView()
        tamgiac2.contentMode = .scaleAspectFit
        tamgiac2.backgroundColor = .clear
        viewLeft.addSubview(tamgiac2)
        tamgiac2.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
        }
        addActionOnLayoutSubviews {
            self.tamgiac2.snapToHorizontalBias(horizontalBias: 0)
            self.tamgiac2.snapToVerticalBias(verticalBias: 0.935)
        }
        
        tamgiac3 = UIImageView()
        tamgiac3.contentMode = .scaleAspectFit
        tamgiac3.backgroundColor = .clear
        viewLeft.addSubview(tamgiac3)
        tamgiac3.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
        }
        addActionOnLayoutSubviews {
            self.tamgiac3.snapToHorizontalBias(horizontalBias: 1)
            self.tamgiac3.snapToVerticalBias(verticalBias: 0.935)
        }
        
        tamgiac60 = UIImageView()
        tamgiac60.contentMode = .scaleAspectFit
        tamgiac60.backgroundColor = .clear
        tamgiac60.isHidden = true
        viewLeft.addSubview(tamgiac60)
        tamgiac60.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
        }
        addActionOnLayoutSubviews {
            self.tamgiac60.snapToHorizontalBias(horizontalBias: 0.625)
            self.tamgiac60.snapToVerticalBias(verticalBias: 0.72)
        }
        
        tamgiac180 = UIImageView()
        tamgiac180.contentMode = .scaleAspectFit
        tamgiac180.backgroundColor = .clear
        tamgiac180.isHidden = true
        viewLeft.addSubview(tamgiac180)
        tamgiac180.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
        }
        addActionOnLayoutSubviews {
            self.tamgiac180.snapToHorizontalBias(horizontalBias: 0.499)
            self.tamgiac180.snapToVerticalBias(verticalBias: 0.93)
        }
        
        tamgiac300 = UIImageView()
        tamgiac300.contentMode = .scaleAspectFit
        tamgiac300.backgroundColor = .clear
        tamgiac300.isHidden = true
        viewLeft.addSubview(tamgiac300)
        tamgiac300.snp.makeConstraints { make in
            make.width.equalTo(viewLeft).multipliedBy(0.5)
            make.height.equalTo(viewLeft).multipliedBy(0.5)
        }
        addActionOnLayoutSubviews {
            self.tamgiac300.snapToHorizontalBias(horizontalBias: 0.374)
            self.tamgiac300.snapToVerticalBias(verticalBias: 0.72)
        }
        
        let rightContainer = UIView()
        rightContainer.clipsToBounds = false
        viewCenter.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(rightContainer.snp.height).multipliedBy(1.1) // Ratio 1.1
            make.top.bottom.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            rightContainer.snapToHorizontalBias(horizontalBias: 0.96)
        }
        
        tamgiac4 = UIImageView()
        tamgiac4.contentMode = .scaleAspectFit
        tamgiac4.backgroundColor = .clear
        tamgiac4.tag = 0
        rightContainer.addSubview(tamgiac4)
        tamgiac4.snp.makeConstraints { make in
            make.height.equalTo(rightContainer).multipliedBy(0.5)
            make.height.equalTo(tamgiac4.snp.width)
            make.centerY.equalToSuperview()
            make.left.equalToSuperview()
        }
                
        tamgiac5 = UIImageView()
        tamgiac5.contentMode = .scaleAspectFit
        tamgiac5.backgroundColor = .clear
        tamgiac5.tag = 1
        rightContainer.addSubview(tamgiac5)
        tamgiac5.snp.makeConstraints { make in
            make.height.equalTo(rightContainer).multipliedBy(0.5)
            make.height.equalTo(tamgiac5.snp.width)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
        }
        
        let tap4 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        tamgiac4.addGestureRecognizer(tap4)
        tamgiac4.isUserInteractionEnabled = true
        //AnimationUtils.setTouchEffect(view: tamgiac4)
        
        let tap5 = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        tamgiac5.addGestureRecognizer(tap5)
        tamgiac5.isUserInteractionEnabled = true
        //AnimationUtils.setTouchEffect(view: tamgiac5)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    override func createGame() {
        super.createGame()
        viewLeft.moveToCenter(of: viewCenter, duration: 0)
        
        tamgiac4.transform = CGAffineTransform(scaleX: 0.05, y: 0.05)
        tamgiac4.alpha = 0.001
        tamgiac5.transform = CGAffineTransform(scaleX: 0.05, y: 0.05)
        tamgiac5.alpha = 0.001
        
        let colors = self.colors.shuffled()
        var svg = Utilities.GetSVGKImage(named: "tuduy_tamgiac3cham")
        svg.caLayerTree.sublayers?[1].setFillColor(color: colors[0].cgColor)
        let a = Int.random(in: 1...2)
        let b = 3 - a
        svg.caLayerTree.sublayers?[2].setFillColor(color: colors[a].cgColor)
        svg.caLayerTree.sublayers?[3].setFillColor(color: colors[b].cgColor)
        tamgiac1.image = svg.uiImage
        
        svg.caLayerTree.sublayers?[2].setFillColor(color: colors[1].cgColor)
        let a2 = Int.random(in: 0...2)
        let b2 = 2 - a2
        svg.caLayerTree.sublayers?[3].setFillColor(color: colors[a2].cgColor)
        svg.caLayerTree.sublayers?[1].setFillColor(color: colors[b2].cgColor)
        tamgiac2.image = svg.uiImage
        
        svg.caLayerTree.sublayers?[3].setFillColor(color: colors[2].cgColor)
        let a3 = Int.random(in: 0...1)
        let b3 = 1 - a3
        svg.caLayerTree.sublayers?[1].setFillColor(color: colors[a3].cgColor)
        svg.caLayerTree.sublayers?[2].setFillColor(color: colors[b3].cgColor)
        tamgiac3.image = svg.uiImage
        
        meIndex = Int.random(in: 0...1)
        var colors2 = meIndex == 0 ? colors : [colors[2], colors[1], colors[0]]
        let count2 = random(1, 2, 3)
        for _ in 0..<count2 {
            let tmp = colors2.removeFirst()
            colors2.append(tmp)
        }
        svg.caLayerTree.sublayers?[1].setFillColor(color: colors2[0].cgColor)
        svg.caLayerTree.sublayers?[2].setFillColor(color: colors2[1].cgColor)
        svg.caLayerTree.sublayers?[3].setFillColor(color: colors2[2].cgColor)
        
        let tamgiac4b = UIImageView()
        tamgiac4b.image = svg.uiImage
        tamgiac4.addSubviewWithInset(subview: tamgiac4b, inset: 0)
        
        
        var colors3 = meIndex == 1 ? colors : [colors[2], colors[1], colors[0]]
        let count3 = random(1, 2, 3)
        for _ in 0..<count3 {
            let tmp = colors3.removeFirst()
            colors3.append(tmp)
        }
        svg.caLayerTree.sublayers?[1].setFillColor(color: colors3[0].cgColor)
        svg.caLayerTree.sublayers?[2].setFillColor(color: colors3[1].cgColor)
        svg.caLayerTree.sublayers?[3].setFillColor(color: colors3[2].cgColor)
        
        let tamgiac5b = UIImageView()
        tamgiac5b.image = svg.uiImage
        tamgiac5.addSubviewWithInset(subview: tamgiac5b, inset: 0)
        
        let delta = meIndex == 0 ? colors.firstIndex(of: colors2[0])! : colors.firstIndex(of: colors3[0])!
        rotation = delta == 1 ? 60 : delta == 2 ? -60 : 180
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_manh tam giac")
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            scheduler.schedule(delay: 1.5) { self.startGame() }
            UIView.animate(withDuration: 0.2) {
                self.viewLeft.transform = .identity
            }
            UIView.animate(withDuration: 0.6, delay: 1.0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self.tamgiac4.transform = .identity
                self.tamgiac4.alpha = 1
            })
            UIView.animate(withDuration: 0.6, delay: 1.5, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self.tamgiac5.transform = .identity
                self.tamgiac5.alpha = 1
            })
            playSound(name: "effect/bubble1", delay: 0.5)
            playSound(name: "effect/bubble2", delay: 1.0)
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_manh tam giac")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        pauseGame()
        let tag = view.tag
        if tag == meIndex {
            animateCoinIfCorrect(view: view)
            var delay = playSound("effect/answer_end")
            if rotation == 180 {
                view.moveToCenter(of: self.tamgiac180)
                UIView.animate(withDuration: 0.5) {
                    //view.frame = self.tamgiac180.frame
                    view.subviews[0].transform = CGAffineTransform(rotationAngle: CGFloat(self.rotation) * .pi / 180)
                }
            } else if rotation == 60 {
                view.moveToCenter(of: self.tamgiac60, duration: 0.5)
                UIView.animate(withDuration: 0.5) {
                    //view.frame = self.tamgiac60.frame
                    view.subviews[0].transform = CGAffineTransform(rotationAngle: CGFloat(self.rotation) * .pi / 180)
                }
            } else if rotation == -60 {
                view.moveToCenter(of: self.tamgiac300, duration: 0.5)
                UIView.animate(withDuration: 0.5) {
                    //view.frame = self.tamgiac300.frame
                    view.subviews[0].transform = CGAffineTransform(rotationAngle: CGFloat(self.rotation) * .pi / 180)
                }
            }
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(delay: delay + 1.0) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

extension CALayer {
    func setFillColor(color: UIColor) {
        setFillColor(color: color.cgColor)
    }
    func setFillColor(color: CGColor) {
        if let layer = self as? CAShapeLayer {
            layer.fillColor = color
        }
    }
    func getFillColor() -> CGColor? {
        if let layer = self as? CAShapeLayer {
            return layer.fillColor
        }
        return nil
    }
}
