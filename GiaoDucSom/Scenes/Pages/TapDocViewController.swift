//
//  TapDocViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import AVFAudio
import AnyCodable

class TapDocViewController: TwoClickExitActivity {
    // MARK: - Properties
    private var gameIndex: Int = 0
    //private let random = RandomNumberGenerator()
    private var oldCoin: Int = 0
    var name: String?
    private var scores: [Float] = []
    var thumb: String?
    var kind: String?
    var letters: [String]?
    private var games: [Animation] = []
      
        // MARK: - Static Methods
    static func createSoundGame(letters: [String]) -> [Animation] {
        var games: [Animation] = []
        guard let letter = letters.first?.lowercased()
             else { return games }
        let LETTER = letter.uppercased()
        var items: [Item] = []
        var folders: [String] = []
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            for item in pack.items {
                if let letterVi = item.letterVi, letterVi.contains(letter) {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
        }
        guard items.count >= 4 else { return games }
        let chooseIndices = Utils.generatePermutation( 4, size: items.count)
        
        // Game: Sound
        var game = Animation()
        game.type = .game
        game.game = .sound
        game.value1 = "\(letter)\n\(LETTER)"
        game.text = "second#@letter#second#@letter#second#đây là âm#@letter#second#second#chúng mình cùng đọc to nào#second#@letter2#second#@letter2"
            .replacingOccurrences(of: "@letter2", with: "\(letter)2")
            .replacingOccurrences(of: "@letter1", with: "\(letter)1")
            .replacingOccurrences(of: "@letter", with: letter)
        games.append(game)
        
        // Random game (bee, crab, or balloon)
        let randomGame = random(0, 1, 2)
        let previous = previousLetter(letter: letter)
        if randomGame == 0 {
            game = Animation()
            game.type = .game
            game.game = .bee
            game.value = "\(letter),\(previous ?? "")"
            game.text = "bé hãy chọn tất cả những chú ong có mang âm#\(letter)"
            games.append(game)
        } else if randomGame == 1 {
            game = Animation()
            game.type = .game
            game.game = .crab
            game.value = "\(letter),\(previous ?? "")"
            game.text = "bé hãy chọn tất cả những chú cua có mang âm#\(letter)"
            games.append(game)
        } else {
            game = Animation()
            game.type = .game
            game.game = .balloon
            game.value = "\(letter),\(previous ?? "")"
            game.text = "bé hãy đập vỡ tất cả quả bóng có âm#\(letter)"
            games.append(game)
        }
        
        // Game: Stickers
        game = Animation()
        game.type = .game
        game.game = .stickers
        game.text = "bé hãy kéo tất cả các hình vào đúng tên của chúng"
        game.values = chooseIndices.map { items[$0].name.vi }.map {AnyCodable($0)}
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        // Game: Listen and Check
        game = Animation()
        game.type = .game
        game.game = .listen_and_check
        game.text = "bé hãy lắng nghe và chọn đúng hình"
        game.values = chooseIndices.map {AnyCodable(items[$0].name.vi!)}
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        // Game: Read the Word
        game = Animation()
        game.type = .game
        game.game = .read_the_word
        game.text = "bé hãy chọn đúng hình có ghi tên bên dưới"
        game.values = chooseIndices.map { AnyCodable(items[$0].name.vi!) }
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        // Game: Read and Check
        game = Animation()
        game.type = .game
        game.game = .read_and_check
        game.text = "bé hãy chọn đúng tên của hình bên cạnh"
        game.values = chooseIndices.map { AnyCodable(items[$0].name.vi!) }
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        return games
    }
    
    static func previousLetter(letter: String) -> String? {
        if letter.lowercased() == "a" {
            return "b"
        }
        let alphabets: [[String]] = [
            ["a", "ă", "â"], ["b", "c"], ["d", "đ"], ["e", "ê"],
            ["g", "h"], ["i", "k"], ["l", "m", "n"], ["o", "ô", "ơ"],
            ["p", "q"], ["r", "s", "t"], ["u", "ư"], ["v", "x", "y"]
        ]
        for (i, group) in alphabets.enumerated() {
            for (j, char) in group.enumerated() {
                if char == letter {
                    if j == 0 {
                        return i == 0 ? nil : alphabets[i - 1].last
                    } else {
                        return group[j - 1]
                    }
                }
            }
        }
        return nil
    }
    
    static func createLetterGame(letters: [String]) -> [Animation] {
        var games: [Animation] = []
        guard let letter = letters.first?.lowercased()
               else { return games }
        let LETTER = letter.uppercased()
        var items: [Item] = []
        var folders: [String] = []
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            for item in pack.items {
                if let letterVi = item.letterVi, letterVi.contains(letter) {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
        }
        guard items.count >= 4 else { return games }
        let chooseIndices = Utils.generatePermutation(4, size: items.count)
        
        // Game: Letter Intro
        var game = Animation()
        game.type = .game
        game.game = .letter_intro
        game.values = [AnyCodable(letter), AnyCodable(LETTER)]
        game.text = "đây là chữ#@letter1#second#đọc là#@letter#second#second#chữ#@letter1#@#có hai cách viết#second#second#@#@letter1#thường#second#và#@#@letter1#hoa"
            .replacingOccurrences(of: "@letter2", with: "\(letter)2")
            .replacingOccurrences(of: "@letter1", with: "\(letter)1")
            .replacingOccurrences(of: "@letter", with: letter)
        games.append(game)
        
        // Random game (bee, crab, or balloon)
        let randomGame = random(0, 1, 2)        
        let previous = previousLetter(letter: letter)
        if randomGame == 0 {
            game = Animation()
            game.type = .game
            game.game = .bee
            game.value = "\(letter)1,\(previous ?? "")1"
            game.text = "bé hãy chọn tất cả những chú ong có mang chữ#@letter1".replacingOccurrences(of: "@letter", with: letter)
            games.append(game)
        } else if randomGame == 1 {
            game = Animation()
            game.type = .game
            game.game = .crab
            game.value = "\(letter)1,\(previous ?? "")1"
            game.text = "bé hãy chọn tất cả những chú cua có mang chữ#@letter1".replacingOccurrences(of: "@letter", with: letter)
            games.append(game)
        } else {
            game = Animation()
            game.type = .game
            game.game = .balloon
            game.value = "\(letter)1,\(previous ?? "")1"
            game.text = "bé hãy đập vỡ tất cả quả bóng có chữ#@letter1".replacingOccurrences(of: "@letter", with: letter)
            games.append(game)
        }
        
        // Game: Drag the Letter
        game = Animation()
        game.type = .game
        game.game = .drag_the_letter
        game.value = letter
        game.text = "bé hãy kéo chữ#@letter1#vào hình".replacingOccurrences(of: "@letter", with: letter)
        game.values = chooseIndices.map { AnyCodable(items[$0].name.vi!) }
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        #if DEBUG
        //games.insert(game, at: 0) // Thêm game đầu tiên để dễ debug
        #endif
        
        // Game: Word Puzzle
        game = Animation()
        game.type = .game
        game.game = .word_puzzle
        game.value = letter
        game.text = "bé hãy di chuyển các chữ cái để tạo thành từ#@".replacingOccurrences(of: "@letter", with: letter)
        game.values = chooseIndices.compactMap { items[$0].name.vi!.contains(" ") ? nil : items[$0].name.vi! }.map {AnyCodable($0)}
        game.paths = chooseIndices.compactMap { items[$0].name.vi!.contains(" ") ? nil : "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.compactMap { items[$0].name.vi!.contains(" ") ? nil : "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        if !game.values!.isEmpty {
            games.append(game)
        }
        
        // Game: Unscramble
        game = Animation()
        game.type = .game
        game.game = .unscramble
        game.value = letter
        game.text = "bé hãy sắp xếp lại các chữ cái cho đúng thứ tự".replacingOccurrences(of: "@letter", with: letter)
        game.values = chooseIndices.prefix(2).compactMap { items[$0].name.vi!.contains(" ") ? nil : items[$0].name.vi }.map{AnyCodable($0)}
        game.paths = chooseIndices.prefix(2).compactMap { items[$0].name.vi!.contains(" ") ? nil : "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.prefix(2).compactMap { items[$0].name.vi!.contains(" ") ? nil : "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        if !game.values!.isEmpty {
            games.append(game)
        }
        
        // Game: Memory
        game = Animation()
        game.type = .game
        game.game = .memory
        game.gamedatatype = "picture"
        game.value = letter
        game.text = "bé hãy lật các thẻ có hình giống nhau".replacingOccurrences(of: "@letter", with: letter)
        game.values = chooseIndices.map {AnyCodable( items[$0].name.vi!) }
        game.paths = chooseIndices.map { "topics/\(folders[$0])/\(items[$0].path!)" }
        game.sounds = chooseIndices.map { "vi/topics/\(folders[$0])/\(items[$0].path!.replacingOccurrences(of: ".svg", with: ""))" }
        games.append(game)
        
        return games
    }
    
    static func haveCommonItem(list1: [String], list2: [String]) -> Bool {
        return !Set(list1).isDisjoint(with: list2)
    }
    
    static func createPracticeGame(letters: [String]) -> [Animation] {
        var games: [Animation] = []
        var mutableLetters = letters
        let originalLetterSize = letters.count
        if letters.count < 3 {
            if let previous = previousLetter(letter: letters[0]) {
                mutableLetters.append(previous)
            }
        }
        
        var items: [Item] = []
        var folders: [String] = []
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            for item in pack.items {
                if let letterVi = item.letterVi, haveCommonItem(list1: letterVi, list2: mutableLetters), countLetter(text: item.name.vi!, letters: mutableLetters) == 1 {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
        }
        guard !items.isEmpty else { return games }
        
        var randomIndices: [Int] = []
        var originItemSize: Int = 0
        var chooseIndices: [Int] = []
        var chooseLetters: [String] = []
        var tries = 0
        while tries < 1000 {
            tries += 1
            randomIndices = Utils.generatePermutation(items.count, size: items.count)
            chooseIndices = []
            chooseLetters = []
            for (i, letter) in mutableLetters.enumerated() {
                var count = 0
                for index in randomIndices {
                    let item = items[index]
                    if item.letterVi!.contains(letter) && vietnamese.removeSign(item.name.vi!).contains(letter) {
                        chooseIndices.append(index)
                        chooseLetters.append(letter)
                        count += 1
                        if count >= 3 { break }
                    }
                }
                if i == originalLetterSize - 1 {
                    originItemSize = chooseIndices.count
                }
            }
            let ok = (0..<originItemSize).contains { !items[chooseIndices[$0]].name.vi!.contains(" ") }
            if ok { break }
        }
        guard tries < 1000 else { return games }
        
        // Game: Basketball
        var game = Animation()
        game.type = .game
        game.game = .basketball
        game.text = "bé hãy lắng nghe và lựa chọn đúng rổ để ném bóng"
        game.values = mutableLetters.map {AnyCodable($0)}
        games.append(game)
        
        // Game: Letter and Sound
        game = Animation()
        game.type = .game
        game.game = .letter_and_sound
        game.text = "đố bé chữ cái nào đọc là#@"
        game.values = mutableLetters.map {AnyCodable($0)}
        games.append(game)
        
        // Game: What Letter Is This
        game = Animation()
        game.type = .game
        game.game = .what_letter_is_this
        game.text = "bé hãy nối chữ thường với chữ hoa"
        game.values = mutableLetters.map { "\($0)1" }
        games.append(game)
        
        // Game: What Sound Is This
        game = Animation()
        game.type = .game
        game.game = .what_sound_is_this
        game.text1 = "bé hãy chọn đúng chữ viết hoa"
        game.text2 = "bé hãy chọn đúng chữ viết thường"
        game.values = mutableLetters.map {AnyCodable($0)}
        games.append(game)
        
        // Game: Memory
        game = Animation()
        game.type = .game
        game.game = .memory
        game.gamedatatype = "letter"
        game.text = "bé hãy lật các thẻ có chữ giống nhau"
        game.values = mutableLetters.map { AnyCodable("\($0)1") }
        game.sounds = mutableLetters.map { "\($0)1" }
        games.append(game)
        
        // Game: Can You Hear
        game = Animation()
        game.type = .game
        game.game = .can_you_hear
        game.text = "bé hãy chọn dấu tích nếu nghe thấy âm#@1#ở trong từ#@2#second#nếu không hãy chọn dấu x"
        game.values = (0..<originItemSize).compactMap { items[chooseIndices[$0]].name.vi!.contains(" ") ? nil : items[chooseIndices[$0]].name.vi } .map {AnyCodable($0)}
        game.paths = (0..<originItemSize).compactMap { items[chooseIndices[$0]].name.vi!.contains(" ") ? nil : "topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!)" }
        game.sounds = (0..<originItemSize).compactMap { items[chooseIndices[$0]].name.vi!.contains(" ") ? nil : "vi/topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!.replacingOccurrences(of: ".svg", with: ""))" }
        game.questions = min(3, games.count)
        game.values1 = mutableLetters.prefix(originalLetterSize).map { AnyCodable($0) }
        if !game.values!.isEmpty {
            games.append(game)
        }
        
        // Game: Listen For1
        game = Animation()
        game.type = .game
        game.game = .listen_for1
        game.text = "bé hãy chọn hình có tên chứa âm#@"
        game.values = (0..<originItemSize).map {
            let item = items[chooseIndices[$0]]
            let name = vietnamese.removeSign(item.name.vi!)
            let key = chooseLetters[$0]
            let index = name.firstIndex(of: key.first!)?.utf16Offset(in: name) ?? 0
            return "\(item.name.vi!.prefix(index))_\(item.name.vi!.dropFirst(index).prefix(key.count))_\(item.name.vi!.dropFirst(index + key.count))"
        }
        game.paths = (0..<originItemSize).map { "topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!)" }
        game.sounds = (0..<originItemSize).map { "vi/topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!.replacingOccurrences(of: ".svg", with: ""))" }
        game.questions = originalLetterSize
        game.values1 = mutableLetters.prefix(originalLetterSize).map { AnyCodable($0) }
        games.append(game)
        
        // Game: Listen For2
        game = Animation()
        game.type = .game
        game.game = .listen_for2
        game.text = "bé hãy đọc và chọn tên có chứa âm#@"
        game.values = (0..<originItemSize).map {
            let item = items[chooseIndices[$0]]
            let name = vietnamese.removeSign(item.name.vi!)
            let key = chooseLetters[$0]
            let index = name.firstIndex(of: key.first!)?.utf16Offset(in: name) ?? 0
            return "\(item.name.vi!.prefix(index))_\(item.name.vi!.dropFirst(index).prefix(key.count))_\(item.name.vi!.dropFirst(index + key.count))"
        }
        game.paths = (0..<originItemSize).map { "topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!)" }
        game.sounds = (0..<originItemSize).map { "vi/topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!.replacingOccurrences(of: ".svg", with: ""))" }
        game.questions = originalLetterSize
        game.values1 = mutableLetters.prefix(originalLetterSize).map { AnyCodable($0) }
        games.append(game)
        
        // Game: Missing Letter
        game = Animation()
        game.type = .game
        game.game = .missing_letter
        game.text = "bé hãy chọn đúng chữ còn thiếu trong từ trên"
        game.values = (0..<originItemSize).map { items[chooseIndices[$0]].name.vi! }.map{ AnyCodable($0)}
        game.paths = (0..<originItemSize).map { "topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!)" }
        game.sounds = (0..<originItemSize).map { "vi/topics/\(folders[chooseIndices[$0]])/\(items[chooseIndices[$0]].path!.replacingOccurrences(of: ".svg", with: ""))" }
        game.questions = originalLetterSize
        game.values1 = mutableLetters.map { "\($0)1" }
        games.append(game)
        
        return games
    }
    
    static func countLetter(text: String, letters: [String]) -> Int {
        let normalizedText = vietnamese.removeSign(text)
        return letters.reduce(0) { count, letter in
            count + (normalizedText.contains(letter) ? 1 : 0)
        }
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = .white // Giả định từ activity_phonics.xml
        
        // Giả định hideStatusBar()
        navigationController?.setNavigationBarHidden(true, animated: false)
        
        initActivity()
        oldCoin = CoinManager.shared.getCoin()
    }
    
    // MARK: - Initialization
    private func initActivity() {
        //name = navigationController?.viewControllers.last?.restorationIdentifier
        //thumb = navigationController?.viewControllers.last?.restorationIdentifier
        //let kind = navigationController?.viewControllers.last?.restorationIdentifier
        //let letters = ["a"] // Giả định từ Intent extras, cần triển khai thực tế
        
        games = []
        switch kind?.lowercased() {
        case "sound":
            games = TapDocViewController.createSoundGame(letters: letters!)
        case "letter":
            games = TapDocViewController.createLetterGame(letters: letters!)
        case "practice":
            games = TapDocViewController.createPracticeGame(letters: letters!)
        default:
            break
        }
        
        loadGame(at: 0)
    }
    
    // MARK: - Game Management
    private func loadGame(at gameIndex: Int) {
        
        guard gameIndex < games.count else {                    
            let completeDialog = LessonCompleteDialog()
            completeDialog.onClosed = { [weak self] in
                guard let self = self else { return }
                let countedLessonComplete = TapDocManager.shared.countLessonComplete(name: name ?? "")
                if countedLessonComplete == 3 {
                    scheduler.schedule(after: 2.0) { [weak self] in
                        guard let self = self else { return }
                        /*
                        let hoc3LanDialog = Hoc3LanTapDocDialogFragment()
                        hoc3LanDialog.lessonName = name
                        hoc3LanDialog.thumb = thumb
                        hoc3LanDialog.isTapdoc = false
                        hoc3LanDialog.show(in: self.navigationController, tag: "hoc-3-lan")
                         */
                    }
                }
                self.dismiss(animated: true, completion: nil)
                self.navigationController?.popViewController(animated: true)
                /*
                guard let self = self else { return }
                let countedLessonComplete = TapDocManager.shared.countLessonComplete(name: self.name ?? "")
                if countedLessonComplete == 3 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                        let hoc3LanDialog = Hoc3LanTapDocDialogFragment()
                        hoc3LanDialog.setLessonName(self.name ?? "")
                        hoc3LanDialog.setThumb(self.thumb ?? "")
                        hoc3LanDialog.show(in: self)
                    }
                }
                self.navigationController?.popViewController(animated: true)
                 */
            }
            let sumScore = scores.reduce(0, +)
            let aveScore = scores.isEmpty ? 0 : sumScore / Float(scores.count)
            completeDialog.setScore(aveScore)
            
            var coinReward = CoinManager.shared.getCoin() - oldCoin
            if coinReward < 0 {
                coinReward = 0
            }
            completeDialog.setCoinReward(coinReward)
            completeDialog.setLanguage("vi")
            
            completeDialog.setupPopupPresentation()
            self.present(completeDialog, animated: false) {
                completeDialog.presentPopup()
            }
            
            TapDocManager.shared.logCompleteTapdoc(lessonName: name ?? "", score: Double(aveScore))
            #if DEBUG
            //self.navigationController?.popViewController(animated: true)
            #endif
            return
        }
        
        self.gameIndex = gameIndex
        let game = games[gameIndex]
        gameFragment = GameFragment.createGameFragment(by: game, tapdoc: true)
        
        (gameFragment as? GameFragment)?.setTapdoc(true)
        gameFragment?.onGameFragmentListener = self
        if let fragment = gameFragment {
            gameView!.removeAllSubviews()
            gameView!.addSubview(fragment)
            fragment.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }
         
    }
}
extension TapDocViewController: OnGameFragmentListener {
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        self.scores.append(gameFragment.score)
        self.loadGame(at: self.gameIndex + 1)
    }
    
    func onUpdateGameProgress(_ progress: Float) {
        
    }
}

import Foundation
import RealmSwift

class TapDocManager {
    // Thể hiện singleton
    static let shared = TapDocManager()
    
    private init() {
        // Khởi tạo riêng tư để đảm bảo singleton
    }
    
    func logCompleteTapdoc(lessonName: String, score: Double) {
        // Chuẩn hóa điểm số tối đa là 1.0
        let normalizedScore = min(score, 1.0)
        
        // Tạo đối tượng nhật ký
        let log = RealmLogCompleteTapdoc()
        log.lessonName = lessonName
        log.score = normalizedScore
        log.createdAt = Int64(Date().timeIntervalSince1970 * 1000)
        log.profileId = DataManager.shared.currentProfile!.id
        
        // Lưu vào Realm
        DataManager.shared.runRealmTransaction { realm in
            realm.add(log)
        }
        
        // Ghi nhật ký lên Firebase
        FirebaseUtils.shared.logCompleteTapdoc(log)
    }
    
    func getLogs() -> [RealmLogCompleteTapdoc] {
        // Lấy tất cả nhật ký từ Realm
        let realm = try! Realm()
        return Array(realm.objects(RealmLogCompleteTapdoc.self))
    }
    
    func countLessonComplete(name: String) -> Int {
        // Đếm số bài học đã hoàn thành theo tên và ID hồ sơ
        let realm = try! Realm()
        let profileId = DataManager.shared.currentProfile!.id
        return realm.objects(RealmLogCompleteTapdoc.self)
            .filter("lessonName == %@ AND profileId == %@", name, profileId)
            .count
    }
    
    func getCompleteTapdocs() -> [RealmLogCompleteTapdoc] {
        // Lấy tất cả nhật ký tapdoc theo ID hồ sơ
        let realm = try! Realm()
        let profileId = DataManager.shared.currentProfile!.id
        return Array(realm.objects(RealmLogCompleteTapdoc.self)
            .filter("profileId == %@", profileId))
    }
    
    func fromFirebase(tapdoc: FBTapdoc) {
        // Chuyển dữ liệu từ Firebase sang Realm
        let realm = try! Realm()
        let profileId = DataManager.shared.currentProfile!.id
        
        // Kiểm tra xem có nhật ký với thời gian createdAt tương ứng không
        let existingLogs = realm.objects(RealmLogCompleteTapdoc.self)
            .filter("createdAt == %@", tapdoc.createdAt)
        
        if existingLogs.isEmpty {
            let log = RealmLogCompleteTapdoc()
            log.lessonName = tapdoc.lessonId
            log.score = tapdoc.score
            log.createdAt = tapdoc.createdAt
            log.profileId = profileId
            
            // Lưu nhật ký mới vào Realm
            DataManager.shared.runRealmTransaction { realm in
                realm.add(log)
            }
        }
    }
    
    func removeFromFirebase(tapdoc: FBTapdoc) {
        // Xóa nhật ký từ Realm dựa trên dữ liệu Firebase
        let realm = try! Realm()
        let time = tapdoc.createdAt
        
        // Kiểm tra xem có nhật ký với thời gian createdAt tương ứng không
        let existingLogs = realm.objects(RealmLogCompleteTapdoc.self)
            .filter("createdAt == %@", time)
        
        if !existingLogs.isEmpty {
            // Xóa tất cả nhật ký khớp với thời gian
            DataManager.shared.runRealmTransaction { realm in
                realm.delete(existingLogs)
            }
        }
    }
}


import RealmSwift

class RealmLogCompleteTapdoc: Object {
    @Persisted var lessonName: String = ""
    @Persisted var score: Double = 0.0
    @Persisted var createdAt: Int64 = Int64(Date().timeIntervalSince1970 * 1000)
    @Persisted var profileId: String = ""
}


struct FBTapdoc : Decodable{
    let lessonId: String
    let score: Double
    let createdAt: Int64 // Assuming timestamp in milliseconds
    func toDictionary() -> [String: Any] {
        var dictionary: [String: Any] = [:]
        dictionary["lessonId"] = lessonId
        dictionary["score"] = score
        dictionary["createdAt"] = createdAt
        return dictionary
    }
}
