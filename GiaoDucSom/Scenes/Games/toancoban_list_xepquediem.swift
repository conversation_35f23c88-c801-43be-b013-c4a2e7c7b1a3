//
//  toancoban_list_xepquediem.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//
import UIKit
import SnapKit

class toancoban_list_xepquediem: NhanBietGameFragment {
    // MARK: - Properties
    private var item1: UIImageView!
    private var item2: UIView!
    private var subItem: UIView!
    private var mainItem: UIView!
    private var itemContainer: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var number: Int = 0
    private var allLayout: [Int] = []
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 238/255, green: 227/255, blue: 208/255, alpha: 1) // #EEE3D0
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        //itemContainer.backgroundColor = .green.withAlphaComponent(0.5)
        addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        mainItem = createItemXepDiem()
        itemContainer.addSubview(mainItem)
        mainItem.makeViewCenterAndKeep(ratio: 0.5)
        
        subItem = createItemXepDiem()
        subItem.viewWithTag(R2.id.item_background)?.backgroundColor = .clear
        itemContainer.addSubview(subItem)
        subItem.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.5)
            make.width.equalTo(subItem.snp.height).multipliedBy(0.5) // Ratio 0.5
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.subItem.snapToHorizontalBias(horizontalBias: 0.15)
        }
        
        let que1 = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que1.contentMode = .scaleAspectFit
        itemContainer.addSubview(que1)
        que1.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que1.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que1.snapToHorizontalBias(horizontalBias: 0.8)
            que1.snapToVerticalBias(verticalBias: 0.2)
        }
        
        let que2 = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que2.contentMode = .scaleAspectFit
        itemContainer.addSubview(que2)
        que2.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que2.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que2.snapToHorizontalBias(horizontalBias: 0.821)
            que2.snapToVerticalBias(verticalBias: 0.2)
        }
        
        let que3 = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que3.contentMode = .scaleAspectFit
        itemContainer.addSubview(que3)
        que3.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que3.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que3.snapToHorizontalBias(horizontalBias: 0.841)
            que3.snapToVerticalBias(verticalBias: 0.2)
        }
        
        let que4 = UIView()
        que4.contentMode = .scaleAspectFit
        //que4.transform = CGAffineTransform(rotationAngle: .pi / 2)
        itemContainer.addSubview(que4)
        let que4x = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que4.addSubviewWithInset(subview: que4x, inset: 0)
        que4x.transform = CGAffineTransformMakeRotation(.pi/2)
        que4.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que4.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que4.snapToHorizontalBias(horizontalBias: 0.821)
            que4.snapToVerticalBias(verticalBias: 1.0)
        }
        
        let que5 = UIView()
        que5.contentMode = .scaleAspectFit
        //que5.transform = CGAffineTransform(rotationAngle: .pi / 2)
        itemContainer.addSubview(que5)
        let que5x = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que5.addSubviewWithInset(subview: que5x, inset: 0)
        que5x.transform = CGAffineTransformMakeRotation(.pi/2)
        que5.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que5.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que5.snapToHorizontalBias(horizontalBias: 0.821)
            que5.snapToVerticalBias(verticalBias: 0.92)
        }
        
        let que6 = UIView()
        que6.contentMode = .scaleAspectFit
        //que6.transform = CGAffineTransform(rotationAngle: .pi / 2)
        itemContainer.addSubview(que6)
        let que6x = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        que6.addSubviewWithInset(subview: que6x, inset: 0)
        que6x.transform = CGAffineTransformMakeRotation(.pi/2)
        que6.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(que6.snp.height).multipliedBy(63.5 / 516.9) // Ratio 63.5:516.9
        }
        addActionOnLayoutSubviews {
            que6.snapToHorizontalBias(horizontalBias: 0.821)
            que6.snapToVerticalBias(verticalBias: 0.84)
        }
        
        item1 = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        item1.contentMode = .scaleAspectFit
        item1.alpha = 0.01
        item1.backgroundColor = UIColor.black.withAlphaComponent(0.0006) // #0f00
        itemContainer.addSubview(item1)
        item1.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.45)
            make.width.equalTo(item1.snp.height).multipliedBy(120.0 / 516.9) // Ratio 120:516.9
        }
        addActionOnLayoutSubviews {
            self.item1.snapToHorizontalBias(horizontalBias: 0.83)
            self.item1.snapToVerticalBias(verticalBias: 0.2)
        }
        
        item2 = UIView()
        item2.alpha = 0.01
        item2.backgroundColor = UIColor.black.withAlphaComponent(0.0006) // #0f00
        itemContainer.addSubview(item2)
        item2.snp.makeConstraints { make in
            make.height.equalTo(itemContainer).multipliedBy(0.11)
            make.width.equalTo(item2.snp.height).multipliedBy(516.9 / 120.0) // Ratio 516.9:120
        }
        addActionOnLayoutSubviews {
            self.item2.snapToHorizontalBias(horizontalBias: 0.91)
            self.item2.snapToVerticalBias(verticalBias: 0.76)
        }
        
        let queInItem2 = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
        queInItem2.contentMode = .scaleAspectFit
        queInItem2.transform = CGAffineTransform(rotationAngle: .pi / 2)
        item2.addSubview(queInItem2)
        queInItem2.snp.makeConstraints { make in
            make.width.equalTo(item2).multipliedBy(0.12)
            make.height.equalTo(queInItem2.snp.width).multipliedBy(516.9/63.5) // Ratio 63.5:516.9
            make.center.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.3)
        
        let itemPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleItemPan(_:)))
        itemContainer.addGestureRecognizer(itemPanGesture)
        
        let mainPanGesture = UIPanGestureRecognizer(target: self, action: #selector(handleMainPan(_:)))
        mainItem.addGestureRecognizer(mainPanGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        number = Int.random(in: 0..<10)
        allLayout = getNumberLayout(number: 8)
        let numberLayout = getNumberLayout(number: number)
        for layout in allLayout {
            subItem.viewWithTag(layout)?.isHidden = !numberLayout.contains(layout)
            mainItem.viewWithTag(layout)?.isHidden = true
        }
        let delay = playSound(openGameSound(), "toan/toan_xep_diem", "topics/Numbers/\(number)")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_xep_diem", "topics/Numbers/\(number)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    // MARK: - Touch Handling
    var originFrame: CGRect!
    @objc private func handleItemPan(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: itemContainer)
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
                currentView.alpha = 1
                playSound("effect/cungchoi_pick\(random(1,2))")
                originFrame = currentView.frame
                //Utils.vibrate(context: self)
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = max(0, min(location.x + dX, itemContainer.frame.width - currentView.frame.width))
                let newY = max(0, min(location.y + dY, itemContainer.frame.height - currentView.frame.height))
                currentView.frame = CGRect(x: newX, y: newY, width: currentView.frame.width, height: currentView.frame.height)
            }
            
        case .ended:
            if let currentView = currentView {
                //Utils.vibrate(context: self)
                let closeDistance = mainItem.frame.height / 10
                var found = false
                for id in allLayout {
                    if let item = mainItem.viewWithTag(id), item.isHidden, abs(item.frame.width - currentView.frame.width) < closeDistance {
                        let distance = item.distanceFromCenterToCenter(to: currentView)
                        if hypot(distance.x, distance.y) < closeDistance {
                            playSound("effect/word puzzle drop")
                            item.isHidden = false
                            item.alpha = 0.001
                            UIView.animate(withDuration: 0.2) {
                                item.alpha = 1
                            }
                            item.bringSubviewToFront(self)
                            found = true
                            UIView.animate(withDuration: 0.2) {
                                currentView.frame = item.frame
                            } completion: { _ in
                                currentView.transform = .identity
                                currentView.alpha = 0.01
                                self.checkFinish()
                            }
                            break
                        }
                    }
                }
                if !found {
                    playSound("effect/slide2")
                    UIView.animate(withDuration: 0.2) {
                        currentView.transform = .identity
                        currentView.frame = self.originFrame
                    }
                }
            }
            
        default:
            break
        }
    }
    
    @objc private func handleMainPan(_ gesture: UIPanGestureRecognizer) {
        let location = gesture.location(in: mainItem)
        switch gesture.state {
        case .began:
            currentView = findViewUnder2(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
                currentView.alpha = 1
                playSound("effect/cungchoi_pick\(random(1,2))")
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = max(-mainItem.frame.width / 2, min(location.x + dX, mainItem.frame.width + mainItem.frame.width / 2 - currentView.frame.width))
                let newY = max(-mainItem.frame.height / 2, min(location.y + dY, mainItem.frame.height + mainItem.frame.height / 2 - currentView.frame.height))
                currentView.frame = CGRect(x: newX, y: newY, width: currentView.frame.width, height: currentView.frame.height)
            }
            
        case .ended:
            if let currentView = currentView {
                let closeDistance = mainItem.frame.height / 10
                var found = false
                for id in allLayout {
                    if let item = mainItem.viewWithTag(id), item.isHidden, abs(item.frame.width - currentView.frame.width) < closeDistance {
                        let distance = item.distanceFromCenterToCenter(to: currentView)
                        if hypot(distance.x, distance.y) < closeDistance {
                            playSound("effect/word puzzle drop")
                            item.isHidden = false
                            item.alpha = 0.001
                            UIView.animate(withDuration: 0.2) {
                                item.alpha = 1
                            }
                            item.bringSubviewToFront(self)
                            found = true
                            let savedView = currentView
                            UIView.animate(withDuration: 0.2) {
                                savedView.frame = item.frame
                            } completion: { _ in
                                savedView.transform = .identity
                                savedView.isHidden = true
                                self.checkFinish()
                            }
                            break
                        }
                    }
                }
                if !found {
                    let savedView = currentView
                    playSound("effect/slide2")
                    UIView.animate(withDuration: 0.2) {
                        savedView.alpha = 0
                    } completion: { _ in
                        savedView.transform = .identity
                        savedView.isHidden = true
                        self.checkFinish()
                    }
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<itemContainer.subviews.count).reversed() {
            let child = itemContainer.subviews[i]
            if x >= child.frame.minX && x <= child.frame.maxX && y >= child.frame.minY && y <= child.frame.maxY {
                if child == item1 || child == item2 {
                    return child
                }
            }
        }
        return nil
    }
    
    private func findViewUnder2(x: CGFloat, y: CGFloat) -> UIView? {
        let adjustedY = y - mainItem.frame.width / 10
        guard let itemBackground = mainItem.viewWithTag(R2.id.item_background) as? UIView else { return nil }
        for i in (0..<itemBackground.subviews.count).reversed() {
            let child = itemBackground.subviews[i]
            if child.isHidden { continue }
            if x >= child.frame.minX && x <= child.frame.maxX && adjustedY >= child.frame.minY && adjustedY <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func checkFinish() {
        for id in allLayout {
            let item1 = mainItem.viewWithTag(id)
            let item2 = subItem.viewWithTag(id)
            if item1?.isHidden != item2?.isHidden {
                return
            }
        }
        pauseGame()
        animateCoinIfCorrect(view: coinView)
        let delay = playSound(finishEndSounds())
        scheduler.schedule(delay: delay) { [weak self] in
            self?.finishGame()
        }
    }
    
    private func getNumberLayout(number: Int) -> [Int] {
        switch number {
        case 0:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_right_2, R2.id.item_bottom, R2.id.item_left_2, R2.id.item_left_1]
        case 1:
            return [R2.id.item_right_1, R2.id.item_right_2]
        case 2:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_center, R2.id.item_left_2, R2.id.item_bottom]
        case 3:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_center, R2.id.item_right_2, R2.id.item_bottom]
        case 4:
            return [R2.id.item_right_1, R2.id.item_right_2, R2.id.item_center, R2.id.item_left_1]
        case 5:
            return [R2.id.item_top, R2.id.item_left_1, R2.id.item_center, R2.id.item_right_2, R2.id.item_bottom]
        case 6:
            return [R2.id.item_top, R2.id.item_left_1, R2.id.item_center, R2.id.item_right_2, R2.id.item_bottom, R2.id.item_left_2]
        case 7:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_right_2]
        case 8:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_right_2, R2.id.item_bottom, R2.id.item_left_2, R2.id.item_left_1, R2.id.item_center]
        case 9:
            return [R2.id.item_top, R2.id.item_right_1, R2.id.item_right_2, R2.id.item_center, R2.id.item_bottom, R2.id.item_left_1]
        default:
            return []
        }
    }
    
    // MARK: - Helper for item_xepdiem layout
    private func createItemXepDiem() -> UIView {
        let container = UIView()
        
        let itemBackground = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_bg"))
        itemBackground.contentMode = .scaleAspectFit
        itemBackground.tag = R2.id.item_background
        container.addSubview(itemBackground)
        itemBackground.makeViewCenterAndKeep(ratio: 574.8 / 1067.4)
        
        let positions: [(id: Int, biasX: CGFloat, biasY: CGFloat, rotation: CGFloat, widthPercent: CGFloat?, heightPercent: CGFloat?)] = [
            (R2.id.item_left_1, 0.025, 0.03, 0, nil, 0.49),
            (R2.id.item_right_1, 0.99, 0.03, 0, nil, 0.49),
            (R2.id.item_left_2, 0.025, 0.93, 0, nil, 0.49),
            (R2.id.item_right_2, 0.99, 0.93, 0, nil, 0.49),
            (R2.id.item_top, 0.99, 0.01, .pi / 2, 0.94, nil),
            (R2.id.item_center, 0.99, 0.505, .pi / 2, 0.94, nil),
            (R2.id.item_bottom, 0.99, 0.995, .pi / 2, 0.94, nil)
        ]
        
        for (id, biasX, biasY, rotation, widthPercent, heightPercent) in positions {
            let item = UIView()
            item.tag = id
            //item.backgroundColor = .red
            itemBackground.addSubview(item)
            item.snp.makeConstraints { make in
                if let widthPercent = widthPercent {
                    make.width.equalTo(itemBackground).multipliedBy(widthPercent)
                    make.height.equalTo(item.snp.width).multipliedBy(63.5 / 516.9)
                } else if let heightPercent = heightPercent {
                    make.height.equalTo(itemBackground).multipliedBy(heightPercent)
                    make.width.equalTo(item.snp.height).multipliedBy(63.5 / 516.9)
                }
            }
            let que = UIImageView(image: Utilities.SVGImage(named: "math_xepdiem_que"))
            que.contentMode = .scaleAspectFit
            que.transform = CGAffineTransform(rotationAngle: rotation)
            item.addSubview(que)
            que.snp.makeConstraints { make in
                if rotation == 0 {
                    make.edges.equalToSuperview()
                } else {
                    make.width.equalTo(item).multipliedBy(0.12)
                    make.height.equalTo(que.snp.width).multipliedBy(516.9/63.5)
                    make.center.equalToSuperview()
                }
            }
            addActionOnLayoutSubviews {
                item.snapToHorizontalBias(horizontalBias: biasX)
                item.snapToVerticalBias(verticalBias: biasY)
            }
        }
        
        return container
    }
}

// MARK: - Supporting Structures

struct R2 {
    struct id {
        static let item_background = 1
        static let item_top = 2
        static let item_right_1 = 3
        static let item_right_2 = 4
        static let item_bottom = 5
        static let item_left_1 = 6
        static let item_left_2 = 7
        static let item_center = 8
    }
}
