//
//  toancoban_list_keosovaodongho.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_keosovaodongho: NhanBietGameFragment {
    private var gridLayout: MyGridView!
    private var hiddenNumbers: [Int] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var hours: [UIView] = Array(repeating: UIView(), count: 12)
    private var clockContainer: UIView!
    private var svgView = SVGKFastImageView(svgkImage: nil)!
    private var imageHour: UIImageView!
    private var imageMinute: UIImageView!
    private var clockSVG: SVGKImage? // Custom SVG model
    private var panGesture: UIPanGestureRecognizer!
    private let rightBg = UIView()
    
    override func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        backgroundColor = UIColor.color(hex: "#E9FDFF")

        // Clock Container
        clockContainer = UIView()
        view.addSubview(clockContainer)
        clockContainer.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalTo(clockContainer.snp.height) // 1:1 aspect ratio
            make.right.equalToSuperview().multipliedBy(0.6) // 60% width for clock
        }

        // Inner Clock Container
        let innerClockContainer = UIView()
        clockContainer.addSubview(innerClockContainer)
        innerClockContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(innerClockContainer.snp.width) // 1:1 aspect ratio
        }

        // SVG View
        innerClockContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Minute Hand
        imageMinute = UIImageView(image: Utilities.SVGImage(named: "math_clock1_minute"))
        innerClockContainer.addSubview(imageMinute)
        imageMinute.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Hour Hand
        imageHour = UIImageView(image: Utilities.SVGImage(named: "math_clock1_hour"))
        innerClockContainer.addSubview(imageHour)
        imageHour.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Hour Markers
        let hourPositions: [(horizontalBias: CGFloat, verticalBias: CGFloat)] = [
            (0.68, 0.19), // hour_1
            (0.81, 0.32), // hour_2
            (0.85, 0.50), // hour_3
            (0.81, 0.68), // hour_4
            (0.68, 0.80), // hour_5
            (0.50, 0.85), // hour_6
            (0.325, 0.80), // hour_7
            (0.20, 0.67), // hour_8
            (0.15, 0.50), // hour_9
            (0.19, 0.32), // hour_10
            (0.32, 0.19), // hour_11
            (0.50, 0.15)  // hour_12
        ]

        for i in 0..<12 {
            let hourView = UIView()
            hourView.backgroundColor = .red
            innerClockContainer.addSubview(hourView)
            hourView.snp.makeConstraints { make in
                make.width.height.equalTo(10)
            }
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                hours[i].snapToHorizontalBias(horizontalBias: hourPositions[i].horizontalBias)
                hours[i].snapToVerticalBias(verticalBias: hourPositions[i].verticalBias)
            }
            hourView.isHidden = true
            hours[i] = hourView
        }

        // Grid Layout
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor.color(hex: "#D6FAFF")
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.color(hex: "#D6FAFF")
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }

        // Pan Gesture
        panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    var zPos = 100.0
    var originX = 0.0
    var originY = 0.0
    @objc func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.origin.x - gesture.location(in: self).x
                dY = currentView.frame.origin.y - gesture.location(in: self).y
                originX = currentView.frame.origin.x
                originY = currentView.frame.origin.y
                currentView.layer.zPosition = zPos
                zPos += 1
                if let background = currentView.viewWithTag(100) {
                    background.isHidden = true
                }
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                vibrate()
            }
        case .changed:
            if let currentView = currentView {
                let translation = gesture.location(in: self)
                currentView.frame.origin = CGPoint(x: translation.x + dX, y: translation.y + dY)
            }
        case .ended:
            if let currentView = currentView {
                vibrate()
                var minDistance: CGFloat = .greatestFiniteMagnitude
                var minView: UIView?
                var hour = 0

                for i in 0..<hours.count {
                    let view = hours[i]
                    let point = currentView.distanceFromCenterToCenter(to: view)
                    let distance = hypot(point.x, point.y)
                    if distance < minDistance {
                        minDistance = distance
                        minView = view
                        hour = i + 1
                    }
                }

                if let minView = minView, minDistance < clockContainer.frame.height / 6 {
                    playSound("effect/word puzzle drop")
                    if let textNumber = currentView.viewWithTag(101) as? UILabel,
                       textNumber.text == "\(hour)" {
                        currentView.moveToCenter(of: minView, duration: 0.2)
                        UIView.animate(withDuration: 0.2, animations: {
                            currentView.alpha = 0
                            currentView.viewWithTag(101)?.transform = CGAffineTransformMakeScale(0.3, 0.3)
                        })
                        
                        if let path = clockSVG?.caLayerTree.sublayers?.first(where: { $0.name == "num\(hour)" }) {
                            path.sublayers?[1].opacity = 1
                            svgView.setNeedsDisplay()
                            hiddenNumbers.removeAll { $0 == hour }

                            let currentHour = Int(round(imageHour.transform.rotationDegrees / 30))
                            var deltaHour = hour - currentHour
                            while deltaHour < -6 { deltaHour += 12 }
                            while deltaHour > 6 { deltaHour -= 12 }

                            pauseGame()
                            UIView.animate(withDuration: 0.2 * Double(abs(deltaHour)), delay: 0, options: .curveLinear, animations: {
                                self.imageHour.transform = self.imageHour.transform.rotated(by: CGFloat(deltaHour * 30).degreesToRadians)
                            }, completion: { _ in
                                self.resumeGame()
                                self.playSound(self.getNumberSound(hour), "vi/toan/giờ")
                            })

                            if hiddenNumbers.isEmpty {
                                pauseGame()
                                animateCoinIfCorrect(view: svgView)
                                let delay = playSound(finishEndSounds())
                                scheduler.schedule(delay: delay) {
                                    self.finishGame()
                                }
                            }
                        }
                    } else {
                        setGameWrong()
                        resetViewPosition(currentView)
                    }
                } else {
                    resetViewPosition(currentView)
                }
                self.currentView = nil
            }
        default:
            break
        }
    }

    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for subview in gridLayout.subviews.reversed() {
            if subview.frame.contains(CGPoint(x: x, y: y)) {
                return subview
            }
        }
        return nil
    }

    private func resetViewPosition(_ view: UIView) {
        if let background = view.viewWithTag(100) as? SVGKFastImageView {
            background.isHidden = false
        }
        playSound("effect/slide2")
        UIView.animate(withDuration: 0.5) {
            view.transform = .identity
            view.frame.origin = CGPoint(x: self.originX, y: self.originY)
        }
    }

    override func updateData() {
        super.updateData()
        hiddenNumbers = Array(1...12).shuffled().prefix(4).sorted()
        var views: [UIView] = []
        
        for value in hiddenNumbers {
            let view = createItemView(value: value)
            view.alpha = 0.01
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            views.append(view)
        }

        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)

        self.clockSVG = Utilities.GetSVGKImage(named: "math_clock1_bg")
        for path in clockSVG?.caLayerTree.sublayers ?? [] {
            if path.name?.starts(with: "num") ?? false {
                if let hour = Int(path.name?.dropFirst(3) ?? ""), self.hiddenNumbers.contains(hour) {
                    path.sublayers?[1].opacity = 0.01
                }
            }
        }
        self.svgView.image = clockSVG
    }

    override func createGame() {
        super.createGame()
        var delay = playSound("toan/toan_keo so dong ho")
        clockContainer.moveToCenter(of: self, duration: 0)
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay, animations: {
            self.clockContainer.transform = .identity
        })
        delay += 0.5
        
        UIView.animate(withDuration: 0.5, delay: delay, animations: {
            self.gridLayout.alpha = 1
            self.clockContainer.transform = .identity
            self.rightBg.alpha = 1
        })
        delay += 0.5
        
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_keo so dong ho")
            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }
    let image = Utilities.GetSVGKImage(named: "option_bg_white_shadow")
    private func createItemView(value: Int) -> UIView {
        let view = UIView()
        let background = SVGKFastImageView(svgkImage: image)!
        background.isUserInteractionEnabled = false
        background.tag = 100
        view.addSubview(background)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        let label = AutosizeLabel()
        label.tag = 101
        label.text = "\(value)"
        label.textColor = UIColor.color(hex: "#74B6FF")
        label.textAlignment = .center
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }

        return view
    }

    private func vibrate() {
        let feedback = UIImpactFeedbackGenerator(style: .medium)
        feedback.impactOccurred()
    }
}



extension CGAffineTransform {
    var rotationDegrees: CGFloat {
        return atan2(self.b, self.a) * 180 / .pi
    }
}

extension CGFloat {
    var degreesToRadians: CGFloat {
        return self * .pi / 180
    }
}

