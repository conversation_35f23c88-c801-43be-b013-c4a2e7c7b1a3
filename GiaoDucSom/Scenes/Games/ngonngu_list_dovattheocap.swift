//
//  ngonngu_list_dovattheocap.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 2/4/25.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class ngonngu_list_dovattheocap: NhanBietGameFragment {
    private var values1 : [Int] = []
    private var values2 : [Int] = []
    private var answers : [Int] = []
    private var randomOrder1: [Int] = []
    private var randomOrder2: [Int] = []
    private var contentLayout: MyGridView = MyGridView()
    private var answerLayout: MyGridView = MyGridView()
    private var meIndex = 0
    private var step = 0
    private var leftViews: [UIView] = []
    private var rightViews: [UIView] = []
    private var allViews: [UIView] = []
    private var selectedView: UIView?
    private var hand: UIView = UIView()
    private var lineContainer = UIView()
    private var drawingLine = LinesBetweenViews()
    private var viewConnectedToView: [UIView : UIView] = [:]
    private var viewConnectedToLine: [UIView : LinesBetweenViews] = [:]
    var coinView = UIView()
    
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = .color(hex: "#849BFD")
        addSubview(contentLayout)
        addSubview(answerLayout)
        addSubview(lineContainer)
        addSubview(hand)
        addSubviewWithInset(subview: drawingLine, inset: 0)
        
        drawingLine.isHidden = true
        hand.frame = CGRectMake(0, 0, 1, 1)
        hand.backgroundColor = .clear
        
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
            
        addSubview(coinView)
        coinView.snp.makeConstraints{ make in
            make.width.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        contentLayout.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        
        answerLayout.snp.makeConstraints { make in
            make.bottom.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        
        lineContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        coinView.isUserInteractionEnabled = false
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        if gameState != .playing { return }
        for view in allViews {
            if touch.placeInView(view: view){
                selectedView = view
                hand.frame = CGRectMake(selectedView!.frame.midX, selectedView!.frame.maxY, 1, 1)
                if let label = selectedView?.viewWithTag(1) as? UILabel {
                    //playSound("topics/Numbers/" + label.text!)
                }
                drawingLine.views[0] = selectedView!
                if let line = viewConnectedToLine[selectedView!] {
                    line.alpha = 0.3
                }
                return;
            }
        }
        selectedView = nil
        return
    }
    
    var folder: [String] = []
    var items: [Item] = []
    
    override func createGame() {
        super.createGame()
        
        for pack in FlashcardsManager.shared.getPacks() {
            for item in pack.items {
                if item.couple != nil {
                    folder.append(pack.folder)
                    items.append(item)
                }
            }
        }
        
        while true {
            let indexes = Utils.generatePermutation(3, size: folder.count)
            
            if haveSameItem(items[indexes[0]].couple, items[indexes[1]].couple) ||
               haveSameItem(items[indexes[0]].couple, items[indexes[2]].couple) ||
               haveSameItem(items[indexes[1]].couple, items[indexes[2]].couple) {
                continue
            }
            
            var indexes2 = [-1, -1, -1]
            let randomOrders = Utils.generatePermutation(folder.count)
            
            for i in 0..<randomOrders.count {
                if indexes2[0] == -1 &&
                   indexes[0] != randomOrders[i] &&
                   haveSameItem(items[indexes[0]].couple, items[randomOrders[i]].couple) {
                    indexes2[0] = randomOrders[i]
                }
                if indexes2[1] == -1 &&
                   indexes[1] != randomOrders[i] &&
                   haveSameItem(items[indexes[1]].couple, items[randomOrders[i]].couple) {
                    indexes2[1] = randomOrders[i]
                }
                if indexes2[2] == -1 &&
                   indexes[2] != randomOrders[i] &&
                   haveSameItem(items[indexes[2]].couple, items[randomOrders[i]].couple) {
                    indexes2[2] = randomOrders[i]
                }
            }
            
            if haveSameItem(items[indexes2[0]].couple, items[indexes2[1]].couple) ||
               haveSameItem(items[indexes2[1]].couple, items[indexes2[2]].couple) ||
               haveSameItem(items[indexes2[2]].couple, items[indexes2[1]].couple) {
                continue
            }
            
            if haveSameItem(items[indexes[0]].couple, items[indexes2[1]].couple) ||
               haveSameItem(items[indexes[0]].couple, items[indexes2[2]].couple) ||
               haveSameItem(items[indexes[1]].couple, items[indexes2[0]].couple) ||
               haveSameItem(items[indexes[1]].couple, items[indexes2[2]].couple) ||
               haveSameItem(items[indexes[2]].couple, items[indexes2[0]].couple) ||
               haveSameItem(items[indexes[2]].couple, items[indexes2[1]].couple) {
                continue
            }
            
            values1 = Array(indexes)
            values2 = Array([indexes2[0], indexes2[1], indexes2[2]])
            break
        }
        
        while true {
            randomOrder1 = Utils.generatePermutation(values1.count)
            randomOrder2 = Utils.generatePermutation(values2.count)
            
            if randomOrder1[0] == randomOrder2[0] &&
               randomOrder1[1] == randomOrder2[1] &&
               randomOrder1[2] == randomOrder2[2] {
                continue
            }
            break
        }
        
        let delay = playSound("ngonngu/ngonngu_do vat theo cap")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        step += 1
        allViews = []
        leftViews = []
        lineContainer.subviews.forEach{$0.removeFromSuperview()}
        for i in 0..<values1.count {
            let index = values1[randomOrder1[i]]
            let view = createItemBottom(text: "topics/\(folder[index])/\(items[index].path!)")
            view.tag = 100 + randomOrder1[i]
            view.stringTag = "top"
            leftViews.append(view)
            allViews.append(view)
        }
        contentLayout.itemRatio = 1
        contentLayout.columns = 3
        contentLayout.insetRatio = 0.1
        contentLayout.reloadItemViews(views: leftViews)
        
        rightViews = []
        for i in 0..<values1.count {
            let index = values2[randomOrder2[i]]
            let view = createItemBottom(text: "topics/\(folder[index])/\(items[index].path!)")
            view.tag = 100 + randomOrder2[i]
            view.stringTag = "bottom"
            rightViews.append(view)
            allViews.append(view)
        }
        answerLayout.itemRatio = 1
        answerLayout.columns = 3
        answerLayout.insetRatio = 0.1
        answerLayout.reloadItemViews(views: rightViews)
        hand.tag = -1
        drawingLine.views = [hand, hand]
    }
    
    func haveSameItem(_ list1: [Int]?, _ list2: [Int]?) -> Bool {
        guard let list1 = list1 else {
            return false
        }
        guard let list2 = list2 else {
            return false
        }
        for item in list1 {
            if list2.contains(item) {
                return true
            }
        }
        return false
    }
    
    func connect(left:UIView, right:UIView){
        if left.superview == right.superview {
            return
        }
        if let line = viewConnectedToLine[left] {
            line.removeFromSuperview()
        }
        if let line = viewConnectedToLine[right] {
            line.removeFromSuperview()
        }
        if let other = viewConnectedToView[left] {
            viewConnectedToView[other] = nil
        }
        if let other = viewConnectedToView[right] {
            viewConnectedToView[other] = nil
        }
        let line = LinesBetweenViews()
        line.views = [left,right]
        line.backgroundColor = .clear
        line.clipsToBounds = false
        lineContainer.addSubviewWithInset(subview: line, inset: 0)
        scheduler.schedule(delay: 0.01) {
            line.createPath()
        }
        let p = hand.convert(CGPoint.zero, to: self)
        viewConnectedToLine[left] = line
        viewConnectedToLine[right] = line
        viewConnectedToView[left] = right
        viewConnectedToView[right] = left
        checkFinish()
    }
    
    func checkFinish(){
        if lineContainer.subviews.count == values1.count {
            var valid = true
            for view in lineContainer.subviews {
                if let view = view as? LinesBetweenViews {
                    if view.views[0].tag != view.views[1].tag{
                        valid = false
                        break
                    }
                }
            }
            if valid {
                pauseGame()
                animateCoinIfCorrect(view: coinView)
                var delay = 0.0
                delay += self.playSound(delay: delay, names: finishCorrect1Sounds())
                delay += 1
                self.scheduler.schedule(delay: delay) {
                    [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                playSound("effect/answer_wrong")
                pauseGame()
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.lineContainer.subviews.forEach{$0.removeFromSuperview()}
                    self.viewConnectedToLine = [:]
                    self.viewConnectedToView = [:]
                    self.resumeGame()
                }
                setGameWrong()
            }
        }
    }
    
    @objc func handlePan(_ sender: UIPanGestureRecognizer) {
        if gameState != .playing {
            return
        }
        let state = sender.state
        if state == .began {
            playSound("effect/cungchoi_pick\(random(1, 2))")
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        let p = sender.location(in: self)
        hand.frame = CGRectMake(p.x, p.y, 1, 1)
        drawingLine.views[1] = hand
        drawingLine.createPath()
        drawingLine.isHidden = false
        
        // Ngưỡng khoảng cách để snap (có thể điều chỉnh)
        let snapThreshold: CGFloat = 50.0
        
        // Kiểm tra snap trong quá trình kéo
        var nearestView: UIView? = nil
        var minDistance: CGFloat = (selectedView?.bounds.height)!/2
        
        for view in allViews where view != selectedView {
            let viewCenter = view.convert(CGPoint(x: view.bounds.width / 2, y: view.bounds.height / 2), to: self)
            let distance = hypot(p.x - viewCenter.x, p.y - viewCenter.y)
            if distance < minDistance {
                minDistance = distance
                nearestView = view
            }
        }
        
        if state == .changed {
            // Nếu gần một view khác trong lúc kéo, cập nhật drawingLine để "tạm snap"
            if let nearest = nearestView{
                if selectedView?.superview != nearest.superview {
                    drawingLine.views[1] = nearest
                    drawingLine.createPath()
                }
            }
        }
        
        if state == .ended || state == .cancelled || state == .failed {
            drawingLine.isHidden = true
            if nearestView != nil {
                playSound("effect/word puzzle drop")
            } else {
                playSound("effect/slide2")
            }
            if let line = viewConnectedToLine[selectedView!] {
                line.alpha = 1
            }
            
            // Snap tự động khi thả tay nếu đủ gần
            if let nearest = nearestView {
                if let label = nearest.viewWithTag(1) as? UILabel {
                    //playSound("topics/Numbers/" + label.text!)
                }
                connect(left: selectedView!, right: nearest)
            } else {
                // Nếu không snap, kiểm tra xem người dùng có thả tay trong một view không
                for view in allViews where view != selectedView {
                    if sender.placeInView(view: view) {
                        if let label = view.viewWithTag(1) as? UILabel {
                            //playSound("topics/Numbers/" + label.text!)
                        }
                        connect(left: selectedView!, right: view)
                        break
                    }
                }
            }
            
            selectedView = nil // Reset selectedView sau khi thả
        }
    }
    
    class LinesBetweenViews: UIView {
        var views: [UIView] = []
        var shapeLayer: CAShapeLayer { return self.layer as! CAShapeLayer }
        public var pathColor: UIColor = .color(hex: "$FFFFFF"){
            didSet {
                guard let layer = self.layer as? CAShapeLayer else { return }
                layer.strokeColor = pathColor.cgColor
            }
        }
        override class var layerClass : AnyClass {
            return CAShapeLayer.self}
        override var bounds: CGRect {
            didSet {
            }
        }
        override func didMoveToSuperview() {
            shapeLayer.strokeColor = pathColor.cgColor
            shapeLayer.fillColor = UIColor.clear.cgColor
            shapeLayer.lineWidth = Utilities.isIPad ? 20 : 10
            shapeLayer.borderWidth = 1
            shapeLayer.lineCap = .round
            shapeLayer.borderColor = UIColor.color(hex: "#FFFFFF").cgColor
        }
        override func didAddSubview(_ subview: UIView) {
            createPath()
        }
        public func createPath() {
            let path = UIBezierPath()
            var view1 = views[0]
            var view2 = views[1]
            var p1 = views[0].convert(CGPoint.zero, to: self)
            var p2 = views[1].convert(CGPoint.zero, to: self)
            if p1.y > p2.y { //Thay đổi so sánh p1.x > p2.x thành p1.y > p2.y
                view1 = views[1]
                view2 = views[0]
                let p = p1
                p1 = p2
                p2 = p
            }
            p1.x += view1.bounds.width / 2 // Thay đổi từ p1.x += view1.bounds.width thành p1.x += view1.bounds.width / 2
            if view1.stringTag == "top" {
                p1.y += view1.bounds.height * 0.95
            } else {
                p1.y += view1.bounds.height * 0.05
            }
            if view2.stringTag == "top" {
                p2.y += view2.bounds.height * 0.95
            } else {
                p2.y += view2.bounds.height * 0.05
            }
            p2.x += view2.bounds.width / 2 // thay đổi từ p2.y += view2.bounds.height/2 thành p2.x += view2.bounds.width / 2
            path.move(to: p1)
            path.addLine(to: p2)
            shapeLayer.path = path.cgPath
        }
    }
        
       
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("ngonngu/ngonngu_do vat theo cap")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    func createItemBottom(text:String)->UIView{
        let view = UIView()
        let background = SVGImageView(SVGName: "option_bg_white_shadow")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let imageview = SVGImageView(SVGName: text)
        imageview.contentMode = .scaleAspectFill
        view.addSubview(imageview)
        imageview.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.width.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
}
