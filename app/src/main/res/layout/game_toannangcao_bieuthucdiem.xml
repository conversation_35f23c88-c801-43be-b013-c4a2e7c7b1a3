<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#EEE3D0"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2.1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.05">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.15">

            <include layout="@layout/item_bieuthucdiem_number" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHorizontal_bias="0.45"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.15">

            <include layout="@layout/item_bieuthucdiem_number" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_3"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHorizontal_bias="0.76"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.15">

            <include layout="@layout/item_bieuthucdiem_number_1" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_4"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.15">

            <include layout="@layout/item_bieuthucdiem_number" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_operator"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.22"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.77"
            app:layout_constraintWidth_percent="0.15">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0.5"
                android:src="@drawable/math_xepdiem_bg2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">


                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <com.kidsup.giaoducsom.view.DragConstraintLayout
                android:id="@+id/item_top"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:translationX="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:rotation="0"
                    android:src="@drawable/math_xepdiem_que" />

            </com.kidsup.giaoducsom.view.DragConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.68"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.77"
            app:layout_constraintWidth_percent="0.15">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/line_top"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.3" />

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/line_bottom"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.7" />

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que"
                    app:layout_constraintBottom_toBottomOf="@id/line_top"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/line_top" />

                <ImageView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que"
                    app:layout_constraintBottom_toBottomOf="@id/line_bottom"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="@id/line_bottom" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.22"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintWidth_percent="0.15">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0.5"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_bg2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.DragConstraintLayout
                android:id="@+id/item_top_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que" />
            </com.kidsup.giaoducsom.view.DragConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.45"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintWidth_percent="0.15">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0.5"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_bg2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.DragConstraintLayout
                android:id="@+id/item_top_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que" />
            </com.kidsup.giaoducsom.view.DragConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.68"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0"
            app:layout_constraintWidth_percent="0.15">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0.5"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_bg2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.DragConstraintLayout
                android:id="@+id/item_top_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.9">

                <ImageView
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:rotation="90"
                    android:src="@drawable/math_xepdiem_que" />
            </com.kidsup.giaoducsom.view.DragConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/text_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0f00"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="4"
            app:layout_constraintHorizontal_bias="0.41"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.05"
            app:layout_constraintWidth_percent="0.55">

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/heightRatioTextView2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#F8EFDE"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="1"
                android:textColor="#DBC7A7"
                app:heightRatio="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/heightRatioTextView"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/heightRatioTextView"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#F8EFDE"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="2"
                android:textColor="#DBC7A7"
                app:heightRatio="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/heightRatioTextView3"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/heightRatioTextView2"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/heightRatioTextView3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#F8EFDE"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="3"
                android:textColor="#DBC7A7"
                app:heightRatio="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/heightRatioTextView"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <TextView
        android:id="@+id/text_intro"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="123"
        android:textSize="30dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>