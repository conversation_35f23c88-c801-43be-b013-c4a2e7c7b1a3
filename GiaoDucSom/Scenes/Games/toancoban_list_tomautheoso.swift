//
//  toancoban_list_tomautheoso.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 10/6/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class toancoban_list_tomautheoso: NhanBietGameFragment {
    // MARK: - Properties
    private let noColor: [UIColor] = [UIColor(hex: "#000000"), UIColor(hex: "#00000000")]
    private var svgView: SVGImageView!
    private var gridPens: MyGridView!
    private var numberToColor: [Int: UIColor] = [:]
    private var pathToRightColor: [CAShapeLayer: UIColor] = [:]
    private var penNumber: Int = -1
    private var data: String?
    private var drawContainer: UIImageView!
    private var svg: SVGKImage?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#C6FFFB")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)        
        mainContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(mainContainer.snp.height).multipliedBy(1.5)
            make.width.equalToSuperview().multipliedBy(0.9).priority(800)
            make.height.equalToSuperview().multipliedBy(0.9).priority(800)
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.9)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.9)
        }
        
        drawContainer = UIImageView()
        drawContainer.isUserInteractionEnabled = true
        drawContainer.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        mainContainer.addSubview(drawContainer)
        drawContainer.makeViewCenterAndKeep(ratio: 1)
        drawContainer.snp.makeConstraints { make in
            make.bottom.right.equalToSuperview()
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.isUserInteractionEnabled = true
        svgView.stringTag = "svg_view"
        drawContainer.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
        svgView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.9)
        }
        
        gridPens = MyGridView()
        gridPens.stringTag = "grid_pens"
        mainContainer.addSubview(gridPens)
        gridPens.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.33)
            make.bottom.left.equalToSuperview()
            make.top.equalTo(drawContainer)
        }
        
        svgView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(svgViewTapped)))
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let filename = data != nil ? "\(data!)" :
        "number coloring/\(StorageManager.manager.list(path: "number coloring").randomElement())"
        
        let svg = Utilities.GetSVGKImage(named: filename)
        self.svg = svg
        for i in 0..<svg.caLayerTree.sublayers!.count {
            if let path = svg.caLayerTree.sublayers?[i] as? CAShapeLayer, let id = path.name {
                if id.hasPrefix("no") {
                    if let number = Int(id.dropFirst(2)) {
                        numberToColor[number] = path.fillColor?.uiColor ?? UIColor.black
                    }
                }
            }
        }
        
        let colors = Array(numberToColor.values)
        for i in 0..<svg.caLayerTree.sublayers!.count {
            if let path = svg.caLayerTree.sublayers?[i] as? CAShapeLayer, colors.contains(path.fillColor?.uiColor ?? UIColor.black) {
                pathToRightColor[path] = path.fillColor?.uiColor ?? UIColor.black
                path.fillColor = UIColor.white.cgColor
            }
        }
        
        svgView.image = svg.uiImage
        
        var views: [UIView] = []
        let sortedNumbers = numberToColor.keys.sorted()
        for key in sortedNumbers {
            let view = UIView()
            let innerView = UIView()
            view.addSubview(innerView)
            innerView.makeViewCenterAndKeep(ratio: 1)
            
            let imageFill = UIImageView()
            imageFill.stringTag = "image_fill"
            imageFill.image = Utilities.SVGImage(named: "bg_tomautheoso_1").withRenderingMode(.alwaysTemplate)
            imageFill.tintColor = numberToColor[key]
            imageFill.isHidden = true
            innerView.addSubview(imageFill)
            imageFill.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let imageStroke = UIImageView()
            imageStroke.stringTag = "image_stroke"
            imageStroke.image = Utilities.SVGImage(named: "bg_tomautheoso_2").withRenderingMode(.alwaysTemplate)
            imageStroke.tintColor = numberToColor[key]
            innerView.addSubview(imageStroke)
            imageStroke.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let textName = UILabel()
            textName.stringTag = "text_name"
            textName.text = "\(key)"
            textName.textColor = numberToColor[key]
            textName.textAlignment = .center
            textName.font = UIFont(name: "SVN-Freude", size: 500)
            textName.adjustsFontSizeToFitWidth = true
            textName.minimumScaleFactor = 0.1
            innerView.addSubview(textName)
            textName.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.7)
                make.height.equalToSuperview()
                make.center.equalToSuperview()
            }
            
            view.stringTag = "\(key)"
            views.append(view)
            
            view.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(penTapped(_:))))
        }
        
        gridPens.itemRatio = 1
        gridPens.columns = views.count > 5 ? 2 : 1
        gridPens.itemSpacingRatio = 0.05
        gridPens.reloadItemViews(views: views)
    
        
        let delay = playSound(delay: 0, names: [openGameSound(), "toan/toan_to mau theo so"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("toan/toan_to mau theo so")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func svgViewTapped(_ gesture: UITapGestureRecognizer) {
        guard gesture.numberOfTouches == 1, penNumber != -1, let svg = svg else {
            playSound("effect/answer_wrong")
            return
        }
        
        let location = gesture.location(in: svgView)
        let size = svg.size ?? CGSize(width: 1, height: 1)
        let isHeight = size.height / size.width > svgView.frame.height / svgView.frame.width
        var unitX = location.x / svgView.frame.width
        var unitY = location.y / svgView.frame.height
        
        if isHeight {
            let w = size.width / size.height * svgView.frame.height
            let deltaX = (svgView.frame.width - w) / 2
            unitX = (location.x - deltaX) / w
        } else {
            let h = size.height / size.width * svgView.frame.width
            let deltaY = (svgView.frame.height - h) / 2
            unitY = (location.y - deltaY) / h
        }
        let rightPath = svg.caLayerTree.hitTest(CGPoint(x: unitX * size.width, y: unitY * size.height)) as? CAShapeLayer
        for i in stride(from: svg.caLayerTree.sublayers!.count - 1, through: 0, by: -1) {
            if let path = svg.caLayerTree.sublayers?[i] as? CAShapeLayer, path.opacity >= 0.5, !noColor.contains((path.fillColor?.uiColor)!) {
                if rightPath == path {
                    if pathToRightColor[path] != nil {
                        path.fillColor = (numberToColor[penNumber] ?? UIColor.black).cgColor
                        svgView.image = svg.uiImage
                        let right = numberToColor[penNumber] == pathToRightColor[path]
                        playSound(right ? "effect/answer_correct" : "effect/answer_wrong")
                        if !right {
                            setGameWrong()
                            pauseGame(stopMusic: false)
                            scheduler.schedule(delay: 0.5) { [weak self] in
                                path.fillColor = UIColor.white.cgColor
                                self?.svgView.image = self?.svg?.uiImage
                                self?.resumeGame(startMusic: false)
                            }
                        } else {
                            checkFinish()
                        }
                    }
                    break
                }
            }
        }
    }
    
    @objc private func penTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let tag = Int(view.stringTag ?? "") else { return }
        playSound(delay: 0, names: ["effect/cungchoi_pick\(Int.random(in: 1...2))", "topics/language/vi/Numbers/\(tag)"])
        penNumber = tag
        
        gridPens.subviews.forEach { _view in
            if let imageFill = _view.viewWithStringTag("image_fill") as? UIImageView,
               let textName = _view.viewWithStringTag("text_name") as? UILabel {
                imageFill.isHidden = _view.stringTag != "\(tag)"
                textName.textColor = _view.stringTag == "\(tag)" ? UIColor.white : numberToColor[Int(_view.stringTag ?? "0")!] ?? UIColor.black
            }
        }
    }
    
    // MARK: - Helper Methods
    private func checkFinish() {
        for (path, rightColor) in pathToRightColor {
            if path.fillColor != rightColor.cgColor {
                return
            }
        }
        
        pauseGame(stopMusic: false)
        animateCoinIfCorrect(view: svgView)
        UIView.animate(withDuration: 0.6) {
            self.gridPens.alpha = 0
        }
        scheduler.schedule(delay: 0.5) {
            [weak self] in
            guard let self = self else { return }
            self.drawContainer.moveToCenter(of: self, duration: 0.6)
        }
        
        let delay = playSound(finishEndSounds())
        if let data = data {
            playToMauTheoSo(data)
        }
        scheduler.schedule(after: delay) { [weak self] in
            self?.finishGame()
        }
    }
    
    private func playToMauTheoSo(_ mask: String) {
        let defaults = UserDefaults.standard
        let profileId = DataManager.shared.currentProfile?.id ?? "default"
        var masks = Set(defaults.stringArray(forKey: "tomautheoso_played_\(profileId)") ?? [])
        masks.insert(mask)
        defaults.set(Array(masks), forKey: "tomautheoso_played_\(profileId)")
    }
    
    // MARK: - Public Methods
    func setData(_ data: String) {
        self.data = data
    }
}
