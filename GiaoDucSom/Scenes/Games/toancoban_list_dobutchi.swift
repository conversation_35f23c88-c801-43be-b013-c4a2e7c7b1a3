//
//  toancoban_list_dobutchi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_dobutchi: NhanBietGameFragment {
    // MARK: - Properties
    private var svgRulerView: UIImageView!
    private var distance: Int = 0
    private var gridLayout: MyGridView!
    private var startPoint: Int = 0
    private var endPoint: Int = 0
    private var viewPencil: UIView!
    private var itemContainer: UIView!
    private var svg: SVGKImage?
    let leftContainer = UIView()
    let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.stringTag = "right_bg"
        rightBg.alpha = 0
        rightBg.backgroundColor = .color(hex: "#D6FAFF")
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        let left2Container = UIView()
        leftContainer.addSubviewWithPercentInset(subview: left2Container, percentInset: 5)
                
        let rulerContainer = UIView()
        left2Container.addSubview(rulerContainer)
        rulerContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        
        svgRulerView = UIImageView()
        svgRulerView.contentMode = .scaleAspectFit
        rulerContainer.addSubview(svgRulerView)
        svgRulerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.alpha = 0
        rulerContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let biases = [0.043, 0.133, 0.223, 0.314, 0.405, 0.497, 0.588, 0.679, 0.770, 0.861, 0.953]
        for i in 0...10 {
            let slider = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
            slider.contentMode = .scaleAspectFit
            slider.tag = 100+i
            slider.alpha = 0
            itemContainer.addSubview(slider)
            slider.snp.makeConstraints { make in
                make.height.equalTo(itemContainer).multipliedBy(0.15)
                make.width.equalTo(slider.snp.height) // Ratio 1:1
                make.centerY.equalToSuperview().multipliedBy(1.8) // Bias 0.9
            }
            let I = i
            addActionOnLayoutSubviews {
                slider.snapToHorizontalBias(horizontalBias: biases[I])
            }
        }
        
        viewPencil = UIView()
        viewPencil.alpha = 0
        viewPencil.clipsToBounds = false
        rulerContainer.addSubview(viewPencil)
        viewPencil.snp.makeConstraints { make in
            make.height.equalTo(rulerContainer).multipliedBy(0.13)
            make.centerY.equalToSuperview().multipliedBy(1.7) // Bias 0.85
        }
        
        let pencilLeft = UIImageView(image: Utilities.SVGImage(named: "toan_dobutchi_1"))
        pencilLeft.contentMode = .scaleAspectFit
        viewPencil.addSubview(pencilLeft)
        pencilLeft.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(pencilLeft.snp.height).multipliedBy(108.4/68.5)
        }
        
        let pencilRight = UIImageView(image: Utilities.SVGImage(named: "toan_dobutchi_3"))
        pencilRight.contentMode = .scaleAspectFit
        viewPencil.addSubview(pencilRight)
        pencilRight.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(pencilRight.snp.height).multipliedBy(108.4/68.5)
        }
        
        let pencilMiddle = UIImageView(image: Utilities.SVGImage(named: "toan_dobutchi_2"))
        pencilMiddle.contentMode = .scaleToFill
        viewPencil.addSubview(pencilMiddle)
        pencilMiddle.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(pencilLeft.snp.right).offset(-5)
            make.right.equalTo(pencilRight.snp.left).offset(5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        distance = 3 + Int.random(in: 0..<7) // 3 to 9
        while true {
            startPoint = Int.random(in: 0..<11) // 0 to 10
            if startPoint - distance >= 0 || startPoint + distance <= 10 {
                endPoint = startPoint + distance <= 10 ? startPoint + distance : startPoint - distance
                break
            }
        }
        
        svg = Utilities.GetSVGKImage(named: "images/toan_thuocke.svg")
        for i in 0...10 {
            svg?.caLayerTree.sublayers?[i + 2].opacity = 1
        }
        svgRulerView.image = svg?.uiImage
        
        buildGrid(grid: gridLayout, count: distance)
    }
    
    override func createGame() {
        super.createGame()
        let min = min(startPoint, endPoint)
        let max = max(startPoint, endPoint)
        guard let startView = itemContainer.viewWithTag(100+min),
              let endView = itemContainer.viewWithTag(100+max) else { return }
        scheduler.schedule(delay: 0.1) {
            [weak self] in
            guard let self = self else { return }
            viewPencil.snp.makeConstraints { make in
                make.width.equalTo(-startView.distanceFromCenterToCenter(to: endView).x)
                make.left.equalTo(startView.snp.left).offset(startView.frame.width / 2)
            }
            self.viewPencil.alpha = 1
        }
        
        var delay = playSound(openGameSound(), "toan/toan_do but chi")
        leftContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.leftContainer.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_do but chi")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == distance
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound("topics/Numbers/\(value)")
            delay += playSound(delay: delay, names: finishCorrect1Sounds())
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
