//
//  FlashcardMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/5/25.
//


import UIKit
import SnapKit

class FlashcardMainViewController: BaseSubjectViewController {
    // MARK: - Properties
    private var viewFlashcardsNoTopic: SVGImageView!
    private var btnPlay: KUButton!
    private var recyclerView: UICollectionView!
    
    override var backgroundMusicFile: String? {
        "bg_list1"
    }
    // MARK: - View Lifecycle
    
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Tráo thẻ")
        titleColor = UIColor(hex: "#FF7761")
        backgroundColor = UIColor(hex: "#FFF2F0")
        initViews()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadItems()
    }
    
    // MARK: - Initialization
    private func initViews() {
        let viewTopLeft = UIView()
        viewTopLeft.stringTag = "view_topleft"
        view.addSubview(viewTopLeft)
        viewTopLeft.snp.makeConstraints { make in
            make.width.equalTo(viewTopLeft.snp.height)
            make.height.equalToSuperview().multipliedBy(0.05)
            make.left.top.equalToSuperview()
        }
        
        let viewTopRight = UIView()
        viewTopRight.stringTag = "view_topright"
        view.addSubview(viewTopRight)
        viewTopRight.snp.makeConstraints { make in
            make.width.equalTo(viewTopRight.snp.height)
            make.height.equalToSuperview().multipliedBy(0.05)
            make.right.top.equalToSuperview()
        }
                
        backButton.stringTag = "btn_close"
        backButton.setImage(Utilities.SVGImage(named: "btn_back3"), for: .normal)
        //AnimationUtils.setTouchEffect(btnClose)
                                
        
        let viewCenter = UIView()
        viewCenter.stringTag = "view_center"
        view.addSubview(viewCenter)
        viewCenter.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0)
            make.height.equalTo(0)
            make.center.equalToSuperview()
        }
        
        
        
        let btnSettings = KUButton()
        btnSettings.stringTag = "btn_settings"
        btnSettings.setImage(Utilities.SVGImage(named: "btn_flashcards_settings"), for: .normal)
        //AnimationUtils.setTouchEffect(btnSettings)
        btnSettings.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(settingsTapped)))
        view.addSubview(btnSettings)
        btnSettings.snp.makeConstraints { make in
            make.width.equalTo(btnSettings.snp.height).multipliedBy(402.0/141.0)
            make.height.equalToSuperview().multipliedBy(0.1)
            make.top.equalTo(backButton)
        }
        btnSettings.snpRightTop(ratio: 1)
        
        let settingsText = AutosizeLabel()
        settingsText.text = "Cài đặt"
        settingsText.textColor = UIColor.white
        settingsText.textAlignment = .center
        settingsText.font = .Freude(size: 20)
        btnSettings.addSubview(settingsText)
        settingsText.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalToSuperview().multipliedBy(0.6)
            make.centerY.equalToSuperview()
        }
        settingsText.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            settingsText.snapToHorizontalBias(horizontalBias: 0.7)
        }
        
        let btnInfo = KUButton()
        btnInfo.stringTag = "btn_info"
        btnInfo.setImage(Utilities.SVGImage(named: "btn_info"), for: .normal)
        btnInfo.imageView?.contentMode = .scaleAspectFit
        btnInfo.addTarget(self, action: #selector(infoTapped), for: .touchUpInside)
        //AnimationUtils.setTouchEffect(btnInfo)
        view.addSubview(btnInfo)
        btnInfo.snp.makeConstraints { make in
            make.width.equalTo(btnInfo.snp.height)
            make.top.bottom.equalTo(btnSettings)
            make.right.equalTo(btnSettings.snp.left).offset(-20)
        }
        
        let flashcardContainer = UIView()
        flashcardContainer.stringTag = "flashcard_container"
        flashcardContainer.clipsToBounds = false
        view.addSubview(flashcardContainer)
        flashcardContainer.snp.makeConstraints { make in
            make.width.equalTo(flashcardContainer.snp.height).multipliedBy(5.2)
            make.top.equalTo(backButton.snp.bottom).offset(20)
            make.right.equalToSuperview()
            make.left.equalTo(backButton)
        }
        
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumLineSpacing = 0
        layout.minimumInteritemSpacing = 0
        recyclerView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        recyclerView.stringTag = "recycler_view"
        recyclerView.backgroundColor = .clear
        recyclerView.dataSource = self
        recyclerView.delegate = self
        recyclerView.register(ItemFlashcardFolderHorizontalCell.self, forCellWithReuseIdentifier: "ItemFlashcardFolderHorizontalCell")
        flashcardContainer.addSubview(recyclerView)
        recyclerView.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.95)
            make.center.equalToSuperview()
        }
        
        viewFlashcardsNoTopic = SVGImageView(frame:.zero)
        viewFlashcardsNoTopic.contentMode = .scaleAspectFit
        viewFlashcardsNoTopic.stringTag = "view_flashcards_no_topic"
        //viewFlashcardsNoTopic.SVGName = "bg_flashcards_no_topic"
        view.addSubview(viewFlashcardsNoTopic)
        viewFlashcardsNoTopic.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
            make.left.bottom.equalToSuperview()
        }
        
        btnPlay = KUButton()
        btnPlay.stringTag = "btn_play"
        //AnimationUtils.setTouchEffect(btnPlay)
        btnPlay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(playTapped)))
        view.addSubview(btnPlay)
        btnPlay.snp.makeConstraints { make in
            make.top.equalTo(flashcardContainer.snp.bottom)
            make.bottom.equalToSuperview()
            make.width.equalTo(btnPlay.snp.height)
            make.centerX.equalToSuperview()
        }

        
        let playImage = SVGImageView(frame: .zero)
        playImage.SVGName = "btn_flashcards_play"
        btnPlay.addSubview(playImage)
        playImage.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
    }
    
    @objc private func settingsTapped() {
        /*
        let dialog = SettingsFlashcardsDialogFragment()
        dialog.setListener { [weak self] in
            FlashcardsManager.shared.reload(language: "vi")
            self?.loadItems()
        }
        dialog.show(from: self, tag: "settings_flashcards")
         */
    }
    
    @objc private func infoTapped() {
        let dialog = MessageDialogView()
        dialog.setTitle("Tráo thẻ Glenn Doman")
            .setMessage("Tiếp thu thông tin tốc độ cao. Chọn tối đa 5 chủ đề, ngày tráo 3 lần, mỗi lần cách nhau tối thiểu 15 phút.")
            .setImageResId("icon_flashcards_popup")
            .setButtonOkText("Đóng")
            .showIn(self.view)
    }
    
    @objc private func playTapped() {
        let inTrialTime = inTrialTime()
        runPremiumFeatures({
            if let nextPack = FlashcardsManager.shared.getNextPack() {
                self.openFlashcard()
            } else {
                
                let leftTime = FlashcardsManager.shared.getLeftTimeToNextPack()
                //let leftTimeInStringmmss = leftTime > 0 ? StringUtils.formatTimeInStringmmss(leftTime: leftTime) : ""
                let dialog = MessageFlashcardCountdownDialogFragment()
                dialog.setTitle("Chú ý")
                    .setButtonLaterText("Vẫn tráo")
                    .setShowLaterButton(true)
                    .setImageResId("icon_flashcards_stop")
                    //.setCountdownInSeconds(Int(leftTime))
                    .setMessage("Mỗi lượt tráo thẻ cách nhau tối thiểu 15 phút, mỗi ngày tráo 3 lượt: " + formatTimeInStringMMSS(leftTime: Int64(leftTime)))
                    .setListener(onConfirm: {},  onClose: { action in
                        switch action {
                        case .confirm:
                            self.openAnyway()
                        case .ok:
                            //self.openFlashcard()
                            break
                        case .close:
                            break
                        }
                    })
                    .showIn(self.view)
            }
        }, inTrialTime)
    }
    
    // MARK: - Helper Methods
    private func loadItems() {
        var selectedFolders = FlashcardsManager.shared.getSelectedPacks()
        #if DEBUG
        //selectedFolders = FlashcardsManager.shared.getPacks().take(count: 4)
        #endif
        if selectedFolders.isEmpty {
            viewFlashcardsNoTopic.isHidden = false
            btnPlay.isHidden = true
            viewFlashcardsNoTopic.SVGName = "bg_flashcards_no_topic"
        } else {
            viewFlashcardsNoTopic.isHidden = true
            btnPlay.isHidden = false
        }
        
        self.folders = selectedFolders
        //adapter = ItemHomeAdapter(folders: selectedFolders)
        recyclerView.dataSource = self
        recyclerView.reloadData()
    }
    
    private func openFlashcard() {
        let vc = FlashcardViewController()
        navigationController?.pushViewController(vc, animated: true)
    }
    
    private func openAnyway() {
        let dialog = ParentGateDialogFragment()
        dialog.listener = {
            [weak self] in
            guard let self = self else { return }
            self.openFlashcard()
        }
        dialog.show(parrent: self)
    }
    
    private var folders: [Folder] = []
}

// MARK: - ItemHomeAdapter
extension FlashcardMainViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
        
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return folders.count + 1
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "ItemFlashcardFolderHorizontalCell", for: indexPath) as! ItemFlashcardFolderHorizontalCell
        cell.tvCheckNumber.isHidden = true
        
        if indexPath.item == folders.count {
            cell.viewBackground.image = Utilities.SVGImage(named: "btn_flashcards_folder")
            cell.ivCheck.isHidden = true
            cell.tvPlus.isHidden = false
            cell.tvFolderName.text = ""
            cell.itemView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(chooseTopicTapped)))
            return cell
        }
        
        cell.ivCheck.isHidden = false
        cell.tvPlus.isHidden = true
        let folder = folders[indexPath.item]
        cell.tvFolderName.text = folder.name.localName
        let topicFinish = AppSettings.isFlashcardTopicFinish(folder: folder.folder)
        cell.tvFolderName.textColor = UIColor(hex: topicFinish ? "#469115" : "#E46209")
        cell.ivCheck.image = Utilities.SVGImage(named: topicFinish ? "btn_flashcards_folder_play2" : "btn_flashcards_folder_play")
        cell.viewBackground.image = Utilities.SVGImage(named: topicFinish ? "btn_flashcards_folder2" : "btn_flashcards_folder")
        cell.itemView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(folderTapped(_:))))
        cell.ivCheck.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(checkTapped(_:))))
        cell.index = indexPath.item
        
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let height = collectionView.bounds.height
        let width = height * 460.0 / 370.0
        return CGSize(width: width, height: height)
    }
    
    @objc private func chooseTopicTapped() {
        
        let fragment = FlashcardChooseTopicDialog()
        fragment.onClosed = { [weak self] in
            self?.loadItems()
        }
        fragment.show(parrent: self)
    }
    
    @objc private func folderTapped(_ gesture: UITapGestureRecognizer) {
        
        if let cell = gesture.view?.superview(ofType: ItemFlashcardFolderHorizontalCell.self), let index = cell.index {
            let dialog = FlashcardTopicDialog()
            dialog.setFolder(folders[index])
            dialog.show(parrent: self)
        }
    }
    
    @objc private func checkTapped(_ gesture: UITapGestureRecognizer) {
        
        if let cell = gesture.view?.superview(ofType: ItemFlashcardFolderHorizontalCell.self) {
            guard let index = cell.index else { return }
            let inTrialTime = inTrialTime() ?? false
            runPremiumFeatures({
                [weak self] in
                guard let self = self else { return }
                let playedTime = FlashcardsManager.shared.getPlayedTime(pack: self.folders[index])
                let formatter = DateFormatter()
                formatter.dateFormat = "dd/MM/yyyy HH:mm"
                let playedTimeString = formatter.string(from: playedTime)
                let currentTime = Date().timeIntervalSince1970
                if playedTime.timeIntervalSince1970 + 15 * 60 > currentTime {
                    let leftTime = Int(playedTime.timeIntervalSince1970 + 15 * 60 - currentTime)
                    let dialog = MessageFlashcardCountdownDialogFragment()
                    dialog.setTitle("Chú ý")
                        .setButtonLaterText("Vẫn tráo")
                        .setShowLaterButton(true)
                        .setImageResId("icon_flashcards_stop")
                        .setMessage("Mỗi lượt tráo thẻ cách nhau tối thiểu 15 phút, mỗi ngày tráo 3 lượt: " + formatTimeInStringMMSS(leftTime: Int64(leftTime)))
                        //.setCountdownInSeconds(leftTime)
                        .setListener ( onClose: { action in
                            switch action {
                            case .confirm:
                                let parentDialog = ParentGateDialogFragment()
                                parentDialog.listener = {
                                    self.doFlashcard(at: index)
                                }
                                parentDialog.show(parrent:self)
                            case .ok:
                                //self.doFlashcard(at: index)
                                break
                            case .close:
                                break
                            }
                        })
                        .showIn(self.view)
                } else {
                    self.doFlashcard(at: index)
                }
            }, inTrialTime)
        }
        //self.doFlashcard(at: 0)
    }
    
    private func doFlashcard(at position: Int) {
        let vc = FlashcardViewController()
        vc.pack = folders[position]
        navigationController?.pushViewController(vc, animated: true)
    }
}

func formatTimeInStringMMSS(leftTime: Int64) -> String {
    let minute = leftTime / 60
    let second = leftTime % 60
    return String(format: "%02d:%02d", minute, second)
}

// MARK: - ItemFlashcardFolderHorizontalCell
class ItemFlashcardFolderHorizontalCell: UICollectionViewCell {
    let tvFolderName = HeightRatioTextView()
    let tvPlus = HeightRatioTextView()
    let ivCheck = UIImageView()
    let tvCheckNumber = HeightRatioTextView()
    let viewBackground = UIImageView()
    var itemView: UIView!
    var index: Int?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupViews()
    }
    
    private func setupViews() {
        itemView = contentView
        viewBackground.stringTag = "view_background"
        viewBackground.isUserInteractionEnabled = true
        contentView.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
                
        tvFolderName.stringTag = "tv_folder_name"
        tvFolderName.setHeightRatio(0.25)
        tvFolderName.numberOfLines = 2
        tvFolderName.textAlignment = .center
        tvFolderName.font = .Freude(size: 20)
        tvFolderName.textColor = .color(hex: "#E5630A")
        viewBackground.addSubview(tvFolderName)
        tvFolderName.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.centerX.equalToSuperview()
        }
        tvFolderName.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.tvFolderName.snapToVerticalBias(verticalBias: 0.65)
        }
        
        tvPlus.stringTag = "tv_plus"
        tvPlus.text = "+"
        tvPlus.setHeightRatio(0.6)
        tvPlus.textAlignment = .center
        tvPlus.font = .Freude(size: 20)
        tvPlus.textColor = .color(hex: "#E5630A")
        viewBackground.addSubview(tvPlus)
        tvPlus.snp.makeConstraints { make in
            make.width.height.equalToSuperview().multipliedBy(0.8)
            make.centerX.equalToSuperview()
        }
        tvPlus.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.tvPlus.snapToVerticalBias(verticalBias: 0.6)
        }
        
        ivCheck.stringTag = "iv_check"
        ivCheck.isUserInteractionEnabled = true
        ivCheck.image = Utilities.SVGImage(named: "btn_play_top")
        viewBackground.addSubview(ivCheck)
        ivCheck.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.2)
            make.width.equalTo(ivCheck.snp.height)
        }
        ivCheck.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.ivCheck.snapToVerticalBias(verticalBias: 0.08)
            self.ivCheck.snapToHorizontalBias(horizontalBias: 0.93)
        }
        
        tvCheckNumber.stringTag = "tv_check_number"
        tvCheckNumber.setHeightRatio(0.6)
        tvCheckNumber.backgroundColor = .color(hex: "#F9761E")
        viewBackground.addSubview(tvCheckNumber)
        tvCheckNumber.snp.makeConstraints { make in
            make.edges.equalTo(ivCheck)
        }
        tvCheckNumber.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.tvCheckNumber.layer.cornerRadius = self.tvCheckNumber.frame.height / 2
            self.tvCheckNumber.layer.masksToBounds = true
        }
        //AnimationUtils.setTouchEffect(ivCheck)
    }
}

class MessageFlashcardCountdownDialogFragment : MessageDialogView {}

extension UIView {
    func superview<T>(ofType type: T.Type) -> T? where T: UIView {
        var currentView: UIView? = self.superview
        while currentView != nil {
            if let typedView = currentView as? T {
                return typedView
            }
            currentView = currentView?.superview
        }
        return nil
    }
}
