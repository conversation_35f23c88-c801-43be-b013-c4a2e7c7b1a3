//
//  toancoban_list_kiemtrapheptoan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_kiemtrapheptoan: NhanBietGameFragment {
    // MARK: - Properties
    private var textResult: UILabel!
    private var btnWrong: UIButton!
    private var btnCorrect: UIButton!
    private var correct: Bool = false
    private var a: Int = 0
    private var b: Int = 0
    private var c: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        let topContainer = UIView()
        view.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        textResult = AutosizeLabel()
        textResult.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        //textResult.maximumContentSize = CGSize(width: 1500, height: 1500)
        textResult.numberOfLines = 1
        topContainer.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        let bottomContainer = UIView()
        view.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(view).multipliedBy(0.5)
        }
        
        let wrongContainer = UIView()
        wrongContainer.backgroundColor = UIColor(red: 255/255, green: 230/255, blue: 230/255, alpha: 1) // #FFE6E6
        bottomContainer.addSubview(wrongContainer)
        wrongContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(bottomContainer).multipliedBy(0.5)
        }
        
        btnWrong = UIButton(type: .custom)
        btnWrong.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal)
        btnWrong.imageView?.contentMode = .scaleAspectFit
        wrongContainer.addSubview(btnWrong)
        btnWrong.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.5)
        }
        btnWrong.addTarget(self, action: #selector(onButtonClicked(_:)), for: .touchUpInside)
        //AnimationUtils.setTouchEffect(view: btnWrong)
        
        let correctContainer = UIView()
        correctContainer.backgroundColor = UIColor(red: 217/255, green: 255/255, blue: 227/255, alpha: 1) // #D9FFE3
        bottomContainer.addSubview(correctContainer)
        correctContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(bottomContainer).multipliedBy(0.5)
        }
        
        btnCorrect = UIButton(type: .custom)
        btnCorrect.setImage(Utilities.SVGImage(named: "btn_check"), for: .normal)
        btnCorrect.imageView?.contentMode = .scaleAspectFit
        correctContainer.addSubview(btnCorrect)
        btnCorrect.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.5)
        }
        btnCorrect.addTarget(self, action: #selector(onButtonClicked(_:)), for: .touchUpInside)
        //AnimationUtils.setTouchEffect(view: btnCorrect)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let operators = ["=", ">", "<"]
        let operatorSymbol = operators.randomElement()!
        correct = Bool.random()
        
        while true {
            a = Int.random(in: 0..<10)
            b = Int.random(in: -9..<10)
            c = Int.random(in: 0..<10)
            if a + b < 10 && a + b >= 0 {
                if operatorSymbol == "=" {
                    if correct && a + b == c {
                        textResult.text = formatMath(a: a, b: b) + " = \(c)"
                        break
                    } else if !correct && a + b != c {
                        textResult.text = formatMath(a: a, b: b) + " = \(c)"
                        break
                    }
                } else if operatorSymbol == ">" {
                    if correct && a + b > c {
                        textResult.text = formatMath(a: a, b: b) + " > \(c)"
                        break
                    } else if !correct && a + b <= c {
                        textResult.text = formatMath(a: a, b: b) + " > \(c)"
                        break
                    }
                } else if operatorSymbol == "<" {
                    if correct && a + b < c {
                        textResult.text = formatMath(a: a, b: b) + " < \(c)"
                        break
                    } else if !correct && a + b >= c {
                        textResult.text = formatMath(a: a, b: b) + " < \(c)"
                        break
                    }
                }
            }
        }
    }
    
    private func formatMath(a: Int, b: Int) -> String {
        return b < 0 ? "\(a) - \(-b)" : "\(a) + \(b)"
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_kiem tra so sanh")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_kiem tra so sanh")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Button Handling
    @objc private func onButtonClicked(_ sender: UIButton) {
        let math = textResult.text ?? ""
        let pressCorrect = sender == btnCorrect
        let formulaCorrect = correct
        if gameState != .playing { return }
        pauseGame()
        print("pressed")
        if pressCorrect != formulaCorrect {
            setGameWrong()
        }
        animateCoinIfCorrect(view: textResult)
        
        var delay: TimeInterval = 0
        if formulaCorrect {
            delay += playSound(delay: delay, names: [
                pressCorrect ? answerCorrect1EffectSound() : answerWrongEffectSound(),
                pressCorrect ? getCorrectHumanSound() : "toan/toan_kiem tra so sanh_nham roi",
                "topics/Numbers/\(a)",
                math.contains("+") ? "toan/cộng" : "toan/trừ",
                "topics/Numbers/\(abs(b))",
                math.contains("=") ? "toan/bằng" : math.contains("<") ? "toan/nhỏ hơn" : "toan/lớn hơn",
                "topics/Numbers/\(c)",
                endGameSound()
            ])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            let operatorSymbol = math.contains("<") ? "<" : math.contains(">") ? ">" : "="
            let rightOperator = a + b < c ? "<" : a + b == c ? "=" : ">"
            let operatorIndex = math.firstIndex(of: operatorSymbol.first!)!.utf16Offset(in: math)
            
            let spannableString = NSMutableAttributedString(string: math)
            spannableString.addAttribute(.foregroundColor, value: UIColor(red: 255/255, green: 119/255, blue: 97/255, alpha: 1), range: NSRange(location: operatorIndex, length: 1)) // #FF7761
            textResult.attributedText = spannableString
            
            delay += playSound(delay: delay, names: [
                pressCorrect ? answerWrongEffectSound() : answerCorrect1EffectSound(),
                pressCorrect ? "toan/toan_kiem tra so sanh_nham roi" : getCorrectHumanSound(),
                "topics/Numbers/\(a)",
                math.contains("+") ? "toan/cộng" : "toan/trừ",
                "topics/Numbers/\(abs(b))"
            ])
            
            scheduler.schedule(delay: delay) { [weak self] in
                self?.playSound("effect/answer_correct")
                let rightSpannableString = NSMutableAttributedString(string: math.replacingCharacters(in: math.range(of: operatorSymbol)!, with: rightOperator))
                rightSpannableString.addAttribute(.foregroundColor, value: UIColor(red: 136/255, green: 215/255, blue: 88/255, alpha: 1), range: NSRange(location: operatorIndex, length: 1)) // #88D758
                self?.textResult.attributedText = rightSpannableString
            }
            
            delay += playSound(delay: delay, names: [
                a + b < c ? "toan/toan_kiem tra so sanh_phai nho hon" : a + b == c ? "toan/toan_kiem tra so sanh_phai bang" : "toan/toan_kiem tra so sanh_phai lon hon",
                "topics/Numbers/\(c)",
                "toan/toan_kiem tra so sanh_moi dung",
                endGameSound()
            ])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
}
