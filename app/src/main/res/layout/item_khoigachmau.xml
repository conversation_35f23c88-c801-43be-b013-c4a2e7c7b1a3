<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="20dp"
        android:background="#0f00"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/brick56"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.95"
            android:scaleY="0.95"
            android:src="@drawable/toan_khoigachmau_orange3"
            android:tag="3"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="200:200"
            app:layout_constraintHeight_percent="0.67"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />


        <ImageView
            android:id="@+id/brick67"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.97"
            android:scaleY="0.95"
            android:src="@drawable/toan_khoigachmau_orange1"
            android:tag="1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="250:150"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.66"
            app:layout_constraintWidth_percent="0.832" />

        <ImageView
            android:id="@+id/brick26"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.95"
            android:scaleY="0.97"
            android:src="@drawable/toan_khoigachmau_orange2"
            android:tag="2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="150:250"
            app:layout_constraintHeight_percent="0.83"
            app:layout_constraintHorizontal_bias="0.33"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">


        </androidx.constraintlayout.widget.ConstraintLayout>


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">

            <ImageView
                android:id="@+id/brick78"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.95"
                android:scaleY="0.95"
                android:src="@drawable/toan_khoigachmau_orange3"
                android:tag="3"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="200:200"
                app:layout_constraintHeight_percent="0.67"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />

            <ImageView
                android:id="@+id/brick23"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.97"
                android:scaleY="0.95"
                android:src="@drawable/toan_khoigachmau_orange1"
                android:tag="1"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="250:150"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                app:layout_constraintWidth_percent="0.832" />

            <ImageView
                android:id="@+id/brick15"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.95"
                android:scaleY="0.97"
                android:src="@drawable/toan_khoigachmau_orange2"
                android:tag="2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="150:250"
                app:layout_constraintHeight_percent="0.83"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="visible">


            <ImageView
                android:id="@+id/brick58"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.97"
                android:scaleY="0.95"
                android:src="@drawable/toan_khoigachmau_orange1"
                android:tag="1"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="250:150"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="1"
                app:layout_constraintWidth_percent="0.832" />

            <ImageView
                android:id="@+id/brick37"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.95"
                android:scaleY="0.97"
                android:src="@drawable/toan_khoigachmau_orange2"
                android:tag="2"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="150:250"
                app:layout_constraintHeight_percent="0.83"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />

            <ImageView
                android:id="@+id/brick12"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:scaleX="0.95"
                android:scaleY="0.95"
                android:src="@drawable/toan_khoigachmau_orange3"
                android:tag="3"
                android:visibility="visible"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="200:200"
                app:layout_constraintHeight_percent="0.67"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />


        </androidx.constraintlayout.widget.ConstraintLayout>


        <ImageView
            android:id="@+id/brick14"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.97"
            android:scaleY="0.95"
            android:src="@drawable/toan_khoigachmau_orange1"
            android:tag="1"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="250:150"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.34"
            app:layout_constraintWidth_percent="0.832" />

        <ImageView
            android:id="@+id/brick34"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.95"
            android:scaleY="0.95"
            android:src="@drawable/toan_khoigachmau_orange3"
            android:tag="3"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="200:200"
            app:layout_constraintHeight_percent="0.67"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0" />

        <ImageView
            android:id="@+id/brick48"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.95"
            android:scaleY="0.97"
            android:src="@drawable/toan_khoigachmau_orange2"
            android:tag="2"
            android:visibility="visible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="150:250"
            app:layout_constraintHeight_percent="0.83"
            app:layout_constraintHorizontal_bias="0.66"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>