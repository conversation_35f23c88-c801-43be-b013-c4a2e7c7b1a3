//
//  ToanMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 24/3/25.
//


import UIKit

class ToanMainViewController: SubjectMainViewController {

    override var backgroundMusicFile: String? {
        "bg_list4"
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Toán học")
        titleColor = UIColor(hex: "#849BFD")
        backgroundColor = UIColor(hex: "#CFDDFF")
    }
    
    override func setupData() {
        // Section 1: Nhận biết số đếm 0-9
        let nhanBietSoDemSection = SectionData(
            header: "Nhận biết số đếm 0-9",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_sosanhtraicay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_dobutchi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_chauhoa", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_ngonnen", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_capbophan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_doidua", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_doigiay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_doitat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demtrung", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demcuaso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_loaivat4chan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_dovat4chan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_5toatau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_5canhhoa", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_tomautheoso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_ongtimmat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_7saccauvong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_8xuctu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_9bacthang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demtraicay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demcanhhinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_thuocke", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_noicungsoqua", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_xepquediem", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demchim", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_timsothieu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demphanloai", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demsao", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demngontay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sovietsai", isTapDocGame: false)
            ]
        )
        
        // Section 2: Tập viết số 0-9
        let tapVietSoItems = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9"].map { number in
            TapVietItem(
                type: .write,
                text: number,
                data: number,
                isFreeToPlay: false
            )
        }
        let tapVietSoKieu2Items = ["2", "3", "4", "5", "7"].map { number in
            TapVietItem(
                type: .write,
                text: number,
                data: number + "_2",
                isFreeToPlay: false
            )
        }
        let tapVietSoSection = SectionData(
            header: "Tập viết số 0-9",
            items: tapVietSoItems + tapVietSoKieu2Items
        )
        
        // Section 3: So sánh
        let soSanhSection = SectionData(
            header: "So sánh",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_solientruoc", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_timsomaxmin", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhluongdovat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhtraicay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhkeo", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhdomino", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_chuoisosanh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhbutchi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhdaingan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhganxa", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_caycaothap", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_dongvatcaothap", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhcaothap", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_xephang", isTapDocGame: false)
            ]
        )
        
        // Section 4: Nhận biết số lớn
        let nhanBietSoLonSection = SectionData(
            header: "Nhận biết số lớn",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_demcanhdagiac", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_daysotangdan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_noichanle", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_quachanle", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sonha", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demhop", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demchan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_timso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_timsomaxminpv100", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_taosomaxmin", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_goidienthoai", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sotrongguong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_solonnhat", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_taochunhat", isTapDocGame: false)
            ]
        )
        
        // Section 5: Cộng trừ số nhỏ
        let congTruSoNhoSection = SectionData(
            header: "Cộng trừ số nhỏ",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_congtruphamvi5", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congngontay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_vedoanthang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptinhtraicay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congmatong", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_demtienxu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_chonpheptoan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_noipheptinh", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_nhomso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congchamtron", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congbacthang", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_kiemtrapheptoan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_dobutchi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_ongroito", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_tratienxu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_taopheptinhpv10", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_diendau", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_dienso", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_toanchuthap", isTapDocGame: false)
            ]
        )
        
        // Section 6: Cộng trừ số lớn
        let congTruSoLonSection = SectionData(
            header: "Cộng trừ số lớn",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_congtrutronchuc", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congtruphamvi20", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_sosanhtuoi", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptoanongroito", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptinhbongbay", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptinhtaorung", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptinhchanle", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_pheptinhchanlechu", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_datcotdoc", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_datcotdoc2", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congtru3so", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_congtruphamvi100", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_toanchuthap2", isTapDocGame: false)
            ]
        )
        
        // Section 7: Đồng hồ
        let dongHoSection = SectionData(
            header: "Đồng hồ",
            items: [
                TroChoiNgonNguItem(data: "toancoban_list_keosovaodongho", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_xemgiochan", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_xemgiophut", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_xemphut", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_chinhgio", isTapDocGame: false),
                TroChoiNgonNguItem(data: "toancoban_list_giohoatdong", isTapDocGame: false)
            ]
        )
        
        // Add all sections
        sections = [                       
            nhanBietSoDemSection,
            tapVietSoSection,
            soSanhSection,
            nhanBietSoLonSection,
            congTruSoNhoSection,
            congTruSoLonSection,
            dongHoSection,
        ]    
    }
    
    override func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let section = sections[indexPath.section]
        if let troChoiItem = section.items[indexPath.item] as? TroChoiNgonNguItem {
            // Kiểm tra premium (giả lập runPremiumFeatures)
            let inTrialTime = installedTimeInDays() < 2
            let freetoplay = false//checkFreeGameByThumbnail(troChoiItem.data)
            let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
            
            if playable {
                switch troChoiItem.data {
                case "toancoban_list_tomautheoso":
                    // Mở ToanToMauTheoSoViewController
                    let veTungNetVC = ToanToMauTheoSoViewController()
                    navigationController?.pushViewController(veTungNetVC, animated: true)
                default:
                    super.collectionView(collectionView, didSelectItemAt: indexPath)
                }
            } else {
                // Hiển thị thông báo cần premium
                print("Need premium to access this game")
            }
        }
    }
    
}
