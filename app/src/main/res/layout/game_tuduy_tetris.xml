<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ffffff"
    android:clipChildren="false"
    android:clipToPadding="false">

    <com.kidsup.giaoducsom.view.TetrisView
        android:id="@+id/tetris_top_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/answer_layout"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.2" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/answer_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:alpha="1"
        android:background="#D6FAFF"
        app:columnCount="2"
        app:horizontalInsetRatio="0.05"
        app:itemRatio="1"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.4"
        app:spacingRatio="0.05"
        app:verticalInsetRatio="0.05" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.3" />

</com.kidsup.giaoducsom.view.PercentConstraintLayout>