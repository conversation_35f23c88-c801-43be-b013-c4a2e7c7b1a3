<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"

        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:alpha="0.5"
            android:background="@drawable/math_xepdiem_bg" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/line_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.035" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/line_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="horizontal"
            app:layout_constraintGuide_percent="0.96" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/line_left"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.065" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/line_right"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.93" />

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_center"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="@id/line_top"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/line_top"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="@id/line_bottom"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="@id/line_bottom"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_left_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="@id/line_left"
            app:layout_constraintRight_toRightOf="@id/line_left"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.06"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_left_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="@id/line_left"
            app:layout_constraintRight_toRightOf="@id/line_left"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.93"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_right_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="@id/line_right"
            app:layout_constraintRight_toRightOf="@id/line_right"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.06"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>

        <com.kidsup.giaoducsom.view.DragConstraintLayout
            android:id="@+id/item_right_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="@id/line_right"
            app:layout_constraintRight_toRightOf="@id/line_right"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.93"
            app:layout_constraintWidth_percent="0.9">

            <ImageView
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/math_xepdiem_que" />
        </com.kidsup.giaoducsom.view.DragConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>