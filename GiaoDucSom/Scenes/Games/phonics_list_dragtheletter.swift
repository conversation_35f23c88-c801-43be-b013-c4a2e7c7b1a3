//
//  phonics_list_dragtheletter.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 24/11/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable


class phonics_list_dragtheletter: GameFragment {
    var centerView = SVGImageView(SVGName: "bg obj white").then{
        $0.contentMode = .scaleAspectFit
    }
    var itemLetter = SVGImageView(SVGName: "option_bg_white_shadow")
    var itemLetterLabel = AutosizeLabel()
    var imageView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#E1EDFD")
        let centerArrowView = SVGImageView(SVGName: "taptrung_daychun_arrow")
        centerArrowView.contentMode = .scaleAspectFit
        addSubview(centerArrowView)
        
        let rightView = UIView()
        addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        let leftView = UIView()
        addSubview(leftView)        
        leftView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        centerArrowView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.2)
            make.width.lessThanOrEqualTo(leftView.snp.width).multipliedBy(0.8)
            make.height.equalTo(centerArrowView.snp.width)
        }
        
        rightView.addSubview(centerView)
        leftView.addSubview(itemLetter)
        itemLetter.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.5)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.5).priority(.high)
            make.height.equalToSuperview().multipliedBy(0.5).priority(.high)
            make.width.equalTo(itemLetter.snp.height)
        }
        itemLetter.addSubview(itemLetterLabel)
        itemLetterLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.8)
        }
        centerView.addSubview(imageView)
        centerView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.lessThanOrEqualToSuperview().multipliedBy(0.5)
            make.height.lessThanOrEqualToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.5).priority(.high)
            make.height.equalToSuperview().multipliedBy(0.5).priority(.high)
            make.width.equalTo(itemLetter.snp.height)
        }
        imageView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        itemLetter.isUserInteractionEnabled = true
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        startGame()
        loadNextStep()
    }
    var selectedView: UIView?
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        let view = itemLetter
        if state == .began {
            if sender.placeInView(view: view){
                UIView.animate(withDuration: 0.3) {
                    //view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                }
                selectedView = view
                return;
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if state == .ended || state == .cancelled || state == .failed {
            if sender.placeInView(view: centerView) {
                playSound(name: "effect/word puzzle drop")
                UIView.animate(withDuration: 0.3) {
                    self.selectedView!.alpha = 0
                }
                self.scheduler.schedule(delay: 0.5, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    self.didDrag()
                })
            } else {
                UIView.animate(withDuration: 0.3) {
                    self.selectedView!.transform = .identity
                }
            }
        }
        
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            let intros = self.parseIntroText()!
            for intro in intros {
                delay += self.playSound(name: intro.replacingOccurrences(of: "@", with: (tapdoc || isVocab() ? data?.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer)!), delay: delay)
            }
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    private var step = 0
    private var answer = ""
    private var values: [String] = []
    func loadNextStep(){
        if step >= values.count {
            var delay = 0.5
            if game.path != nil && game.path != nil {
                delay += self.playSound(name: "\(game.path!)/\(answer)", delay: delay)
                delay += 1
            }
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
        step += 1
        isRead = false
        isDrag = false
        itemLetter.alpha = 1
        itemLetter.transform = .identity
        //imageView.SVGName = "flashcards/\(game.level!)/\(answer).svg"
        imageView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(answer)))!] : "english phonics/\(game.level!)/\(answer).svg"
        itemLetterLabel.text = tapdoc || isVocab() ? data?.value : answer.prefix(1).string
        itemLetterLabel.textColor = .color(hex: "#849BFE")
        var delay = 0.5
        if step == 1 {
            let intros = self.parseIntroText()!
            for intro in intros {
                delay += self.playSound(name: intro.replacingOccurrences(of: "@", with: (tapdoc || isVocab() ? data?.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer)!), delay: delay)
            }
        }
        self.scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.didRead()
        }
    }
    var isRead = false
    var isDrag = false
    func didRead(){
        if !isRead {
            isRead = true
            if isRead && isDrag {
                loadNextStep()
            }
        }
    }
    func didDrag(){
        if !isDrag {
            isDrag = true
            if isRead && isDrag {
                loadNextStep()
            }
        }
    }
}
