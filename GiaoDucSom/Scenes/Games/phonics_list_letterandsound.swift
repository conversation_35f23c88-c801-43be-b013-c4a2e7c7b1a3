//
//  phonics_list_letterandsound.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 09/11/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_letterandsound: GameFragment {
    private var values : [String] = []
    var answerLayout = MyGridView()
    var bottomView = SVGImageView(SVGName: "btn bg blue").then {
        $0.isUserInteractionEnabled = true
    }
    
    var soundButton = SVGButton(SVGIcon: "btn sound3")
    var selectedValues: [String] = []
    
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#839BFE")
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#7BD2FF")
        addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        bottomView.isUserInteractionEnabled = true
        leftView.addSubview(bottomView)
        bottomView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview()
            make.height.equalTo(bottomView.snp.width).multipliedBy(151.0/400.0)
        }        
        bottomView.addSubview(answerLayout)
        answerLayout.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(1)
            make.height.equalToSuperview().multipliedBy(1)
            make.center.equalToSuperview()
        }
        
        let rightView = UIView()
        addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.top.right.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        rightView.addSubview(soundButton)
        soundButton.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
            make.width.equalTo(soundButton.snp.height).multipliedBy(56.0/59.0)            
        }
        soundButton.addTarget(self, action: #selector(soundClick), for: .touchUpInside)
    }
    @objc func soundClick(){
        pauseGame()
        self.playSound(name: answer)
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            var delay = 0.0
            for sound in self.parseIntroText()! {
                delay += self.playSound(name: sound.replacingOccurrences(of: "@", with: answer), delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        selectedValues = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 2)
        loadNextStep()
    }
    var step = 0
    var answer = ""
    func loadNextStep(){
        CoinAnimationUtils.shared.removeList()
        if step >= 1 {
            var delay = 0.0
            delay += playSound( delay: delay, names: finishEndSounds())
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = selectedValues[step]
        step += 1
        var delay = playSound(openGameSound())
        
        for sound in self.parseIntroText()! {
            delay += self.playSound(name: sound.replacingOccurrences(of: "@", with: answer), delay: delay)
        }
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
        
        var listViews : [UIView] = []
        for i in 0..<values.count {
            let view = createItem(text: values[i].uppercased())
            view.tag = 100 + i
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        answerLayout.itemRatio = 1
        answerLayout.itemSpacingRatio = 0.01
        answerLayout.insetRatio = 0.15
        answerLayout.columns = 3
        answerLayout.reloadItemViews(views: listViews)
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        for sound in self.parseIntroText()! {
            delay += self.playSound(name: sound.replacingOccurrences(of: "@", with: answer), delay: delay)
        }
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func createItem(text:String)->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        autoLabel.text = text
        autoLabel.textColor = .color(hex: "#68C1FF")
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        return view
    }
    @objc func itemClick(_ sender : UIControl){
        var label = sender.viewWithTag(1) as! AutosizeLabel
        if answer == label.text!.lowercased() {
            pauseGame()
            animateCoinIfCorrect(view: sender)
            label.textColor = .color(hex: "#73D048")
            var delay = 0.5
            let sounds = (tapdoc ? ["chữ"]: []) + ["\(label.text!.lowercased())1","said",label.text!.lowercased()]
            delay += playSound(name: answerCorrect1EffectSound(), delay: delay)
            delay += playSound(delay: delay, names: sounds)
            delay += playSound(name: getCorrectHumanSound(), delay: delay)
            delay += 0.3
            delay += 0.7
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
                self.resumeGame()
                self.loadNextStep()
            })
        } else {
            incorrect += 1
            pauseGame()
            setGameWrong()
            label.textColor = .color(hex: "#FF7761")
            var delay = 0.3
            delay += playSound(name: answerWrongEffectSound(), delay: delay)
            delay += playSound(delay: delay, names: (tapdoc ? ["chữ"]: []) + ["\(label.text!.lowercased())1","said",label.text!.lowercased()])
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
                label.textColor = .color(hex: "#68C1FF")
            })
        }
    }
    
    override func getSkills()->[GameSkill]{
        return [.GameListening]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}

