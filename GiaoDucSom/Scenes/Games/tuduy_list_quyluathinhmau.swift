//
//  tuduy_list_quyluathinhmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_quyluathinhmau: NhanBietGameFragment {
    // MARK: - Properties
    private let colors = [
        UIColor(red: 255/255, green: 0/255, blue: 0/255, alpha: 1), // #FF0000
        UIColor(red: 0/255, green: 255/255, blue: 0/255, alpha: 1), // #00FF00
        UIColor(red: 0/255, green: 0/255, blue: 255/255, alpha: 1), // #0000FF
        UIColor(red: 255/255, green: 255/255, blue: 0/255, alpha: 1), // #FFFF00
        UIColor(red: 0/255, green: 255/255, blue: 255/255, alpha: 1), // #00FFFF
        UIColor(red: 255/255, green: 0/255, blue: 255/255, alpha: 1) // #FF00FF
    ]
    private var colorIndexs: [Int] = []
    private var matrixColors: [[Int]]?
    private var points: [[Int]] = []
    private var pointsOK: [[Int]] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var viewDones: [UIView] = []
    private var gridLayout: MyGridView!
    private var leftGridLayout: MyGridView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFFFFF
        
        let leftContainer = UIView()
        leftContainer.backgroundColor = .white
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        let leftGridContainer = UIView()
        leftContainer.addSubview(leftGridContainer)
        leftGridContainer.makeViewCenterAndKeep(ratio: 1)
        leftGridLayout = MyGridView()
        leftGridLayout.backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1)
        leftGridContainer.addSubview(leftGridLayout)
        scheduler.schedule(delay: 1) {
            [weak self] in
            guard let self = self else { return }
            leftGridLayout.layer.cornerRadius = leftGridLayout.frame.width * 0.05
        }
        leftGridLayout.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        
        let rightContainer = UIView()
        rightContainer.backgroundColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(view).multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        let rightBackgroud = UIView()
        rightBackgroud.backgroundColor = .color(hex: "#74B6FF")
        rightContainer.addSubview(rightBackgroud)
        rightBackgroud.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.right.bottom.equalTo(self)
        }
        let rightGridContainer = UIView()
        rightContainer.addSubview(rightGridContainer)
        rightGridContainer.makeViewCenterAndKeep(ratio: 1)
        gridLayout = MyGridView()
        gridLayout.clipsToBounds = false
        rightGridContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "tuduy/tuduy_pattern9")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        colorIndexs = Utils.generatePermutation(3, size: colors.count)
        let files = StorageManager.manager.list(path: "topics/2D Shapes/")
            .filter { $0.hasSuffix(".svg") }
            .shuffled()
            .prefix(3)
            .map { "topics/2D Shapes/\($0)" }
        
        var matrixShapes = createMatrix()
        matrixColors = createMatrix()
        while matrixShapes[1][0] == matrixColors![1][0] {
            matrixColors = createMatrix()
        }
        
        while true {
            points = randomPoints(count: 3)
            let (x1, y1) = (points[0][0], points[0][1])
            let (x2, y2) = (points[1][0], points[1][1])
            let (x3, y3) = (points[2][0], points[2][1])
            if matrixShapes[x1][y1] == matrixShapes[x2][y2] && matrixShapes[x1][y1] == matrixShapes[x3][y3] {
                continue
            }
            if matrixColors![x1][y1] == matrixColors![x2][y2] && matrixColors![x1][y1] == matrixColors![x3][y3] {
                continue
            }
            if !checkAllPointsInSameLine(points: points) {
                break
            }
        }
        let svgs = files.map { Utilities.GetSVGKImage(named: $0) }
        var views: [UIView] = []
        for i in 0..<3 {
            for j in 0..<3 {
                let view = self.createItemPattern9(svg: svgs[matrixShapes[i][j]], color: self.colors[self.colorIndexs[matrixColors![i][j]]])
                view.backgroundColor = .white
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    view.layer.cornerRadius = view.frame.width * 0.2
                }
                let svgThumbnail = view.subviews.first as! UIImageView
                let point = [i, j]
                let showPoint = self.containPoint(points: self.points, point: point)
                svgThumbnail.alpha = showPoint ? 1 : 0
                let tag = [matrixShapes[i][j], matrixColors![i][j]]
                view.tag = tag[0] * 1000 + tag[1]
                if showPoint {
                    self.pointsOK.append(tag)
                }
                views.append(view)
            }
        }
        self.leftGridLayout.columns = 3
        self.leftGridLayout.itemRatio = 1
        self.leftGridLayout.itemSpacingRatio = 0.05
        self.leftGridLayout.insetRatio = 0.05
        self.leftGridLayout.reloadItemViews(views: views)
        
        let rightShapeIndexes = Utils.generatePermutation(3, size: 3)
        let rightColorIndexes = Utils.generatePermutation(3, size: 3)
        views = []
        for i in 0..<3 {
            for j in 0..<3 {
                let view = self.createItemPattern9(svg: svgs[rightShapeIndexes[i]], color: self.colors[self.colorIndexs[rightColorIndexes[j]]])
                view.backgroundColor = .white
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    view.layer.cornerRadius = view.frame.width * 0.2
                }
                let svgThumbnail = view.subviews.first as! UIImageView
                let tag = [rightShapeIndexes[i], rightColorIndexes[j]]
                let viewDone = self.containPoint(points: self.pointsOK, point: tag)
                svgThumbnail.alpha = viewDone ? 0 : 1
                view.alpha = viewDone ? 0.5 : 1
                view.tag = tag[0] * 1000 + tag[1]
                if viewDone {
                    self.viewDones.append(svgThumbnail)
                }
                views.append(view)
            }
        }
        self.gridLayout.columns = 3
        self.gridLayout.itemRatio = 1
        self.gridLayout.itemSpacingRatio = 0.05
        self.gridLayout.insetRatio = 0.05
        self.gridLayout.reloadItemViews(views: views)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_pattern9")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    var originX = 0.0, originY = 0.0
    var zPosition = 5.0
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: gridLayout)
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                zPosition += 1
                currentView.layer.zPosition = zPosition
                playSound("effect/cungchoi_pick\(random(1,2))")
                originX = currentView.frame.minX
                originY = currentView.frame.minY
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = location.x + dX
                let newY = location.y + dY
                currentView.frame.origin = CGPoint(x: newX, y: newY)
            }
            
        case .ended:
            if let currentView = currentView {
                var minDistance = CGFloat.greatestFiniteMagnitude
                var targetView: UIView?
                for child in leftGridLayout.subviews {
                    let dvector = currentView.distanceFromCenterToCenter(to: child)
                    let distance = hypot(dvector.x, dvector.y)
                    if distance < minDistance {
                        minDistance = distance
                        targetView = child
                    }
                }
                if minDistance < currentView.frame.width / 2, let targetView = targetView {
                    let tag = currentView.superview?.tag ?? 0
                    let targetTag = targetView.tag
                    if tag == targetTag {
                        playSound("effect/word puzzle drop")
                        currentView.moveToCenter(of: targetView, duration: 0.2)                        
                        viewDones.append(currentView)
                        if viewDones.count == 9 {
                            pauseGame()
                            animateCoinIfCorrect(view: leftGridLayout)
                            let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
                            scheduler.schedule(delay: delay) { [weak self] in
                                self?.finishGame()
                            }
                        } else {
                            playSound("effect/answer_correct")
                        }
                        self.currentView = nil
                        return
                    }
                }
                UIView.animate(withDuration: 0.8) {
                    currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                    currentView.transform = .identity
                }
                setGameWrong()
                playSound("effect/slide2")
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: Float, y: Float) -> UIView? {
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            if x >= Float(child.frame.minX) && x <= Float(child.frame.maxX) && y >= Float(child.frame.minY) && y <= Float(child.frame.maxY) {
                child.bringSubviewToFront(self)
                guard let svgThumbnail = child.subviews.first else { return nil }
                if viewDones.contains(svgThumbnail) { return nil }
                svgThumbnail.tag = child.tag
                return svgThumbnail
            }
        }
        return nil
    }
    
    private func createItemPattern9(svg: SVGKImage, color: UIColor) -> UIView {
        let view = UIView()
        let svgThumbnail = UIImageView()
        svgThumbnail.image = svg.uiImage.withRenderingMode(.alwaysTemplate)
        svgThumbnail.contentMode = .scaleAspectFit
        svgThumbnail.tintColor = color
        view.addSubview(svgThumbnail)
        svgThumbnail.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(svgThumbnail.snp.height) // Ratio 1:1
        }
        return view
    }
    
    private func containPoint(points: [[Int]], point: [Int]) -> Bool {
        return points.contains { $0[0] == point[0] && $0[1] == point[1] }
    }
    
    private func createMatrix() -> [[Int]] {
        var matrix = [[Int]](repeating: [0, 0, 0], count: 3)
        matrix[0] = [0, 1, 2]
        while true {
            matrix[1] = Utils.generatePermutation(3, size: 3)
            if matrix[0][0] != matrix[1][0] && matrix[0][1] != matrix[1][1] && matrix[0][2] != matrix[1][2] {
                break
            }
        }
        matrix[2][0] = 3 - matrix[0][0] - matrix[1][0]
        matrix[2][1] = 3 - matrix[0][1] - matrix[1][1]
        matrix[2][2] = 3 - matrix[0][2] - matrix[1][2]
        return matrix
    }
    
    private func randomPoints(count: Int) -> [[Int]] {
        var points: [[Int]] = []
        for _ in 0..<count {
            while true {
                let x = Int.random(in: 0..<3)
                let y = Int.random(in: 0..<3)
                let newPoint = [x, y]
                if !containPoint(points: points, point: newPoint) {
                    points.append(newPoint)
                    break
                }
            }
        }
        return points
    }
    
    private func checkAllPointsInSameLine(points: [[Int]]) -> Bool {
        if points.count < 2 { return true }
        let x0 = points[0][0]
        let y0 = points[0][1]
        let dx = points[1][0] - x0
        let dy = points[1][1] - y0
        for i in 2..<points.count {
            let x = points[i][0]
            let y = points[i][1]
            if dx * (y - y0) != dy * (x - x0) { return false }
        }
        return true
    }
}

