//
//  phonics_list_unscramble.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_unscramble: GameFragment {
    private var values : [String] = []
    var answer = ""
    var svgViewContainer = UIView()
    var svgView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    var bottomView = UIView().then{
        $0.backgroundColor = .color(hex: "#C3E87C")
    }
    var gridBottom1 = MyGridView()
    var gridBottom2 = MyGridView()
    var selectedView : UIView?
    var zPosition : CGFloat = 0
    var busy = false
    var listViews: [UIView] = []
    var listviews2 : [UIView] = []
    var originListviews : [UIView] = []
    var move = 0
    let button = KUButton().then{
        $0.setBackgroundImage(SVGKImage(contentsOf: Utilities.SVGURL(of: "btn sound2")).uiImage, for: .normal)
        $0.contentMode = .scaleAspectFit
    }
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#88D35A")
        addSubview(svgViewContainer)
        let svgViewBackground = SVGImageView(SVGName: "bg obj white").then{
            $0.contentMode = .scaleAspectFit
        }
        
        svgViewContainer.addSubview(svgViewBackground)
        svgViewContainer.addSubview(svgView)
        addSubview(bottomView)
        bottomView.snp.makeConstraints{ make in
            make.left.bottom.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        svgViewContainer.snp.makeConstraints{ make in
            make.left.right.equalToSuperview()
            make.top.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top)
        }
        svgViewBackground.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        svgView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        bottomView.addSubview(gridBottom1)
        bottomView.addSubview(gridBottom2)
        gridBottom1.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        gridBottom1.alpha = 0.01
        gridBottom2.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
        if hasPath() {
            svgViewBackground.isHidden = true
            let buttonContainer = UIView()
            addSubview(buttonContainer)
            buttonContainer.snp.makeConstraints{ make in
                make.left.right.equalToSuperview()
                make.top.equalToSuperview()
                make.bottom.equalTo(bottomView.snp.top)
            }
            let smallButton = UIView()
            buttonContainer.addSubview(smallButton)
            smallButton.snp.makeConstraints{ make in
                make.width.height.equalToSuperview().multipliedBy(0.5)
                make.center.equalToSuperview()
            }
            buttonContainer.addSubview(smallButton)
            smallButton.addSubview(button)
            button.makeViewCenterAndKeep(ratio: 56.0/59.0)
            button.addTarget(self, action: #selector(replayClicked), for: .touchUpInside)
        }
    }
    
    func hasPath()->Bool {
        return game.path != nil && game.path != ""
    }
    @objc func replayClicked(){
        self.playSound(name: hasPath() ? "\(game.path!)/\(answer)" : answer, delay: 0)
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = self.playSound(delay: 0, names: self.parseIntroText()!)
            delay += self.playSound(name: hasPath() ? "\(game.path!)/\(answer)" : answer, delay: delay)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        answer = values.randomOrder()[0]
        if !hasPath() {
            svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(answer)))!] : "english phonics/\(game.level!)/\(answer).svg"
        }
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: hasPath() ? "\(game.path!)/\(answer)" : answer, delay: delay)
        
        
        for i in 0..<answer.count {
            let value = String(answer[i])
            let view = createItem()
            let label: AutosizeLabel = view.viewWithTag(1) as! AutosizeLabel
            label.text = String(value)
            view.tag = 100 + i
            listViews.append(view)
        }
        originListviews = listViews
        gridBottom1.columns = answer.count
        gridBottom1.itemRatio = 40 / 42.0
        gridBottom1.itemSpacingRatio = 0.001
        gridBottom1.insetRatio = 0.01
        gridBottom1.reloadItemViews(views: listViews)
        
        var randomIndexes : [Int] = []
        while true {
            randomIndexes = Array(0..<answer.count).randomOrder()
            var foundAnswer = false
            for i in 0..<answer.count {
                if answer[i] != answer[randomIndexes[i]] {
                    foundAnswer = true
                    break
                }
            }
            if foundAnswer {
                break
            }
        }
        for i in 0..<answer.count {
            let value = String(answer[randomIndexes[i]])
            let view = createItem()
            let label: AutosizeLabel = view.viewWithTag(1) as! AutosizeLabel
            label.text = String(value)
            view.tag = 100 + randomIndexes[i]
            listviews2.append(view)
        }
        
        gridBottom2.columns = answer.count
        gridBottom2.itemRatio = 40 / 42.0
        gridBottom2.itemSpacingRatio = 0.001
        gridBottom2.insetRatio = 0.01
        gridBottom2.reloadItemViews(views: listviews2)
            
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: hasPath() ? "\(game.path!)/\(answer)" : answer, delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    var imageBottom = SVGKImage(contentsOf: Utilities.SVGURL(of: "btn white bg light green2")).uiImage
    func createItem()->UIView{
        let view = UIView()
        let background = UIImageView()
        background.image = imageBottom
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        let autoLabel = AutosizeLabel(frame: CGRectZero)
        autoLabel.tag = 1
        background.addSubview(autoLabel)
        autoLabel.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        autoLabel.textColor = .color(hex: "#1497E0")
        return view
    }
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            for view in gridBottom2.subviews {
                if sender.placeInView(view: view){
                    selectedView = view
                    zPosition += 1
                    view.layer.zPosition = zPosition
                    UIView.animate(withDuration: 0.3) {
                        view.transform = CGAffineTransformMakeScale(1.1, 1.1)
                    }
                    //let background = view.viewWithTag(2) as! SVGImageView
                    //background.SVGName = "drag bg3"
                    playSound("effect/cungchoi_pick\(random(1,2))")
                    return;
                }
            }
            selectedView = nil
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if !busy {
            guard let selectedView = selectedView else { return }
            var oldIndex = listviews2.firstIndex(of: selectedView)!
            for i in 0..<listviews2.count {
                let view2 = listviews2[i]
                if selectedView == view2 {
                    continue
                }
                if sender.placeInView(view: view2) {
                    playSound(name: "en/english phonics/effects/bubble" + String(Int.random(in: 1...4)))
                    busy = true
                    scheduler.schedule(delay: 0.3, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        self.busy = false
                    })
                    var index = i
                    var delta = index > oldIndex ? 1 : -1
                    var start = oldIndex
                    while start != index {
                        var viewTmp = listviews2[start]
                        listviews2[start] = listviews2[start + delta]
                        listviews2[start + delta] = viewTmp
                        start += delta
                    }
                    for j in 0..<listviews2.count {
                        if listviews2[j] != selectedView {
                            let dest = originListviews[j]
                            let source = listviews2[j]
                            var p = dest.convert( CGPointMake(dest.bounds.width / 2, dest.bounds.height / 2), to: source)
                            p.x -= source.bounds.width / 2
                            p.y -= source.bounds.height / 2
                            UIView.animate(withDuration: 0.2) {
                                var tran = source.transform
                                tran.tx += p.x
                                tran.ty += p.y
                                source.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
                            } completion: { [weak self] done in
                                guard let self = self else { return }
                                
                            }
                        }
                    }
                    break
                }
            }
        }
        if state == .ended || state == .cancelled || state == .failed {
            playSound("effect/word puzzle drop")
            for j in 0..<listviews2.count {
                if listviews2[j] == selectedView {
                    let source = listviews2[j]
                    let dest = originListviews[j]
                    var p = dest.convert( CGPointMake(dest.bounds.width / 2, dest.bounds.height / 2), to: source)
                    p.x -= source.bounds.width / 2
                    p.y -= source.bounds.height / 2
                    UIView.animate(withDuration: 0.2) {
                        var tran = source.transform
                        tran.tx += p.x
                        tran.ty += p.y
                        source.transform = CGAffineTransformMakeTranslation(tran.tx, tran.ty)
                    } completion: { [weak self] done in
                        guard let self = self else { return }
                        
                    }
                }
            }
            for j in 0..<listviews2.count {
                let textview = listviews2[j].viewWithTag(1) as! AutosizeLabel
                let textview2 = originListviews[j].viewWithTag(1) as! AutosizeLabel
                let rightOrder = textview.text == textview2.text
                textview.textColor = .color(hex: rightOrder ? "#73D048" : "#68C1FF")
            }
            let index = listviews2.firstIndex(of: selectedView!)!
            scheduler.schedule(delay: 0.3, execute: {
                [weak self] in
                guard let self = self else { return }
                for view in self.listviews2 {
                    view.transform = .identity
                }
                self.gridBottom2.reloadItemViews(views: self.listviews2)
            })
            move += 1
            checkFinish()
        }
    }
    func checkFinish(){
        var finish = true
        for j in 0..<listviews2.count {
            let textview = listviews2[j].viewWithTag(1) as! AutosizeLabel
            let textview2 = listViews[j].viewWithTag(1) as! AutosizeLabel
            let rightOrder = textview.text == textview2.text
            if !rightOrder {
                finish = false
                break
            }
        }
        if finish {
            pauseGame()
            var delay = 0.0
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                if hasPath() {
                    button.animateCoin(answer: true)
                } else {
                    svgView.animateCoin(answer: true)
                }
            })
            delay += 1
            delay += playSound(delay: delay, names:[answerCorrect1EffectSound(), getCorrectHumanSound(), endGameSound()])
            delay += 1
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading]
    }
    override func getScore()->Float{
        var score = Float(answer.count) / Float(move)
        if score > 1 {
            score = 1
        }
        return score
    }
}
