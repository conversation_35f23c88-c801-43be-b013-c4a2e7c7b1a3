//
//  ngonngu_list_timchu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import SnapKit
import AVFAudio

class ngonngu_list_timchu: NhanBietGameFragment {
    // MARK: - Properties
    private let alphabets: [[String]] = [
        ["a", "ă", "â"], ["b", "c"], ["d", "đ"], ["e", "ê"],
        ["g", "h"], ["i", "k"], ["l", "m", "n"], ["o", "ô", "ơ"],
        ["p", "q"], ["r", "s", "t"], ["u", "ư"], ["v", "x", "y"]
    ]
    private var letters: [String] = []
    private var meIndex: Int = 0
    private var gridLayout: MyGridView!
    private var values: [String] = []
    private var count: Int = 0
    private var player: AVAudioPlayer?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#D5F9FF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let mainContainer = UIView()
        mainContainer.clipsToBounds = false
        view.addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.columns = 5
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.01
        gridLayout.insetRatio = 0.01
        mainContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(gridLayout.snp.width).multipliedBy(3.0 / 5.0) // Ratio 5:3
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        letters = alphabets.randomElement()!.shuffled()
        letters.append(contentsOf: letters.map { $0.uppercased() })
        letters.shuffle()
        meIndex = Int.random(in: 0..<2)
        
        while true {
            values.removeAll()
            for _ in 0..<15 {
                values.append(letters.randomElement()!)
            }
            let size = values.filter { $0.lowercased() == letters[meIndex].lowercased() }.count
            if size >= 3 && size <= 6 {
                count = size
                break
            }
        }
        
        var views: [UIView] = []
        for i in 0..<values.count {
            let view = createItemView(value: values[i])
            views.append(view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.stringTag = "\(i)"
        }
        
        gridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        
        let delay = playSound(delay: 0, names: [
            openGameSound(),
            "ngonngu/ngonngu_timchu",
            "topics/Alphabet/\(letters[meIndex].lowercased())"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound(delay: 0, names: [
                "ngonngu/ngonngu_timchu",
                "topics/Alphabet/\(letters[meIndex].lowercased())"
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let index = Int(view.stringTag ?? "0"),
              index < values.count else { return }
        
        let textNumber = view.findSubviews(ofType: HeightRatioTextView.self).first!
        if values[index].lowercased() == letters[meIndex].lowercased() {
            count -= 1
            var delay: TimeInterval = 0
            UIView.animate(withDuration: 0.2) {
                view.transform = CGAffineTransform(scaleX: 1.2, y: 1.2).rotated(by: 0)
            }
            if count == 0 {
                delay = playSound(delay: 0, names: [
                    "effect/answer_end",
                    self.getCorrectHumanSound(),
                    self.endGameSound()
                ])
                animateCoinIfCorrect(view: textNumber)
            } else {
                delay = playSound("effect/answer_correct")
            }
            textNumber.textColor = UIColor.color(hex: "#73D047")
            view.gestureRecognizers?.removeAll()
            
            if count == 0 {
                pauseGame(stopMusic: false)
                scheduler.schedule(after: delay + 1) { [weak self] in
                    self?.finishGame()
                }
            }
        } else {
            pauseGame(stopMusic: false)
            setGameWrong()
            let delay = playSound(delay: 0, names: [
                "effect/answer_wrong",
                "answer_wrong\(Int.random(in: 1...2))"
            ])
            textNumber.textColor = UIColor.color(hex: "#FF7760")
            scheduler.schedule(after: delay) { [weak self] in
                textNumber.textColor = UIColor.color(hex: "#68C1FF")
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemView(value: String) -> UIView {
        let view = UIView()
        view.clipsToBounds = false
        view.isUserInteractionEnabled = true
        
        let textNumber = HeightRatioTextView()
        textNumber.setHeightRatio(0.8)
        textNumber.textAlignment = .center
        textNumber.textColor = UIColor.color(hex: "#68C1FF")
        textNumber.font = .Freude(size: 20)
        textNumber.text = value
        view.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let rotation = CGFloat.random(in: -10...10)
        view.transform = CGAffineTransform(rotationAngle: rotation.degreesToRadians)
        
        return view
    }
}
