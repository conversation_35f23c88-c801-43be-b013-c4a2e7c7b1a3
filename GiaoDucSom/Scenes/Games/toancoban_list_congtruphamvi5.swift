//
//  toancoban_list_congtruphamvi5.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_congtruphamvi5
class toancoban_list_congtruphamvi5: NhanBietGameFragment {
    // MARK: - Properties
    private var a = 0
    private var b = 0
    private var textA: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textC: AutosizeLabel!
    private var textOperator: AutosizeLabel!
    private var sliderView: MySliderView!
    private var operatorSymbol: String?

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")

        let topView = UIView()
        topView.stringTag = "top_view"
        view.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }

        let innerView = UIView()
        topView.addSubview(innerView)
        innerView.makeViewCenterAndKeep(ratio: 7.0)

        textA = AutosizeLabel()
        textA.stringTag = "text_a"
        textA.text = "1"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 30)
        textA.textAlignment = .center
        innerView.addSubview(textA)
        

        textOperator = AutosizeLabel()
        textOperator.stringTag = "text_operator"
        textOperator.text = "+"
        textOperator.textColor = UIColor(hex: "#87D657")
        textOperator.font = .Freude(size: 30)
        textOperator.textAlignment = .center
        innerView.addSubview(textOperator)
        

        textB = AutosizeLabel()
        textB.stringTag = "text_b"
        textB.text = "2"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 30)
        textB.textAlignment = .center
        innerView.addSubview(textB)
        

        let textEqual = AutosizeLabel()
        textEqual.stringTag = "text_equal"
        textEqual.text = "="
        textEqual.textColor = UIColor(hex: "#74B6FF")
        textEqual.font = .Freude(size: 30)
        textEqual.textAlignment = .center
        innerView.addSubview(textEqual)
        
        
        let resultBg = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        resultBg.stringTag = "result_bg"
        innerView.addSubview(resultBg)
        

        textC = AutosizeLabel()
        textC.stringTag = "text_c"
        textC.text = "?"
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 30)
        textC.textAlignment = .center
        innerView.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textC.snp.height).multipliedBy(0.8)
            make.left.equalTo(textEqual.snp.right)
        }
        textEqual.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textEqual.snp.height).multipliedBy(0.8)
            make.right.equalTo(textC.snp.left)
        }

        textOperator.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textOperator.snp.height).multipliedBy(0.8)
            make.right.equalTo(textB.snp.left)
        }
        
        textB.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textB.snp.height).multipliedBy(0.8)
            make.right.equalTo(textEqual.snp.left)
            make.centerX.equalToSuperview()
        }
        
        textA.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textA.snp.height).multipliedBy(0.8)
            make.right.equalTo(textOperator.snp.left)
        }
        resultBg.snp.makeConstraints { make in
            make.left.right.centerY.equalTo(textC)
            make.height.equalTo(resultBg.snp.width)
        }

        let bottomView = UIView()
        bottomView.stringTag = "bottom_view"
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(topView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        sliderView = MySliderView()
        sliderView.stringTag = "slider_view"
        bottomView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(sliderView.snp.width).dividedBy(6)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }
        sliderView.setMinValue(0)
        sliderView.setMaxValue(5)
        sliderView.setStep(1)
        sliderView.setSelectedTick(0)
        sliderView.setListener { [weak self] value in
            guard let self = self else { return }
            self.textC.text = "\(value)"
            let correct = value == self.a + self.b
            self.textC.textColor = correct ? UIColor(hex: "#87D657") : UIColor(hex: "#FF7760")
            self.pauseGame()
            self.sliderView.setRight(correct)
            if correct {
                self.animateCoinIfCorrect(view: self.textC)
                let delay = self.playSound(self.answerCorrect1EffectSound(), self.getCorrectHumanSound(), "topics/Numbers/\(self.a)", self.operatorSymbol == "+" ? "toan/cộng" : "toan/trừ", "topics/Numbers/\(abs(self.b))", "toan/bằng", "topics/Numbers/\(self.a + self.b)", self.endGameSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                self.setGameWrong()
                self.playSound("effect/answer_wrong", self.getWrongHumanSound())
                self.scheduler.schedule(delay: 0.5) { [weak self] in
                    guard let self = self else { return }
                    self.textC.text = "?"
                    self.textC.textColor = UIColor(hex: "#74B6FF")
                    self.resumeGame()
                }
            }
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = Int.random(in: 0...5)
            b = Int.random(in: -5...5)
            if a + b >= 0 && a + b <= 5 {
                textA.text = "\(a)"
                operatorSymbol = b > 0 ? "+" : b < 0 ? "-" : ["+", "-"].randomElement()
                textOperator.text = operatorSymbol
                if operatorSymbol == "-" {
                    textOperator.textColor = UIColor(hex: "#FF7760")
                }
                textB.text = "\(abs(b))"
                if a + b == 0 {
                    sliderView.setSelectedTick(5)
                }
                break
            }
        }
        let delay = playSound(openGameSound(), "toan/toan_congtruphamvi5")
        scheduler.schedule(delay: Double(delay)) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_congtruphamvi5")
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}




// MARK: - MySliderView
class MySliderView: UIView {
    // MARK: - Properties
    private var scaleFactor: CGFloat = 1.0
    private var listener: ((Int) -> Void)?
    private var scrollToListener: ((Int, Int) -> Void)?
    private var scrollEndListener: ((Int, Int) -> Void)?
    private var animating = false
    private var thumbDrawable: UIImage?
    private let normalThumbDrawable = Utilities.SVGImage(named: "toan_slider_blue")
    private let rightThumbDrawable = Utilities.SVGImage(named: "toan_slider_green")
    private let wrongThumbDrawable = Utilities.SVGImage(named: "toan_slider_red")
    private var minValue = 0
    private var maxValue = 30
    private var step = 5
    private var selectedTick = 3
    private var currentThumbX: CGFloat = 0
    private var right: Bool?
    private let lineColor = UIColor(hex: "#BDD0D7")
    private let tickColor = UIColor(hex: "#91A2AE")
    private var highlightColor = UIColor(hex: "#74B6FF")
    private let normalHighlightColor = UIColor(hex: "#74B6FF")
    private let rightHighlightColor = UIColor(hex: "#88D758")
    private let wrongHighlightColor = UIColor(hex: "#FF7761")
    private var lineWidth: CGFloat = 4
    private var tickWidth: CGFloat = 6
    private var tickHeight: CGFloat = 40
    private var textSize: CGFloat = 40
    private var thumbRadius: CGFloat = 30
    private var padding: CGFloat = 20
    private let paintLine = Paint()
    private let paintTick = Paint()
    private let paintText = Paint()
    private var textMap: [Int: String] = [:]
    var horizontalPadding: CGFloat = 1

    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }

    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }

    private func initView() {
        clipsToBounds = false
        layer.masksToBounds = false
        backgroundColor = .clear
        paintLine.color = lineColor
        paintLine.strokeWidth = lineWidth
        paintLine.style = .stroke
        paintTick.color = tickColor
        paintTick.strokeWidth = tickWidth
        paintTick.style = .stroke
        paintText.color = tickColor
        paintText.textSize = textSize
        paintText.textAlign = .center
        thumbDrawable = normalThumbDrawable
        isUserInteractionEnabled = true
    }

    // MARK: - Layout
    override func layoutSubviews() {
        super.layoutSubviews()
        let viewHeight = bounds.height
        lineWidth = viewHeight / 20
        tickWidth = lineWidth
        tickHeight = tickWidth * 5
        textSize = tickHeight
        thumbRadius = tickHeight
        paintLine.strokeWidth = lineWidth
        paintTick.strokeWidth = tickWidth
        paintText.textSize = textSize
        padding = tickHeight * horizontalPadding
        let totalTicks = getTotalTicks()
        let spacing = (bounds.width - 2 * padding) / CGFloat(totalTicks - 1)
        if !animating {
            currentThumbX = padding + CGFloat(selectedTick) * spacing
        }
    }

    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        super.draw(rect)
        guard let context = UIGraphicsGetCurrentContext() else { return }
        let width = rect.width - 2 * padding
        let centerY = rect.height / 2
        context.move(to: CGPoint(x: padding, y: centerY))
        context.addLine(to: CGPoint(x: width + padding, y: centerY))
        context.setStrokeColor(paintLine.color.cgColor)
        context.setLineWidth(paintLine.strokeWidth)
        context.strokePath()
        let totalTicks = getTotalTicks()
        let spacing = width / CGFloat(totalTicks - 1)
        for i in 0..<totalTicks {
            let x = padding + CGFloat(i) * spacing
            paintTick.color = i == selectedTick ? (right == nil ? normalHighlightColor : right! ? rightHighlightColor : wrongHighlightColor) : tickColor
            paintText.color = paintTick.color
            
            // Draw tick mark
            context.move(to: CGPoint(x: x, y: centerY - tickHeight / 2))
            context.addLine(to: CGPoint(x: x, y: centerY + tickHeight / 2))
            context.setStrokeColor(paintTick.color.cgColor)
            context.setLineWidth(paintTick.strokeWidth)
            context.setLineCap(.round)
            context.setLineJoin(.round)
            context.strokePath()
            
            // Draw label
            let label = textMap[i] ?? "\(minValue + i * step)"
            let textY = rect.height * 0
            
            // Set up centered text attributes
            let paragraphStyle = NSMutableParagraphStyle()
            paragraphStyle.alignment = .center
            let attributes: [NSAttributedString.Key: Any] = [
                .font: UIFont.Freude(size: paintText.textSize),
                .foregroundColor: paintText.color,
                .paragraphStyle: paragraphStyle
            ]
            
            // Define a rectangle for centered text
            let textWidth: CGFloat = 200 // Adjust based on your label width
            let textRect = CGRect(x: x - textWidth / 2, y: textY, width: textWidth, height: paintText.textSize * 1.2)
            let attributedString = NSAttributedString(string: label, attributes: attributes)
            attributedString.draw(in: textRect)
        }
        let thumbY = centerY + tickHeight / 2 + thumbRadius * 0.9
        let halfH = thumbRadius * 0.6
        let halfW = halfH * 173 / 221
        thumbDrawable = right == nil ? normalThumbDrawable : right! ? rightThumbDrawable : wrongThumbDrawable
        if let thumbDrawable = thumbDrawable {
            context.saveGState()
            context.translateBy(x: currentThumbX, y: thumbY)
            context.scaleBy(x: 1, y: scaleFactor)
            context.translateBy(x: -currentThumbX, y: -thumbY)
            thumbDrawable.draw(in: CGRect(x: currentThumbX - halfW, y: thumbY - halfH, width: halfW * 2, height: halfH * 2))
            context.restoreGState()
        }
    }

    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard !animating, let touch = touches.first else { return }
        right = nil
        QueueSoundPlayer.shared.play(sound: "effect/cungchoi_pick\(Int.random(in: 1...2))", delay: 0)
        Utilities.vibrate()
        animateScaleFactor(from: 1.0, to: 0.8)
        handleTouch(touch)
    }

    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard !animating, let touch = touches.first else { return }
        handleTouch(touch)
    }

    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard !animating, let touch = touches.first else { return }
        QueueSoundPlayer.shared.play(sound: "effect/word puzzle drop", delay: 0)
        Utilities.vibrate()
        animateScaleFactor(from: 0.8, to: 1.0)
        let x = touch.location(in: self).x
        let totalTicks = getTotalTicks()
        let spacing = (bounds.width - 2 * padding) / CGFloat(totalTicks - 1)
        selectedTick = Int(round((clampX(x) - padding) / spacing))
        selectedTick = max(0, min(selectedTick, totalTicks - 1))
        let finalX = padding + CGFloat(selectedTick) * spacing
        animating = true
        UIView.animate(withDuration: 0.3, animations: {
            self.currentThumbX = finalX
            self.setNeedsDisplay()
        }) { _ in
            self.animating = false
            self.listener?(self.minValue + self.selectedTick * self.step)
            self.scrollEndListener?(self.minValue + self.selectedTick * self.step, self.selectedTick)
        }
    }

    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }

    private func handleTouch(_ touch: UITouch) {
        let x = touch.location(in: self).x
        let totalTicks = getTotalTicks()
        let spacing = (bounds.width - 2 * padding) / CGFloat(totalTicks - 1)
        currentThumbX = clampX(x)
        let newSelected = Int(round((currentThumbX - padding) / spacing))
        let clampedSelected = max(0, min(newSelected, totalTicks - 1))
        if clampedSelected != selectedTick {
            selectedTick = clampedSelected
            Utilities.vibrate()
            scrollToListener?(minValue + selectedTick * step, selectedTick)
        }
        setNeedsDisplay()
    }

    private func animateScaleFactor(from: CGFloat, to: CGFloat) {
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [from, to]
        let timeChange = Interpolate(values: [0,1],
        apply: { [weak self] (value) in
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self?.scaleFactor = finalValue
            self?.setNeedsDisplay()
        })
        timeChange.animate(1, duration: 0.3){
            
        }            
    }

    private func getTotalTicks() -> Int {
        return (maxValue - minValue) / step + 1
    }

    private func clampX(_ x: CGFloat) -> CGFloat {
        return max(padding, min(x, bounds.width - padding))
    }

    // MARK: - Public Methods
    func setMinValue(_ value: Int) {
        minValue = value
        setNeedsDisplay()
    }

    func setMaxValue(_ value: Int) {
        maxValue = value
        setNeedsDisplay()
    }

    func setStep(_ value: Int) {
        step = value
        setNeedsDisplay()
    }

    func setSelectedTick(_ value: Int) {
        selectedTick = value
        let totalTicks = getTotalTicks()
        let spacing = (bounds.width - 2 * padding) / CGFloat(totalTicks - 1)
        if !animating {
            currentThumbX = padding + CGFloat(selectedTick) * spacing
        }
        setNeedsDisplay()
    }

    func setRight(_ value: Bool?) {
        right = value
        setNeedsDisplay()
    }
    
    func setTextAtPosition(_ text: String, at position: Int) {
        self.setTextAtPosition(text, at: position, clearOld: true)
    }
    
    func setTextAtPosition(_ text: String, at position: Int, clearOld: Bool) {
        if clearOld {
            textMap.removeAll()
        }
        textMap[position] = text
        setNeedsDisplay()
    }
    
    @discardableResult
    func setListener(_ listener: @escaping (Int) -> Void) -> MySliderView {
        self.listener = listener
        return self
    }
    
    @discardableResult
    func setScrollToListener(_ listener: @escaping (Int, Int) -> Void) -> MySliderView {
        self.scrollToListener = listener
        return self
    }
    
    @discardableResult
    func setScrollEndListener(_ listener: @escaping (Int, Int) -> Void) -> MySliderView {
        self.scrollEndListener = listener
        return self
    }
}
