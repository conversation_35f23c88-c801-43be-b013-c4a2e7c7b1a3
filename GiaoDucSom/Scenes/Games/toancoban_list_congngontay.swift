//
//  toancoban_list_congngontay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/4/25.
//


import UIKit
import SnapKit

class toancoban_list_congngontay: NhanBietGameFragment {
    // MARK: - Properties
    private var imageLeft: UIImageView!
    private var imageRight: UIImageView!
    private var value: Int = 0
    private var leftValue: Int = 0
    private var rightValue: Int = 0
    private var numpad: MathNumpad!
    private var answerText: UILabel!
    private var textLeft: UILabel!
    private var textRight: UILabel!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 102/255, green: 53/255, blue: 53/255, alpha: 1) // #663535
        
        let groundView = UIView()
        view.addSubview(groundView)
        groundView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.415)
        }
        
        let colorView = UIView()
        colorView.backgroundColor = UIColor(hex: "#482828")
        groundView.addSubview(colorView)
        colorView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.right.bottom.equalTo(self)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let answerLayout = UIImageView()
        answerLayout.isUserInteractionEnabled = true
        answerLayout.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        view.addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.18)
            make.height.equalTo(answerLayout.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            answerLayout.snapToHorizontalBias(horizontalBias: 0.6)
        }
        
        answerText = AutosizeLabel()
        answerText.text = "?"
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        answerText.font = .Freude(size: 20)
        answerText.textAlignment = .center
        answerText.adjustsFontSizeToFitWidth = true
        answerText.minimumScaleFactor = 0.1
        answerLayout.addSubview(answerText)
        answerText.snp.makeConstraints { make in
            make.height.equalTo(answerLayout).multipliedBy(0.75)
            make.width.equalTo(answerLayout)
            make.center.equalToSuperview()
        }
        
        let handsContainer = UIView()
        handsContainer.clipsToBounds = false
        view.addSubview(handsContainer)
        handsContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(answerLayout.snp.left)
        }
        
        let innerHandsContainer = UIView()
        handsContainer.addSubview(innerHandsContainer)
        innerHandsContainer.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.left.equalToSuperview().inset(20)
            make.right.equalToSuperview()
            make.width.equalTo(innerHandsContainer.snp.height).multipliedBy(4.0) // Ratio 4:1
        }
        
        let leftContainer = UIView()
        innerHandsContainer.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.25)
            make.height.equalTo(leftContainer.snp.width) // Ratio 1:1
            make.top.bottom.left.equalToSuperview()
        }
        
        textLeft = AutosizeLabel()
        textLeft.text = "1"
        textLeft.textColor = UIColor(hex: "#F1A78E")
        textLeft.font = .Freude(size: 20)
        textLeft.textAlignment = .center
        textLeft.adjustsFontSizeToFitWidth = true
        textLeft.minimumScaleFactor = 0.1
        textLeft.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        leftContainer.addSubview(textLeft)
        textLeft.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let plusContainer = UIView()
        innerHandsContainer.addSubview(plusContainer)
        plusContainer.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.25)
            make.height.equalTo(plusContainer.snp.width) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.left.equalTo(leftContainer.snp.right)
        }
        
        let plusText = AutosizeLabel()
        plusText.text = "+"
        plusText.textColor = UIColor(hex: "#F1A78E")
        plusText.font = .Freude(size: 20)
        plusText.textAlignment = .center
        plusText.adjustsFontSizeToFitWidth = true
        plusText.minimumScaleFactor = 0.1
        plusText.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        plusContainer.addSubview(plusText)
        plusText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let rightContainer = UIView()
        innerHandsContainer.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.25)
            make.height.equalTo(rightContainer.snp.width) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.left.equalTo(plusContainer.snp.right)
        }
        
        textRight = AutosizeLabel()
        textRight.text = "2"
        textRight.textColor = UIColor(hex: "#F1A78E")
        textRight.font = .Freude(size: 20)
        textRight.textAlignment = .center
        textRight.adjustsFontSizeToFitWidth = true
        textRight.minimumScaleFactor = 0.1
        textRight.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        rightContainer.addSubview(textRight)
        textRight.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let equalContainer = UIView()
        innerHandsContainer.addSubview(equalContainer)
        equalContainer.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.25)
            make.height.equalTo(equalContainer.snp.width) // Ratio 1:1
            make.top.bottom.right.equalToSuperview()
            make.left.equalTo(rightContainer.snp.right)
        }
        
        let equalText = AutosizeLabel()
        equalText.text = "="
        equalText.textColor = UIColor(hex: "#F1A78E")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalText.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        equalContainer.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageLeft = UIImageView()
        imageLeft.contentMode = .scaleAspectFit
        imageLeft.transform = CGAffineTransform(scaleX: -1.5, y: 1.5) // scaleX="-1.5", scaleY="1.5"
        innerHandsContainer.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.2)
            make.height.equalTo(imageLeft.snp.width) // Ratio 1:1
            make.bottom.equalTo(innerHandsContainer.snp.top)
            make.right.equalToSuperview().multipliedBy(0.22)
        }
        
        imageRight = UIImageView()
        imageRight.contentMode = .scaleAspectFit
        imageRight.transform = CGAffineTransform(scaleX: 1.5, y: 1.5) // scaleX="1.5", scaleY="1.5"
        innerHandsContainer.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.width.equalTo(innerHandsContainer).multipliedBy(0.2)
            make.height.equalTo(imageRight.snp.width) // Ratio 1:1
            make.bottom.equalTo(innerHandsContainer.snp.top)
            make.right.equalToSuperview().multipliedBy(0.72)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        value = 2 + Int.random(in: 0..<9)
        while true {
            leftValue = Int.random(in: 0..<value)
            rightValue = value - leftValue
            if leftValue >= 1 && leftValue <= 5 && rightValue >= 1 && rightValue <= 5 {
                break
            }
        }
        
        textLeft.text = String(leftValue)
        textRight.text = String(rightValue)
        
        let handImages: [Int: String] = [
            1: "math_hand_1",
            2: "math_hand_2",
            3: "math_hand_3",
            4: "math_hand_4",
            5: "math_hand_5"
        ]
        imageLeft.image = leftValue == 0 ? nil : Utilities.SVGImage(named: handImages[leftValue] ?? "")
        imageRight.image = rightValue == 0 ? nil : Utilities.SVGImage(named: handImages[rightValue] ?? "")
        imageLeft.isHidden = leftValue == 0
        imageRight.isHidden = rightValue == 0
        
        let delay = playSound(openGameSound(), "toan/toan_cong ngon tay", "topics/Numbers/\(leftValue)", "toan/cộng", "topics/Numbers/\(rightValue)", "toan/toan_cong ngon tay2")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_cong ngon tay", "topics/Numbers/\(leftValue)", "toan/cộng", "topics/Numbers/\(rightValue)", "toan/toan_cong ngon tay2")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_congngontay: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    }
    
    func onDelClick(value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    }
    
    func onCheckClick(value: Int) {
        answerText.text = String(value)
        pauseGame()
        let correct = value == self.value
        var delay = playSound(correct ? answerCorrect1EffectSound() : answerWrongEffectSound())
        
        if correct {
            delay += playSound(delay: delay, names: [
                getCorrectHumanSound(),
                "topics/Numbers/\(leftValue)",
                "toan/cộng",
                "topics/Numbers/\(rightValue)",
                "toan/bằng",
                "topics/Numbers/\(value)",
                endGameSound()
            ])
            animateCoinIfCorrect(view: answerText)
            answerText.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            delay += playSound(delay: delay, names: [getWrongHumanSound()])
            setGameWrong()
            answerText.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            numpad.reset()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

