<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/shadow_svg_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/black_svg_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <ImageView
            android:id="@+id/icon_lock"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/icon_lock_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/svg_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent" />

        <com.kidsup.giaoducsom.view.SVGAutosizeView
            android:id="@+id/svg_animation_view"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleX="1"
            android:scaleY="1" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>