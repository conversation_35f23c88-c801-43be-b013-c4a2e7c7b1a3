<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#0f00"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="8:6"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#fff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.667"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.75">

            <com.kidsup.giaoducsom.view.MyGridLayout
                android:id="@+id/grid_layout"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_start"
            android:layout_width="0dp"
            android:layout_height="0dp"

            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.16666"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:scaleX="10">

                <View
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/tuduy_pipe_4"
                    android:scaleType="fitXY"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintWidth_percent="0.5" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <View
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/tuduy_pipe_3" />

            <View
                android:id="@+id/start_button"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="@drawable/tuduy_pipe_6" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <ImageView
            android:id="@+id/view_end"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_pipe_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.16666"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.8" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_water"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:rotation="0"
            android:src="@drawable/tuduy_pipe_5"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.16666"
            app:layout_constraintHorizontal_bias="0.14285714285"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.2">

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/image_water"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1011:1401"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.7" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>