//
//  phonics_list_listening.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_listening: GameFragment {
    var topView : UIView?
    var bottomGrid = MyGridView()
    var btnSound = SVGButton().then{
        $0.contentMode = .scaleAspectFit
        let image = SVGImageView(SVGName: "english_option_bg_white_shadow_intro")
        $0.addSubviewWithInset(subview: image, inset: 0)
        image.contentMode = .scaleAspectFit
    }
    private var values : [String] = []
    var meIndex = 0
    var chooseKey = ""
    var corrects : [UIView] = []
    override func configureLayout(_ view: UIView) {
        let leftView = UIView()
        leftView.backgroundColor = .init(hex: "#7CD7FF")
        view.addSubview(leftView)
        leftView.snp.makeConstraints{ make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
                
        let rightView = UIView()
        rightView.backgroundColor = .color(hex: "#849BFD")
        view.addSubview(rightView)
        rightView.snp.makeConstraints{ make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(leftView.snp.right)
        }
        
        let leftColor = UIView()
        leftColor.backgroundColor = leftView.backgroundColor
        leftView.addSubview(leftColor)
        leftColor.snp.makeConstraints{ make in
            make.top.left.bottom.equalTo(self)
            make.right.equalToSuperview()
        }
        
        let rightColor = UIView()
        rightColor.backgroundColor = rightView.backgroundColor
        rightView.addSubview(rightColor)
        rightColor.snp.makeConstraints{ make in
            make.top.right.bottom.equalTo(self)
            make.left.equalToSuperview()
        }
                
        rightView.addSubview(btnSound)
        btnSound.snp.makeConstraints{ make in
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalTo(btnSound.snp.width)
            make.center.equalToSuperview()
        }
        
        leftView.addSubview(bottomGrid)
        bottomGrid.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightView.addSubview(btnSound)
        btnSound.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.6)
        }
                        
        btnSound.addTarget(self, action: #selector(soundClick), for: .touchUpInside)
    }
    @objc func soundClick(){
        self.playSound(name: "\(game.sentence ?? false ? "sentence": game.level!)/\(values[meIndex].lowercased())", delay: 0)
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            delay += self.playSound(name: "\(game.sentence ?? false ? "sentence" : game.level!)/\(values[meIndex].lowercased())", delay: delay)
            for i in 0..<values.count {
                self.playSound(name: "effects/bubble\(i+1)", delay: delay)
                delay += 0.3
            }
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 4)
        meIndex = Int.random(in: 0..<values.count)
        
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: "\(game.sentence ?? false ? "sentence" : game.level!)/\(values[meIndex].lowercased())", delay: delay)
        var listViews : [UIView] = []
        for i in 0..<values.count {
            let view = createGridItem()
            let label = view.viewWithTag(3) as! AutosizeLabel
            label.text = values[i]
            view.tag = 100 + i
            listViews.append(view)
            view.alpha = 0
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        bottomGrid.itemRatio = 1139.2/235.7
        bottomGrid.itemSpacingRatio = 0.01
        bottomGrid.insetRatio = 0.03
        bottomGrid.columns = 1
        bottomGrid.reloadItemViews(views: listViews)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.bottomGrid.playSound = true
            self.bottomGrid.step = 1.0
            self.bottomGrid.showItems()
            for i in 0..<values.count {
                //self.playSound(name: "effects/bubble\(i+1)", delay: 0.3 * Double(i))
            }
            self.scheduler.schedule(delay: 0.5 * Double(values.count), execute: {
                [weak self] in
                guard let self = self else { return }
                self.startGame()
            })
        })
    }
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue long long")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 1139.2/235.7)
        let label = AutosizeLabel()
        label.tag = 3
        label.textColor = .color(hex: "#68C1FF")
        view.addSubview(label)
        label.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.8)
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.4)
        }
        return view
    }
    
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += self.playSound(name: "\(game.sentence ?? false ? "sentence": game.level!)/\(values[meIndex].lowercased())", delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    @objc func itemClick(_ sender : UIControl){
        let label = sender.viewWithTag(3) as! AutosizeLabel
        let text = label.text!
        let tag = sender.tag
        if tag == 100 + meIndex {
            sender.animateCoin(answer: true)
            if !corrects.contains(sender) {
                corrects.append(sender)
                label.alpha = 1
                label.textColor = .color(hex: "#73D048")
                pauseGame()
                var delay = 0.5
                delay += self.playSound(delay: delay, names: ["effects/cheer\(Int.random(in:1...4))","effects/end game"])
                scheduler.schedule(delay: delay, execute: {
                    [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                })
            }
        } else {
            incorrect += 1
            pauseGame()
            sender.animateCoin(answer: false)
            label.textColor = .color(hex: "#FF7761")
            var delay = 0.5
            delay += playSound(name: "\(game.sentence ?? false ? "sentence": game.level!)/\(values[tag-100])", delay: delay)
            delay += 0.2
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                label.textColor = .color(hex: "#68C1FF")
                self.resumeGame()
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}

