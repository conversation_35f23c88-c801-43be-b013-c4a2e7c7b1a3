<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E0E0E4"
    android:clipChildren="false"
    android:clipToPadding="false">

    <View
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#fff"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintWidth_percent="0.5" />

    <View
        android:id="@+id/view21"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        android:id="@+id/view21_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/view21" />

    <View
        android:id="@+id/view21_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/view21"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <View
        android:id="@+id/viewfull"
        android:layout_width="match_parent"
        android:layout_height="match_parent" />


    <View
        android:id="@+id/viewfull_top"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/viewfull" />

    <View
        android:id="@+id/viewfull_bottom"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="@id/viewfull"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_top"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="bottom"
        app:constraint_referenced_ids="viewfull_top,view21_top" />

    <androidx.constraintlayout.widget.Barrier
        android:id="@+id/barrier_bottom"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        app:barrierDirection="top"
        app:constraint_referenced_ids="viewfull_bottom,view21_bottom" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/content_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="@id/barrier_bottom"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/barrier_top">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/controller_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.Guideline
                    android:id="@+id/guideline_center"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:orientation="horizontal"
                    app:layout_constraintGuide_percent="0.5" />

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:layout_width="match_parent"
                    android:layout_height="0dp"
                    android:background="@drawable/tuduy_car_pad"
                    app:layout_constraintDimensionRatio="1234.4:853.2"
                    app:layout_constraintTop_toTopOf="@id/guideline_center">

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/button_play"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/music_phimdan_play"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="1"
                        app:layout_constraintHorizontal_bias="0.8"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.28"
                        app:layout_constraintWidth_percent="0.20" />

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/button_right"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/tuduy_car_pad2"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="165.1:124.8"
                        app:layout_constraintHorizontal_bias="0.32"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.32"
                        app:layout_constraintWidth_percent="0.13" />

                    <androidx.constraintlayout.widget.ConstraintLayout
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:scaleX="-1"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="165.1:124.8"
                        app:layout_constraintHorizontal_bias="0.14"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.32"
                        app:layout_constraintWidth_percent="0.13">

                        <com.kidsup.giaoducsom.view.AnimatedImageButton
                            android:id="@+id/button_left"
                            android:layout_width="match_parent"
                            android:layout_height="match_parent"
                            android:background="@drawable/tuduy_car_pad2" />
                    </androidx.constraintlayout.widget.ConstraintLayout>

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/button_down"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/tuduy_car_pad2"
                        android:rotation="90"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="165.1:124.8"
                        app:layout_constraintHorizontal_bias="0.23"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.45"
                        app:layout_constraintWidth_percent="0.13" />

                    <com.kidsup.giaoducsom.view.AnimatedImageButton
                        android:id="@+id/button_up"
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:background="@drawable/tuduy_car_pad2"
                        android:rotation="-90"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintDimensionRatio="165.1:124.8"
                        app:layout_constraintHorizontal_bias="0.23"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="0.19"
                        app:layout_constraintWidth_percent="0.13" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <com.kidsup.giaoducsom.view.MyGridLayout
                    android:id="@+id/grid_layout"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#E1E1E5"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="8"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.3"
                    app:layout_constraintWidth_percent="1" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.5">

            <com.kidsup.giaoducsom.view.MyGridLayout
                android:id="@+id/grid_layout_map"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"></com.kidsup.giaoducsom.view.MyGridLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/view_car"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.2">

                <ImageView
                    android:id="@+id/image_car"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent"
                    android:src="@drawable/tuduy_car" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <ImageView
                android:id="@+id/image_car_zoom"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:alpha="0.0"
                android:src="@drawable/tuduy_car_end2"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>