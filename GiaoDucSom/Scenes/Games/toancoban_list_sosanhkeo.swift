//
//  toancoban_list_sosanhkeo.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sosanhkeo: NhanBietGameFragment {
    // MARK: - Properties
    private var boyContainer: UIView!
    private var girlContainer: UIView!
    private var values: [Int] = []
    private let candyIds = ["math_sosanhkeo_candy1", "math_sosanhkeo_candy2", "math_sosanhkeo_candy3", "math_sosanhkeo_candy4", "math_sosanhkeo_candy5", "math_sosanhkeo_candy6"]
    private var candyGirlIndex: [Int] = []
    private var candyBoyIndex: [Int] = []
    private var imageGirl: UIImageView!
    private var imageBoy: UIImageView!
    private var textGirl: UILabel!
    private var textBoy: UILabel!
    private var textCompare: UILabel!
    private var askBigger: Bool = false
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 248/255, green: 240/255, blue: 183/255, alpha: 1) // #F8F0B7
        
        let containerLayout = UIView()
        containerLayout.clipsToBounds = false
        view.addSubview(containerLayout)
        containerLayout.makeViewCenterAndKeep(ratio: 1.9)
        
        imageBoy = UIImageView(image: Utilities.SVGImage(named: "math_sosanhkeo_boy"))
        imageBoy.contentMode = .scaleAspectFit
        containerLayout.addSubview(imageBoy)
        imageBoy.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.4)
            make.height.equalTo(imageBoy.snp.width).multipliedBy(734.0 / 978.0) // Ratio 978:734
            make.top.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.imageBoy.snapToHorizontalBias(horizontalBias: 1.0)
        }
        
        imageGirl = UIImageView(image: Utilities.SVGImage(named: "math_sosanhkeo_girl"))
        imageGirl.contentMode = .scaleAspectFit
        containerLayout.addSubview(imageGirl)
        imageGirl.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.4)
            make.height.equalTo(imageGirl.snp.width).multipliedBy(734.0 / 978.0) // Ratio 978:734
            make.top.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.imageGirl.snapToHorizontalBias(horizontalBias: 0.0)
        }
        
        girlContainer = UIView()
        containerLayout.addSubview(girlContainer)
        girlContainer.snp.makeConstraints { make in
            make.top.equalTo(imageGirl.snp.bottom)
            make.left.right.equalTo(imageGirl)
            make.bottom.equalToSuperview()
        }
        
        boyContainer = UIView()
        containerLayout.addSubview(boyContainer)
        boyContainer.snp.makeConstraints { make in
            make.top.equalTo(imageBoy.snp.bottom)
            make.left.right.equalTo(imageBoy)
            make.bottom.equalToSuperview()
        }
        
        textGirl = AutosizeLabel()
        textGirl.textColor = UIColor(red: 184/255, green: 67/255, blue: 223/255, alpha: 1) // #B843DF
        textGirl.font = .Freude(size: 20)
        textGirl.textAlignment = .center
        textGirl.adjustsFontSizeToFitWidth = true
        textGirl.minimumScaleFactor = 0.1
        textGirl.isHidden = true
        containerLayout.addSubview(textGirl)
        textGirl.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.12)
            make.height.equalTo(textGirl.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            self.textGirl.snapToHorizontalBias(horizontalBias: 0.3)
            self.textGirl.snapToVerticalBias(verticalBias: 0.04)
        }
        
        textBoy = AutosizeLabel()
        textBoy.textColor = UIColor(red: 14/255, green: 175/255, blue: 225/255, alpha: 1) // #0EAFE1
        textBoy.font = .Freude(size: 20)
        textBoy.textAlignment = .center
        textBoy.adjustsFontSizeToFitWidth = true
        textBoy.minimumScaleFactor = 0.1
        textBoy.isHidden = true
        containerLayout.addSubview(textBoy)
        textBoy.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.12)
            make.height.equalTo(textBoy.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            self.textBoy.snapToHorizontalBias(horizontalBias: 0.7)
            self.textBoy.snapToVerticalBias(verticalBias: 0.04)
        }
        
        textCompare = AutosizeLabel()
        textCompare.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
        textCompare.font = .Freude(size: 20)
        textCompare.textAlignment = .center
        textCompare.adjustsFontSizeToFitWidth = true
        textCompare.minimumScaleFactor = 0.1
        textCompare.isHidden = true
        containerLayout.addSubview(textCompare)
        textCompare.snp.makeConstraints { make in
            make.width.equalTo(containerLayout).multipliedBy(0.12)
            make.height.equalTo(textCompare.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews {
            self.textCompare.snapToHorizontalBias(horizontalBias: 0.5)
            self.textCompare.snapToVerticalBias(verticalBias: 0.04)
        }
        
        let boyTap = UITapGestureRecognizer(target: self, action: #selector(onBoyTapped))
        imageBoy.addGestureRecognizer(boyTap)
        imageBoy.isUserInteractionEnabled = true
        imageBoy.isExclusiveTouch = true // Prevents multiple taps
        
        let girlTap = UITapGestureRecognizer(target: self, action: #selector(onGirlTapped))
        imageGirl.addGestureRecognizer(girlTap)
        imageGirl.isUserInteractionEnabled = true
        imageGirl.isExclusiveTouch = true // Prevents multiple taps
        
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        askBigger = Bool.random()
        while true {
            let value = Int.random(in: 0..<8) + 2 // 2 to 9
            values = [value]
            let nextValue = value + (1 + Int.random(in: 0..<2)) * (Int.random(in: 0..<2) * 2 - 1)
            if nextValue >= 2 && nextValue <= 9 {
                values.append(nextValue)
                break
            }
        }
        values.shuffle()
        textGirl.text = String(values[0])
        textBoy.text = String(values[1])
        textCompare.text = values[0] > values[1] ? ">" : values[0] < values[1] ? "<" : "="
        textCompare.textColor = values[0] > values[1] ? UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) : UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #87D657 or #FF7760
        textGirl.isHidden = true
        textBoy.isHidden = true
        textCompare.isHidden = true
        candyGirlIndex = (0..<candyIds.count).shuffled()
        candyBoyIndex = (0..<candyIds.count).shuffled()
    }
    
    override func createGame() {
        super.createGame()
        let girlPoints = PointsHelper.getPoints(size: values[0], container: girlContainer)
        let boyPoints = PointsHelper.getPoints(size: values[1], container: boyContainer)
        let candyWidth: CGFloat = 0.20
        
        for (i, point) in girlPoints.enumerated() {
            let imageView = UIImageView(image: Utilities.SVGImage(named: candyIds[candyGirlIndex[i % candyGirlIndex.count]]))
            imageView.contentMode = .scaleAspectFit
            girlContainer.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.width.equalTo(girlContainer).multipliedBy(candyWidth)
                make.height.equalTo(imageView.snp.width).multipliedBy(77.0 / 175.0) // Ratio 175:77
                make.center.equalToSuperview()
            }
            imageView.transform = CGAffineTransformMakeTranslation(CGFloat(point.x - Float(girlContainer.frame.width) / 2.0), CGFloat(point.y - Float(girlContainer.frame.width) / 2.0))
        }
        
        for (i, point) in boyPoints.enumerated() {
            let imageView = UIImageView(image: Utilities.SVGImage(named: candyIds[candyBoyIndex[i % candyBoyIndex.count]]))
            imageView.contentMode = .scaleAspectFit
            boyContainer.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.width.equalTo(boyContainer).multipliedBy(candyWidth)
                make.height.equalTo(imageView.snp.width).multipliedBy(77.0 / 175.0) // Ratio 175:77
                make.center.equalToSuperview()
            }
            imageView.transform = CGAffineTransformMakeTranslation(CGFloat(point.x - Float(boyContainer.frame.width / 2.0)), CGFloat(point.y - Float(boyContainer.frame.width / 2.0)))
        }
        
        var delay = playSound(openGameSound(), "toan/toan_so sanh keo\(askBigger ? "1" : "2")")
        girlContainer.alpha = 0
        boyContainer.alpha = 0
        UIView.animate(withDuration: 0.6, delay: delay, options: .curveEaseOut) {
            self.girlContainer.alpha = 1
            self.girlContainer.transform = .identity
        }
        UIView.animate(withDuration: 0.6, delay: delay + 0.5, options: .curveEaseOut) {
            self.boyContainer.alpha = 1
            self.boyContainer.transform = .identity
        }
        delay += playSound(name: "effect/bubble1", delay: delay)
        delay += playSound(name: "effect/bubble2", delay: delay + 0.5)
        delay += 0.6
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so sanh keo\(askBigger ? "1" : "2")")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onBoyTapped(_ gesture: UITapGestureRecognizer) {
        pauseGame()
        let sign = askBigger ? 1 : -1
        if values[1] * sign > values[0] * sign {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound(), "topics/Numbers/\(values[0])", values[0] > values[1] ? "toan/lớn hơn" : values[0] == values[1] ? "toan/bằng" : "toan/nhỏ hơn", "topics/Numbers/\(values[1])", endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
            textGirl.isHidden = false
            textBoy.isHidden = false
            textCompare.isHidden = false
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    @objc private func onGirlTapped(_ gesture: UITapGestureRecognizer) {
        pauseGame()
        let sign = askBigger ? 1 : -1
        if values[0] * sign > values[1] * sign {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound(), "topics/Numbers/\(values[0])", values[0] > values[1] ? "toan/lớn hơn" : values[0] == values[1] ? "toan/bằng" : "toan/nhỏ hơn", "topics/Numbers/\(values[1])", endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
            textGirl.isHidden = false
            textBoy.isHidden = false
            textCompare.isHidden = false
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

