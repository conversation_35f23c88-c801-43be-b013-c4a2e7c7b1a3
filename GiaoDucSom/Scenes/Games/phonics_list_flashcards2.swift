//
//  phonics_list_flashcards2.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_flashcards2: GameFragment {
    private var values : [String] = []
    var step = 0
    var svgView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#F4FAFF")
        addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1)
    }
    
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        var delay = 0.5
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        delay += 0.5
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
        })
    }
    func loadNextStep(){
        if step >= values.count {
            self.playSound(name: "effects/end game")
            DictionaryManager.shared.unlock(names: values)
            scheduler.schedule(delay: 1.5, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        let answer = values[step]
        step += 1
        var delay = 0.0
        playSound(name: answer, delay: delay)
        svgView.SVGName = "flashcards/\(game.level!)/\(answer).svg"
        delay += 2
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.svgView.SVGName = "flashcards/\(game.level!)/\(answer)2.svg"
        })
        playSound(name: answer, delay: delay)
        delay += 2
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading, .GameListening, .GameSpeaking]
    }
}
