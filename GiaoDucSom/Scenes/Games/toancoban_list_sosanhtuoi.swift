//
//  toancoban_list_sosanhtuoi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_sosanhtuoi: NhanBietGameFragment {
    // MARK: - Properties
    private var age1: Int = 0
    private var age2: Int = 0
    private var ageInfo1: Age?
    private var ageInfo2: Age?
    private var image1: UIImageView!
    private var image2: UIImageView!
    private var images: [String] = []
    private var itemContainer: UIView!
    private var gridLayout: MyGridView!
    private var text1: UILabel!
    private var text2: UILabel!
    private let rightBg = UIView()
    private let ages = [
        Age(fromAge: 1, toAge: 2, index: 1),
        Age(fromAge: 4, toAge: 6, index: 2),
        Age(fromAge: 7, toAge: 16, index: 3),
        Age(fromAge: 18, toAge: 29, index: 4),
        Age(fromAge: 30, toAge: 38, index: 5),
        Age(fromAge: 41, toAge: 55, index: 6),
        Age(fromAge: 60, toAge: 90, index: 7)
    ]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.alpha = 0.01
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        rightBg.snp.makeConstraints { make in
            make.top.right.bottom.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        itemContainer = UIView()
        leftView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 0.8)
        itemContainer.transform = CGAffineTransformMakeScale(0.9, 0.9)
        
        image1 = UIImageView()
        image1.contentMode = .scaleAspectFit
        itemContainer.addSubview(image1)
        image1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(image1.snp.height).multipliedBy(509.0 / 960.0) // Ratio 509:960
            make.left.bottom.equalToSuperview()
        }
        
        image2 = UIImageView()
        image2.contentMode = .scaleAspectFit
        itemContainer.addSubview(image2)
        image2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(image2.snp.height).multipliedBy(509.0 / 960.0) // Ratio 509:960
            make.right.bottom.equalToSuperview()
        }
        
        text1 = AutosizeLabel()
        text1.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        text1.font = .Freude(size: 20)
        text1.textAlignment = .center
        text1.adjustsFontSizeToFitWidth = true
        text1.minimumScaleFactor = 0.1
        text1.alpha = 0.01
        itemContainer.addSubview(text1)
        text1.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.equalToSuperview()
            make.bottom.equalTo(image1.snp.top)
            make.left.equalToSuperview()
        }
        
        text2 = AutosizeLabel()
        text2.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        text2.font = .Freude(size: 20)
        text2.textAlignment = .center
        text2.adjustsFontSizeToFitWidth = true
        text2.minimumScaleFactor = 0.1
        text2.alpha = 0.01
        itemContainer.addSubview(text2)
        text2.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.equalToSuperview()
            make.bottom.equalTo(image2.snp.top)
            make.right.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        images = [random("man", "woman"), random("man", "woman")]
        
        while true {
            age1 = Int.random(in: 0..<80)
            age2 = Int.random(in: 0..<80)
            ageInfo1 = getAge(age: age1)
            ageInfo2 = getAge(age: age2)
            if let ageInfo1 = ageInfo1, let ageInfo2 = ageInfo2, ageInfo1.index != ageInfo2.index {
                break
            }
        }
        
        text1.text = String(age1)
        text2.text = String(age2)
        let count = abs(age1 - age2)
        buildGrid(grid: gridLayout, count: count)
    }
    
    private func getAge(age: Int) -> Age? {
        return ages.first { $0.fromAge <= age && age <= $0.toAge }
    }
    
    override func createGame() {
        super.createGame()
        let svgPaths = [
            "life cycle/\(images[0])\(ageInfo1!.index).svg",
            "life cycle/\(images[1])\(ageInfo2!.index).svg"
        ]
        let svgImages = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        
        image1.image = svgImages[0].uiImage
        image2.image = svgImages[1].uiImage
        
        let height1_1 = svgImages[0].size.height
        let height1_2 = svgImages[0].trimmedSize().height
        let translation1 = (height1_1 - height1_2) / height1_1 * CGFloat(image1.frame.height)
        UIView.animate(withDuration: 0.2) {
            self.text1.transform = CGAffineTransform(translationX: 0, y: CGFloat(translation1))
            self.text1.alpha = 1
        }
        
        let height2_1 = svgImages[1].size.height
        let height2_2 = svgImages[1].trimmedSize().height
        let translation2 = (height2_1 - height2_2) / height2_1 * CGFloat(image2.frame.height)
        UIView.animate(withDuration: 0.2) {
            self.text2.transform = CGAffineTransform(translationX: 0, y: CGFloat(translation2))
            self.text2.alpha = 1
        }
        
        var delay = playSound("toan/toan_so sanh tuoi")
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        delay += 0.5
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 0.5
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so sanh tuoi")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == abs(age1 - age2)
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound(delay: delay, names: [
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                "topics/Numbers/\(value)",
                endGameSound()
            ])
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}

// MARK: - Supporting Structures
struct Age {
    let fromAge: Int
    let toAge: Int
    let index: Int
}

extension SVGKImage {
    func trimmedSize() -> CGSize {
        // Giả lập, cần thay bằng implement thực tế từ dự án
        return size
    }
}
