//
//  amnhac_list_timgiaidieu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class amnhac_list_timgiaidieu: MusicGameFragment {
    // MARK: - Properties
    private var item1: UIControl!
    private var item2: UIControl!
    private var btnPlay: KUButton!
    private var song: Lyric?
    private var meIndex: Int = 0
    private var timGiaiDieu: TimGiaiDieu?
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .white // #FFF
        
        let bgCloud = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_cloud"))
        bgCloud.contentMode = .scaleAspectFill
        view.addSubview(bgCloud)
        bgCloud.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        btnPlay = KUButton()
        btnPlay.setImage(Utilities.SVGImage(named: "music_btn_playback"), for: .normal)
        btnPlay.addTarget(self, action: #selector(playIntro), for: .touchUpInside)
        view.addSubview(btnPlay)
        btnPlay.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.15)
            make.height.equalTo(btnPlay.snp.width) // Ratio 1:1
            make.left.equalTo(view.snp.right).multipliedBy(0.05)
            make.centerY.equalToSuperview()
        }
        
        let rightContainer = UIView()
        view.addSubview(rightContainer)
        rightContainer.snp.makeConstraints { make in
            make.left.equalTo(btnPlay.snp.right)
            make.right.top.bottom.equalToSuperview()
        }
        
        let rightPaddingView = UIView()
        rightContainer.addSubviewWithPercentInset(subview: rightPaddingView, percentInset: 5)
        
        let musicContainer = UIView()
        rightPaddingView.addSubview(musicContainer)
        musicContainer.makeViewCenterAndKeep(ratio: 2)
        
        item1 = createItemTimGiaiDieu()
        item1.stringTag = "0"
        item1.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        musicContainer.addSubview(item1)
        item1.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        item2 = createItemTimGiaiDieu()
        item2.stringTag = "1"
        item2.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
        musicContainer.addSubview(item2)
        item2.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
    }
    var loadingSounds = false
    func loadPiano() {
        loadingSounds = true
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            let notes = timGiaiDieu?.notes ?? []
            var sum = 0
            for i in 2..<notes.count {
                sum += timGiaiDieu?.durations[i] ?? 0
                if sum > 13 { break }
                let note = notes[i]
                if noteToMusicId.keys.contains(note) { continue }
                if let url = Utilities.url(soundPath: "effect/music/piano_\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
            loadingSounds = false
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        
        let lyrics = MusicManager.shared.getLyrics()!
        song = lyrics.randomElement()
        #if DEBUG
        song = lyrics[7]
        #endif
        
        meIndex = Int.random(in: 0..<2)
        let timGiaiDieu1 = TimGiaiDieu(song: song!, view: item1, wrong: meIndex != 0)
        let timGiaiDieu2 = TimGiaiDieu(song: song!, view: item2, wrong: meIndex != 1)
        timGiaiDieu = meIndex == 0 ? timGiaiDieu1 : timGiaiDieu2
        loadPiano()
        let delay = playSound("music/giai dieu_tim giai dieu")
        scheduler.schedule(after: delay) { [weak self] in
            self?.playIntro()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/giai dieu_tim giai dieu")
            scheduler.schedule(after: delay) { [weak self] in
                self?.playIntro()
            }
        }
    }
    
    @objc private func playIntro() {
        pauseGame()
        var delay: TimeInterval = 0
        let notes = timGiaiDieu?.notes ?? []
        var sum = 0
        for i in 2..<notes.count {
            let note = notes[i]
            sum += timGiaiDieu?.durations[i] ?? 0
            if sum > 13 { break }
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                if let player = noteToMusicId[note] {
                    player.currentTime = 0
                    player.play()
                }
            }
            if let player = noteToMusicId[note] {
                delay += player.duration
            }
        }
        scheduler.schedule(after: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
    
    func tryToPlayIntro(){
        if loadingSounds {
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.tryToPlayIntro()
            }
        }
        playIntro()
    }
    
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame()
        if index == meIndex {
            animateCoinIfCorrect(view: sender)
            playSound("effect/answer_correct")
            scheduler.schedule(after: 1.5) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong")
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemTimGiaiDieu() -> UIControl {
        let view = KUButton()
        //view.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        
        let innerContainer = UIView()
        innerContainer.isUserInteractionEnabled = false
        view.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 4.5)
        
        let bg1 = UIImageView(image: Utilities.SVGImage(named: "music_timgiaidieu_btn1"))
        bg1.contentMode = .scaleAspectFit
        innerContainer.addSubview(bg1)
        bg1.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalTo(bg1.snp.height).multipliedBy(312.3/489.3)
        }
        
        let bg3 = UIImageView(image: Utilities.SVGImage(named: "music_timgiaidieu_btn3"))
        bg3.contentMode = .scaleAspectFit
        innerContainer.addSubview(bg3)
        bg3.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(bg1.snp.height).multipliedBy(312.3/489.3)
        }
        
        let bg2 = UIImageView(image: Utilities.SVGImage(named: "music_timgiaidieu_btn2"))
        bg2.tintColor = .color(hex: "#2EAFFF")
        bg2.contentMode = .scaleToFill
        innerContainer.addSubview(bg2)
        bg2.snp.makeConstraints { make in
            make.left.equalTo(bg1.snp.right)
            make.right.equalTo(bg3.snp.left)
            make.top.bottom.equalToSuperview()
        }
        
        let musicNoteContainer = UIView()
        musicNoteContainer.isUserInteractionEnabled = false
        musicNoteContainer.clipsToBounds = false
        view.addSubview(musicNoteContainer)
        musicNoteContainer.makeViewCenterAndKeep(ratio: 7)
        
        let bgMusic2 = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg").withRenderingMode(.alwaysTemplate))
        bgMusic2.contentMode = .scaleToFill
        musicNoteContainer.addSubview(bgMusic2)
        bgMusic2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg").withRenderingMode(.alwaysTemplate))
        clef.tintColor = UIColor.color(hex: "#74B6FF")
        musicNoteContainer.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(63.0 / 130.0) // Ratio 63:130
            make.height.equalTo(musicNoteContainer)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let beat = UIImageView()
        beat.stringTag = "beat"
        beat.contentMode = .scaleAspectFit
        beat.tintColor = UIColor.color(hex: "#74B6FF")
        musicNoteContainer.addSubview(beat)
        beat.snp.makeConstraints { make in
            make.width.equalTo(beat.snp.height).multipliedBy(63.0 / 130.0) // Ratio 63:130
            make.height.equalTo(musicNoteContainer)
            //make.centerX.equalToSuperview().multipliedBy(0.12) // Bias 0.06
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            beat.snapToHorizontalBias(horizontalBias: 0.12)
        }
        
        let beatImage = UIImageView()
        beatImage.tintColor = UIColor.color(hex: "#74B6FF")
        musicNoteContainer.addSubview(beatImage)
        beatImage.snp.makeConstraints { make in
            make.width.equalTo(beatImage.snp.height).multipliedBy(21.0 / 130.0) // Ratio 21:130
            make.height.equalTo(musicNoteContainer)
            make.left.equalTo(clef.snp.right)
            make.centerY.equalToSuperview()
        }
        
        let sampleNote = UIImageView(image: Utilities.SVGImage(named: "music/note/music_e.svg"))
        sampleNote.isHidden = true
        musicNoteContainer.addSubview(sampleNote)
        sampleNote.snp.makeConstraints { make in
            make.width.equalTo(sampleNote.snp.height).multipliedBy(48.0 / 130.0) // Ratio 48:130
            make.height.equalTo(musicNoteContainer)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let leftLine = UIView()
        //leftLine.backgroundColor = .red
        musicNoteContainer.addSubview(leftLine)
        leftLine.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(1)
            make.right.equalToSuperview().multipliedBy(0.33)
        }
        
        let rightLine = UIView()
        //rightLine.backgroundColor = .red
        musicNoteContainer.addSubview(rightLine)
        rightLine.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(1)
            make.right.equalToSuperview().multipliedBy(0.5)
        }
        let highlightBgContainer = UIView()
        musicNoteContainer.addSubview(highlightBgContainer)
        highlightBgContainer.snp.makeConstraints { make in
            make.right.equalTo(rightLine)
            make.top.left.bottom.equalToSuperview()
        }
        let highlightBg = UIView()
        highlightBg.backgroundColor = UIColor.color(hex: "#2EAFFF").withAlphaComponent(0.33) // #552EAFFF
        highlightBg.isHidden = true
        highlightBg.transform = CGAffineTransform(scaleX: 1, y: 5)
        highlightBgContainer.addSubview(highlightBg)
        highlightBg.makeViewCenterAndKeep(ratio: 0.3)
        
        
        let textContainer = UIView()
        textContainer.clipsToBounds = false
        textContainer.isHidden = true
        musicNoteContainer.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.width.equalTo(highlightBg)
            make.height.equalTo(70)
            make.left.equalTo(highlightBg)
            make.bottom.equalTo(highlightBg.snp.top)
        }
        
        let textNote = HeightRatioTextView()
        textNote.setHeightRatio(0.7)
        textNote.text = "A"
        textNote.font = .Freude(size: 20)
        textNote.textColor = .red // #f00
        textNote.textAlignment = .center
        textNote.backgroundColor = .clear
        textNote.transform = CGAffineTransform(scaleX: 1.5, y: 1.5)
        textContainer.addSubview(textNote)
        textNote.makeViewCenterAndKeep(ratio: 1)
        
        let blockContainer = UIView()
        blockContainer.stringTag = "blockContainer"
        blockContainer.clipsToBounds = false
        highlightBgContainer.addSubview(blockContainer)
        blockContainer.makeViewCenterAndKeep(ratio: 92.0/130.0)
        
        
        let notesContainer = UIView()
        notesContainer.stringTag = "notesContainer"
        notesContainer.clipsToBounds = false
        musicNoteContainer.addSubview(notesContainer)
        notesContainer.snp.makeConstraints { make in
            make.centerX.equalTo(rightLine).multipliedBy(0.5)
            make.width.equalTo(notesContainer.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.top.bottom.equalToSuperview()
        }
        
        let wrongNote = UIImageView()
        wrongNote.isHidden = true
        musicNoteContainer.addSubview(wrongNote)
        wrongNote.snp.makeConstraints { make in
            make.edges.equalTo(notesContainer)
        }
        
        return view
    }
    
    // MARK: - TimGiaiDieu Inner Class
    class TimGiaiDieu {
        let distanceRatio: CGFloat = 1.1
        let sampleNotes = ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "c2", "d2", "e2", "f2"]
        var lyrics: [String] = [" ", " ", "Cây ", "đèn ", " ", " "]
        var notes: [String] = [" ", " ", "2c1", "4c1", " ", " "]
        var durations: [Int] = [0, 0, 500, 1000, 0, 0]
        var themeColor = true
        var notesMap: [Int: UIView] = [:]
        var slursMap: [Int: UIView] = [:]
        var wrong = false
        var wrongIndex = -1
        private var song: Lyric
        private var notesContainer: UIView
        private var blockContainer: UIView
        private var beat: UIImageView
        
        init(song: Lyric, view: UIView, wrong: Bool) {
            self.song = song
            self.wrong = wrong
            self.notesContainer = view.viewWithStringTag("notesContainer")!
            self.blockContainer = view.viewWithStringTag("blockContainer")!
            self.beat = view.viewWithStringTag("beat")! as! UIImageView
            
            notesContainer.transform = CGAffineTransform(translationX: -1.2 * notesContainer.frame.height, y: 0)
            blockContainer.transform = CGAffineTransform(translationX: -1.2 * blockContainer.frame.height, y: 0)
            
            let beatDrawable = "music/note/beat_\(song.beat)_\(song.speed).svg"
            beat.image = Utilities.SVGImage(named: beatDrawable).withRenderingMode(.alwaysTemplate)
            beat.tintColor = UIColor.color(hex: "#74B6FF")
            
            parse(song: song)
            
            if wrong {
                var duration = 0
                for i in 0..<durations.count {
                    duration += durations[i]
                    if duration > 13 {
                        let numberOfErrors = 2 + arc4random_uniform(2) // Randomly choose 2 or 3 notes to modify
                        // Mảng để lưu lại những vị trí đã sửa, tránh lặp lại
                        var usedIndexes: [Int] = []

                        var tries = 0
                        // Lặp cho đến khi đủ số lỗi mong muốn
                        while usedIndexes.count < numberOfErrors {
                            
                            var wrongIndex: Int
                            while true {
                                tries += 1
                                wrongIndex = Int.random(in: 0..<i)
                                // 🔥 Kiểm tra xem nốt này có nội dung VÀ chưa bị sửa lần nào
                                if !notes[wrongIndex].trimmingCharacters(in: .whitespaces).isEmpty && !usedIndexes.contains(wrongIndex) {
                                    break
                                }
                                if tries > 1000 {
                                    // Nếu đã thử quá nhiều lần mà không tìm được nốt hợp lệ, thoát khỏi vòng lặp
                                    break
                                }
                            }
                            
                            // Thêm vị trí vừa chọn vào danh sách "đã dùng"
                            usedIndexes.append(wrongIndex)
                            
                            // Phần còn lại của code y hệt như cũ
                            let note = notes[wrongIndex]
                            let noteIndex = sampleNotes.firstIndex(of: String(note.dropFirst())) ?? 0
                            var startIndex = noteIndex - 2
                            if startIndex < 0 { startIndex = 0 }
                            var endIndex = noteIndex + 2
                            if endIndex >= sampleNotes.count { endIndex = sampleNotes.count - 1 }
                            
                            while true {
                                let newIndex = startIndex + Int.random(in: 0...(endIndex - startIndex))
                                if newIndex != noteIndex {
                                    notes[wrongIndex] = note.replacingOccurrences(of: sampleNotes[noteIndex], with: sampleNotes[newIndex])
                                    break
                                }
                            }
                        }
                        break
                    }
                }
            }
            
            for i in 2..<self.durations.count - 1 {
                self.addMusicNoteAtPosition(i)
            }
            // notesContainer.transform = .identity
            // blockContainer.transform = .identity
        }
        
        private func getMusicIndex(note: String) -> Int {
            for (index, sampleNote) in sampleNotes.enumerated() {
                if note.contains(sampleNote) {
                    return index
                }
            }
            return 0
        }
        
        private func getMusicDuration(note: String) -> Int {
            guard let firstChar = note.first else { return 0 }
            return Int(String(firstChar)) ?? 0
        }
        
        private func getSlurFilename(note: Int, note2: Int) -> String {
            let delta = note - note2
            let tbLetter = note <= 5 ? "b" : "t"
            return "slur\(delta < 0 ? "_" : "")\(abs(delta))\(tbLetter)"
        }
        
        private func parse(song: Lyric) {
            var lyric = song.lyric.replacingOccurrences(of: "  ", with: " ").trimmingCharacters(in: .whitespaces)
            if !lyric.hasSuffix("_") { lyric += "_" }
            var notes = song.notes.replacingOccurrences(of: "  ", with: " ").trimmingCharacters(in: .whitespaces).components(separatedBy: .whitespaces)
            notes.insert("", at: 0)
            notes.insert("", at: 0)
            notes.append("")
            notes.append("")
            self.notes = notes
            var lyrics: [String] = [" ", " "]
            var word = ""
            for char in lyric {
                if char == " " || char == "_" {
                    if !word.isEmpty {
                        lyrics.append(word + (char == " " ? " " : ""))
                        word = ""
                    } else {
                        lyrics.append(" ")
                    }
                    continue
                }
                word.append(char)
            }
            lyrics.append(" ")
            lyrics.append(" ")
            self.lyrics = lyrics
            self.durations = Array(repeating: 0, count: notes.count)
            for i in 2..<notes.count - 2 {
                self.durations[i] = getMusicDuration(note: notes[i])
            }
        }
        
        private func addMusicNoteAtPosition(_ position: Int) {
            let note = getMusicIndex(note: notes[position])
            var duration = 0
            var duration2 = 0
            for i in 0..<position {
                duration += durations[i]
                if i >= (song.beat_offset ?? 0) + 2 {
                    duration2 += durations[i]
                }
            }
            if duration > 13 { return }
            
            let imageView = UIImageView()
            imageView.contentMode = .scaleToFill
            let drawableId = "music/note/\(notes[position]).svg"
            imageView.image = Utilities.SVGImage(named: drawableId).withRenderingMode(.alwaysTemplate)
            imageView.tintColor = UIColor.color(hex: "#74B6FF")
            notesContainer.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            imageView.transform = CGAffineTransform(translationX: notesContainer.frame.height * (CGFloat(duration) + (3.2 / distanceRatio)) / (3.2 / distanceRatio), y: 0)
            notesMap[position] = imageView
            let songBeat = Int(song.beat)
            let songSpeed = Int(song.speed)
            if position >= 2 + (song.beat_offset ?? 0) && position > 2 && duration2 % ((songBeat ?? 4) * 8 / (songSpeed ?? 4)) == 0 {
                let separatorView = UIImageView()
                separatorView.contentMode = .scaleAspectFit
                separatorView.image = Utilities.SVGImage(named: durations[position] == 0 ? "music/note/music_end.svg" : "music/note/barline.svg").withRenderingMode(.alwaysTemplate)
                separatorView.tintColor = themeColor ? UIColor.color(hex: "#74B6FF") : .black
                blockContainer.addSubview(separatorView)
                separatorView.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                separatorView.transform = CGAffineTransform(translationX: notesContainer.frame.height * (CGFloat(duration) + (3.2 / distanceRatio) - 0.42) / (3.2 / distanceRatio), y: 0)
                notesMap[-position] = separatorView
            }
            
            if durations[position] != 0 && position > 0 && lyrics[position].trimmingCharacters(in: .whitespaces).isEmpty && !lyrics[position - 1].trimmingCharacters(in: .whitespaces).isEmpty {
                let separatorView = UIImageView()
                separatorView.contentMode = .scaleAspectFit
                let filename = "music/note/\(getSlurFilename(note: getMusicIndex(note: notes[position - 1]), note2: getMusicIndex(note: notes[position]))).svg"
                separatorView.image = Utilities.SVGImage(named: filename).withRenderingMode(.alwaysTemplate)
                separatorView.tintColor = themeColor ? UIColor.color(hex: "#2EAFFF") : .black
                blockContainer.addSubview(separatorView)
                separatorView.snp.makeConstraints { make in
                    make.edges.equalToSuperview()
                }
                let translationY = -(CGFloat(getMusicIndex(note: notes[position - 1])) - 7.99 + (filename.hasSuffix("t") ? 4 : 0)) * notesContainer.frame.height / 8.33 / 2
                separatorView.transform = CGAffineTransform(translationX: notesContainer.frame.height * (CGFloat(duration) + (3.2 / distanceRatio) - 0.60) / (3.2 / distanceRatio), y: translationY)
                slursMap[position] = separatorView
            }
        }
    }
}

// MARK: - Supporting Structures

struct Lyric : Codable{
    let name: String
    let location: [String]
    let lyric: String
    let notes: String
    let beat: String
    let speed: String
    let beat_offset: Int?
}

class MusicManager {
    static let shared = MusicManager()
    private var context: Any? // Context is not directly applicable in iOS; retained for compatibility

    private init() {}

    func initialize(context: Any?) {
        self.context = context
    }

    private var musics: [Lyric]?

    func getLyrics() -> [Lyric]? {
        if musics != nil {
            return musics
        }
        guard let url = Bundle.main.url(forResource: "music", withExtension: "json") else {
            print("Error: Could not find sticker_size in the bundle")
            return []
        }
        
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            musics = try decoder.decode([Lyric].self, from: data)
            //print("Successfully decoded \(stickerSizes.count) folders")
            return musics
        } catch {
            print("Error decoding JSON: \(error)")
            return []
        }
        return musics
    }
}
