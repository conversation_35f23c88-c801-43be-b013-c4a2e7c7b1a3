<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">


    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom_item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercent="0.05">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/bottom_2"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_a"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/bottom_3"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/bottom_1"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_operator"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/bottom_4"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/bottom_2"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_b"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_4"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/bottom_5"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/bottom_3"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_operator_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_5"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/bottom_6"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/bottom_4"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_c"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_6"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.5"
                app:layout_constraintEnd_toStartOf="@+id/bottom_7"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/bottom_5"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="="
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/bottom_7"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toEndOf="@+id/bottom_6"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_result"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center_vertical|left"
                    android:text="65"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#D6FAFF"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/top_item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercent="0.05">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_1"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/top_2"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_2"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/top_3"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/top_1"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_3"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/top_2"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/top_4"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_3"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_4"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/top_5"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/top_3"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_4"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_5"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/top_6"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/top_4"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_5"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#87D657"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/top_6"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/top_5"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_6"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#87D657"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>