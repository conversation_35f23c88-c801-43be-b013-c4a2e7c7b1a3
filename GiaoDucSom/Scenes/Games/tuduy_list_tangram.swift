//
//  tuduy_list_tangram.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 14/6/25.
//


import UIKit
import SnapKit
import SVGKit
import CoreGraphics
import AVFAudio

// MARK: - Models
struct Pack: Codable {
    let name: String
    let size: PackSize
    var items: [Item2]
}

struct PackSize: Codable {
    let w: CGFloat
    let h: CGFloat
}

struct Item2: Codable {
    var tX: CGFloat
    var tY: CGFloat
    var r: CGFloat
    var sX: CGFloat
    var sY: CGFloat
    var cX: CGFloat
    var cY: CGFloat
}

// MARK: - tuduy_list_tangram
class tuduy_list_tangram: NhanBietGameFragment {
    // MARK: - Properties
    static let svgSize: CGFloat = 326.0
    static let centerSize: CGFloat = 50
    private var soundFilename: String?
    private var randomFilename: Bool = false
    private var coinView: UIView!
    private var arrowLayout: UIView!
    private var contentLayout: UIView!
    private var answerLayout: UIView!
    private var completeParts: Int = 0
    private var pack: Pack?
    private var width: CGFloat = 0
    private var height: CGFloat = 0
    private var paddingLeft: CGFloat = 0
    private var scale: CGFloat = 1.0
    private var views: [UIView] = []
    private var strokeBottomView: UIImageView!
    private var grayBottomView: UIImageView!
    private var filename: String?

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFFFFF")
        view.removeAllSubviews()

        let horizontalStack = UIStackView()
        horizontalStack.axis = .horizontal
        horizontalStack.distribution = .fill
        horizontalStack.spacing = 0
        horizontalStack.clipsToBounds = false
        view.addSubview(horizontalStack)
        horizontalStack.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        answerLayout = UIView()
        answerLayout.stringTag = "answer_layout"
        answerLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06)
        answerLayout.clipsToBounds = false
        horizontalStack.addArrangedSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(1.0/3.0).priority(.high)
        }

        arrowLayout = UIView()
        arrowLayout.stringTag = "arrow_layout"
        arrowLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06)
        arrowLayout.isHidden = true
        horizontalStack.addArrangedSubview(arrowLayout)
        arrowLayout.snp.makeConstraints { make in
            make.width.equalTo(50)
        }

        let arrowImage = UIImageView(image: UIImage(named: "taptrung_daychun_arrow"))
        arrowLayout.addSubview(arrowImage)
        arrowImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentLayout = UIView()
        contentLayout.stringTag = "content_layout"
        contentLayout.clipsToBounds = false
        horizontalStack.addArrangedSubview(contentLayout)
        contentLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(2.0/3.0).priority(.high)
        }

        coinView = UIView()
        coinView.stringTag = "coin_view"
        view.addSubview(coinView)
        coinView.makeViewCenterAndKeep(ratio: 0.3)
        coinView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
        }
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        if filename == nil {
            let files = StorageManager.manager.list(path: "tangram").filter { $0.hasSuffix(".svg") }
            filename = files.randomElement()
            randomFilename = true
        }
    }

    override func createGame() {
        super.createGame()
        let svg = Utilities.GetSVGKImage(named: "tangram")
        guard let url = Bundle.main.url(forResource: "tangram", withExtension: "txt"),
              let data = try? Data(contentsOf: url),
              var items = try? JSONDecoder().decode([String: Pack].self, from: data) else {
            print("Error loading sticker_size.json")
            return
        }
        
        self.pack = items.first(where: { $0.value.name == self.filename })?.value

        guard let pack = self.pack else { return }
        self.width = pack.size.w
        self.height = pack.size.h
        let measuredHeight = self.bounds.height
        let measuredWidth = self.bounds.width

        var scale : CGFloat = measuredWidth / (CGFloat(self.width + Self.svgSize) + 300.0)
        if CGFloat(max(self.height, Self.svgSize)) * scale > measuredHeight * 0.8 {
            scale = measuredHeight * 0.8 / CGFloat(max(self.height, Self.svgSize))
        }
        self.scale = (scale)
        self.paddingLeft = CGFloat((measuredWidth - CGFloat(self.width + Self.svgSize) * scale) / 4.0)

        let contentWeight = (self.width * scale + 2.0 * CGFloat(self.paddingLeft)) / measuredWidth
        let answerWeight = (Self.svgSize * scale + 2.0 * self.paddingLeft) / measuredWidth
        self.contentLayout.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(contentWeight).priority(.high)
        }
        self.answerLayout.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(answerWeight).priority(.high)
        }

        let bottomLp = CGSize(width: CGFloat(Self.svgSize) * scale, height: CGFloat(Self.svgSize) * scale)
        self.strokeBottomView = UIImageView()
        let strokeBottomSVG = svg//.deepClone()
        strokeBottomSVG.caLayerTree.sublayers?.forEach { ($0 as? CAShapeLayer)?.strokeColor = UIColor(hex: "#E4E4E4").cgColor }
        self.strokeBottomView.image = strokeBottomSVG.uiImage
        self.strokeBottomView.frame = CGRect(origin: .zero, size: bottomLp)
        self.strokeBottomView.center = self.answerLayout.center

        self.grayBottomView = UIImageView()
        self.grayBottomView.backgroundColor = UIColor(hex: "#E4E4E4")
        self.grayBottomView.frame = CGRect(origin: .zero, size: bottomLp)
        self.grayBottomView.center = self.answerLayout.center

        self.answerLayout.addSubview(self.strokeBottomView)
        self.answerLayout.addSubview(self.grayBottomView)

        let difficult = DataManager.shared.profileAge() >= 5
        for i in 0..<7 {
            svg.caLayerTree.sublayers?.forEach { path in
                path.isHidden = path != svg.caLayerTree.sublayers?[i]
            }
            let autosizeView = UIImageView()
            autosizeView.image = svg.uiImage
            autosizeView.frame = CGRect(origin: .zero, size: bottomLp)
            autosizeView.center = self.answerLayout.center
            var item = pack.items[i]
            while item.r < -180 {
                item.r += 360
            }
            while item.r > 180 {
                item.r -= 360
            }
            self.pack!.items[i] = item
            autosizeView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))

            let listener = TangramTouchListener(item: item, paddingLeft: self.paddingLeft, scale: self.scale, answerLayout: self.answerLayout, height: self.height, onComplete: { [weak self] in
                self?.completeParts += 1
                if self?.completeParts == 7 {
                    self?.animateCoinIfCorrect(view: self?.coinView)
                    if !(self?.randomFilename ?? true) {
                        self?.playTangram(self?.filename ?? "")
                    }
                    UIView.animate(withDuration: 0.3, delay: 0.3, animations: {
                        self?.answerLayout.transform = CGAffineTransform(translationX: 20, y: 0)
                    }) { _ in
                        UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.3, initialSpringVelocity: 0, animations: {
                            self?.answerLayout.transform = .identity
                        })
                    }
                    let delay = self?.playSound("effect/answer_end", self?.getCorrectHumanSound() ?? "", self?.endGameSound() ?? "") ?? 0
                    self?.scheduler.schedule(delay: Double(delay)) {
                        [weak self] in
                        guard let self = self else { return }
                        self.finishGame()
                    }
                } else {
                    self?.playSound("effect/answer_correct")
                }
            }, onWrong: { [weak self] in
                self?.setGameWrong()
                self?.playSound("effect/slide2", self?.answerWrongEffectSound() ?? "")
            })
            autosizeView.isUserInteractionEnabled = true
            autosizeView.addGestureRecognizer(listener)

            let blackSVG = svg//.deepClone()
            if let path = blackSVG.caLayerTree.sublayers?.first as? CAShapeLayer, let color = path.fillColor?.uiColor {
                let gray = (Int(color.xred * 30 + color.xgreen * 59 + color.xblue * 11) / 100)
                let rate = difficult ? 0 : 3
                let finalGray = (gray + rate * 255) / (rate + 1)
                path.fillColor = difficult ? UIColor(hex: "#E5E5E5").cgColor : UIColor(red: CGFloat(finalGray)/255, green: CGFloat(finalGray)/255, blue: CGFloat(finalGray)/255, alpha: 1).cgColor
            }
            let placeHolderView = UIImageView()
            placeHolderView.image = blackSVG.uiImage
            placeHolderView.frame = CGRect(origin: .zero, size: bottomLp)
            placeHolderView.center = self.answerLayout.center
            placeHolderView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))

            let shadowSVG = svg//.deepClone()
            if let path = shadowSVG.caLayerTree.sublayers?.first as? CAShapeLayer {
                path.strokeColor = difficult ? UIColor(hex: "#E5E5E5").cgColor : UIColor(hex: "#FFFFFF").cgColor
                path.lineWidth = 0.02 * Self.svgSize
            }
            let shadowView = UIImageView()
            shadowView.image = shadowSVG.uiImage
            shadowView.frame = CGRect(origin: .zero, size: bottomLp)
            shadowView.center = self.answerLayout.center
            shadowView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))

            shadowView.layer.zPosition = 0
            placeHolderView.layer.zPosition = 1
            autosizeView.layer.zPosition = 2

            self.answerLayout.addSubview(shadowView)
            self.answerLayout.addSubview(placeHolderView)
            self.answerLayout.addSubview(autosizeView)

            self.views.append(shadowView)
            self.views.append(placeHolderView)
            self.views.append(autosizeView)

            let tranX = (item.tX * scale) + CGFloat(2.0) * self.paddingLeft + Utilities.dpToPx(Self.centerSize / CGFloat(2.0)) + Self.svgSize * scale
            let tranY = item.tY * scale - (self.height - Self.svgSize) * scale / 2

            shadowView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            placeHolderView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            UIView.animate(withDuration: 0, delay: 0, animations: {
                autosizeView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            }) { _ in
                UIView.animate(withDuration: 0.3, delay: 1 + 0.2 * Double(i), animations: {
                    autosizeView.transform = .identity
                })
            }
            self.playSound(name: "slide", delay: 1 + 0.2 * CGFloat(i))
        }

        self.arrowLayout.isHidden = false
        self.scheduler.schedule(delay: 1.5) {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        }
    }

    override func updateLayout() {
        super.updateLayout()
        guard let pack = pack else { return }
        width = pack.size.w
        height = pack.size.h
        let measuredHeight = self.bounds.height
        let measuredWidth = self.bounds.width
        var scale = measuredWidth / (CGFloat(width + Self.svgSize) + 300.0)
        if CGFloat(max(height, Self.svgSize)) * scale > measuredHeight * 0.8 {
            scale = CGFloat(measuredHeight * 0.8 / max(height, Self.svgSize))
        }
        self.scale = (scale)
        let bottomLp = CGSize(width: Self.svgSize * scale, height: CGFloat(Self.svgSize) * scale)
        strokeBottomView.frame = CGRect(origin: .zero, size: bottomLp)
        strokeBottomView.center = answerLayout.center
        grayBottomView.frame = CGRect(origin: .zero, size: bottomLp)
        grayBottomView.center = answerLayout.center
        paddingLeft = ((measuredWidth) - (width + Self.svgSize) * scale) / 4
        let contentWeight = (width * scale + 2 * paddingLeft) / measuredWidth
        let answerWeight = (Self.svgSize * scale + 2 * paddingLeft) / measuredWidth
        contentLayout.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(contentWeight).priority(.high)
        }
        answerLayout.snp.remakeConstraints { make in
            make.width.equalToSuperview().multipliedBy(answerWeight).priority(.high)
        }
        for i in 0..<7 {
            let autosizeView = views[i * 3 + 2]
            let lp = CGSize(width: CGFloat(Self.svgSize) * scale, height: CGFloat(Self.svgSize) * scale)
            autosizeView.frame = CGRect(origin: .zero, size: lp)
            autosizeView.center = answerLayout.center
            var item = pack.items[i]
            while item.r < -180 {
                item.r += 360
            }
            while item.r > 180 {
                item.r -= 360
            }
            self.pack?.items[i] = item
            autosizeView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))
            let placeHolderView = views[i * 3 + 1]
            placeHolderView.frame = CGRect(origin: .zero, size: lp)
            placeHolderView.center = answerLayout.center
            placeHolderView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))
            let shadowView = views[i * 3]
            shadowView.frame = CGRect(origin: .zero, size: lp)
            shadowView.center = answerLayout.center
            shadowView.layer.anchorPoint = CGPoint(x: CGFloat(item.cX / Self.svgSize), y: CGFloat(item.cY / Self.svgSize))
            let tranX = (item.tX * scale) + 2.0 * paddingLeft + Utilities.dpToPx(Self.centerSize / 2.0) + Self.svgSize * scale
            let tranY = item.tY * scale - (height - Self.svgSize) * scale / 2
            shadowView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            placeHolderView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            if autosizeView.transform.tx != 0 || autosizeView.transform.ty != 0 {
                autosizeView.transform = CGAffineTransform(scaleX: CGFloat(item.sX), y: CGFloat(item.sY)).rotated(by: CGFloat(item.r * .pi / 180)).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
            }
        }
    }

    // MARK: - Public Methods
    var getFilename: String? {
        return filename
    }

    func setFilename(_ filename: String?) {
        self.filename = filename
    }

    private func playTangram(_ tangram: String) {
        let preferences = UserDefaults.standard
        var tangrams = Set(preferences.stringArray(forKey: "tangram_played_\(DataManager.shared.currentProfile?.id ?? "default")") ?? [])
        tangrams.insert(tangram)
        preferences.set(Array(tangrams), forKey: "tangram_played_\(DataManager.shared.currentProfile?.id ?? "default")")
    }
}

// MARK: - TangramTouchListener
class TangramTouchListener: UIPanGestureRecognizer {
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var item: Item2
    private var paddingLeft: CGFloat
    private var scale: CGFloat
    private weak var answerLayout: UIView?
    private var height: CGFloat
    private var onComplete: (() -> Void)?
    private var onWrong: (() -> Void)?

    init(item: Item2, paddingLeft: CGFloat, scale: CGFloat, answerLayout: UIView, height: CGFloat, onComplete: @escaping () -> Void, onWrong: @escaping () -> Void) {
        self.item = item
        self.paddingLeft = paddingLeft
        self.scale = scale
        self.answerLayout = answerLayout
        self.height = height
        self.onComplete = onComplete
        self.onWrong = onWrong
        super.init(target: nil, action: nil)
        addTarget(self, action: #selector(handlePan))
    }

    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {        
        guard let view = gesture.view, gesture.numberOfTouches == 1 else { return }
        switch gesture.state {
        case .began:
            let location = gesture.location(in: view)
            let visible = view.isPixelVisible(x: Int(location.x), y: Int(location.y))
            guard visible else { return }
            dX = view.frame.origin.x - gesture.location(in: view.superview).x
            dY = view.frame.origin.y - gesture.location(in: view.superview).y
            view.superview?.bringSubviewToFront(view)
            UIView.animate(withDuration: 0.2) {
                view.transform = view.transform.scaledBy(x: CGFloat(self.item.sX * 1.1), y: CGFloat(self.item.sY * 1.1))
            }
            //self.playsound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            Utilities.vibrate()
        case .changed:
            let translation = gesture.location(in: view.superview)
            view.frame.origin = CGPoint(x: translation.x + dX, y: translation.y + dY)
        case .ended, .cancelled:
            Utilities.vibrate()
            let tranX = (item.tX * scale) + CGFloat(2.0) * paddingLeft + Utilities.dpToPx(tuduy_list_tangram.centerSize / CGFloat(2.0)) + tuduy_list_tangram.svgSize * scale
            let tranY = item.tY * scale - (height - tuduy_list_tangram.svgSize) * scale / 2
            let distance = hypot(tranX - CGFloat(view.transform.tx), tranY - CGFloat(view.transform.ty))
            if distance < 80 * scale {
                //self.playsound("effect/word puzzle drop")
                UIView.animate(withDuration: 0.2) {
                    view.transform = CGAffineTransform(scaleX: view.transform.a > 0 ? 1 : -1, y: view.transform.d > 0 ? 1 : -1).translatedBy(x: CGFloat(tranX), y: CGFloat(tranY))
                }
                view.gestureRecognizers?.forEach { view.removeGestureRecognizer($0) }
                onComplete?()
            } else {
                let d = hypot(view.transform.tx, view.transform.ty)
                let duration = max(0.2, Double(d / scale / 1.5))
                UIView.animate(withDuration: duration) {
                    view.transform = CGAffineTransform(scaleX: 1, y: 1).rotated(by: 0).translatedBy(x: 0, y: 0)
                }
                onWrong?()
            }
        default:
            break
        }
    }
}

// MARK: - Helper Extensions


extension UIColor {
    var xred: CGFloat { var r: CGFloat = 0; getRed(&r, green: nil, blue: nil, alpha: nil); return r }
    var xgreen: CGFloat { var g: CGFloat = 0; getRed(nil, green: &g, blue: nil, alpha: nil); return g }
    var xblue: CGFloat { var b: CGFloat = 0; getRed(nil, green: nil, blue: &b, alpha: nil); return b }
    var xalpha: CGFloat { var a: CGFloat = 0; getRed(nil, green: nil, blue: nil, alpha: &a); return a }
}

