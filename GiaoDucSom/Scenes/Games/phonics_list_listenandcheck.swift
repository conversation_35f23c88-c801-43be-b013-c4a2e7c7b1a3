//
//  phonics_list_listenandcheck.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_listenandcheck: GameFragment {
    var bottomGrid = MyGridView()
    var soundButton = SVGButton(SVGIcon: "music_amsac_btn2")
    private var values : [String] = []
    var meIndex = 0
    
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
        addSubview(bottomGrid)
        bottomGrid.snp.makeConstraints{ make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        addSubview(soundButton)
        soundButton.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(isRunningOnIpad() ? 50 : 20)
            make.bottom.equalTo(bottomGrid.snp.top).offset(isRunningOnIpad() ? -50 : -20)
            make.width.equalTo(soundButton.snp.height).multipliedBy(56.0/59.0)
        }
        soundButton.addTarget(self, action: #selector(soundClick), for: .touchUpInside)
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!)
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : values[meIndex], delay: delay)
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 4)
        meIndex = Int.random(in: 0..<values.count)
        var delay = playSound(openGameSound())
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        soundButton.alpha = 0
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            let easy = BackEaseInterpolater()
            easy.mode = .easeOut
            let animValues: [Double] = [0, 1]
            let timeChange = Interpolate(values: [0,1],
                                         apply: { [weak self] (value) in
                guard let self = self else { return }
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                let scale = 0.5 + finalValue / 2
                self.soundButton.transform = CGAffineTransformMakeScale(scale,scale)
                self.soundButton.alpha = finalValue
            })
            timeChange.animate(1, duration: 0.5){
                
            }
        })
        delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : values[meIndex], delay: delay)
        let values1 = values.randomOrder()
        var listViews : [UIView] = []
        for value in values1 {
            let view = createGridItem()
            let svgView: SVGImageView = view.viewWithTag(1) as! SVGImageView
            svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(value)))!] :  "english phonics/\(game.level!)/\(value).svg"
            svgView.contentMode = .scaleAspectFit
            view.tag = 100 + values.firstIndex(of: value)!
            listViews.append(view)
            view.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        }
        bottomGrid.columns = 0
        bottomGrid.itemRatio = 400 / 423.0
        bottomGrid.reloadItemViews(views: listViews)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    @objc func itemClick(_ sender: UIControl){
        if sender.tag == meIndex + 100 {
            pauseGame()
            animateCoinIfCorrect(view: sender)
            var delay = 0.5
            delay += self.playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound()])
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : values[meIndex], delay: delay)
            delay += self.playSound(name: endGameSound(), delay: delay)
            delay += 1
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
        } else {
            pauseGame()
            setGameWrong()
            incorrect += 1
            var delay = 0.3
            delay += self.playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            delay += self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[sender.tag-100])))!] : values[sender.tag-100], delay: delay)
            
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            })
        }
    }
    func createGridItem()->UIControl{
        let view = KUButton()
        let background = SVGImageView(SVGName: "btn white bg blue")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 400/423)
        let svgView = SVGImageView(frame: CGRectZero)
        svgView.transform = CGAffineTransformMakeScale(0.75, 0.75)
        svgView.tag = 1
        background.addSubviewWithInset(subview: svgView, inset: 0)
        return view
    }
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!)
        UIView.animate(withDuration: 0.3, animations: {
            self.soundButton.alpha = 0
        })
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.soundButton.alpha = 0
            let easy = BackEaseInterpolater()
            easy.mode = .easeOut
            let animValues: [Double] = [0, 1]
            let timeChange = Interpolate(values: [0,1],
                                         apply: { [weak self] (value) in
                guard let self = self else { return }
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                let scale = 0.5 + finalValue / 2
                self.soundButton.transform = CGAffineTransformMakeScale(scale,scale)
                self.soundButton.alpha = finalValue
            })
            timeChange.animate(1, duration: 0.5){
                
            }
        })
        delay += self.playSound(name: values[meIndex], delay: delay)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    @objc func soundClick(){
        pauseGame()
        let delay = self.playSound(name: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(values[meIndex])))!] : values[meIndex], delay: 0)
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening]
    }
    override func getScore()->Float{
        return 1.0 / (1.0 + Float(incorrect))
    }
}
