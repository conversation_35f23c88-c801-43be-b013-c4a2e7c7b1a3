//
//  taptrung_list_caphinhdenhau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 23/4/25.
//


import UIKit
import SnapKit
import AVFAudio

class taptrung_list_caphinhdenhau: NhanBietGameFragment {
    // MARK: - Properties
    private var hamiltonPath = HamiltonPath()
    private var gridView: MyGridView!
    private var gridView2: MyGridView!
    private var itemContainer: UIView!
    private var coinView: UIView!
    private var allLines: [[(Int, Int, Int, Int)]] = []
    private var path1: [(Int, Int)] = []
    private var path2: [(Int, Int)] = []
    private var leftLines1: [(Int, Int, Int, Int)] = []
    private var leftLines2: [(Int, Int, Int, Int)] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#E9FDFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        //itemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(view.frame.height * 0.05)
            make.bottom.equalToSuperview().offset(-view.frame.height * 0.05)
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        gridView2 = MyGridView()
        gridView2.backgroundColor = .clear
        gridView2.clipsToBounds = false
        itemContainer.addSubview(gridView2)
        gridView2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.color(hex: "#D6FAFF")
        gridView.clipsToBounds = false
        view.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.4)
        }
        
        coinView = UIView()
        coinView.clipsToBounds = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        while true {
            hamiltonPath.generateHamiltonPath(length: random(6, 7, 8))
            path1 = hamiltonPath.path
            hamiltonPath.generateHamiltonPath(length: random(4, 5, 6))
            path2 = hamiltonPath.path
            leftLines1 = convertPathToLines(path: path1)
            allLines = []
            var path22 = path2
            for i in 0..<3 {
                let line2 = convertPathToLines(path: path22)
                if i == 1 {
                    leftLines2 = line2
                    allLines.insert(leftLines1 + line2, at: 0)
                } else {
                    allLines.append(leftLines1 + line2)
                }
                if !path22.isEmpty {
                    path22.removeLast()
                }
            }
            var path3 = path1
            if !path3.isEmpty {
                path3.removeFirst()
            }
            var path4 = path1
            if !path4.isEmpty {
                path4.removeLast()
            }
            let line3 = convertPathToLines(path: path3) + convertPathToLines(path: path2)
            let line4 = convertPathToLines(path: path4) + convertPathToLines(path: path2)
            allLines.append(line3)
            allLines.append(line4)
            
            var allLines2: [[(Int, Int, Int, Int)]] = []
            for line in allLines {
                if !allLines2.contains(where: { sameList(a: $0, b: line) }) {
                    allLines2.append(line)
                }
            }
            if allLines2.count >= 4 {
                allLines = allLines2
                break
            }
        }
        
        var views: [UIView] = []
        for i in 0..<2 {
            let gridView = GridView()
            gridView.backgroundColor = .clear
            gridView.addLines(lines: i == 0 ? leftLines1 : leftLines2)
            views.append(gridView)
        }
        gridView2.columns = 2
        gridView2.itemRatio = 1
        gridView2.itemSpacingRatio = 0.1
        gridView2.insetRatio = 0.1
        gridView2.reloadItemViews(views: views)
        
        views = []
        for i in 0..<4 {
            let gridView = GridView()
            gridView.backgroundColor = .clear
            gridView.addLines(lines: allLines[i])
            gridView.stringTag = "\(i)"
            gridView.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            views.append(gridView)
            //gridView.transform = CGAffineTransform(scaleX: 0, y: 0)
            //gridView.alpha = 0
        }
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.itemSpacingRatio = 0.1
        gridView.insetRatio = 0.1
        gridView.reloadItemViews(views: views.shuffled())
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        gridView2.moveToCenter(of: self, duration: 0)
        gridView.alpha = 0
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_2 hinh de nhau"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.gridView2.transform = .identity
            }
            UIView.animate(withDuration: 0.2, delay: 0.5) {
                self.gridView.alpha = 1
            }
        }
        
        delay += 1.0
        delay += gridView.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound("taptrung/taptrung_2 hinh de nhau")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == 0 {
            animateCoinIfCorrect(view: coinView)
            var delay: TimeInterval = playSound("effect/answer_correct1")
            let vector = gridView2.subviews[0].distanceFromCenterToCenter(to: gridView2.subviews[1])
            let distance = hypot(vector.x, vector.y)
            UIView.animate(withDuration: 0.6, delay: delay) {
                self.gridView2.subviews[0].transform = CGAffineTransform(translationX: distance / 2, y: 0)
                self.gridView2.subviews[1].transform = CGAffineTransform(translationX: -distance / 2, y: 0)
            }
            delay += 0.6
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func convertPathToLines(path: [(Int, Int)]) -> [(Int, Int, Int, Int)] {
        var lines: [(Int, Int, Int, Int)] = []
        for i in 0..<path.count - 1 {
            let p1 = path[i]
            let p2 = path[i + 1]
            lines.append((p1.0, p1.1, p2.0, p2.1))
        }
        return lines
    }
    
    private func sameArray(a: (Int, Int, Int, Int), b: (Int, Int, Int, Int)) -> Bool {
        return (a.0 == b.0 && a.1 == b.1 && a.2 == b.2 && a.3 == b.3) ||
               (a.0 == b.2 && a.1 == b.3 && a.2 == b.0 && a.3 == b.1)
    }
    
    private func containArray(list: [(Int, Int, Int, Int)], a: (Int, Int, Int, Int)) -> Bool {
        return list.contains { sameArray(a: a, b: $0) }
    }
    
    private func sameList(a: [(Int, Int, Int, Int)], b: [(Int, Int, Int, Int)]) -> Bool {
        return a.allSatisfy { containArray(list: b, a: $0) } && b.allSatisfy { containArray(list: a, a: $0) }
    }
    
    // MARK: - HamiltonPath
    
    
    // MARK: - GridView
    class GridView: KUButton {
        private var lines: [(Int, Int, Int, Int)] = []
        private let gridPaint = Paint()
        private let strokePaint = Paint()
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            initPaints()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initPaints()
        }
        
        private func initPaints() {
            backgroundColor = UIColor.color(hex: "#E9FDFF")
            gridPaint.color = UIColor.color(hex: "#2DCEC9")
            gridPaint.strokeWidth = 5
            gridPaint.strokeCap = .round
            gridPaint.strokeJoin = .round
            
            strokePaint.color = UIColor.color(hex: "#FF7760")
            strokePaint.style = .stroke
            strokePaint.strokeWidth = 10
            strokePaint.strokeCap = .round
            strokePaint.strokeJoin = .round
        }
        
        func addLines(lines: [(Int, Int, Int, Int)]) {
            self.lines = lines
            setNeedsDisplay()
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            gridPaint.strokeWidth = frame.width / 100
            strokePaint.strokeWidth = frame.width / 50
        }
        
        override func draw(_ rect: CGRect) {
            super.draw(rect)
            
            guard let context = UIGraphicsGetCurrentContext() else { return }
            
            let width = frame.width
            let height = frame.height
            let rows = 4
            let cols = 4
            let cellWidth = width / CGFloat(cols)
            let cellHeight = height / CGFloat(rows)
            
            context.setStrokeColor(gridPaint.color.cgColor)
            context.setLineWidth(gridPaint.strokeWidth)
            context.setLineCap(gridPaint.strokeCap)
            context.setLineJoin(gridPaint.strokeJoin)
            
            for i in 0...rows {
                if i == 0 || i == rows {
                    context.move(to: CGPoint(x: 0, y: CGFloat(i) * cellHeight))
                    context.addLine(to: CGPoint(x: width, y: CGFloat(i) * cellHeight))
                    context.strokePath()
                }
            }
            
            for i in 0...cols {
                if i == 0 || i == cols {
                    context.move(to: CGPoint(x: CGFloat(i) * cellWidth, y: 0))
                    context.addLine(to: CGPoint(x: CGFloat(i) * cellWidth, y: height))
                    context.strokePath()
                }
            }
            
            context.setStrokeColor(strokePaint.color.cgColor)
            context.setLineWidth(strokePaint.strokeWidth)
            context.setLineCap(strokePaint.strokeCap)
            context.setLineJoin(strokePaint.strokeJoin)
            
            for line in lines {
                context.move(to: CGPoint(x: CGFloat(line.0 + 1) * cellWidth, y: CGFloat(line.1 + 1) * cellHeight))
                context.addLine(to: CGPoint(x: CGFloat(line.2 + 1) * cellWidth, y: CGFloat(line.3 + 1) * cellHeight))
                context.strokePath()
            }
        }
    }
}

class HamiltonPath {
    private let N = 3 // Kích thước lưới 3x3
    private let dx = [-1, 1, 0, 0, -1, -1, 1, 1] // Hướng di chuyển
    private let dy = [0, 0, -1, 1, -1, 1, -1, 1]
    private var grid: [[Int]] = []
    var visited: [[Bool]] = []
    var path: [(Int, Int)] = []
    var length: Int = 0
    
    init() {
        grid = Array(repeating: Array(repeating: 0, count: N), count: N)
        visited = Array(repeating: Array(repeating: false, count: N), count: N)
    }
    
    func generateHamiltonPath(length: Int) {
        self.length = length
        visited = Array(repeating: Array(repeating: false, count: N), count: N)
        path = []
        let startX = Int.random(in: 0..<N)
        let startY = Int.random(in: 0..<N)
        _ = dfs(x: startX, y: startY)
    }
    
    private func dfs(x: Int, y: Int) -> Bool {
        path.append((x, y))
        visited[x][y] = true
        
        if path.count == length {
            return true
        }
        
        let directions = (0..<8).shuffled()
        for dir in directions {
            let nx = x + dx[dir]
            let ny = y + dy[dir]
            
            if isValid(x: x, y: y, nx: nx, ny: ny) {
                if dfs(x: nx, y: ny) {
                    return true
                }
            }
        }
        
        path.removeLast()
        visited[x][y] = false
        return false
    }
    
    private func isValid(x: Int, y: Int, nx: Int, ny: Int) -> Bool {
        if nx < 0 || nx >= N || ny < 0 || ny >= N || visited[nx][ny] {
            return false
        }
        
        if abs(nx - x) == 1 && abs(ny - y) == 1 {
            return (x == 1 && y == 1) || (nx == 1 && ny == 1)
        }
        
        return true
    }
    /*
    var path: [(Int, Int)] {
completetionHandler: { (path, error) in
        self.path = path
    }*/
}
