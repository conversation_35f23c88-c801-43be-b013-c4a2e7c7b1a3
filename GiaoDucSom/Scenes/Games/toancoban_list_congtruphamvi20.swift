//
//  toancoban_list_congtruphamvi20.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import AVFAudio
import Interpolate

// MARK: - toancoban_list_congtruphamvi20
class toancoban_list_congtruphamvi20: NhanBietGameFragment {
    // MARK: - Properties
    private var a = 0
    private var b = 0
    private var textA: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textC: AutosizeLabel!
    private var textOperator: AutosizeLabel!
    private var sliderView: MySliderView!
    private var operatorSymbol: String?

    // MARK: - Lifecycle
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(hex: "#E9FDFF")        

        let topView = UIView()
        topView.stringTag = "top_view"
        view.addSubview(topView)
        topView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }

        let innerView = UIView()
        topView.addSubview(innerView)
        innerView.makeViewCenterAndKeep(ratio: 7.0)

        textA = AutosizeLabel()
        textA.stringTag = "text_a"
        textA.text = "1"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 30)
        textA.textAlignment = .center
        innerView.addSubview(textA)

        textOperator = AutosizeLabel()
        textOperator.stringTag = "text_operator"
        textOperator.text = "+"
        textOperator.textColor = UIColor(hex: "#87D657")
        textOperator.font = .Freude(size: 30)
        textOperator.textAlignment = .center
        innerView.addSubview(textOperator)

        textB = AutosizeLabel()
        textB.stringTag = "text_b"
        textB.text = "2"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 30)
        textB.textAlignment = .center
        innerView.addSubview(textB)

        let textEqual = AutosizeLabel()
        textEqual.stringTag = "text_equal"
        textEqual.text = "="
        textEqual.textColor = UIColor(hex: "#74B6FF")
        textEqual.font = .Freude(size: 30)
        textEqual.textAlignment = .center
        innerView.addSubview(textEqual)

        let resultBg = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        resultBg.stringTag = "result_bg"
        innerView.addSubview(resultBg)

        textC = AutosizeLabel()
        textC.stringTag = "text_c"
        textC.text = "?"
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 30)
        textC.textAlignment = .center
        innerView.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textC.snp.height).multipliedBy(1.2)
            make.left.equalTo(textEqual.snp.right)
        }
        textEqual.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textEqual.snp.height).multipliedBy(0.8)
            make.right.equalTo(textC.snp.left)
        }
        textB.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textB.snp.height).multipliedBy(1.2)
            make.right.equalTo(textEqual.snp.left)
            make.centerX.equalToSuperview()
        }
        textOperator.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textOperator.snp.height).multipliedBy(0.7)
            make.right.equalTo(textB.snp.left)
        }
        textA.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textA.snp.height).multipliedBy(1.2)
            make.right.equalTo(textOperator.snp.left)
        }
        resultBg.snp.makeConstraints { make in
            make.left.right.centerY.equalTo(textC)
            make.height.equalTo(resultBg.snp.width)
        }

        let bottomView = UIView()
        bottomView.stringTag = "bottom_view"
        view.addSubview(bottomView)
        bottomView.snp.makeConstraints { make in
            make.top.equalTo(topView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }

        sliderView = MySliderView()
        sliderView.stringTag = "slider_view"
        bottomView.addSubview(sliderView)
        sliderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalTo(sliderView.snp.width).dividedBy(6)
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }
        sliderView.setMinValue(0)
        sliderView.setMaxValue(5)
        sliderView.setStep(1)
        sliderView.setSelectedTick(0)
        sliderView.setListener { [weak self] value in
            guard let self = self else { return }
            self.textC.text = "\(value)"
            let correct = value == self.a + self.b
            self.textC.textColor = correct ? UIColor(hex: "#87D657") : UIColor(hex: "#FF7760")
            self.pauseGame()
            self.sliderView.setRight(correct)
            if correct {
                self.animateCoinIfCorrect(view: self.textC)
                let delay = self.playSound(self.answerCorrect1EffectSound(), self.getCorrectHumanSound(), "topics/Numbers/\(self.a)", self.operatorSymbol == "+" ? "toan/cộng" : "toan/trừ", "topics/Numbers/\(abs(self.b))", "toan/bằng", "topics/Numbers/\(value)", self.endGameSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                self.setGameWrong()
                let delay = self.playSound(self.answerWrongEffectSound(), self.getWrongHumanSound())
                self.scheduler.schedule(delay: Double(delay)) { [weak self] in
                    guard let self = self else { return }
                    self.textC.text = "?"
                    self.textC.textColor = UIColor(hex: "#74B6FF")
                    self.sliderView.setRight(nil)
                    self.resumeGame()
                }
            }
        }
        /* 
        sliderView.setScrollListener { [weak self] value, position in
            guard let self = self else { return }
            let correct = value == self.a + self.b
            self.sliderView.setRight(correct)
        }*/
    }

    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = Int.random(in: 0..<20)
            b = Int.random(in: -20...20)
            if a + b >= 0 && a + b <= 19 && (abs(a) > 9 || abs(b) > 9 || abs(a + b) > 9) {
                textA.text = "\(a)"
                operatorSymbol = b > 0 ? "+" : b < 0 ? "-" : ["+", "-"].randomElement()
                textOperator.text = operatorSymbol
                if operatorSymbol == "-" {
                    textOperator.textColor = UIColor(hex: "#FF7760")
                }
                textB.text = "\(abs(b))"
                var start = a + b - Int.random(in: 0..<10)
                if start < 0 { start = 0 }
                if start > 10 { start = 10 }
                sliderView.setMinValue(start)
                sliderView.setMaxValue(start + 9)
                if a + b == start {
                    sliderView.setSelectedTick(5)
                }
                break
            }
        }
        let delay = playSound(openGameSound(), "toan/toan_congtruphamvi5")
        scheduler.schedule(delay: Double(delay)) {
            self.startGame()
        }
    }

    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_congtruphamvi5")
            scheduler.schedule(delay: Double(delay)) {
                self.resumeGame()
            }
        }
    }
}
