//
//  toancoban_list_solientruoc.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_solientruoc: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var leftGridLayout: MyGridView!
    private let ids = ["math_bia1", "math_bia2", "math_bia3", "math_bia4", "math_bia5", "math_bia6", "math_bia7", "math_bia8", "math_bia9", "math_bia10", "math_bia11", "math_bia12", "math_bia13", "math_bia14", "math_bia15"]
    private var lientruoc: Bool = false
    private var pivotNumber: Int = 0
    private var coinView: UIView!
    var numbers: [Int] = []
    var leftNumbers: [Int] = []
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 0/255, green: 139/255, blue: 40/255, alpha: 1) // #008B28
        
        let containerLayout = UIView()
        containerLayout.clipsToBounds = false
        addSubviewWithPercentInset(subview: containerLayout, percentInset: 0)
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 47/255, green: 183/255, blue: 82/255, alpha: 1) // #2FB752
        containerLayout.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(containerLayout).multipliedBy(0.5)
        }
        
        leftGridLayout = MyGridView()
        containerLayout.addSubview(leftGridLayout)
        leftGridLayout.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        coinView = createCoinView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        
        lientruoc = Bool.random()
        let start = 10 + Int.random(in: 0..<87)
        if lientruoc {
            leftNumbers = [0, start + 2, start + 3]
            numbers = [start + 1, start]
            pivotNumber = start + 2
        } else {
            leftNumbers = [start, start + 1, 0]
            numbers = [start + 2, start + 3]
            pivotNumber = start + 1
        }
        
        var views: [UIView] = []
        for (i, number) in numbers.enumerated() {
            let view = createItemBia(number: number)            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.tag = i
            views.append(view)
        }
        gridLayout.columns = 2
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views.shuffled())
        
        views = leftNumbers.map { createItemBia(number: $0) }
        leftGridLayout.columns = 3
        leftGridLayout.itemSpacingRatio = 0.05
        leftGridLayout.insetRatio = 0.05
        leftGridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        gridLayout.alpha = 0
        var delay = playSound(openGameSound(), lientruoc ? "toan/toan_so giua lien truoc sau_truoc" : "toan/toan_so giua lien truoc sau_sau", "topics/Numbers/\(pivotNumber)", "toan/toan_so giua lien truoc sau_la so may")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.leftGridLayout.transform = .identity
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
        }
        delay += 1.0
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(lientruoc ? "toan/toan_so giua lien truoc sau_truoc" : "toan/toan_so giua lien truoc sau_sau", "topics/Numbers/\(pivotNumber)", "toan/toan_so giua lien truoc sau_la so may")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        if index == 0 {
            let delay = playSound(finishCorrect1Sounds())
            UIView.animate(withDuration: 0.5) {
                view.transform = CGAffineTransform(scaleX: 0.5, y: 0.5)
                view.alpha = 0
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
            view.tag = -1
            let rightItemView = self.leftGridLayout.subviews[leftNumbers.firstIndex(of: 0)!] as! UIImageView
            coinView.frame = rightItemView.frame
            animateCoinIfCorrect(view: coinView)
            UIView.animate(withDuration: 0.6) {
                rightItemView.transform = CGAffineTransform(scaleX: 0, y: 0).concatenating(CGAffineTransform(scaleX: 1, y: 1))
                rightItemView.alpha = 0
                rightItemView.transform = .identity
                rightItemView.alpha = 1
            }
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                [weak self] in
                guard let self = self else { return }
                guard let viewBackground = rightItemView.viewWithTag(R5.id.view_background) as? UIImageView,
                      let textNumber = rightItemView.viewWithTag(R5.id.text_number) as? UILabel else { return }
                let number = self.numbers[0]
                viewBackground.image = Utilities.SVGImage(named: self.getNumberDrawableId(number: number))
                textNumber.text = String(number)
                rightItemView.image = Utilities.SVGImage(named: self.getNumberDrawableId(number: number))
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func getNumberDrawableId(number: Int) -> String {
        if number == 0 { return "math_bia0" }
        var adjustedNumber = number
        while adjustedNumber > 15 {
            adjustedNumber -= 7
        }
        return ids[adjustedNumber - 1]
    }
    
    private func createItemBia(number: Int) -> UIView {
        let view = UIImageView()
        view.image = Utilities.SVGImage(named: getNumberDrawableId(number: number))
        
        let viewBackground = UIImageView()
        viewBackground.tag = R5.id.view_background
        viewBackground.image = Utilities.SVGImage(named: getNumberDrawableId(number: number))
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textNumber = AutosizeLabel()
        textNumber.tag = R5.id.text_number
        textNumber.text = number == 0 ? "" : String(number)
        textNumber.textColor = UIColor.black
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        view.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.4)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            textNumber.snapToVerticalBias(verticalBias: 0.46)
        }
        
        //view.transform = CGAffineTransform(scaleX: 0, y: 0)
        //view.alpha = 0
        return view
    }
    
    private func createCoinView() -> UIView {
        let view = UIView()
        // Giả định item_coin_view.xml có nội dung tương tự, cần thêm logic nếu có
        return view
    }
}

// MARK: - Supporting Structures
struct R5 {
    struct id {
        static let view_background = 1
        static let text_number = 2
    }
}
