//
//  ngonngu_list_sapxepcau.swift
//  GiaoDucSom
//
//  Created by V<PERSON> on 28/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class ngonngu_list_sapxepcau: NhanBietGameFragment {
    // MARK: - Properties
    private let sentences: [String] = [
        "bé có quà quê", "mẹ có giỏ cá", "bà kho cá ngừ", "bố mẹ cho bé ra phố",
        "bố và bé đi về quê", "mẹ cho bé ra phố đi bộ", "bố Nga là nghệ sĩ",
        "bé đi nhà trẻ", "mẹ bé pha trà", "mẹ ghi địa chỉ nhà cô",
        "gió lùa qua khe cửa", "bé và chị nô đùa ở sân",
        "mẹ làm giò lụa cho bữa trưa", "mẹ ru bé ngủ trưa",
        "Nga đi bộ ở vỉa hè", "<PERSON> có tờ bìa đỏ", "chị Lụa có lá tía tô",
        "trời mùa thu mát mẻ", "các bạn chơi trốn tìm", "em yêu chim bồ câu",
        "mẹ mua cốm cho hai chị em", "An để giấy nháp dưới ngăn bàn",
        "bé sắp xếp mọi thứ ngăn nắp", "bà cho bé gói quà quê",
        "bà mua xôi cho bé", "chú voi có cái vòi dài", "mẹ hái quả ổi cho bé",
        "mẹ rửa cối để giã giò", "bố Khôi ở thủ đô Hà Nội",
        "mẹ cài nơ cho bé Mai", "bố Hải lái xe ô tô",
        "bé gửi thư cho chú bộ đội", "cá bơi dưới suối", "mái ngói đỏ tươi",
        "chú chó vẫy đuôi", "nhà bé nuôi gà", "bé ngồi thổi sáo",
        "bé yêu mẹ và cô", "bà nội Hà đã già yếu", "Hoa là cô bé hiếu thảo",
        "cả nhà đi chơi hội", "bé và chị chơi bập bênh",
        "chú chim nhảy nhót trên cành", "nắng chiếu trên hiên nhà",
        "cả nhà đều yêu quý bé", "bé thấy chú hươu ở sở thú",
        "bé và chị đi thả diều sáo", "cây cau sai trĩu quả",
        "em cần giữ vở cẩn thận", "bé và chị nặn con thỏ",
        "bé ăn món bún riêu mẹ nấu", "mèo con đi rón rén bên bàn",
        "mai sau, bé sẽ là cô giáo", "tàu bè nối đuôi vào bến",
        "đàn yến bay lượn trên bầu trời", "mùa hè tới, bố cho bé đi bơi",
        "dưới đáy biển có nhiều san hô", "phía xa khơi có nhiều hòn đảo",
        "bé vui chơi trên bãi biển", "cây lựu đã ra quả",
        "bé ngồi trên yên xe", "kiến lửa mới xây tổ",
        "cô và mẹ đều dịu hiền", "chị và bé chơi đố chữ",
        "bé có đôi tất mới"
    ]
    private var textContainer: UIStackView!
    private var textBottomContainer: UIStackView!
    private var contentLayout: UIStackView!
    private var gogogo: SVGKFastImageView!
    private var itemContainer: UIView!
    private var colorRead = UIColor.color(hex: "#FF0000")
    private var colorReading = UIColor.color(hex: "#00FF00")
    private var colorNormal = UIColor.color(hex: "#0000FF")
    private var colorBackground = UIColor.color(hex: "#7197CF")
    private var svg: SVGKImage?
    private var flyItemResource: String = ""
    private var step: Int = 0
    private var values: [String] = []
    private var swingId: Int = 0
    private var items: [String] = []
    private var chooseIndexes: [Int] = []
    private var player: AVAudioPlayer?
    private var containerLayout: UIView?
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        containerLayout = view
        backgroundColor = UIColor.color(hex: "#849BFD")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        textContainer = UIStackView()
        textContainer.axis = .horizontal
        textContainer.alignment = .center
        textContainer.distribution = .equalSpacing
        textContainer.spacing = 0
        textContainer.isLayoutMarginsRelativeArrangement = true
        itemContainer.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
            //make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        textBottomContainer = UIStackView()
        textBottomContainer.axis = .horizontal
        textBottomContainer.alignment = .center
        textBottomContainer.distribution = .equalSpacing
        textBottomContainer.spacing = 0
        textBottomContainer.isLayoutMarginsRelativeArrangement = true
        textBottomContainer.isHidden = true
        itemContainer.addSubview(textBottomContainer)
        textBottomContainer.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
            //make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        let contentContainer = UIView()
        itemContainer.addSubview(contentContainer)
        contentContainer.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(textBottomContainer.snp.top)
        }
        
        contentLayout = UIStackView()
        contentLayout.axis = .horizontal
        contentLayout.alignment = .center
        contentLayout.distribution = .equalSpacing
        contentLayout.spacing = 0
        contentLayout.isLayoutMarginsRelativeArrangement = true
        contentLayout.isHidden = true
        contentContainer.addSubview(contentLayout)
        contentLayout.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
        }
        
        gogogo = SVGKFastImageView(frame: .zero)
        svg = Utilities.GetSVGKImage(named: "rabbit")
        let firstLayer = svg?.caLayerTree.sublayers?.first
        svg?.caLayerTree.sublayers?.forEach { layer in
            layer.isHidden = layer !== firstLayer
        }
        //gogogo.backgroundColor = .red
        //gogogo.image = svg
        gogogo.accessibilityIdentifier = "gogogo"
        gogogo.alpha = 0.01
        itemContainer.addSubview(gogogo)
        gogogo.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.2)
            make.width.equalTo(gogogo.snp.height)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        values = sentences.shuffled()
        let delay = playSound(delay: 0.0, names: [openGameSound(), "ngonngu/ngonngu_sap xep cau"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        loadNextStep()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("ngonngu/ngonngu_sap xep cau")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func loadNextStep() {
        if step >= 1 {
            let delay = playSound(delay: 0, names: [getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
            return
        }
        
        let answer = values[step].trimmingCharacters(in: .whitespaces)
        let capitalizedAnswer = answer.prefix(1).uppercased() + answer.dropFirst() + "."
        step += 1
        
        contentLayout.isHidden = true
        textBottomContainer.isHidden = true
        contentLayout.arrangedSubviews.forEach { $0.removeFromSuperview() }
        textBottomContainer.arrangedSubviews.forEach { $0.removeFromSuperview() }
        textContainer.arrangedSubviews.forEach { $0.removeFromSuperview() }
        textContainer.alpha = 1
        
        items = capitalizedAnswer.split(separator: " ").map { String($0) }
        let itemIndex = 1 // Giả định chọn cố định để đơn giản hóa
        flyItemResource = ["bird", "rabbit", "rocket", "turtle"][itemIndex]
        let colorReadValues = [UIColor.color(hex: "#EC6149"), .white, .white, .white]
        let colorNormalValues = [UIColor.color(hex: "#587AC2"), UIColor.color(hex: "#1C5ED2"), UIColor.color(hex: "#6695FF"), UIColor.color(hex: "#8ACE7C")]
        let colorBackgroundValues = [UIColor.color(hex: "#7197CF"), .white, UIColor.color(hex: "#6695FF"), UIColor.color(hex: "#8ACE7C")]
        
        colorRead = colorReadValues[itemIndex]
        colorReading = colorReadValues[itemIndex]
        colorNormal = colorNormalValues[itemIndex]
        colorBackground = colorBackgroundValues[itemIndex]
               
        let textSize = getTextSize(text: capitalizedAnswer)
        let typeface = UIFont(name: "SVN-Freude", size: textSize) ?? .systemFont(ofSize: textSize)
        
        let count = max(2, Int(round(Float(items.count) * 0.6)))
        chooseIndexes = (0..<items.count).shuffled().prefix(count).map { $0 }
        
        for item in items {
            let textView = UILabel()
            textView.text = item + " "
            textView.font = typeface
            textView.textColor = colorNormal
            textView.backgroundColor = .clear
            textView.sizeToFit()
            textContainer.addArrangedSubview(textView)
        }
        
        for (i, item) in items.enumerated() {
            let linearLayout = UIStackView()
            linearLayout.axis = .horizontal
            linearLayout.spacing = 0
            
            let textView = UILabel()
            textView.text = item + " "
            textView.font = typeface
            textView.textColor = chooseIndexes.contains(i) ? colorBackground : colorNormal
            textView.backgroundColor = chooseIndexes.contains(i) ? colorBackground : .clear
            textView.layer.cornerRadius = textSize * 0.2
            textView.layer.masksToBounds = true
            textView.sizeToFit()
            linearLayout.addArrangedSubview(textView)
            
            let spaceView = UILabel()
            spaceView.text = " "
            spaceView.font = typeface
            spaceView.textColor = colorRead
            spaceView.backgroundColor = .clear
            spaceView.sizeToFit()
            linearLayout.addArrangedSubview(spaceView)
            
            textBottomContainer.addArrangedSubview(linearLayout)
        }
        
        for index in chooseIndexes {
            let spaceView1 = UILabel()
            spaceView1.text = " "
            spaceView1.font = typeface
            spaceView1.textColor = colorRead
            spaceView1.backgroundColor = .clear
            spaceView1.sizeToFit()
            contentLayout.addArrangedSubview(spaceView1)
            
            let textView = UILabel()
            textView.text = items[index]
            textView.font = typeface
            textView.textColor = colorRead
            textView.backgroundColor = .clear
            textView.sizeToFit()
            contentLayout.addArrangedSubview(textView)
            setTouchEvent(textView: textView)
            
            let spaceView2 = UILabel()
            spaceView2.text = " "
            spaceView2.font = typeface
            spaceView2.textColor = colorRead
            spaceView2.backgroundColor = .clear
            spaceView2.sizeToFit()
            contentLayout.addArrangedSubview(spaceView2)
        }
        
        playSound("games/slide")
        UIView.animate(withDuration: 0.5, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
            self.textBottomContainer.transform = .identity
        }, completion: { _ in
            let delay = self.playSound(delay: 0, names: [])
            self.scheduler.schedule(after: delay) { [weak self] in
                self?.moveToBottomAndStartGame()
            }
        })
        textContainer.transform = .identity
        textContainer.alpha = 0.01
        
        scheduler.schedule(after: 0.3) { [weak self] in
            self?.moveToBottomAndStartGame()
        }
    }
    
    private func getTextSize(text: String) -> CGFloat {
        let a = itemContainer.frame.width * 35 / 20 / 15
        let b = sqrt(itemContainer.frame.height * itemContainer.frame.width / CGFloat(text.count)) / 3
        let c = isRunningOnIpad() ? 100.0 : 70.0
        return min(a, min(b,c))
    }
    
    private func setTouchEvent(textView: UILabel) {
        textView.isUserInteractionEnabled = true
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        textView.addGestureRecognizer(panGesture)
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UILabel else { return }
        var dX: CGFloat = 0, dY: CGFloat = 0
        var oX: CGFloat = 0, oY: CGFloat = 0
        var snapView: UILabel?
        
        switch gesture.state {
        case .began:
            dX = view.frame.minX - gesture.location(in: view.superview).x
            dY = view.frame.minY - gesture.location(in: view.superview).y
            oX = view.frame.minX
            oY = view.frame.minY
            view.textColor = colorNormal
            UIView.animate(withDuration: 0.1) {
                view.transform = .init(scaleX: 1.1, y: 1.1)
            }
            playSound("effect/cungchoi_pick\(random(1,2))")
            
        case .changed:
            let translation = gesture.translation(in: view.superview)
            view.transform = CGAffineTransformMakeTranslation(translation.x + dX, translation.y + dY)
            var closedView: UILabel?
            var distance = CGFloat.greatestFiniteMagnitude
            let textViews = textBottomContainer.findSubviews(ofType: UILabel.self)
            for text in textViews {
                let centerDistance = hypot(
                    text.center.x - view.center.x,
                    text.center.y - view.center.y
                )
                if centerDistance < distance {
                    distance = centerDistance
                    closedView = text
                }
            }
            
            if distance < view.frame.height / 2 {
                if closedView != snapView {
                    restoreSnapView(snapView: snapView)
                    if closedView?.textColor != colorRead {
                        snapView = closedView
                        //snapView?.layer.pivotY = snapView?.frame.height ?? 0
                        UIView.animate(withDuration: 0.1) {
                            snapView?.alpha = 0.5
                        }
                    }
                }
            } else {
                restoreSnapView(snapView: snapView)
            }
            
        case .ended:
            UIView.animate(withDuration: 0.1) {
                //view.transform = .identity
            }
            
            var closedView: UILabel?
            var minDistance = CGFloat.greatestFiniteMagnitude
            let textViews = textBottomContainer.findSubviews(ofType: UILabel.self)
            for text in textViews {
                let vector = view.distanceFromCenterToCenter(to: text)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    closedView = text
                }
            }
            
            if minDistance < view.frame.height / 2,
                let closedView = closedView,
                view.text?.trimmingCharacters(in: .whitespaces) == closedView.text?.trimmingCharacters(in: .whitespaces),
                closedView.textColor != colorNormal {
                processRightDrag(closedView: closedView, textView: view)
                playSound("effect/word puzzle drop")
            } else {
                UIView.animate(withDuration: 0.2) {
                    view.transform = .identity
                }
                view.textColor = colorRead
                restoreSnapView(snapView: snapView)
                playSound("effect/slide2")
                if closedView?.text?.trimmingCharacters(in: .whitespaces).isEmpty == false {
                    setGameWrong()
                }
            }
            
        default:
            break
        }
    }
    
    private func restoreSnapView(snapView: UILabel?) {
        guard let snapView = snapView else { return }
        UIView.animate(withDuration: 0.1) {
            snapView.alpha = 1
        }
    }
    
    private func processRightDrag(closedView: UILabel, textView: UILabel) {
        // Move textView to closedView's position
        textView.moveToCenter(of: closedView, duration: 0.1) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                closedView.alpha = 0.01
            }, completion: { _ in
                closedView.backgroundColor = .clear
                closedView.textColor = self.colorNormal
                closedView.alpha = 1
                textView.alpha = 0.01
                textView.transform = .identity
                textView.frame = .zero
                self.checkFinish()
            })
        }
    }
    
    private func checkFinish() {
        let textViews = contentLayout.findSubviews(ofType: UILabel.self)
        let finish = textViews.allSatisfy { $0.alpha != 1 || $0.text?.trimmingCharacters(in: .whitespaces).isEmpty == true }
        if finish {
            finishAnimation()
        }
    }
    
    private func finishAnimation() {
        let startDelay: TimeInterval = 1
        for i in 0..<textContainer.arrangedSubviews.count {
            let view = textContainer.arrangedSubviews[i]
            view.transform = .identity
            view.frame = .zero
            view.alpha = 1
        }
        //textContainer.alpha = 1
        textContainer.layoutIfNeeded()
        
        scheduler.schedule(after: 0.5) { [weak self] in
            guard let self = self else { return }
            for i in 0..<self.textContainer.arrangedSubviews.count {
                let view = self.textContainer.arrangedSubviews[i]
                let targetView = self.textBottomContainer.arrangedSubviews[i]
                targetView.moveToCenter(of: view, duration: 0.3)                                
            }
            
            scheduler.schedule(after: 0.3) { [weak self] in
                guard let self = self else { return }
                UIView.animate(withDuration: 0.05) {
                    self.textContainer.alpha = 1
                    self.textBottomContainer.alpha = 0
                }
                self.scheduler.schedule(after: 0.2) { [weak self] in
                    self?.jumpAnimation()
                }
            }
        }
        
        playSound(name: "games/slide2", delay: startDelay)
    }
    
    private func jumpAnimation() {
        var delay: TimeInterval = 1
        for i in 0..<textContainer.arrangedSubviews.count {
            let child = textContainer.arrangedSubviews[i] as! UILabel
            delay += 0.2
            var item = items[i].lowercased().replacingOccurrences(of: ",", with: "").replacingOccurrences(of: ".", with: "").replacingOccurrences(of: ":", with: "")
            if item == "con" { item = "con2" }
            let duration = max(0.2, playSound(name: "ngonngu/noi tu ghep/\(item)", delay: delay) + (items[i].contains(",") || items[i].contains(".") || items[i].contains(":") ? 0.8 : 0))
            
            scheduler.schedule(after: delay) { [weak self] in
                guard let self = self else { return }
                child.textColor = self.colorReading
                if i > 0 {
                    let child2 = self.textContainer.arrangedSubviews[i - 1] as! UILabel
                    child2.textColor = self.colorRead
                }
                var translation = self.gogogo.moveToCenterInfo(of: child)
                translation.ty -= child.frame.height * 1.5
                UIView.animate(withDuration: 0.5, delay: 0, options: .curveLinear) {
                    self.gogogo.transform = translation
                }
                               
                self.gogogo.alpha = 1
                
                // Giả lập animation frame cho các SVG khác bird
                let count = svg?.caLayerTree.sublayers?.count
                self.gogogo.contentMode = .scaleToFill
                self.gogogo.image = svg
                for j in 0..<count!{
                    self.scheduler.schedule(after: CGFloat(j) * 0.041) {
                        [weak self] in
                        guard let self = self else { return }
                        self.svg?.caLayerTree.sublayers?.forEach { layer in
                            layer.isHidden = layer !== self.svg?.caLayerTree.sublayers![j]
                        }
                        self.gogogo.image = svg
                    }
                }
            }
            delay += duration
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.animateCoinIfCorrect(view: self?.textContainer)
        }
        
        scheduler.schedule(after: delay + 1) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.3) {
                self.gogogo.alpha = 0.01
            }
            self.pauseGame(stopMusic: false)
            self.scheduler.schedule(after: 1) { [weak self] in
                guard let self = self else { return }
                self.resumeGame(startMusic: false)
                UIView.animate(withDuration: 0.5, delay: 1, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                    self.textContainer.transform = .init(translationX: self.containerLayout!.frame.width, y: 0)
                }, completion: { _ in
                    self.textBottomContainer.transform = .init(translationX: -self.containerLayout!.frame.width, y: 0)
                    self.textBottomContainer.snp.remakeConstraints { make in
                        make.centerX.equalToSuperview()
                        make.bottom.equalToSuperview()
                        //make.width.equalToSuperview().multipliedBy(0.8)
                    }

                    self.textBottomContainer.layoutIfNeeded()
                    self.loadNextStep()
                })
            }
        }
    }
    
    private func moveToBottomAndStartGame() {
        playSound("effects/games/bell")
       
        textBottomContainer.alpha = 0
        textBottomContainer.isHidden = false
        contentLayout.alpha = 0
        contentLayout.isHidden = false
        
        scheduler.schedule(after: 0.3) { [weak self] in
            UIView.animate(withDuration: 0.5) {
                self?.contentLayout.alpha = 1
            }
            UIView.animate(withDuration: 0.3, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0, options: [], animations: {
                self?.textBottomContainer.alpha = 1
                self?.textBottomContainer.transform = .identity
            })
        }
    }
}
