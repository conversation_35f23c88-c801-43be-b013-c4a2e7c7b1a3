//
//  GalleryMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 20/5/25.
//


import UIKit
import SnapKit
import SVGKit

class GalleryMainViewController: BaseViewController {
    // MARK: - Properties
    var selectedIndex: Int = -1
    private var folders: [Folder] = []
    var maps: [String] = []
    private var svg: SVGKImage = Utilities.GetSVGKImage(named: "icon_album_lock")
    var mapToColor: [String: String] = [:]
    private var unlockedMaps: [String] = []
    
    
    private let collectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        return UICollectionView(frame: .zero, collectionViewLayout: layout)
    }()
    private let btnClose = KUButton()
    private let gradientView = FadeGradientView()
    private let txtTitle = AutosizeLabel()
    
    override var backgroundMusicFile: String? {
        "bg_sticker"
    }
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        view.backgroundColor = UIColor(hex: "#512714")
        // backgroundMusicName = "bg_sticker"
        //hideStatusBar()
        initData()
        setupUI()
        collectionView.reloadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if selectedIndex != -1 {
            collectionView.reloadItems(at: [IndexPath(item: selectedIndex, section: 0)])
        }
    }
    
    // MARK: - Setup
    private func initData() {
        unlockedMaps = GalleryManager.shared.getUnlockedMaps()
        folders = FlashcardsManager.shared.getPacks().filter { $0.maps != nil && !($0.maps?.isEmpty ?? true) }
        for folder in folders {
            if let folderMaps = folder.maps {
                maps.append(contentsOf: folderMaps)
                for map in folderMaps {
                    mapToColor[map] = folder.mapColor
                }
            }
        }
    }
    
    private func setupUI() {
        view.removeAllSubviews()
        
        // btnClose
        btnClose.setImage(Utilities.SVGImage(named: "btn_back_sticker"), for: .normal)
        btnClose.contentMode = .scaleAspectFit
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.height.equalTo(view.snp.height).multipliedBy(0.1)
            make.width.equalTo(btnClose.snp.height)
            make.top.equalTo(view.snp.bottom).multipliedBy(0.05)
        }
        btnClose.snpLeftTop(ratio: 1)
        // txtTitle
        txtTitle.text = "Bộ sưu tập sticker"
        txtTitle.font = .Freude(size: 20)
        txtTitle.textColor = UIColor(hex: "#FFFFFF")
        txtTitle.textAlignment = .left
        txtTitle.overrideTextAlignment = false
        txtTitle.numberOfLines = 1
        view.addSubview(txtTitle)
        txtTitle.snp.makeConstraints { make in
            make.centerY.equalTo(btnClose)
            make.width.equalTo(view.snp.width).multipliedBy(0.45)
            make.height.equalTo(view.snp.height).multipliedBy(0.06)
        }
        txtTitle.snpLeftTop(ratio: 3.0)
        
        
                
        // collectionView
        collectionView.backgroundColor = .clear
        collectionView.clipsToBounds = false
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.register(GalleryCell.self, forCellWithReuseIdentifier: "GalleryCell")
        if let layout = collectionView.collectionViewLayout as? UICollectionViewFlowLayout {
            let column = Utilities.isIPad ? 4 : 4
            let dpToPx = view.bounds.width / CGFloat(column) * 0.92
            layout.itemSize = CGSize(width: dpToPx, height: dpToPx * 616 / 560)
            layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
        }
        view.addSubview(collectionView)
        
        
        // gradientView
        gradientView.setupGradient(color: .color(hex: "#512714"))
        view.addSubview(gradientView)
        gradientView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(gradientView.snp.width).dividedBy(30)
            make.top.equalTo(btnClose.snp.bottom)
        }
        
        let viewTop = UIView()
        viewTop.backgroundColor = .color(hex: "#512714")
        view.addSubview(viewTop)
        viewTop.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(gradientView.snp.top)
        }
        
        collectionView.snp.makeConstraints { make in
            make.top.equalTo(gradientView.snp.bottom)
            make.left.equalTo(btnClose)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let coinView = CoinView()
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.top.bottom.equalTo(btnClose)
        }
        coinView.snpRightTop(ratio: 1)
        view.bringSubviewToFront(btnClose)
        view.bringSubviewToFront(txtTitle)
    }
  
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
        navigationController?.popViewController(animated: true)
    }
}

// MARK: - GalleryCell
class GalleryCell: UICollectionViewCell {
    private let imageThumb = SVGImageView(frame: .zero)
    private let imageLock = UIImageView()
    private var map: String?
    static var coinImage: UIImage!
    private weak var viewController: GalleryMainViewController?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.accessibilityIdentifier = "contentView"
        let ratioContentView = UIView()
        ratioContentView.accessibilityIdentifier = "ratioContentView"
        contentView.addSubview(ratioContentView)
        ratioContentView.makeViewCenterAndKeep(ratio: 560.0 / 616.0)
        // imageThumb
        imageThumb.contentMode = .scaleAspectFit
        ratioContentView.addSubview(imageThumb)
        imageThumb.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.9)
            make.width.equalTo(imageThumb.snp.height).multipliedBy(515.0 / 616.0)
        }
        
        // imageLock
        imageLock.contentMode = .scaleAspectFit
        ratioContentView.addSubview(imageLock)
        imageLock.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalTo(imageLock.snp.width)
        }
        imageLock.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            self.imageLock.snapToVerticalBias(verticalBias: 0.32)
            self.imageLock.snapToHorizontalBias(horizontalBias: 0.85)
        }
        
        // Tap gesture
        let tap = UITapGestureRecognizer(target: self, action: #selector(itemTapped))
        contentView.addGestureRecognizer(tap)
        contentView.isUserInteractionEnabled = true
    }
    
    func configure(with map: String, svg: SVGKImage?, unlockedMaps: [String], mapToColor: [String: String], viewController: GalleryMainViewController) {
        self.map = map
        self.viewController = viewController
        
        imageThumb.SVGName = "gallery/\(map)_thumb.svg"
        if let svg = svg, let color = mapToColor[map] {
            // Adjust SVG fill color (requires SVGKit manipulation)
            // Note: SVGKImage doesn't directly support path color changes; may need custom logic
            svg.caLayerTree.sublayers![1].setFillColor(color: .color(hex: mapToColor[map]!))
            imageLock.image = svg.uiImage
        }
        imageLock.isHidden = unlockedMaps.contains(map)
        contentView.alpha = unlockedMaps.contains(map) ? 1.0 : 0.4
    }
    
    @objc private func itemTapped() {
        
        guard let map = map, let viewController = viewController else { return }
        viewController.selectedIndex = viewController.maps.firstIndex(of: map) ?? -1
        let vc = GalleryViewController()
        vc.map = map
        vc.mapColor = viewController.mapToColor[map]
        viewController.navigationController?.pushViewController(vc, animated: true)
         
    }
}

// MARK: - GalleryAdapter
extension GalleryMainViewController: UICollectionViewDataSource, UICollectionViewDelegate, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return maps.count ?? 0
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "GalleryCell", for: indexPath) as! GalleryCell
        let map = maps[indexPath.item]
        cell.configure(with: map, svg: svg, unlockedMaps: unlockedMaps, mapToColor: mapToColor, viewController: self)
        return cell
    }
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let spanCount = Utilities.isIPad ? 4 : 4
        let totalSpacing = CGFloat(spanCount - 1) * 10 + 20 // 10 là minimumInteritemSpacing, 20 là padding trái + phải từ sectionInset
        let width = (collectionView.bounds.width - totalSpacing) / CGFloat(spanCount)
        return CGSize(width: width, height: width * 616.0 / 560.0)
    }
}
