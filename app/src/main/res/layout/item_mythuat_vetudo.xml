<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.8"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/mythuat_tranhtomau_list_bg"
            app:layout_constraintDimensionRatio="0.8"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_name"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="Name"
                android:textColor="#f39a71"
                app:heightRatio="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.15"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.9" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_plus"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                android:text="+"
                app:fontFamily="@font/svn_freude"
                app:heightRatio="0.5"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.kidsup.giaoducsom.view.SmoothStrokeView
                android:id="@+id/svg_thumbnail"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#fff"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="2"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.5"
                app:layout_constraintWidth_percent="0.8" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>