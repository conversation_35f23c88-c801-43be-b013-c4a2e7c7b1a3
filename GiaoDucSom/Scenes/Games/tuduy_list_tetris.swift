//
//  tuduy_list_tetris.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_tetris: NhanBietGameFragment {
    // MARK: - Properties
    private var coinView: UIView!
    private var tetrisTopView: TetrisView!
    private var answerLayout: MyGridView!
    private var meIndex: Int = 0
    private var meTopRotation: Int = 0
    private var lastRotation: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .white // #FFFFFF
        
        answerLayout = MyGridView()
        answerLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.6)
        }
        
        let leftPaddingView = UIView()
        leftContainer.addSubviewWithPercentInset(subview: leftPaddingView, percentInset: 10)
                
        tetrisTopView = TetrisView()
        leftPaddingView.addSubview(tetrisTopView)
        tetrisTopView.makeViewCenterAndKeep(ratio: 1)
        
        view.bringSubviewToFront(answerLayout)
        
        coinView = UIView() // Giả lập từ item_coin_view.xml
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let blocks = StorageManager.manager.list(path: "tetris/")
            .filter { $0.hasSuffix(".svg") && $0.hasPrefix("block") }
            .shuffled()
            .prefix(4)
        let blockPaths = ["tetris/sample1.svg", "tetris/sample2.svg"] + blocks.map { "tetris/\($0)" }
        let svgList = blockPaths.map { Utilities.GetSVGKImage(named: $0) }
        self.tetrisTopView.setBlockFill(svg: svgList[0])
        self.tetrisTopView.setBlockEmpty(svg: svgList[1])
        self.meIndex = Int.random(in: 2..<svgList.count)
        
        var datas: [[[Int]]] = []
        var maxWidth = 0
        var maxHeight = 0
        for i in 2..<svgList.count {
            var data = self.parseSvgData(svg: svgList[i])
            data = self.trimData(data: data)
            data = self.rotateRandom(sourceData: data)
            datas.append(data)
            maxWidth = max(maxWidth, data[0].count)
            maxHeight = max(maxHeight, data.count)
        }
        
        var ansItemViewList: [UIView] = []
        for i in 2..<svgList.count {
            let view = TetrisView()
            view.tag = i
            view.setBlockFill(svg: svgList[0])
            view.setBlockEmpty(svg: svgList[1])
            var data = datas[i - 2]
            if i == self.meIndex {
                self.lastRotation = 0
                let topData = self.createTopData(answer: svgList.count - 2 > 2 ? self.rotateRandom(sourceData: data) : data)
                self.meTopRotation = self.lastRotation
                self.tetrisTopView.setData(data: topData)
            }
            data = self.expandData(data: data, newWidth: maxWidth, newHeight: maxHeight)
            view.setData(data: data)
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
            ansItemViewList.append(view)
        }
        
        self.answerLayout.columns = 2
        self.answerLayout.itemRatio = 1
        self.answerLayout.itemSpacingRatio = 0.05
        self.answerLayout.insetRatio = 0.05
        self.answerLayout.reloadItemViews(views: ansItemViewList)
        
        let delay = self.playSound(self.openGameSound(), "tuduy/tuduy_tetris")
        self.answerLayout.showItems(startDelay: delay)
        self.scheduler.schedule(after: delay + 0.5) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/xep gach")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view as? TetrisView else { return }
        guard gameState != .paused else { return }
        
        pauseGame()
        let id = view.tag
        if id == meIndex {
            animateCoinIfCorrect(view: coinView)
            playSound("effect/answer_end")
            
            var index = 0
            for i in 0..<answerLayout.subviews.count {
                let item = answerLayout.subviews[i]
                if item != view {
                    index += 1
                    UIView.animate(withDuration: 0.2, delay: TimeInterval(index) * 0.2) {
                        item.alpha = 0
                        item.transform = CGAffineTransform(scaleX: 0.01, y: 0.01)
                    }
                }
            }
            
            scheduler.schedule(after: max(TimeInterval(index) * 0.2, 1.0)) {
                [weak self] in
                guard let self = self else { return }
                let scale = self.tetrisTopView.cellSize / view.cellSize
                //view.backgroundColor = .green.withAlphaComponent(0.5)
                UIView.animate(withDuration: 0.3) {
                    view.subviews[0].transform = CGAffineTransform(scaleX: scale, y: scale)
                } completion: { _ in
                    let distance = self.tetrisTopView.distanceFromCenterToCenter(to: view)
                    var data = view.getData()
                    let measuredHeight = view.frame.height
                    let measuredWidth = view.frame.width
                    let th = view.cellSize * CGFloat(data.count)
                    let tw = view.cellSize * CGFloat(data[0].count)
                    
                    var deltaX: CGFloat = 0
                    var deltaY: CGFloat = 0
                    if (self.meTopRotation == 90 || self.meTopRotation == 270) && abs(data.count - data[0].count) % 2 == 1 {
                        let delta = view.transform.a * (measuredHeight - measuredWidth) / 2
                        deltaX += delta
                        deltaY -= delta
                    }
                    
                    let bound = self.getBound(data: self.rotateData(sourceData: data, rotation: self.meTopRotation), value: 1)
                    let topBound = self.getBound(data: self.tetrisTopView.getData(), value: 0)
                    deltaX += (CGFloat(topBound.minX - bound.minX)) * self.tetrisTopView.cellSize
                    deltaY += (CGFloat(topBound.minY - bound.minY)) * self.tetrisTopView.cellSize
                    
                    var finalRotation = self.meTopRotation % 360
                    if finalRotation > 180 {
                        finalRotation -= 360
                    }
                    view.rotateItems(rotation: finalRotation)
                    // -90 trên phải
                    // 90 dưới trái
                    // 180, -180 dưới phải
                    // 0 trên trái
                    let dot = UIView()
                    dot.backgroundColor = .black
                    view.subviews[0].addSubview(dot)
                    dot.snp.makeConstraints { make in
                        let size = 0
                        if finalRotation % 360 == 0 {
                            make.left.top.equalToSuperview()
                            make.width.height.equalTo(size)
                        }
                        if finalRotation % 360 == -90 || finalRotation % 360 == 270 {
                            make.left.bottom.equalToSuperview()
                            make.width.height.equalTo(size)
                        }
                        if finalRotation % 360 == 90 || finalRotation % 360 == -270 {
                            make.right.top.equalToSuperview()
                            make.width.height.equalTo(size)
                        }
                        if finalRotation % 360 == 180 || finalRotation % 360 == -180 {
                            make.right.bottom.equalToSuperview()
                            make.width.height.equalTo(size)
                        }
                    }
                    print(finalRotation)
                    UIView.animate(withDuration: 1.0) {
                        let tran = view.subviews[0].transform
                        view.subviews[0].transform = CGAffineTransform(rotationAngle: CGFloat(-finalRotation) * .pi / 180).concatenating(.identity.scaledBy(x: scale, y: scale))
                    }
                    self.scheduler.schedule(delay: 1.01) {
                        [weak self] in
                        guard let self = self else { return }
                        let d = dot.distanceFromLeftToLeft(to: self.tetrisTopView)
                        let w = view.getData()[0].count
                        let h = view.getData().count
                        var dx = 0.0
                        var dy = 0.0
                        if w > h {
                            dy = -Double(w-h)/2.0
                        }
                        if h > w {
                            dx = -Double(h - w)/2.0
                        }
                        if finalRotation % 360 == -90 || finalRotation % 360 == 270 || finalRotation == 90 || finalRotation == -270 {
                            let tmp = dx
                            dx = dy
                            dy = tmp
                        }
                        //let dx = topBound.width > topBound.height ? 0.5 : 0
                        //let dy = topBound.width < topBound.height ? 0.5 : 0
                        UIView.animate(withDuration: 1) {
                            view.transform = CGAffineTransformMakeTranslation(-d.x + (CGFloat(topBound.minX - bound.minX + dx)) * self.tetrisTopView.cellSize, -d.y + (CGFloat(topBound.minY - bound.minY + dy)) * self.tetrisTopView.cellSize)
                        }
                        //view.moveToTopLeft(of: self.tetrisTopView, duration: 1, deltaX: (topBound.minX-bound.minX)*self.tetrisTopView.cellSize/2 * 0, deltaY: (topBound.minY-bound.minY)*self.tetrisTopView.cellSize/2 * 0)
                    }
                    
                    self.playSound("effect/slide1")
                    self.playSound(name: "effect/word puzzle drop", delay: 0.8)
                    let delay = 3.0 + self.playSound(delay: 3.0, names: [self.getCorrectHumanSound(), self.endGameSound()])
                    self.scheduler.schedule(delay: delay) { [weak self] in
                        self?.finishGame()
                    }
                }
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createTopData(answer: [[Int]]) -> [[Int]] {
        var data = Array(repeating: Array(repeating: 0, count: 6), count: 6)
        let deltaX = Int.random(in: 0...(data.count - answer.count))
        let deltaY = Int.random(in: 0...(data[0].count - answer[0].count))
        data = fillData(data: data, value: 1)
        for i in 0..<answer.count {
            for j in 0..<answer[0].count {
                if answer[i][j] == 1 {
                    data[deltaX + i][deltaY + j] = 0
                }
            }
        }
        return data
    }
    
    private func rotate90(sourceData: [[Int]]) -> [[Int]] {
        let width = sourceData.count
        let height = sourceData[0].count
        var answerData = Array(repeating: Array(repeating: 0, count: width), count: height)
        for i in 0..<width {
            for j in 0..<height {
                answerData[height - 1 - j][i] = sourceData[i][j]
            }
        }
        return answerData
    }
    
    private func rotateData(sourceData: [[Int]], rotation: Int) -> [[Int]] {
        var normalizedRotation = (rotation + 360) % 360
        if normalizedRotation == 0 {
            return sourceData
        } else if normalizedRotation == 90 {
            return rotate90(sourceData: sourceData)
        } else if normalizedRotation == 180 {
            return rotate90(sourceData: rotate90(sourceData: sourceData))
        } else if normalizedRotation == 270 {
            return rotate90(sourceData: rotate90(sourceData: rotate90(sourceData: sourceData)))
        }
        return sourceData
    }
    
    private func rotateRandom(sourceData: [[Int]]) -> [[Int]] {
        let angles = [0, 90, 90, 180, 180, 270, 270]
        let angle = angles.randomElement()!
        lastRotation = angle
        return rotateData(sourceData: sourceData, rotation: angle)
    }
    
    private func getBound(data: [[Int]], value: Int) -> CGRect {
        var minX = data.count
        var minY = data[0].count
        var maxX = 0
        var maxY = 0
        for i in 0..<data.count {
            for j in 0..<data[i].count {
                if data[i][j] == value {
                    minX = min(minX, i)
                    minY = min(minY, j)
                    maxX = max(maxX, i)
                    maxY = max(maxY, j)
                }
            }
        }
        return CGRect(x: CGFloat(minY), y: CGFloat(minX), width: CGFloat(maxY - minY + 1), height: CGFloat(maxX - minX + 1))
    }
    
    private func trimData(data: [[Int]]) -> [[Int]] {
        let bound = getBound(data: data, value: 1)
        var answerData = Array(repeating: Array(repeating: 0, count: Int(bound.width)), count: Int(bound.height))
        for i in 0..<answerData.count {
            for j in 0..<answerData[0].count {
                answerData[i][j] = data[Int(bound.minY) + i][Int(bound.minX) + j]
            }
        }
        return answerData
    }
    
    private func expandData(data: [[Int]], newWidth: Int, newHeight: Int) -> [[Int]] {
        var answerData = Array(repeating: Array(repeating: -1, count: newWidth), count: newHeight)
        let deltaX = (newHeight - data.count) / 2
        let deltaY = (newWidth - data[0].count) / 2
        for i in 0..<data.count {
            for j in 0..<data[0].count {
                answerData[deltaX + i][deltaY + j] = data[i][j]
            }
        }
        return answerData
    }
    
    private func parseSvgData(svg: SVGKImage) -> [[Int]] {
        let width = Int(svg.size.width / 10)
        let height = Int(svg.size.height / 10)
        var data = Array(repeating: Array(repeating: -1, count: height), count: width)
        for i in 0..<svg.caLayerTree.sublayers!.count {
            let path = svg.caLayerTree.sublayers![i]
            let bound = path.shapeContentBounds!
            let x = Int(bound.minX / 10)
            let y = Int(bound.minY / 10)
            if x < width && y < height {
                data[x][y] = 1
            }
        }
        return data
    }
    
    private func fillData(data: [[Int]], value: Int) -> [[Int]] {
        var result = data
        for i in 0..<data.count {
            for j in 0..<data[0].count {
                result[i][j] = value
            }
        }
        return result
    }
}

import UIKit
import SVGKit

import UIKit
import SVGKit

class TetrisView: UIView {
    // MARK: - Properties
    private var rootLayout: UIStackView!
    private var blockFill: SVGKImage?
    private var blockEmpty: SVGKImage?
    private var data: [[Int]] = []
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initView()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initView()
    }
    
    private func initView() {
        let container = UIView()
        addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        rootLayout = UIStackView()
        rootLayout.axis = .vertical
        rootLayout.distribution = .fillEqually
        container.addSubview(rootLayout)
    }
    
    // MARK: - Public Methods
    var cellSize: CGFloat {
        return rootLayout.subviews.first?.frame.height ?? 1
    }
    
    func getData() -> [[Int]] {
        return data
    }
    
    func setData(data: [[Int]]) {
        self.data = data
        loadData()
    }
    
    func setBlockFill(svg: SVGKImage) {
        self.blockFill = svg
        loadData()
    }
    
    func setBlockEmpty(svg: SVGKImage) {
        self.blockEmpty = svg
        loadData()
    }
    
    func rotateItems(rotation: Int) {
        for view in rootLayout.subviews.flatMap({ ($0 as? UIStackView)?.subviews ?? [] }) {
            view.transform = CGAffineTransform(rotationAngle: CGFloat(rotation) * .pi / 180)
        }
    }
    
    // MARK: - Private Methods
    private func loadData() {
        guard let blockFill = blockFill, let blockEmpty = blockEmpty, !data.isEmpty else { return }
        
        //rootLayout.backgroundColor = .red.withAlphaComponent(0.3)
        rootLayout.arrangedSubviews.forEach { $0.removeFromSuperview() }
        rootLayout.snp.makeConstraints { make in
            make.width.equalTo(rootLayout.snp.height).multipliedBy(Float(data[0].count) / Float(data.count))
            make.width.lessThanOrEqualToSuperview()
            make.height.lessThanOrEqualToSuperview()
            make.width.equalToSuperview().priority(.high)
            make.height.equalToSuperview().priority(.high)
            make.center.equalToSuperview()
        }
        for i in 0..<data.count {
            let row = UIStackView()
            row.axis = .horizontal
            row.distribution = .fillEqually
            rootLayout.addArrangedSubview(row)
            row.snp.makeConstraints { make in
                make.height.equalTo(rootLayout).dividedBy(CGFloat(data.count))
            }
            
            
            for j in 0..<data[i].count {
                let box = UIImageView()
                box.contentMode = .scaleAspectFit
                row.addArrangedSubview(box)
                if data[i][j] == 0 {
                    box.image = blockEmpty.uiImage
                } else if data[i][j] == 1 {
                    box.image = blockFill.uiImage
                }
            }
        }
    }
}
