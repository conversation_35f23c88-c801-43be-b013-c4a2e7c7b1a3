//
//  taptrung_list_vehaitay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/6/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import CoreGraphics

// MARK: - VeHaiTayGameFragment
class taptrung_list_vehaitay: NhanBietGameFragment {
    // MARK: - Properties
    private var svg: SVGKImage?
    private var leftPoints: [CGPoint] = []
    private var rightPoints: [CGPoint] = []
    private var leftDonePoints: [CGPoint] = []
    private var rightDonePoints: [CGPoint] = []
    private let numberOfSegments = 13
    private var leftContainer: UIView!
    private var rightContainer: UIView!
    private var leftTracing: TracingVe2TayView!
    private var rightTracing: TracingVe2TayView!
    private var leftHand: UIImageView!
    private var rightHand: UIImageView!
    private var leftPosition: Float = 0
    private var rightPosition: Float = 0
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#D7FBFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.stringTag = "coin_view"
        view.addSubview(coinView)
        coinView.makeViewCenterAndKeep(ratio: 1)
        coinView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        
        let backgroundView = UIView()
        backgroundView.backgroundColor = UIColor(hex: "#C4F5F7")
        view.addSubview(backgroundView)
        backgroundView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        let leftMainContainer = UIView()
        view.addSubview(leftMainContainer)
        leftMainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.left.equalToSuperview()
        }
        
        leftContainer = UIView()
        leftContainer.stringTag = "left_container"
        leftMainContainer.addSubview(leftContainer)
        leftContainer.makeViewCenterAndKeep(ratio: 1)
        
        leftTracing = TracingVe2TayView()
        leftTracing.stringTag = "left_tracing"
        leftContainer.addSubview(leftTracing)
        leftTracing.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        leftHand = UIImageView()
        leftHand.stringTag = "left_hand"
        leftHand.image = Utilities.SVGImage(named: "circle_ve2tay_left")
        leftContainer.addSubview(leftHand)
        leftHand.snp.makeConstraints { make in
            make.width.height.equalToSuperview().multipliedBy(0.2)
            make.left.top.equalToSuperview()
        }
        
        leftTracing.setListener { [weak self] sender, position, point in
            guard let self = self else { return false }
            self.leftPosition = position
            let canUpdate = self.leftPosition - self.rightPosition < 0.1
            if canUpdate {
                self.leftHand.transform = CGAffineTransform(translationX: point.x - self.leftHand.frame.width / 2, y: point.y - self.leftHand.frame.height / 2)
            }
            self.checkFinish()
            return canUpdate
        }
        
        let rightMainContainer = UIView()
        view.addSubview(rightMainContainer)
        rightMainContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.top.bottom.right.equalToSuperview()
        }
        
        rightContainer = UIView()
        rightContainer.stringTag = "right_container"
        rightContainer.clipsToBounds = false
        rightMainContainer.addSubview(rightContainer)
        rightContainer.makeViewCenterAndKeep(ratio: 1)
        
        rightTracing = TracingVe2TayView()
        rightTracing.stringTag = "right_tracing"
        rightContainer.addSubview(rightTracing)
        rightTracing.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        rightHand = UIImageView()
        rightHand.stringTag = "right_hand"
        rightHand.image = Utilities.SVGImage(named: "circle_ve2tay_right")
        rightContainer.addSubview(rightHand)
        rightHand.snp.makeConstraints { make in
            make.width.height.equalToSuperview().multipliedBy(0.2)
            make.left.top.equalToSuperview()
        }
        
        rightTracing.setListener { [weak self] sender, position, point in
            guard let self = self else { return false }
            self.rightPosition = position
            let canUpdate = self.rightPosition - self.leftPosition < 0.1
            if canUpdate {
                self.rightHand.transform = CGAffineTransform(translationX: point.x - self.rightHand.frame.width / 2, y: point.y - self.rightHand.frame.height / 2)
            }
            self.checkFinish()
            return canUpdate
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(delay: 0, names: [openGameSound(), "\(getLanguage() ?? "vi")/taptrung/ve hai tay"])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        let svg = Utilities.GetSVGKImage(named: "ve2tay/\(Int.random(in: 1...15)).svg")
        self.svg = svg
        guard let path1 = (svg.caLayerTree.sublayers?[0] as? CAShapeLayer)?.path, let path2 = (svg.caLayerTree.sublayers?[1] as? CAShapeLayer)?.path else { return }
        
        let pathMeasure1 = CGPathMeasure(path: path1)
        let pathMeasure2 = CGPathMeasure(path: path2)
        let length1 = pathMeasure1.length
        let length2 = pathMeasure2.length
        
        let numberPointPerSegment = 50
        let numPoints = (numberOfSegments - 1) * numberPointPerSegment
        let scale = leftTracing.frame.height / 400.0
                
        let point1 = svg.caLayerTree.sublayers![0].convert( CGRect.zero, to: svg.caLayerTree)
        let point2 = svg.caLayerTree.sublayers![1].convert( CGRect.zero, to: svg.caLayerTree)
        leftPoints = getPoints(path: UIBezierPath(cgPath: path1))
        rightPoints = getPoints(path: UIBezierPath(cgPath: path2))
        leftPoints = leftPoints.map{CGPoint(x: (point1.minX+$0.x) * scale, y: (point1.minY+$0.y) * scale)}
        rightPoints = rightPoints.map{CGPoint(x: (point2.minX+$0.x - 400.0) * scale, y: (point2.minY+$0.y) * scale)}
                
        leftTracing.setPoints(leftPoints)
        rightTracing.setPoints(rightPoints)
        leftTracing.updatePaintColor(UIColor(hex: "#00C9EA"))
        rightTracing.updatePaintColor(UIColor(hex: "#09D622"))
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage() ?? "vi")/taptrung/ve hai tay")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func checkFinish() {
        if leftPosition > 0.9 && rightPosition > 0.9 && gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            animateCoinIfCorrect(view: coinView)
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    private func getPoints(path: UIBezierPath) -> [CGPoint] {
        var points: [CGPoint] = []
        let count = Int(path.length)
        return path.evenlySpacedPointsUsingDash(count: count)
    }
}


// MARK: - TracingVe2TayView
class TracingVe2TayView: UIView {
    // MARK: - Properties
    private var listener: ((TracingVe2TayView, Float, CGPoint) -> Bool)?
    private let paint2 = Paint()
    private var points: [CGPoint] = []
    private var leftPoints: [CGPoint] = []
    private var paths: [CGPath] = []
    private var newPath: CGMutablePath?
    private var newPathJustCreated: Bool = false
    private var startPoint: CGPoint?
    private var startIndex: Int = 0
    private var currentIndex: Int = 0
    private var wrong: Bool = false
    private var canUpdate: Bool = true
    private var path: CGPath?
    private var leftPath: CGPath?
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        initPaints()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        initPaints()
    }
    
    private func initPaints() {
        backgroundColor = .clear
        paint2.color = UIColor.red
        paint2.strokeWidth = 16
    }
    
    // MARK: - Public Methods
    func setListener(_ listener: @escaping (TracingVe2TayView, Float, CGPoint) -> Bool) {
        self.listener = listener
    }
    
    func updatePaintColor(_ color: UIColor) {
        paint2.color = color
    }
    
    func setPoints(_ newPoints: [CGPoint]) {
        let tenDp = bounds.width / 50
        var path = CGMutablePath()
        points = []
        for (i, point) in newPoints.enumerated() {
            points.append(point)
            if i == 0 {
                path.move(to: point)
            } else {
                path.addLine(to: point)
            }
        }
        self.path = path
        leftPoints = points
        leftPath = path
        updateLeftPath()
        setNeedsDisplay()
    }
    
    // MARK: - Drawing
    override func draw(_ rect: CGRect) {
        guard let context = UIGraphicsGetCurrentContext(), let leftPath = leftPath else { return }
        context.addPath(leftPath)
        context.setStrokeColor(paint2.color.cgColor)
        context.setLineWidth(paint2.strokeWidth)
        context.setLineCap(.round)
        context.setLineJoin(.round)
        context.strokePath()
    }
    
    // MARK: - Touch Handling
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        currentIndex = points.count - leftPoints.count
        let oldCurrentIndex = currentIndex
        if let nearest = nearestPoint(to: point) {
            wrong = false
            startPoint = nearest
            startIndex = points.firstIndex(of: nearest) ?? 0
            if startIndex < currentIndex && currentIndex - startIndex < points.count / 10 {
                // Stay at currentIndex
            } else if startIndex >= currentIndex && startIndex - currentIndex < points.count / 10 {
                currentIndex = startIndex
            }
            let canUpdate = updateLeftPath()
            if canUpdate {
                if startIndex < oldCurrentIndex && oldCurrentIndex - startIndex < points.count / 10 {
                    startIndex = oldCurrentIndex
                    startPoint = points[startIndex]
                } else if startIndex >= oldCurrentIndex && startIndex - oldCurrentIndex < points.count / 10 {
                    newPath = CGMutablePath()
                    for i in oldCurrentIndex...startIndex {
                        leftPoints.removeAll { $0 == points[i] }
                        while i + leftPoints.count - points.count >= 0 {
                            leftPoints.removeFirst()
                        }
                        if i == oldCurrentIndex {
                            newPath?.move(to: points[i])
                        } else {
                            newPath?.addLine(to: points[i])
                        }
                    }
                } else {
                    wrong = true
                }
                setNeedsDisplay()
            }
        } else {
            wrong = true
        }
    }
    
    override func touchesMoved(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        let point = touch.location(in: self)
        currentIndex = points.count - leftPoints.count
        if wrong {
            return
        }
        if let nearest = nearestPoint(to: point) {
            let canUpdate = updateLeftPath()
            if canUpdate {
                if newPath == nil {
                    newPath = CGMutablePath()
                    newPathJustCreated = true
                    startPoint = nearest
                    startIndex = points.firstIndex(of: nearest) ?? 0
                    newPath?.move(to: nearest)
                } else {
                    lineTo2(nearest)
                }
                setNeedsDisplay()
            }
        } else {
            if newPath != nil {
                setNeedsDisplay()
            }
        }
    }
    
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        let longestContinueLeftPoints = findLongestContinueLeftPoints()
        if longestContinueLeftPoints < 20 {
            alpha = 0.5
        }
    }
    
    override func touchesCancelled(_ touches: Set<UITouch>, with event: UIEvent?) {
        touchesEnded(touches, with: event)
    }
    
    // MARK: - Helper Methods
    private func nearestPoint(to touch: CGPoint) -> CGPoint? {
        guard !leftPoints.isEmpty else { return nil }
        var nearest: CGPoint?
        var minDistance = CGFloat.greatestFiniteMagnitude
        let leftIndex = points.firstIndex(of: leftPoints[0]) ?? 0
        let rangeStart = max(0, leftIndex - points.count / 10)
        let rangeEnd = min(leftIndex + points.count / 10, points.count)
        for i in rangeStart..<rangeEnd {
            let leftPoint = points[i]
            let distance = Utilities.distance(from: touch, to: leftPoint)
            if distance < minDistance {
                minDistance = distance
                nearest = leftPoint
            }
        }
        if minDistance > bounds.width / 10 {
            return nil
        }
        return nearest
    }
    
    private func lineTo2(_ point: CGPoint) {
        guard let endIndex = points.firstIndex(of: point) else { return }
        if endIndex == startIndex { return }
        let max = Swift.max(startIndex, endIndex)
        let min = Swift.min(startIndex, endIndex)
        let distance = max - min
        if distance > points.count / 10 || distance < -points.count / 10 {
            newPathJustCreated = true
            return
        }
        if startIndex < endIndex {
            for i in (startIndex + 1)...endIndex {
                newPath?.addLine(to: points[i])
                leftPoints.removeAll { $0 == points[i] }
                while i + leftPoints.count - points.count >= 0 {
                    leftPoints.removeFirst()
                }
            }
        } else {
            for i in (endIndex...startIndex - 1).reversed() {
                newPath?.addLine(to: points[i])
                leftPoints.removeAll { $0 == points[i] }
                while i + leftPoints.count - points.count >= 0 {
                    leftPoints.removeFirst()
                }
            }
        }
        startIndex = endIndex
        newPathJustCreated = false
    }
    
    private func findLongestContinueLeftPoints() -> Int {
        var max = 0
        var count = 0
        for point in points {
            if leftPoints.contains(point) {
                count += 1
            } else {
                max = Swift.max(max, count)
                count = 0
            }
        }
        return Swift.max(max, count)
    }
    
    private func updateLeftPath() -> Bool {
        guard let path = path else { return false }
        let pathMeasure = CGPathMeasure(path: path)
        let length = pathMeasure.length
        let newPath = CGMutablePath()
        for i in currentIndex..<points.count {
            if i == currentIndex {                
                newPath.move(to: points[i])
            } else {
                newPath.addLine(to: points[i])
            }
        }
        //let segment = pathMeasure.getSegment(start: Float(currentIndex) * Float(length) / Float(points.count), end: length, destination: newPath)
        if let listener = listener {
            canUpdate = listener(self, Float(currentIndex) / Float(points.count), points[currentIndex])
            if canUpdate {
                leftPath = newPath
                return true
            }
        }
        return false
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        paint2.strokeWidth = bounds.width / 16
    }
}
