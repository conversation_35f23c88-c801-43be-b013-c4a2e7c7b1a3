//
//  taptrung_list_chiecorucro.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 22/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_chiecorucro: NhanBietGameFragment {
    // MARK: - Properties
    private var gridView: MyGridView!
    private var svgView: SVGImageView!
    private var svgs: [SVGKImage] = []
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F73B60"),
        UIColor.color(hex: "#FFCC29"),
        UIColor.color(hex: "#02BEFF"),
        UIColor.color(hex: "#D43BF4"),
        UIColor.color(hex: "#4FEE1A")
    ]
    private var umbrellas: [[Int]] = []
    private var leftUmbrella: [Int] = []
    private var indexes: [Int] = (0..<4).shuffled()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let leftBg = UIView()
        leftBg.backgroundColor = UIColor.color(hex: "#FFF")
        view.addSubview(leftBg)
        leftBg.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.color(hex: "#D7FBFF")
        gridView.clipsToBounds = false
        view.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit // contentScale=0.7
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        createUmbrellas()
        let svgPaths = ["taptrung_o1", "taptrung_o2"]
        svgs = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        loadData()
    }
    
    private func loadData() {
        if let svg = svgs.first {
            for i in 0..<leftUmbrella.count {
                svg.caLayerTree.sublayers![1 + i].setFillColor(color: colors[leftUmbrella[i]])
            }
            svgView.image = svg.uiImage
        }
        
        var views: [UIView] = []
        for i in 0..<umbrellas.count {
            let view = KUButton()
            view.backgroundColor = .clear
            let svg = svgs[1]
            for j in 0..<umbrellas[indexes[i]].count {
                svg.caLayerTree.sublayers![j].setFillColor(color: colors[umbrellas[indexes[i]][j]])
            }
            view.stringTag = "\(indexes[i])"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "nhanbiet_bg_option_white"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            
            let imageView = SVGImageView(frame: .zero)
            imageView.image = svg.uiImage
            bgContainer.addSubviewWithPercentInset(subview: imageView, percentInset: 0.15)
            views.append(view)
            view.transform = CGAffineTransform(scaleX: 0, y: 0)
            view.alpha = 0
        }
        
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.itemSpacingRatio = 0.05
        gridView.insetRatio = 0.05
        gridView.reloadItemViews(views: views)
        
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), "taptrung/taptrung_o"])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.svgView.transform = CGAffineTransformMakeScale(0.7, 0.7)
            }
            UIView.animate(withDuration: 0.5, delay: 0.5) {
                self.gridView.alpha = 1
            }
        }
        
        delay += 1.0
        delay += gridView.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        svgView.moveToCenter(of: self, duration: 0, completion: {
            [weak self] _ in
            guard let self = self else { return }
            svgView.transform = svgView.transform.concatenating(CGAffineTransformMakeScale(0.7, 0.7))
        })
        gridView.alpha = 0
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay: TimeInterval = playSound(delay: 0, names: ["taptrung/taptrung_o"])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == 0 {
            animateCoinIfCorrect(view: svgView)
            let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func rotate(umbrella: inout [Int]) {
        guard !umbrella.isEmpty else { return }
        let color = umbrella.removeFirst()
        umbrella.append(color)
    }
    
    private func randomRotate(umbrella: inout [Int]) {
        let random = Int.random(in: 0..<10)
        for _ in 0..<random {
            rotate(umbrella: &umbrella)
        }
    }
    
    private func equalLeft(umbrellaA: [Int], umbrellaB: [Int]) -> Bool {
        let size = min(umbrellaA.count, umbrellaB.count)
        for i in 0..<size {
            if umbrellaA[i] != umbrellaB[i] {
                return false
            }
        }
        return true
    }
    
    private func equalRotate(umbrellaA: [Int], umbrellaFull: [Int]) -> Bool {
        var umbrellaC = umbrellaFull
        for _ in 0..<umbrellaC.count {
            rotate(umbrella: &umbrellaC)
            if equalLeft(umbrellaA: umbrellaA, umbrellaB: umbrellaC) {
                return true
            }
        }
        return false
    }
    
    private func createUmbrellas() {
        umbrellas = []
        leftUmbrella = []
        var umbrella: [Int] = []
        for j in 0..<10 {
            var nextValue = Int.random(in: 0..<colors.count)
            while j > 0 && (nextValue == umbrella[j - 1] || (j == 9 && nextValue == umbrella[0])) {
                nextValue = Int.random(in: 0..<colors.count)
            }
            umbrella.append(nextValue)
            if j < 5 {
                leftUmbrella.append(nextValue)
            }
        }
        randomRotate(umbrella: &umbrella)
        umbrellas.append(umbrella)
        
        for _ in 0..<3 {
            var newUmbrella = umbrella
            while true {
                let index = Int.random(in: 0..<newUmbrella.count)
                var newValue: Int
                while true {
                    newValue = Int.random(in: 0..<colors.count)
                    if index == 0 {
                        if newValue != newUmbrella[1] && newValue != newUmbrella[newUmbrella.count - 1] {
                            break
                        }
                    } else if index == newUmbrella.count - 1 {
                        if newValue != newUmbrella[index - 1] && newValue != newUmbrella[0] {
                            break
                        }
                    } else {
                        if newValue != newUmbrella[index - 1] && newValue != newUmbrella[index + 1] {
                            break
                        }
                    }
                }
                newUmbrella[index] = newValue
                if !equalRotate(umbrellaA: leftUmbrella, umbrellaFull: newUmbrella) {
                    umbrellas.append(newUmbrella)
                    break
                }
            }
        }
    }
}
