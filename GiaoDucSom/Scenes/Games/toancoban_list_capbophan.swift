//
//  toancoban_list_capbophan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/3/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_capbophan: NhanBietGameFragment {
    // MARK: - Properties
    private var svgView: UIImageView? // Thay SVGAutosizeView
    private var centerContainer: UIView?
    private var itemContainer: UIView?
    private var positionParts: [UIView] = [UIView(), UIView(), UIView(), UIView(), UIView(), UIView()]
    private var parts: [UIImageView] = []
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var tX: CGFloat = 0
    private var tY: CGFloat = 0


    // MARK: - Setup Layout from XML
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 238/255, green: 227/255, blue: 208/255, alpha: 1.0) // #EEE3D0

        // Item Container
        itemContainer = UIView()
        itemContainer?.isUserInteractionEnabled = true
        addSubview(itemContainer!)
        itemContainer?.makeViewCenterAndKeep(ratio: 2)
        
        // Center Container
        centerContainer = UIView()
        centerContainer?.isUserInteractionEnabled = true
        //centerContainer?.backgroundColor = .green
        itemContainer?.addSubview(centerContainer!)
        centerContainer?.makeViewCenterAndKeep(ratio: 500/498.4)
        
        // SVG View
        svgView = UIImageView() // Placeholder cho SVGAutosizeView
        centerContainer?.addSubview(svgView!)
        svgView?.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Parts
        
        for (index, part) in positionParts.enumerated() {
            part.backgroundColor = .clear
            itemContainer?.addSubview(part)
            part.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.25) // Width 25%
                make.height.equalToSuperview().multipliedBy(0.33) // Height 33%
                //make.centerY.equalToSuperview()

                // Horizontal và vertical bias
                switch index {
                case 0: // part_1
                    make.left.equalToSuperview() // Bias 0
                    make.top.equalToSuperview() // Bias 0
                case 1: // part_2
                    make.left.equalToSuperview() // Bias 0
                    make.centerY.equalToSuperview() // Bias mặc định
                case 2: // part_3
                    make.left.equalToSuperview() // Bias 0
                    make.bottom.equalToSuperview() // Bias 1
                case 3: // part_4
                    make.right.equalToSuperview() // Bias 1
                    make.top.equalToSuperview() // Bias 0
                case 4: // part_5
                    make.right.equalToSuperview() // Bias 1
                    make.centerY.equalToSuperview() // Bias mặc định
                case 5: // part_6
                    make.right.equalToSuperview() // Bias 1
                    make.bottom.equalToSuperview() // Bias 1
                default:
                    break
                }
            }
        }
         

        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true

        // Thêm gesture cho drag-and-drop
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer?.addGestureRecognizer(panGesture)
    }

    // MARK: - GameFragment Methods
    var svgImage : SVGKImage!
    override open func createGame() {
        super.createGame()
        svgImage = Utilities.GetSVGKImage(named: "math_2head")
        var bound = svgImage.caLayerTree.bounds
        var imageParts: [UIImage] = []
        // Load SVG chính cho svgView
        svgGroups = svgImage.caLayerTree.sublayers?.filter { $0.name != nil } ?? []
        var svg = SVG(image: svgImage)
        for groupLayer in svgGroups {
            imageParts.append(svg.clone(pathIndex: svgImage.caLayerTree.sublayers!.firstIndex(of: groupLayer)!).uiImage)
        }
        for groupLayer in svgGroups {
            if let sublayers = groupLayer.sublayers {
                for pathLayer in sublayers {
                    pathLayer.opacity = pathLayer.opacity < 0.1 ? pathLayer.opacity : 0.3
                }
            }
        }
        svgView?.image = svgImage.uiImage
        svgView?.layer.setNeedsDisplay()

        // Load SVG cho các parts
        let shuffledParts = positionParts.shuffled()
        for (index, groupLayer) in svgGroups.enumerated(){
            let partView = UIImageView()
            //partView.backgroundColor = .green.withAlphaComponent(0.01)
            partView.image = imageParts[index]
            partView.tag = index // Gán tag để khớp với group ID
            partView.stringTag = groupLayer.name ?? ""
            centerContainer?.addSubviewWithInset(subview: partView, inset: 0)
            parts.append(partView)
            let box = groupLayer.shapeContentBounds
            let scale =  (centerContainer?.frame.width)! / bound.width
            let tranx = (0 - box!.minX / 2 - box!.maxX / 2) * scale + centerContainer!.frame.width / 2
            let trany = (0 - box!.minY / 2 - box!.maxY / 2) * scale + centerContainer!.frame.height / 2
            shuffledParts[index].transform = CGAffineTransformMakeTranslation(tranx,trany)
            scheduler.schedule(delay: 1 + 0.3 * Double(index)) {
                [weak self] in
                guard let self = self else { return }
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    partView.moveToCenter(of: shuffledParts[index], duration: 0.4)
                }
            }
        }
    }

    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        setupLayout()
    }

    override open func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), getLanguage() + "/toan/toan_2_head")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }

    // MARK: - Event Handling
    var zPosition = 1.0
    @objc private func handlePan(_ sender: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = sender.view else { return }
        let location = sender.location(in: view)
        
        switch sender.state {
        case .began:
            currentView = findViewUnder(x: location.x - view.bounds.width / 4, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.origin.x - sender.location(in: self).x
                dY = currentView.frame.origin.y - sender.location(in: self).y
                tX = currentView.transform.tx
                tY = currentView.transform.ty
                currentView.layer.zPosition = 1
                currentView.alpha = 1.0
                playSound("effect/cungchoi_pick\(random(1, 2))")
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
                zPosition += 1
                currentView.layer.zPosition = zPosition
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = sender.translation(in: self)
                sender.setTranslation(CGPointZero, in: self)
                var transform = currentView.transform
                transform.tx += translation.x
                transform.ty += translation.y
                currentView.transform = transform
            }
            
        case .ended:
            if let currentView = currentView {
                UIImpactFeedbackGenerator(style: .light).impactOccurred()
                let distance = hypot(currentView.transform.tx, currentView.transform.ty)
                if distance < currentView.bounds.height / 5 {
                    pauseGame(stopMusic: false)
                    scheduler.schedule(delay: 0.5) { [weak self] in
                        self?.resumeGame(startMusic: false)
                    }
                    let tag = currentView.tag
                    playSound("effect/word puzzle drop", "topics/Body Parts/\(currentView.stringTag!.replacingOccurrences(of: "1", with: "").replacingOccurrences(of: "2", with: ""))")
                    UIView.animate(withDuration: 0.5, animations: {
                        currentView.transform = .identity
                    }) { [weak self] _ in
                        guard let self = self else { return }
                        if let groupLayer = self.svgGroups[tag].sublayers {
                            for pathLayer in groupLayer {
                                pathLayer.opacity = pathLayer.opacity < 0.1 ? pathLayer.opacity : 1.0
                            }
                        }
                        self.svgView?.image = svgImage.uiImage
                        currentView.isHidden = true
                        self.checkFinish()
                    }
                } else {
                    setGameWrong()
                    UIView.animate(withDuration: 0.5) {
                        currentView.transform = CGAffineTransform(translationX: self.tX, y: self.tY)
                    }
                    playSound("effect/slide2")
                }
            }
            currentView = nil
        default:
            break
        }
    }
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        guard let centerContainer = centerContainer else { return nil }
        for subview in centerContainer.subviews.reversed() where subview != svgView {
            if isPixelVisible(view: subview, x: Int(x), y: Int(y)) {
                return subview
            }
        }
        return nil
    }

    private func isPixelVisible(view: UIView, x: Int, y: Int) -> Bool {
        guard view.isHidden == false else { return false }
        return view.isPixelVisible(x: x, y: y)    
    }

    private func checkFinish() {
        let isFinished = parts.allSatisfy { $0.transform.tx == 0 && $0.transform.ty == 0 || $0.isHidden }
        if isFinished {
            pauseGame()
            animateCoinIfCorrect(view: svgView)
            let delay = playSound(finishEndSounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }

    override open func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(getLanguage() + "/toan/toan_2_head")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // Placeholder cho SVG data
    var svgGroups : [CALayer] = []
    
    private func getGroupCenter(forTag tag: Int) -> CGPoint {
        guard tag >= 0 && tag < svgGroups.count, let groupLayer = svgGroups[tag] as? CALayer else {
            return .zero
        }
        
        // Lấy frame của group layer trong hệ tọa độ của SVG
        let groupFrame = groupLayer.frame
        let centerX = groupFrame.midX
        let centerY = groupFrame.midY
        let width = groupFrame.width
        let height = groupFrame.height
        
        // Trả về trung tâm của group trong hệ tọa độ của svgImage
        return CGPoint(x: centerX, y: centerY)
    }
}

import UIKit

extension CALayer {
    /// Lấy content bounds bao quanh tất cả các path trong layer hoặc sublayer nếu là CAShapeLayer.
    /// Bounds được tính trong hệ tọa độ của layer gốc, có tính đến transform.
    var shapeContentBounds: CGRect? {
        var allBounds: [CGRect] = []
        
        // Kiểm tra layer hiện tại
        if let shapeLayer = self as? CAShapeLayer, let path = shapeLayer.path, !path.isEmpty {
            let bounds = path.boundingBoxOfPath
            // Chuyển bounds sang hệ tọa độ gốc nếu cần
            let transformedBounds = shapeLayer.convert(bounds, to: nil)
            allBounds.append(transformedBounds)
        }
        
        // Kiểm tra sublayer
        if let sublayers = sublayers {
            for sublayer in sublayers where !sublayer.isHidden {
                if let shapeLayer = sublayer as? CAShapeLayer, let path = shapeLayer.path, !path.isEmpty {
                    let bounds = path.boundingBoxOfPath
                    let transformedBounds = shapeLayer.convert(bounds, to: nil)
                    allBounds.append(transformedBounds)
                }
                // Đệ quy (có thể giới hạn độ sâu nếu cần)
                if let subBounds = sublayer.shapeContentBounds {
                    allBounds.append(subBounds)
                }
            }
        }
        
        // Trả về nil nếu không có bounds
        guard !allBounds.isEmpty else { return nil }
        
        // Tính union hiệu quả hơn
        return allBounds.reduce(allBounds[0]) { $0.union($1) }
    }
}

import SVGKit
