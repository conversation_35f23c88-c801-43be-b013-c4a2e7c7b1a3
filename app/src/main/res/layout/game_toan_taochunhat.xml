<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#00f0"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="2"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercent="0.05">

        <view
            android:id="@+id/piece_view"
            class="com.kidsup.giaoducsom.fragment.game.tuduy.XepHaiMiengGhepGameFragment$PieceView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.ResizableRectangleView
            android:id="@+id/rect_view"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_question"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="64"
            android:textColor="#419DEE"
            app:heightRatio="0.7"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.22"
            app:layout_constraintHorizontal_bias="0.03"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.49" />

        <view class="com.kidsup.giaoducsom.fragment.game.tuduy.XepHaiMiengGhepGameFragment$PieceView"
            android:layout_width="0dp"
            android:id="@+id/piece_view_left"
            android:layout_height="0dp"
            android:background="#4f00"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="1"
            android:textColor="#419DEE"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.125"
            app:layout_constraintHorizontal_bias="0.14"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/rect_count_textview"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="4"
            android:textColor="#419DEE"
            app:heightRatio="0.7"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.22"
            app:layout_constraintHorizontal_bias="0.87"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.49" />

        <view class="com.kidsup.giaoducsom.fragment.game.tuduy.XepHaiMiengGhepGameFragment$PieceView"
            android:id="@+id/piece_view_right"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#4f00"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="1"
            android:textColor="#419DEE"
            app:heightRatio="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.125"
            app:layout_constraintHorizontal_bias="0.94"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>