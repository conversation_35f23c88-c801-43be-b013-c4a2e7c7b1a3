//
//  amnhac_list_kyhieutrenphimdan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFoundation

class amnhac_list_kyhieutrenphimdan: MusicGameFragment {
    // MARK: - Properties
    private var svgPhimDanView: SVGKFastImageView!
    private var phimDanSVG: SVGKImage?
    private var btnC1: UIView!
    private var btnD1: UIView!
    private var btnE1: UIView!
    private var btnF1: UIView!
    private var btnG1: UIView!
    private var btnA1: UIView!
    private var btnB1: UIView!
    private var btnC2: UIView!
    private var gridLayout: MyGridView!
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    private var musicStreamIdMap: [String: Bool] = [:]
    private var textColorMap: [String: UIColor] = [:]
    private var bgColorMap: [String: UIColor] = [:]
    private var bg2ColorMap: [String: UIColor] = [:]
    private let notes: [String] = ["c1", "d1", "e1", "f1", "g1", "a1", "b1"]
    private let colors: [UIColor] = [
        UIColor.color(hex: "#F73535"),
        UIColor.color(hex: "#FF8700"),
        UIColor.color(hex: "#FFD400"),
        UIColor.color(hex: "#05BD34"),
        UIColor.color(hex: "#00CFFF"),
        UIColor.color(hex: "#1B7CCC"),
        UIColor.color(hex: "#BF04BB"),
        UIColor.color(hex: "#F73535")
    ]
    private var busy: Bool = false
    private var tappedTime: TimeInterval = 0
    private var currentIndex: Int = 0
    private var newIndex: Int = -1
    private var selectedNotes: [String] = []
    private var currentView: UIView?
    private var currentNote: String?
    private var lastNote: String?
    let innerContainer = UIImageView()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
        
        innerContainer.isUserInteractionEnabled = true
        innerContainer.image = Utilities.SVGImage(named: "music_phimdan3")
        innerContainer.clipsToBounds = false
        itemContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 1840.0 / 1241.0)
        
        
        let image = Utilities.GetSVGKImage(named: "music_phimdan3")
        for i in 0..<image.caLayerTree.sublayers!.count {
            let layer = image.caLayerTree.sublayers![i]
            if (layer.name ?? "").contains("text") {
                layer.opacity  = 0
            }
            if (layer.name ?? "").contains("c2bg") {
                layer.opacity  = 0.15
            }
            if (layer.name ?? "").contains("c2") {
                //layer.opacity  = 0.15
            }
        }
        svgPhimDanView = SVGKFastImageView(svgkImage: image)
        svgPhimDanView.contentMode = .scaleToFill
        svgPhimDanView.stringTag = "svg_phimdan_view"
        innerContainer.addSubview(svgPhimDanView)
        svgPhimDanView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let buttons: [(id: String, bias: CGFloat, width: CGFloat)] = [
            ("c1", 0.041, 0.10),
            ("d1", 0.173, 0.10),
            ("e1", 0.305, 0.10),
            ("f1", 0.435, 0.10),
            ("g1", 0.567, 0.10),
            ("a1", 0.698, 0.10),
            ("b1", 0.83, 0.10),
            ("c2", 0.96, 0.10)
        ]
        
        for (id, bias, width) in buttons {
            let btn = UIView()
            btn.stringTag = id
            btn.backgroundColor = UIColor.black.withAlphaComponent(0.12) // #1f00
            btn.alpha = 0.1
            if id == "c2" { btn.isHidden = true } // visibility="gone"
            innerContainer.addSubview(btn)
            btn.snp.makeConstraints { make in
                make.width.equalTo(innerContainer).multipliedBy(width)
                make.height.equalTo(innerContainer).multipliedBy(0.41)
                make.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                btn.snapToHorizontalBias(horizontalBias: bias)
            }
            btn.isUserInteractionEnabled = true
            
            switch id {
            case "c1": btnC1 = btn
            case "d1": btnD1 = btn
            case "e1": btnE1 = btn
            case "f1": btnF1 = btn
            case "g1": btnG1 = btn
            case "a1": btnA1 = btn
            case "b1": btnB1 = btn
            case "c2": btnC2 = btn
            default: break
            }
        }
        
        let mask = PassthroughImageView()
        mask.image = Utilities.SVGImage(named: "music_phimdan2_mask")
        innerContainer.addSubview(mask)
        mask.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        gridLayout.clipsToBounds = false
        innerContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.96)
            make.height.equalTo(innerContainer).multipliedBy(0.56)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.58) // verticalBias=0.08
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleGridPan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
        
        loadPiano()
    }
    
    func loadPiano() {
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in notes {
                if let url = Utilities.url(soundPath: "effect/music/piano_2\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
        }
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        if touches.count == 1 {
            let touch = touches.first!
            for i in 0..<innerContainer.subviews.count {
                let view = innerContainer.subviews[i]
                if touch.placeInView(view: view) {
                    if let note = view.stringTag as? String, note != "svg_phimdan_view" {
                        pressNote(note: note)
                    }
                }
            }
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        
        let ids: [Int] = [
            R16.drawable.ic_8c1,
            R16.drawable.ic_8d1,
            R16.drawable.ic_8e1,
            R16.drawable.ic_8f1,
            R16.drawable.ic_8g1,
            R16.drawable.ic_8a1,
            R16.drawable.ic_8b1,
            R16.drawable.ic_8c2
        ]
        let allNotes = notes
        selectedNotes = allNotes.shuffled().prefix(2).map { $0 }
        
        var views: [UIView] = []
        for (i, note) in selectedNotes.enumerated() {
            let view = createItemKyHieuPhimDan(note: note, imageRes: ids[notes.firstIndex(of: note) ?? 0])
            view.stringTag = note
            views.append(view)
        }
        
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.columns = 2
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound("music/kyhieutrenphimdan")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("music/kyhieutrenphimdan")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleGridPan(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentNote = nil
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                originX = currentView.frame.minX
                originY = currentView.frame.minY
                currentView.layer.zPosition = CGFloat.greatestFiniteMagnitude
                UIView.animate(withDuration: 0.2) {
                    currentView.transform = CGAffineTransform(scaleX: 0.75, y: 0.75)
                    currentView.alpha = 0.5
                }
                lastNote = nil
                if let note = currentView.stringTag {
                    playSound("topics/Music Notes/\(note.replacingOccurrences(of: "1", with: ""))")
                }
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: view)
                
                let buttons = [btnC1, btnD1, btnE1, btnF1, btnG1, btnA1, btnB1, btnC2]
                var minDistance = CGFloat.greatestFiniteMagnitude
                var closestButton: UIView?
                
                for button in buttons {
                    let vector = currentView.distanceFromCenterToCenter(to: button!)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        closestButton = button
                    }
                }
                
                if let closestButton = closestButton, minDistance < currentView.frame.width / 2, let targetNote = closestButton.stringTag {
                    currentNote = targetNote
                    if busy { return }
                    if targetNote != lastNote {
                        pressNote(note: targetNote)
                        busy = true
                        scheduler.schedule(after: 0.1) { [weak self] in
                            self?.releaseNote(note: targetNote)
                            self?.busy = false
                        }
                        lastNote = targetNote
                    }
                } else {
                    currentNote = nil
                    lastNote = nil
                }
            }
            
        case .ended:
            currentNote = nil
            if let currentView = currentView {
                let buttons = [btnC1, btnD1, btnE1, btnF1, btnG1, btnA1, btnB1, btnC2]
                var minDistance = CGFloat.greatestFiniteMagnitude
                var closestButton: UIView?
                
                for button in buttons {
                    let vector = currentView.distanceFromCenterToCenter(to: button!)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        closestButton = button
                    }
                }
                
                if let closestButton = closestButton, minDistance < currentView.frame.width / 2, let note = currentView.stringTag, let targetNote = closestButton.stringTag {
                    if note == targetNote {
                        UIView.animate(withDuration: 0.2) {
                            currentView.alpha = 0
                            currentView.center = closestButton.center
                        }
                        
                        currentIndex += 1
                        if currentIndex == selectedNotes.count {
                            animateCoinIfCorrect(view: gridLayout)
                            let delay = playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound()])
                            pauseGame()
                            scheduler.schedule(after: delay + 1.0) { [weak self] in
                                self?.finishGame()
                            }
                        } else {
                            playSound("effect/answer_correct")
                        }
                    } else {
                        setGameWrong()
                        playSound("effect/answer_wrong")
                        scheduler.schedule(after: 0.2) { [weak self] in
                            self?.resumeGame()
                        }
                        UIView.animate(withDuration: 0.2) {
                            currentView.transform = .identity
                            currentView.alpha = 1
                            currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                        }
                    }
                } else {
                    UIView.animate(withDuration: 0.2) {
                        currentView.transform = .identity
                        currentView.alpha = 1
                        currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                    }
                }
                self.currentView = nil
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func pressNote(note: String) {
        if let player = noteToMusicId[note] {
            player.currentTime = 0
            player.volume = 1
            player.play()
            musicStreamIdMap[note] = true
        }
        
        // Placeholder for SVG path manipulation
        textColorMap[note] = .color(hex: "#B8C8D3")
        bgColorMap[note] = .color(hex: "#DCE4EA")
        bg2ColorMap[note] = .color(hex: "#DCE4EA")
        svgPhimDanView.setNeedsDisplay()
    }
    
    private func releaseNote(note: String) {
        if currentNote == note {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.releaseNote(note: note)
            }
            return
        }
        
        if let player = noteToMusicId[note] {
            let duration: TimeInterval = 0.2
            let startTime = Date().timeIntervalSince1970
            let endTime = startTime + duration
            
            Timer.scheduledTimer(withTimeInterval: 0.01, repeats: true) { timer in
                let elapsedTime = Date().timeIntervalSince1970 - startTime
                let volume = 1.0 * (1.0 - Float(elapsedTime / duration))
                player.volume = max(0, volume)
                
                if Date().timeIntervalSince1970 >= endTime {
                    player.stop()
                    timer.invalidate()
                }
            }
        }
        
        // Placeholder for SVG path manipulation
        textColorMap.removeValue(forKey: note)
        bgColorMap.removeValue(forKey: note)
        bg2ColorMap.removeValue(forKey: note)
        svgPhimDanView.setNeedsDisplay()
        musicStreamIdMap[note] = false
    }
    
    private func createItemKyHieuPhimDan(note: String, imageRes: Int) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_card1")
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let bgWhite = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_card2"))
        container.addSubview(bgWhite)
        bgWhite.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let innerContainer = UIView()
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height).multipliedBy(1.5) // Ratio 1.5:1
            make.width.equalTo(container).multipliedBy(0.95)
            make.center.equalToSuperview()
        }
        
        let viewImage = UIView()
        viewImage.stringTag = "view_image"
        viewImage.isHidden = true
        innerContainer.addSubview(viewImage)
        viewImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        viewImage.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(viewImage).multipliedBy(0.94)
            make.height.equalTo(viewImage)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg").withRenderingMode(.alwaysTemplate))
        barline.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg").withRenderingMode(.alwaysTemplate))
        endline.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let imageNote = UIImageView(image: Utilities.SVGImage(named: "music/note/\(imageRes.toDrawableNameKHTPD()!).svg").withRenderingMode(.alwaysTemplate))
        imageNote.stringTag = "image_note"
        imageNote.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.width.equalTo(imageNote.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(1.3) // Bias 0.65
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg").withRenderingMode(.alwaysTemplate))
        clef.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(0.3) // Bias 0.15
            make.centerY.equalToSuperview()
        }
        
        let viewText = HeightRatioTextView()
        viewText.setHeightRatio(0.7)
        viewText.stringTag = "view_text"
        viewText.text = note.prefix(1).uppercased()
        viewText.font = .Freude(size: 20)
        viewText.textColor = UIColor.color(hex: "#74B6FF")
        viewText.textAlignment = .center
        viewText.backgroundColor = .clear
        container.addSubview(viewText)
        viewText.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(container).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9) // Bias 0.3
        }
        
        return view
    }
    
    // MARK: - Supporting Structures
    var originX: CGFloat = 0.0, originY: CGFloat = 0.0
    
    struct VisualTree {
        static func moveElement(viewA: UIView, viewB: UIView, duration: TimeInterval, scale: CGFloat = 1, alpha: CGFloat = 1) {
            // Giả lập, cần thay bằng implement thực tế
            UIView.animate(withDuration: duration) {
                viewA.center = viewB.center
                viewA.transform = CGAffineTransform(scaleX: scale, y: scale)
                viewA.alpha = alpha
            }
        }
    }
    
    
}

struct R16 {
    struct drawable {
        static let music_phimdan3 = 1
        static let music_phimdan2_mask = 2
        static let nhanbiet_bg_card1 = 3
        static let nhanbiet_bg_card2 = 4
        static let music_bg = 5
        static let ic_barline = 6
        static let endline = 7
        static let ic_8c1 = 8
        static let ic_8d1 = 9
        static let ic_8e1 = 10
        static let ic_8f1 = 11
        static let ic_8g1 = 12
        static let ic_8a1 = 13
        static let ic_8b1 = 14
        static let ic_8c2 = 15
        static let music_clef = 16
        static let music_kyhieutrenphimdan = 17
        static let music_item_kyhieuphimdan = 18
    }
}

extension Int {
    func toDrawableNameKHTPD() -> String? {
        switch self {
        case R16.drawable.music_phimdan3:
            return "music_phimdan3"
        case R16.drawable.music_phimdan2_mask:
            return "music_phimdan2_mask"
        case R16.drawable.nhanbiet_bg_card1:
            return "nhanbiet_bg_card1"
        case R16.drawable.nhanbiet_bg_card2:
            return "nhanbiet_bg_card2"
        case R16.drawable.music_bg:
            return "music_bg"
        case R16.drawable.ic_barline:
            return "barline"
        case R16.drawable.endline:
            return "endline"
        case R16.drawable.ic_8c1:
            return "8c1"
        case R16.drawable.ic_8d1:
            return "8d1"
        case R16.drawable.ic_8e1:
            return "8e1"
        case R16.drawable.ic_8f1:
            return "8f1"
        case R16.drawable.ic_8g1:
            return "8g1"
        case R16.drawable.ic_8a1:
            return "8a1"
        case R16.drawable.ic_8b1:
            return "8b1"
        case R16.drawable.ic_8c2:
            return "8c2"
        case R16.drawable.music_clef:
            return "music_clef"
        case R16.drawable.music_kyhieutrenphimdan:
            return "music_kyhieutrenphimdan"
        case R16.drawable.music_item_kyhieuphimdan:
            return "music_item_kyhieuphimdan"
        default:
            return "empty"
        }
    }
}


