//
//  BaseViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/3/25.
//
import UIKit

class BaseViewController : UIViewController {
    var backgroundMusicFile: String? {
        nil
    }
    let scheduler = Scheduler()
    func playSound(_ names: [String]) -> TimeInterval {
        playSound(delay: 0, names: names)
    }
    
    public func playSound(_ names: String...) -> TimeInterval {
        playSound(delay: 0, names: names)
    }

    func playSound(delay: TimeInterval, names: [String]) -> TimeInterval {
        var d = delay
        for name in names where !name.isEmpty {
            d += playSound(name: name, delay: d)
        }
        return d - delay
    }

    func playSound(name: String, delay: TimeInterval) -> TimeInterval {
        /*
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            let soundKey = SoundManager.shared.play(name)
            self.soundPlayerKeyList.append(soundKey)
        }
        let duration = soundDuration(name: name)
        #if DEBUG
        return duration == 0 ? 2 : duration
        #else
        return duration
        #endif
         */
        scheduler.schedule(delay: delay) { [weak self] in
            guard self != nil else { return }
            QueueSoundPlayer.shared.play(sound: name, delay: 0)
        }
        return QueueSoundPlayer.shared.calcDuration(sound: name)
    }

    func soundDuration(name: String) -> TimeInterval {
        let filepath = "vi/\(name).mp3" // Giả định ngôn ngữ mặc định là "vi"
        var duration = QueueSoundPlayer.shared.play(sound: filepath, delay: 0, play: false)
        if duration <= 0 {
            duration =  QueueSoundPlayer.shared.play(sound: "\(name).mp3", delay: 0, play: false)
        }
        return TimeInterval(duration / 1000) // Chuyển từ ms sang seconds
    }
    deinit {
        scheduler.clearAll()
        NotificationCenter.default.removeObserver(self)
    }
    // MARK: - Check Timeout Methods
    func checkTimeoutAndRun(_ action: @escaping () -> Void) {
        checkTimeoutAndRun(action, cancelAction: nil)
    }

    func checkTimeoutAndRun(_ action: @escaping () -> Void, cancelAction: (() -> Void)?) {
        TimerGateHelper.run(action, cancelAction: cancelAction)
    }
    func checkPlayBackgroundMusic() {
        if enableBackgroundMusic {
            playBackgroundMusic()
        } else {
            stopBackgroundMusic()
        }
    }
    
    private func playBackgroundMusic() {
        guard let backgroundMusicFile = backgroundMusicFile else { return }
        
        SoundHelper.shared.playBackgroundMusic(sound: backgroundMusicFile)
    }
    
    private func stopBackgroundMusic() {
        guard backgroundMusicFile != nil else { return }
        
        SoundHelper.shared.stopBackgroundMusic()
    }
    var enableBackgroundMusic: Bool {
        AppSettings.musicOn        
    }
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        hideNavigationBar()
        ActivityTracker.setCurrentViewController(self)
        startUpdateTimeRunnable()
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        if ActivityTracker.getCurrentViewController() === self {
            ActivityTracker.setCurrentViewController(nil)
        }
        stopUpdateTimeRunnable()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        checkPlayBackgroundMusic()
    }
    
    override func viewWillDisappear(_ animated: Bool) {
        super.viewWillDisappear(animated)
        
        stopBackgroundMusic()
    }
    // Biến để lưu timer
    private var updateTimeTimer: Timer?

    // Hàm khởi tạo và chạy timer
    private func startUpdateTimeRunnable() {
        // Dừng timer cũ nếu có
        updateTimeTimer?.invalidate()
        
        // Tạo timer cập nhật mỗi 0.033 giây (33ms)
        updateTimeTimer = Timer.scheduledTimer(withTimeInterval: 0.033, repeats: true) { [weak self] _ in
            guard let self = self else { return }
            
            // Cập nhật UI
            if let topRightView = self.topRightView {
                topRightView.text = StopwatchUtils.getManager().getElapsedFormattedTime()
                //topRightView.isHidden = true // Tương đương setVisibility(View.INVISIBLE)
            }
        }
    }

    // Dừng timer khi không cần nữa
    private func stopUpdateTimeRunnable() {
        updateTimeTimer?.invalidate()
        updateTimeTimer = nil
    }
    
    var topRightView: UILabel?
    
    private func setupTopRightView() {
        topRightView = UILabel()
        topRightView?.textColor = .red
        view.addSubview(topRightView!)
        topRightView?.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(10)
            make.right.equalToSuperview().offset(-10)
        }
    }
    override func viewDidLoad() {
        super.viewDidLoad()
        scheduler.schedule(delay: 2) {
            [weak self] in
            guard let self = self else { return }
            self.setupTopRightView()
        }
    }
    public func runPremiumFeatures(_ action: @escaping () -> Void, _ isFreeDuringTrial: Bool) {
        // Giả định logic kiểm tra premium features
        // Trong thực tế, bạn cần triển khai logic này
        if isFreeDuringTrial || PaymentHelper.isFullVersion() {
            action()
        } else {
            // Hiển thị thông báo hoặc màn hình mua premium
            print("Premium feature required")
        }
    }
    func getAppModuleName() -> String? {
        return Bundle.main.infoDictionary?["CFBundleName"] as? String
    }
    
    func inTrialTime() -> Bool {
        return installedTimeInDays() < 2
    }
    
    func installedTimeInDays() -> Int {
        let appStartTime = AppSettings.appStartTime ?? 0
        let currentTime = Date().timeIntervalSince1970
        let diff = currentTime - appStartTime
        return Int(diff / (60.0 * 60.0 * 24.0))
    }
    func checkGameUnlocked(gameId: String) -> Bool {
        let gameLevel = GameLevelHelper.getGameLevelNumberByThumb(thumbnail: gameId) ?? 0
        let profileLevel = DataManager.shared.profileLevel()
        let className = GameLevelHelper.getGameClassNameByThumb(thumbnail: gameId)!
        return profileLevel >= gameLevel || GameUnlockUtils.shared.getGameLevelUp(className: className) > 0
    }
    func checkAgeAndOpenGame(gameId: String, action: @escaping () -> Void) {
        if !checkGameUnlocked(gameId: gameId) {
            playSound("vi/popup_hardgame")
            let dialog = MessageDialogView()
                .setTitle("Câu hỏi khó")
                .setMessage("Đây là câu hỏi khó! Bé vẫn muốn thử sức chứ?")
                .setShowLaterButton(true)
                .setButtonLaterText("Thử")
                .setButtonOkText("Đóng")
                .setImageResId("icon_hard")
                .setListener { [weak self] buttonIndex in
                    guard let self = self else { return }
                    if buttonIndex == .ok {
                        self.scheduler.schedule(delay: 0.8) { [weak self] in
                            guard self != nil else { return }
                            action()
                        }
                    }
                }
            dialog.showIn(view)
            return
        }
        action()
    }
}


extension BaseViewController {
    static func random(_ numbers: Int...) -> Int {
        let index = Int.random(in: 0..<numbers.count)
        return numbers[index]
    }
    static func random(_ texts: String...) -> String {
        let index = Int.random(in: 0..<texts.count)
        return texts[index]
    }
}

import UIKit

extension UIViewController {
    
    // Store the active text field using associated objects
    private var activeTextField: UITextField? {
        get {
            objc_getAssociatedObject(self, &AssociatedKeys.activeTextFieldKey) as? UITextField
        }
        set {
            objc_setAssociatedObject(self, &AssociatedKeys.activeTextFieldKey, newValue, .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
        }
    }
    
    // Define a unique key for associated object
    private struct AssociatedKeys {
        static var activeTextFieldKey: UInt8 = 0
    }
    
    // Setup keyboard observers and tap gesture to dismiss
    func setupMoveWhenShowHideKeyboard() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow(_:)),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide(_:)),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
        
        // Add tap gesture to dismiss keyboard
        let tap = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard2))
        tap.cancelsTouchesInView = false
        view.addGestureRecognizer(tap)
        
        // Track active text field
        setupTextFieldObservers()
    }
    
    // Observe text field focus
    private func setupTextFieldObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textFieldDidBeginEditingx(_:)),
            name: UITextField.textDidBeginEditingNotification,
            object: nil
        )
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(textFieldDidEndEditingx(_:)),
            name: UITextField.textDidEndEditingNotification,
            object: nil
        )
    }
    
    @objc private func textFieldDidBeginEditingx(_ notification: Notification) {
        activeTextField = notification.object as? UITextField
    }
    
    @objc private func textFieldDidEndEditingx(_ notification: Notification) {
        activeTextField = nil
    }
    
    @objc func keyboardWillShow(_ notification: Notification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
              let animationDuration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double,
              let activeTextField = activeTextField else { return }
        
        let keyboardHeight = keyboardFrame.height
        let textFieldFrame = activeTextField.convert(activeTextField.bounds, to: view)
        let textFieldBottom = textFieldFrame.maxY
        
        // Calculate the visible area above the keyboard
        let visibleAreaBottom = view.frame.height - keyboardHeight - view.safeAreaInsets.bottom
        
        // If the text field is below the visible area, move the view up
        if textFieldBottom > visibleAreaBottom {
            let offset = textFieldBottom - visibleAreaBottom + 20 // Add padding for better visibility
            
            // If using a scroll view, adjust content inset instead
            if let scrollView = view as? UIScrollView {
                var contentInset = scrollView.contentInset
                contentInset.bottom = keyboardHeight
                scrollView.contentInset = contentInset
                scrollView.scrollIndicatorInsets = contentInset
                scrollView.scrollRectToVisible(activeTextField.frame, animated: true)
            } else {
                // Move the view up with animation
                UIView.animate(withDuration: animationDuration) {
                    self.view.frame.origin.y = -offset
                }
            }
        }
    }
    
    @objc func keyboardWillHide(_ notification: Notification) {
        guard let animationDuration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double else { return }
        
        // Reset view or scroll view
        if let scrollView = view as? UIScrollView {
            var contentInset = scrollView.contentInset
            contentInset.bottom = 0
            scrollView.contentInset = contentInset
            scrollView.scrollIndicatorInsets = contentInset
        } else {
            UIView.animate(withDuration: animationDuration) {
                self.view.frame.origin.y = 0
            }
        }
    }
    
    @objc func dismissKeyboard2() {
        view.endEditing(true)
    }
}
