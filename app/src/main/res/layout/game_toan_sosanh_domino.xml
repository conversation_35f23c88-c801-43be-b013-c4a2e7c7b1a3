<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#C5FFE0"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1.9"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="4."
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/domino_view_left"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1.3"
                    app:layout_constraintHorizontal_bias="0"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <include layout="@layout/item_domino" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/view_compare"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/math_sosanhqua"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text=""
                        android:textColor="#FF7760"
                        app:autoSizeMaxTextSize="500dp"
                        app:autoSizeTextType="uniform"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="1.15"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/domino_view_right"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="#0f00"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1.3"
                    app:layout_constraintHorizontal_bias="1"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <include layout="@layout/item_domino" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:clipChildren="false"
                android:clipToPadding="false"
                android:splitMotionEvents="false"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="4"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/button_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/nhanbiet_bg_option_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/button_2"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="&gt;"
                        android:textColor="#87D657"
                        app:autoSizeMaxTextSize="500dp"
                        app:autoSizeTextType="uniform"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="1.15"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/button_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/nhanbiet_bg_option_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toStartOf="@+id/button_3"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/button_1"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="&lt;"
                        android:textColor="#FF7760"
                        app:autoSizeMaxTextSize="500dp"
                        app:autoSizeTextType="uniform"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="1.15"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1" />
                </androidx.constraintlayout.widget.ConstraintLayout>

                <androidx.constraintlayout.widget.ConstraintLayout
                    android:id="@+id/button_3"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:background="@drawable/nhanbiet_bg_option_white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintDimensionRatio="1"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintHorizontal_bias="0.5"
                    app:layout_constraintStart_toEndOf="@+id/button_2"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="0dp"
                        android:fontFamily="@font/svn_freude"
                        android:gravity="center"
                        android:text="="
                        android:textColor="#74B6FF"
                        app:autoSizeMaxTextSize="500dp"
                        app:autoSizeTextType="uniform"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintHeight_percent="1.15"
                        app:layout_constraintLeft_toLeftOf="parent"
                        app:layout_constraintRight_toRightOf="parent"
                        app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintVertical_bias="1" />
                </androidx.constraintlayout.widget.ConstraintLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>


    </androidx.constraintlayout.widget.ConstraintLayout>

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>