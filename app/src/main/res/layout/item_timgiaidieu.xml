<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#0f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="4.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/bg_1"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:src="@drawable/music_timgiaidieu_btn1"
            app:layout_constraintLeft_toLeftOf="parent" />

        <ImageView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleType="fitXY"
            android:src="@drawable/music_timgiaidieu_btn2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toRightOf="@id/bg_1"
            app:layout_constraintRight_toLeftOf="@id/bg_3"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/bg_3"
            android:layout_width="wrap_content"
            android:layout_height="match_parent"
            android:adjustViewBounds="true"
            android:src="@drawable/music_timgiaidieu_btn3"
            app:layout_constraintRight_toRightOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/music_note_container"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_weight="4"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="7"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:scaleType="fitXY"
            android:src="@drawable/ic_music_bg2" />

        <ImageView
            android:id="@+id/clef"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/ic_music_clef"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63:130"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="#74B6FF" />

        <ImageView
            android:id="@+id/beat"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63:130"
            app:layout_constraintHorizontal_bias="0.06"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="#74B6FF" />

        <ImageView
            android:id="@+id/beat_image"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="21:130"
            app:layout_constraintLeft_toRightOf="@id/clef"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="#74B6FF" />

        <ImageView
            android:id="@+id/sample_note"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/ic_music_e"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="48:130"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/left_line"
            android:layout_width="100dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.33" />

        <androidx.constraintlayout.widget.Guideline
            android:id="@+id/right_line"
            android:layout_width="100dp"
            android:layout_height="0dp"
            android:orientation="vertical"
            app:layout_constraintGuide_percent="0.5" />

        <View
            android:id="@+id/highlight_bg"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#552EAFFF"
            android:scaleY="5"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/right_line"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="70dp"
            android:fontFamily="@font/utm_avo_bold"
            android:gravity="center"
            app:layout_constraintBottom_toTopOf="@id/highlight_bg"
            app:layout_constraintLeft_toLeftOf="@id/highlight_bg"
            app:layout_constraintRight_toRightOf="@id/highlight_bg">

            <TextView
                android:id="@+id/text_note"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/utm_avo_bold"
                android:gravity="center"
                android:scaleX="1.5"
                android:scaleY="1.5"
                android:text="A"
                android:textColor="#f00"
                app:autoSizeTextType="uniform"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <FrameLayout
            android:id="@+id/block_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="92:130"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/right_line"
            app:layout_constraintTop_toTopOf="parent" />

        <FrameLayout
            android:id="@+id/notes_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="50:130"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/right_line"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/wrong_note"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="@id/notes_container"
            app:layout_constraintLeft_toLeftOf="@id/notes_container"
            app:layout_constraintRight_toRightOf="@id/notes_container"
            app:layout_constraintTop_toTopOf="@id/notes_container" />
    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>