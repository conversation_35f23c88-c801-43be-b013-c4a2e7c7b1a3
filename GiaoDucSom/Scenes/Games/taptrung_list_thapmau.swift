//
//  taptrung_list_thapmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 22/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class taptrung_list_thapmau: NhanBietGameFragment {
    // MARK: - Properties
    private let colors: [UIColor] = [
        UIColor.color(hex: "#4FEE1A"),
        UIColor.color(hex: "#02BEFF"),
        UIColor.color(hex: "#D43BF4"),
        UIColor.color(hex: "#F73B60")
    ]
    private var gridView: MyGridView!
    private var leftContainer: UIView!
    private var indexes: [Int] = (0..<4).shuffled()
    private var svgs: [SVGKImage] = []
    private var leftFromTop: Bool = false
    private var leftItem: Item!
    private var items: [Item] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.color(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        leftContainer = UIView()
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
        
        gridView = MyGridView()
        gridView.backgroundColor = UIColor.color(hex: "#D7FBFF")
        gridView.clipsToBounds = false
        view.addSubview(gridView)
        gridView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.5)
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        leftContainer.transform = .identity
        gridView.alpha = 0
        
        let svgPaths = [
            "taptrung_hinhchieu1",
            "taptrung_hinhchieu2",
            "taptrung_hinhchieu2_1",
            "taptrung_hinhchieu2_2",
            "taptrung_hinhchieu2_3",
            "taptrung_hinhchieu2_4"
        ]
        svgs = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        loadData()
    }
    
    private func loadData() {
        leftFromTop = Bool.random()
        leftItem = Item()
        for _ in 0..<colors.count {
            leftItem.discs.append(Disc())
        }
        leftItem = leftItem.newWithRandomColorAndSize()
        items = [leftItem]
        
        while true {
            let randomSize = leftItem.newWithRandomSize()
            if !randomSize.checkSameSeeTopBottom(other: leftItem) {
                items.append(randomSize)
                break
            }
        }
        
        while true {
            let randomColor = leftItem.newWithRandomColor()
            if !randomColor.checkSameSeeTopBottom(other: leftItem) {
                items.append(randomColor)
                break
            }
        }
        
        while true {
            let randomColorAndSize = leftItem.newWithRandomColorAndSize()
            if !randomColorAndSize.checkSameSeeTopBottom(other: leftItem) {
                items.append(randomColorAndSize)
                break
            }
        }
        
        let leftView = createItemView(isTop: leftFromTop)
        updateData(view: leftView, item: items[0])
        leftContainer.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
        }
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = KUButton()
            view.backgroundColor = .clear
            let itemView = createItemView(isTop: !leftFromTop)
            updateData(view: itemView, item: items[indexes[i]])
            view.addSubview(itemView)
            itemView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            view.stringTag = "\(indexes[i])"
            view.addTarget(self, action: #selector(itemTapped(_:)), for: .touchUpInside)
            let bgContainer = SVGImageView(frame: .zero)
            bgContainer.SVGName = "nhanbiet_bg_option_white"
            view.addSubview(bgContainer)
            bgContainer.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            bgContainer.addSubviewWithPercentInset(subview: itemView, percentInset: 0.15)
            views.append(view)
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
        }
        
        gridView.columns = 2
        gridView.itemRatio = 1
        gridView.itemSpacingRatio = 0.05
        gridView.insetRatio = 0.05
        gridView.reloadItemViews(views: views)
        
        let soundName = "taptrung/taptrung_hinh chieu\(leftFromTop ? "2" : "1")"
        var delay: TimeInterval = playSound(delay: 0, names: [openGameSound(), soundName])
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.leftContainer.transform = .identity
            }
            UIView.animate(withDuration: 0.5, delay: 0.5) {
                self.gridView.alpha = 1
            }
        }
        
        delay += 1.0
        delay += gridView.showItems(startDelay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let soundName = "taptrung/taptrung_hinh chieu\(leftFromTop ? "2" : "1")"
            let delay: TimeInterval = playSound(soundName)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func itemTapped(_ sender: UIView) {
        guard let index = Int(sender.stringTag ?? "0") else { return }
        pauseGame(stopMusic: false)
        if index == 0 {
            animateCoinIfCorrect(view: sender.subviews.first!)
            let delay: TimeInterval = playSound(delay: 0, names: ["effect/answer_correct1", getCorrectHumanSound(), endGameSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(delay: 0, names: ["effect/answer_wrong", getWrongHumanSound()])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createItemView(isTop: Bool) -> UIView {
        let view = UIView()
        view.backgroundColor = .clear
        
        let bgContainer = SVGImageView(frame: .zero)
        bgContainer.SVGName = "nhanbiet_bg_option_white"
        view.addSubview(bgContainer)
        bgContainer.makeViewCenterAndKeep(ratio: 1)
        
        if isTop {
            let svgView = UIImageView()
            svgView.contentMode = .scaleAspectFit
            bgContainer.addSubviewWithPercentInset(subview: svgView, percentInset: 0.1)
            svgView.stringTag = "svg_view"
            svgView.image = svgs[0].uiImage
        } else {
            let paddingView = UIView()
            bgContainer.addSubviewWithPercentInset(subview: paddingView, percentInset: 0.1)
            let stackContainer = SVGImageView(frame: .zero)
            stackContainer.SVGName = "taptrung_hinhchieu2"
            paddingView.addSubview(stackContainer)
            stackContainer.makeViewCenterAndKeep(ratio: 374.3/239.7)
            
            let verticalBiases: [CGFloat] = [0.151, 0.363, 0.574, 0.786]
            for i in 0..<4 {
                let svgView = UIImageView()
                //svgView.backgroundColor = .red.withAlphaComponent(0.4)
                svgView.contentMode = .scaleAspectFit
                //svgView.SVGName = "taptrung_hinhchieu2_3"
                stackContainer.addSubview(svgView)
                svgView.snp.makeConstraints { make in
                    make.height.equalTo(svgView.snp.width).multipliedBy(41.7 / 374.3)
                    make.left.right.equalToSuperview()
                    //make.centerY.equalToSuperview().multipliedBy((1 + verticalBiases[i]) / 2)
                }
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    svgView.snapToVerticalBias(verticalBias: verticalBiases[i])
                }
                svgView.stringTag = "svg_view_\(i)"
            }
        }
        
        return view
    }
    
    private func updateData(view: UIView, item: Item) {
        print("\(item.visibleDiscs().count)")
        for i in 0..<item.discs.count {
            print("\(i)=\(item.discs[i].size):\(item.discs[i].color)")
        }
        let svgViews = view.findSubviews(ofType: UIImageView.self).filter { $0.stringTag?.contains("svg_view") ?? false }
        if svgViews.count == 1 {
            // View from top
            let discs = item.visibleDiscs()
            let svg = svgs[0]
            for i in 0..<item.discs.count {
                var foundPath = false
                for j in 0..<discs.count {
                    if discs[j].size == i {
                        foundPath = true
                        svg.caLayerTree.sublayers![4-i].opacity = 1
                        svg.caLayerTree.sublayers![4-i].sublayers![1].setFillColor(color: colors[discs[j].color])
                        break
                    }
                }
                if !foundPath {
                    svg.caLayerTree.sublayers![4-i].opacity = 0
                }
            }
            //svg.fillColor(color: .red, opacity: 1)
            svgViews[0].image = svg.uiImage
        
        } else if svgViews.count == 4 {
            // View as stack
            for i in 0..<item.discs.count {
                let svg = svgs[item.discs[i].size + 2]
                svg.caLayerTree.sublayers![0].sublayers![0].setFillColor(color: colors[item.discs[i].color])
                //svg.fillColor(color: .red, opacity: 1)
                svgViews[i].image = svg.uiImage
            }
        }
    }
    
    // MARK: - Model Classes
    class Disc {
        var size: Int = 0
        var color: Int = 0
    }
    
    class Item {
        var discs: [Disc] = []
        
        func visibleDiscs() -> [Disc] {
            var visibleDiscs: [Disc] = [discs[0]]
            var currentSize = discs[0].size
            for i in 1..<discs.count {
                let disc = discs[i]
                if disc.size > currentSize {
                    visibleDiscs.append(disc)
                    currentSize = disc.size
                }
            }
            return visibleDiscs
        }
        
        func checkSameSeeTopBottom(other: Item) -> Bool {
            let discs1 = visibleDiscs()
            let discs2 = other.visibleDiscs()
            if discs1.count != discs2.count {
                return false
            }
            for i in 0..<discs1.count {
                if discs1[i].size != discs2[i].size || discs1[i].color != discs2[i].color {
                    return false
                }
            }
            return true
        }
        
        func newWithRandomColor() -> Item {
            let item = Item()
            let colors = (0..<discs.count).shuffled()
            for i in 0..<discs.count {
                let disc = discs[i]
                let newDisc = Disc()
                newDisc.size = disc.size
                newDisc.color = colors[i]
                item.discs.append(newDisc)
            }
            return item
        }
        
        func newWithRandomSize() -> Item {
            let item = Item()
            var sizes: [Int] = []
            while true {
                sizes = (0..<discs.count).shuffled()
                if sizes[0] != discs.count - 1 {
                    break
                }
            }
            for i in 0..<discs.count {
                let disc = discs[i]
                let newDisc = Disc()
                newDisc.size = sizes[i]
                newDisc.color = disc.color
                item.discs.append(newDisc)
            }
            return item
        }
        
        func newWithRandomColorAndSize() -> Item {
            let item = Item()
            var sizes: [Int] = []
            while true {
                sizes = (0..<discs.count).shuffled()
                if sizes[0] != discs.count - 1 {
                    break
                }
            }
            let colors = (0..<discs.count).shuffled()
            for i in 0..<discs.count {
                let disc = discs[i]
                let newDisc = Disc()
                newDisc.size = sizes[i]
                newDisc.color = colors[i]
                item.discs.append(newDisc)
            }
            return item
        }
    }
}
