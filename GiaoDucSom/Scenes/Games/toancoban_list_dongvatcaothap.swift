//
//  toancoban_list_dongvatcaothap.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/4/25.
//

import UIKit
import SnapKit
import SVGKit

class toancoban_list_dongvatcaothap: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var items: [Item] = []
    private var folders: [String] = []
    private var svgPaths: [String] = []
    private var selectedItems: [Item] = []
    private var isTwoItems: Bool = false
    private var isHigh: Bool = false
    private var meIndex: Int = 0
    private var high: Int = 0
    private var most: Int = 0
    var stickerSizes: [MapData]!
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 197/255, green: 247/255, blue: 255/255, alpha: 1) // #C5F7FF
        
        let groundView = UIView()
        groundView.backgroundColor = UIColor(red: 159/255, green: 187/255, blue: 68/255, alpha: 1) // #9FBB44
        addSubviewWithPercentInset(subview: groundView, percentInset: 0)
        groundView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.15)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        itemContainer = UIView()
        addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
        itemContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.75)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.itemContainer.snapToVerticalBias(verticalBias: 0.5)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        guard let url = Bundle.main.url(forResource: "sticker_size", withExtension: "json") else {
            print("Error: Could not find sticker_size in the bundle")
            return
        }
        
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            let stickerSizes = try decoder.decode([MapData].self, from: data)
            self.stickerSizes = stickerSizes
            print("Successfully decoded \(stickerSizes.count) folders")
        } catch {
            print("Error decoding JSON: \(error)")
            return
        }
        
        let packs = FlashcardsManager.shared.getPacks()
        
        for pack in packs where pack.folder == "Farm Animals" || pack.folder == "Wild Animals" {
            for var item in pack.items {
                if item.name.en == nil {
                    continue
                }
                let id = item.name.en!.lowercased().replacingOccurrences(of: " ", with: "_")
                for stickerMap in stickerSizes {
                    if let mapItem = stickerMap.Items.first(where: { $0.Id == id }) {
                        item.height = Int(mapItem.H)
                        break
                    }
                }
                if let height = item.height, height > 0 {
                    items.append(item)
                    folders.append(pack.folder)
                }
            }
            break
        }
        
        let size = random(2, 3)
        isHigh = Bool.random()
        isTwoItems = size == 2
        
        while true {
            selectedItems = items.shuffled().prefix(size).map { $0 }
            let minHeight = selectedItems.min(by: { $0.height! < $1.height! })!.height!
            let maxHeight = selectedItems.max(by: { $0.height! < $1.height! })!.height!
            if Float(maxHeight) / Float(minHeight) > 3 {
                continue
            }
            var dataOK = true
            for _ in 0..<100 {
                let take = selectedItems.shuffled().prefix(2).map { $0 }
                let min = take.min(by: { $0.height! < $1.height! })!.height!
                let max = take.max(by: { $0.height! < $1.height! })!.height!
                if Float(max) / Float(min) < 1.3 {
                    dataOK = false
                    break
                }
            }
            if !dataOK {
                continue
            }
            svgPaths = selectedItems.enumerated().map { "topics/\(folders[$0.offset])/\($0.element.path!)" }
            break
        }
        
        high = isHigh ? 1 : 2
        most = isTwoItems ? 2 : 0
        let maxmin = isHigh ? selectedItems.max(by: { $0.height! < $1.height! })!.height! : selectedItems.min(by: { $0.height! < $1.height! })!.height!
        let meItem = selectedItems.first { $0.height == maxmin }!
        meIndex = selectedItems.firstIndex(of: meItem)!
        
        let delay = playSound(openGameSound(), "toan/toan_con cao thap\(high + most)")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_con cao thap\(high + most)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        let svgImages = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        let owidths = svgImages.map { $0.size.width }
        let oheights = svgImages.map { $0.size.height }
        let trimSVGs = svgImages.map{ $0.trimmedToContent()!}
        let widths2 = trimSVGs.map { $0.size.width }
        let heights2 = trimSVGs.map { $0.size.height }
        var widths = trimSVGs.map { $0.size.width }
        var heights = trimSVGs.map { $0.size.height }
        
        for i in 0..<widths.count {
            let newHeight = Float(heights[0]) * Float(selectedItems[i].height!) / Float(selectedItems[0].height!)
            let newWidth = widths[i] * CGFloat(newHeight) / heights[i]
            heights[i] = CGFloat(newHeight)
            widths[i] = newWidth
        }
        
        let totalSVGWidth = widths.reduce(0, +)
        let width = Float(itemContainer.frame.width)
        let height = Float(itemContainer.frame.height)
        let paddingRatio: Float = 0.05
        let totalWidth = width - paddingRatio * width * Float(selectedItems.count + 1)
        let ratio = CGFloat(totalWidth) / totalSVGWidth
        let renderWidths = widths.map { $0 * ratio }
        let renderHeights = heights.map { $0 * ratio }
        
        let maxRenderHeight = renderHeights.max()!
        let totalScale = maxRenderHeight > CGFloat(height) ? CGFloat(height) / maxRenderHeight : 1.0
        let finalWidths = renderWidths.map { $0 * totalScale }
        let finalHeights = renderHeights.map { $0 * totalScale }
        
        let totalFinalWidth = finalWidths.reduce(0, +)
        let padding = (width - Float(totalFinalWidth)) / Float(selectedItems.count + 1)
        var currentPadding = padding
        
        for i in 0..<trimSVGs.count {
            let svgView = UIImageView()
            //svgView.backgroundColor = .red.withAlphaComponent(0.3)
            svgView.contentMode = .scaleAspectFit
            svgView.tag = i
            self.itemContainer.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.width.equalTo(finalWidths[i])
                make.height.equalTo(finalHeights[i])
                make.left.equalToSuperview().offset(currentPadding)
                make.bottom.equalToSuperview()
            }
            
            let tap = UITapGestureRecognizer(target: self, action: #selector(self.onImageTapped(_:)))
            svgView.addGestureRecognizer(tap)
            svgView.isUserInteractionEnabled = true
            let imageView = UIImageView(image: trimSVGs[i].uiImage)
            svgView.addSubviewWithInset(subview: imageView, inset: 0)
            let scaleX = owidths[i]/widths2[i]
            let scaleY = oheights[i]/heights2[i]
            imageView.transform = CGAffineTransformMakeScale(scaleX, scaleY)
            currentPadding += Float(finalWidths[i]) + padding
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onImageTapped(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UIImageView else { return }
        pauseGame()
        let correct = view.tag == meIndex
        
        if correct {
            animateCoinIfCorrect(view: view)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - Supporting Structures
extension Item: Equatable {
    static func ==(lhs: Item, rhs: Item) -> Bool {
        return lhs.path == rhs.path && lhs.height == rhs.height
    }
}


struct MapData: Codable {
    let Map: String
    let Items: [MapItem]
    let H: Float
    let W: Float
}

struct MapItem: Codable {
    let Id: String
    let H: Float
    let W: Float
}
