//
//  tuduy_list_xauhatmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_xauhatmau: NhanBietGameFragment {
    // MARK: - Properties
    private var answerList: [[Int]] = []
    private var items = Array(repeating: 0, count: 24)
    private var svgView: UIImageView!
    private var gridLayout: MyGridView!
    let rightBackground = UIView()
    private var rightItems: [Int] = []
    private var colors = [
        UIColor(red: 255/255, green: 0/255, blue: 0/255, alpha: 1), // #FF0000
        UIColor(red: 0/255, green: 255/255, blue: 0/255, alpha: 1), // #00FF00
        UIColor(red: 0/255, green: 0/255, blue: 255/255, alpha: 1), // #0000FF
        UIColor(red: 255/255, green: 255/255, blue: 0/255, alpha: 1), // #FFFF00
        UIColor(red: 0/255, green: 255/255, blue: 255/255, alpha: 1), // #00FFFF
        UIColor(red: 255/255, green: 0/255, blue: 255/255, alpha: 1) // #FF00FF
    ]
    private var pathMap: [Int: CALayer] = [:]
    private var coinView: UIView!
    private var svg2: SVGKImage?
    private var foundIndex: Int = 0
    private var finalFoundIndex: Int = 0
    private var meIndex: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFFFFF
        
        svgView = UIImageView()
        svgView.alpha = 0.001
        svgView.contentMode = .scaleAspectFit
        svgView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        view.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.top.bottom.right.equalToSuperview()
        }
       
        rightBackground.alpha = 0
        view.addSubview(rightBackground)
        rightBackground.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        rightBackground.snp.makeConstraints { make in
            make.left.equalTo(gridLayout)
            make.top.right.bottom.equalTo(self)
        }
        view.bringSubviewToFront(gridLayout)
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        let colorIndexes = Utils.generatePermutation(3, size: colors.count)
        while true {
            for i in 0..<12 {
                items[i] = Int.random(in: 0..<3)
                items[12 + i] = items[i]
            }
            rightItems = Array(repeating: 0, count: 3)
            while true {
                for j in 0..<3 {
                    rightItems[j] = Int.random(in: 0..<3)
                }
                if containArrayInTwoWay(a: items, b: rightItems) {
                    finalFoundIndex = foundIndex
                    break
                }
            }
            
            var wrongItems = Array(repeating: 0, count: 3)
            var wrongList: [[Int]] = []
            for _ in 0..<1000 {
                for j in 0..<3 {
                    wrongItems[j] = Int.random(in: 0..<3)
                }
                if containArrayInTwoWay(a: items, b: wrongItems) {
                    continue
                }
                if containArray(wrongList: wrongList, items: wrongItems) {
                    continue
                }
                wrongList.append(wrongItems)
                wrongItems = Array(repeating: 0, count: 3)
                if wrongList.count == 2 { break }
            }
            
            if wrongList.count == 2 {
                answerList = wrongList
                answerList.insert(rightItems, at: Int.random(in: 0...answerList.count))
                break
            }
        }
        meIndex = answerList.firstIndex(where: { $0 == rightItems }) ?? 0
        self.svg2 = Utilities.GetSVGKImage(named: "tuduy_xauhat_question")
        
        var colorList: [UIColor] = []
        for i in 0..<self.svg2!.caLayerTree.sublayers!.count {
            let path = self.svg2!.caLayerTree.sublayers![i] as! CAShapeLayer
            if let id = path.name, id.hasPrefix("c") {
                let color = path.fillColor!.uiColor!
                if !colorList.contains(color) {
                    colorList.append(color)
                }
            }
        }
        self.colors = colorList
        
        for i in 0..<self.svg2!.caLayerTree.sublayers!.count {
            let path = self.svg2!.caLayerTree.sublayers![i] as! CAShapeLayer
            if let id = path.name, id.hasPrefix("c") {
                let index = Int(id.dropFirst())! - 1
                path.fillColor = self.colors[colorIndexes[self.items[index]]].cgColor
                self.pathMap[index] = path
            }
        }
        let imageView = self.svgView.addImage(image: self.svg2!)
        imageView.transform = CGAffineTransformMakeScale(0.8, 0.8)
        svgView.alpha = 1
        //self.svgView.image = self.svg2!
        
        var views: [UIView] = []
        for i in 0..<self.answerList.count {
            let answer = self.answerList[i]
            let box = UIImageView()
            let svg = Utilities.GetSVGKImage(named: "tuduy_xauhat_option")
            for j in 0..<svg.caLayerTree.sublayers!.count {
                let path = svg.caLayerTree.sublayers![j] as! CAShapeLayer
                if let id = path.name, id.hasPrefix("c") {
                    let index = Int(id.dropFirst())! - 1
                    path.fillColor = self.colors[colorIndexes[answer[index]]].cgColor
                }
            }
            //box.image = svg.uiImage
            box.addImage(image: svg)
            box.contentMode = .scaleAspectFit
            box.tag = i
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.handleBoxTap(_:)))
            box.addGestureRecognizer(tapGesture)
            box.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: box)
            views.append(box)
            
            //box.transform = CGAffineTransform(scaleX: 0, y: 0)
            //box.alpha = 0
        }
        
        self.gridLayout.columns = 1
        self.gridLayout.itemRatio = 3
        self.gridLayout.itemSpacingRatio = 0.1
        self.gridLayout.insetRatio = 0.1
        self.gridLayout.reloadItemViews(views: views)
        /*
        self.gridLayout.soundProvider = { index in
            ("en/english phonics/effects/bubble\(1 + index)", 1.0)
        }
        */
        let delay = self.playSound(openGameSound(), "tuduy/tuduy_xau hat")
        self.scheduler.schedule(after: delay) { [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.6) {
                self.svgView.transform = .identity
                self.gridLayout.alpha = 1
                self.gridLayout.transform = .identity
                self.rightBackground.alpha = 1
            }
            self.gridLayout.showItems(startDelay: 0.6)
            self.scheduler.schedule(after: 1.5) { [weak self] in
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_xau hat")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        svgView.moveToCenter(of: self, duration: 0)
        gridLayout.alpha = 0
    }
    
    // MARK: - Touch Handling
    @objc private func handleBoxTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let index = view.tag
        pauseGame()
        
        if index == meIndex {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            for j in 0..<10 {
                let finalJ = j
                scheduler.schedule(after: TimeInterval(j) * 0.5) {
                    self.pathMap[(self.finalFoundIndex - 0) % 12]?.opacity = finalJ % 2 == 1 ? 1 : 0.085
                    self.pathMap[(self.finalFoundIndex + 1) % 12]?.opacity = finalJ % 2 == 1 ? 1 : 0.085
                    self.pathMap[(self.finalFoundIndex + 2) % 12]?.opacity = finalJ % 2 == 1 ? 1 : 0.085
                    self.svgView.setNeedsDisplay()
                }
            }
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", "answer_wrong\(self.random(1,2))")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func containArrayInTwoWay(a: [Int], b: [Int]) -> Bool {
        return containArray(a: a, b: b) || containArray(a: a, b: b.reversed())
    }
    
    private func containArray(wrongList: [[Int]], items: [Int]) -> Bool {
        return wrongList.contains { sameInTwoWay(a: $0, b: items) }
    }
    
    private func containArray(a: [Int], b: [Int]) -> Bool {
        for i in 0...(a.count - b.count) {
            var found = true
            for j in 0..<b.count {
                if a[i + j] != b[j] {
                    found = false
                    break
                }
            }
            if found {
                foundIndex = i
                return true
            }
        }
        return false
    }
    
    private func sameInTwoWay(a: [Int], b: [Int]) -> Bool {
        return sameArray(a: a, b: b) || sameArrayReverse(a: a, b: b)
    }
    
    private func sameArray(a: [Int], b: [Int]) -> Bool {
        guard a.count == b.count else { return false }
        return a.elementsEqual(b)
    }
    
    private func sameArrayReverse(a: [Int], b: [Int]) -> Bool {
        guard a.count == b.count else { return false }
        return a.elementsEqual(b.reversed())
    }
}

// MARK: - Supporting Structures

extension CGColor {
    var uiColor: UIColor? {
        return UIColor(cgColor: self)
    }
}

extension UIView {
    func addImage(image: SVGKImage)->SVGKFastImageView{
        let svgView = SVGKFastImageView(svgkImage: image)!
        let ratio = Float(image.size.width) / Float(image.size.height)
        svgView.contentMode = .scaleAspectFit
        addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: ratio)
        return svgView
    }
}
