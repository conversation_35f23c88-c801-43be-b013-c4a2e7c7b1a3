<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="1"
        android:background="@drawable/tuduy_rotnuoc_binh"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="0.36"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/view_nap_water"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/tuduy_rotnuoc_nap"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="parent"
            app:layout_constraintDimensionRatio="175.9:133"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent" />

        <View
            android:id="@+id/view_drop_water"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#f00"
            android:visibility="invisible"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="1.2"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintWidth_percent="0.05" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipChildren="true"
            android:clipToPadding="true">

            <com.kidsup.giaoducsom.view.FourColorView
                android:id="@+id/four_color_view"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#0f00"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.25"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.65" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="@id/four_color_view"
                app:layout_constraintLeft_toLeftOf="@id/four_color_view"
                app:layout_constraintRight_toRightOf="@id/four_color_view"
                app:layout_constraintTop_toTopOf="@id/four_color_view">

                <View
                    android:id="@+id/view_new_water"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/tuduy_rotnuoc_binh_2"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>