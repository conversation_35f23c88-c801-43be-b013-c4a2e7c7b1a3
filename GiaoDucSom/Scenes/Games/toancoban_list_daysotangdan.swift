//
//  toancoban_list_daysotangdan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit

class toancoban_list_daysotangdan: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private let column = 7
    private let row = 5
    private var arrows: [UIView] = []
    private var points: [Point] = []
    private var startPoint: Point!
    private var endPoint: Point!
    private var curerntPointIndex = -1
    private var views: [UIView] = []
    private var boatImage: UIImageView!
    private var boatView: UIView!
    private var destView: UIView!
    private var drawableNormal: CornerDrawable!
    private var drawableSelected: CornerDrawable!
    private let colorNormal = UIColor(red: 118/255, green: 199/255, blue: 255/255, alpha: 1) // #76C7FF
    private let colorSelected = UIColor.white
    private var grid = Array(repeating: Array(repeating: -1, count: 7), count: 5)
    private var currentPoint: Point?
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 182/255, green: 223/255, blue: 243/255, alpha: 1) // #B6DFF3
        
        let paddingView = UIView()
        addSubview(paddingView)
        paddingView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.7)
            make.height.equalToSuperview().multipliedBy(0.8)
        }
        
        gridLayout = MyGridView()
        gridLayout.clipsToBounds = false
        paddingView.addSubview(gridLayout)
        gridLayout.makeViewCenterAndKeep(ratio: 7.0 / 5.0)
        
        let overlayContainer = UIView()
        overlayContainer.clipsToBounds = false
        overlayContainer.isUserInteractionEnabled = false
        paddingView.addSubview(overlayContainer)
        overlayContainer.makeViewCenterAndKeep(ratio: 7.0 / 5.0)
        
        boatView = UIView()
        overlayContainer.addSubview(boatView)
        boatView.snp.makeConstraints { make in
            make.width.equalTo(overlayContainer).multipliedBy(0.1428571428571429) // 1/7
            make.height.equalTo(overlayContainer).multipliedBy(0.2)
            make.left.top.equalToSuperview()
        }
        
        boatImage = UIImageView(image: Utilities.SVGImage(named: "math_daysotangdan1"))
        boatImage.contentMode = .scaleAspectFit
        boatImage.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        boatView.addSubview(boatImage)
        boatImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        destView = UIView()
        overlayContainer.addSubview(destView)
        destView.snp.makeConstraints { make in
            make.width.equalTo(overlayContainer).multipliedBy(0.1428571428571429) // 1/7
            make.height.equalTo(overlayContainer).multipliedBy(0.2)
            make.right.top.equalToSuperview()
        }
        
        let destImage = UIImageView(image: Utilities.SVGImage(named: "math_daysotangdan2"))
        destImage.contentMode = .scaleAspectFit
        destImage.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        destView.addSubview(destImage)
        destImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            points = GridPathGenerator().generatePath()
            for y in 0..<row {
                for x in 0..<column {
                    grid[y][x] = -1
                }
            }
            for (i, point) in points.enumerated() {
                grid[point.row][point.col] = i
            }
            if FillArray().make(grid: &grid) {
                break
            }
        }
        
        startPoint = points[0]
        endPoint = points[points.count - 1]
        drawableNormal = CornerDrawable(color: .white, cornerRadiusPercent: 5)
        drawableSelected = CornerDrawable(color: UIColor(red: 190/255, green: 233/255, blue: 247/255, alpha: 1), cornerRadiusPercent: 5) // #BEE9F7
        
        views = []
        for y in 0..<row {
            for x in 0..<column {
                let view = createItemDaySoTangDan(value: grid[y][x])
                view.tag = Point(row: y, col: x).hashValue
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onItemTapped(_:)))
                view.addGestureRecognizer(tapGesture)
                view.isUserInteractionEnabled = true
                views.append(view)
            }
        }
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.01
        gridLayout.columns = column
        gridLayout.insetRatio = 0.01
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "toan/toan_day so tang dan")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        boatView.transform = CGAffineTransform(translationX: -gridLayout.frame.width / CGFloat(column), y: CGFloat(startPoint.row) * gridLayout.frame.height / CGFloat(row))
        destView.transform = CGAffineTransform(translationX: gridLayout.frame.width / CGFloat(column), y: CGFloat(endPoint.row) * gridLayout.frame.height / CGFloat(row))
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_day so tang dan")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let viewBackground = view.viewWithTag(R7.id.view_background),
              let textName = view.viewWithTag(R7.id.text_name) as? UILabel else { return }
        pauseGame()
        let point = Point.fromHash(view.tag)
        
        if curerntPointIndex == -1 {
            if point == startPoint {
                UIView.animate(withDuration: 0.2, delay: 0.5) {
                    self.boatView.transform = CGAffineTransform(translationX: CGFloat(point.col) * self.gridLayout.frame.width / CGFloat(self.column), y: CGFloat(point.row) * self.gridLayout.frame.height / CGFloat(self.row))
                }
                curerntPointIndex = 0
                currentPoint = startPoint
                viewBackground.backgroundColor = drawableSelected.backgroundColor
                textName.textColor = self.colorSelected
                playSound("effect/penguin_jump", "topics/Numbers/0")
            } else {
                playSound("effect/answer_wrong")
            }
            resumeGame()
            return
        }
        
        let currentValue = grid[point.row][point.col]
        if abs(currentPoint!.col - point.col) + abs(currentPoint!.row - point.row) == 1 {
            let correct = curerntPointIndex + 1 == currentValue
            if !correct {
                var delay = 0.5
                setGameWrong()
                animateCoinIfCorrect(view: textName)
                delay += playSound(delay: delay, names: ["effect/penguin_fall", getWrongHumanSound(), endGameSound()])
                scheduler.schedule(after: 0.5) {
                    self.boatImage.image = Utilities.SVGImage(named: "math_daysotangdan3")
                }
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                currentPoint = point
                playSound("effect/penguin_jump", "topics/Numbers/\(currentValue)")
                viewBackground.backgroundColor = drawableSelected.backgroundColor
                textName.textColor = colorSelected
                curerntPointIndex += 1
                if point == endPoint {
                    pauseGame()
                    self.removeCurrentArrows()
                    scheduler.schedule(after: 0.5) { [weak self] in
                        self?.animateCoinIfCorrect(view: self?.destView ?? UIView())
                    }
                    boatView.bringSubviewToFront(self)
                    //boatView.layer.anchorPoint = CGPoint(x: 0.5, y: 0.7)
                    scheduler.schedule(delay: 2) {
                        [weak self] in
                        guard let self = self else { return }
                        self.boatView.moveToCenter(of: destView, duration: 0.5, zoomOut: true)
                        UIView.animate(withDuration: 0.5) {
                            self.boatImage.transform = CGAffineTransformMakeScale(0.2, 0.2)
                            self.boatImage.alpha = 0.01
                        }
                    }
                    
                    let delay = 1.0 + playSound(delay: 1.0, names: finishCorrect1Sounds())
                    scheduler.schedule(delay: delay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    resumeGame()
                    updateHint()
                }
            }
            UIView.animate(withDuration: 0.2, delay: 0.5) {
                self.boatView.transform = CGAffineTransform(translationX: CGFloat(point.col) * self.gridLayout.frame.width / CGFloat(self.column), y: CGFloat(point.row) * self.gridLayout.frame.height / CGFloat(self.row))
            }
        } else {
            playSound("effect/answer_wrong")
            resumeGame()
        }
    }
    
    // MARK: - Helper Methods
    private func removeCurrentArrows() {
        // Chưa có logic cụ thể trong Java, để trống
    }
    
    private func updateHint() {
        // Chưa có logic cụ thể trong Java, để trống
    }
    class MyRoundedView : UIView {
        override func layoutSubviews(){
            super.layoutSubviews()
            layer.cornerRadius = min(bounds.width/10, bounds.height/10)
            clipsToBounds = true
        }
    }
    private func createItemDaySoTangDan(value: Int) -> UIView {
        let view = MyRoundedView()
        view.backgroundColor = drawableNormal.backgroundColor
        
        let viewBackground = MyRoundedView()
        viewBackground.tag = R7.id.view_background
        viewBackground.backgroundColor = drawableNormal.backgroundColor
        view.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textName = AutosizeLabel()
        textName.tag = R7.id.text_name
        textName.text = String(value)
        textName.textColor = colorNormal
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.1
        view.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.7)
            make.left.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        return view
    }
    
    // MARK: - Point Struct
    struct Point: Equatable, Hashable {
        let row: Int
        let col: Int
        
        static func fromHash(_ hash: Int) -> Point {
            let row = hash / 1000
            let col = hash % 1000
            return Point(row: row, col: col)
        }
        
        var hashValue: Int {
            return row * 1000 + col
        }
        
        static func == (lhs: Point, rhs: Point) -> Bool {
            return lhs.row == rhs.row && lhs.col == rhs.col
        }
    }
    
    // MARK: - GridPathGenerator
    class GridPathGenerator {
        private let ROWS = 5
        private let COLS = 7
        private let MIN_LENGTH = 10
        private let MAX_LENGTH = 20
        
        private var visited: [[Bool]] = []
        private var path: [Point] = []
        private let random = Random()
        
        func generatePath() -> [Point] {
            while true {
                visited = Array(repeating: Array(repeating: false, count: COLS), count: ROWS)
                path = []
                
                let startRow = random.nextInt(bound: ROWS)
                let startPoint = Point(row: startRow, col: 0)
                path.append(startPoint)
                visited[startRow][0] = true
                
                let endRow = random.nextInt(bound: ROWS)
                let endPoint = Point(row: endRow, col: COLS - 1)
                
                if dfs(current: startPoint, endPoint: endPoint) {
                    return path
                }
            }
        }
        
        private func dfs(current: Point, endPoint: Point) -> Bool {
            if current == endPoint && path.count >= MIN_LENGTH && path.count <= MAX_LENGTH {
                return true
            }
            if path.count > MAX_LENGTH {
                return false
            }
            
            var neighbors = getNeighbors(point: current)
            neighbors.shuffle()
            
            for neighbor in neighbors {
                let row = neighbor.row
                let col = neighbor.col
                if !visited[row][col] && !isAdjacentToPreviousPoints(neighbor: neighbor) {
                    visited[row][col] = true
                    path.append(neighbor)
                    if dfs(current: neighbor, endPoint: endPoint) {
                        return true
                    }
                    visited[row][col] = false
                    path.removeLast()
                }
            }
            return false
        }
        
        private func getNeighbors(point: Point) -> [Point] {
            var neighbors: [Point] = []
            let directions = [(-1, 0), (0, -1), (1, 0), (0, 1)]
            for (dRow, dCol) in directions {
                let newRow = point.row + dRow
                let newCol = point.col + dCol
                if isValidCell(row: newRow, col: newCol) {
                    neighbors.append(Point(row: newRow, col: newCol))
                }
            }
            return neighbors
        }
        
        private func isValidCell(row: Int, col: Int) -> Bool {
            return row >= 0 && row < ROWS && col >= 0 && col < COLS
        }
        
        private func isAdjacentToPreviousPoints(neighbor: Point) -> Bool {
            for i in 0..<path.count - 1 {
                if areAdjacent(p1: neighbor, p2: path[i]) {
                    return true
                }
            }
            return false
        }
        
        private func areAdjacent(p1: Point, p2: Point) -> Bool {
            let rowDiff = abs(p1.row - p2.row)
            let colDiff = abs(p1.col - p2.col)
            return rowDiff + colDiff == 1
        }
    }
    
    // MARK: - FillArray
    class FillArray {
        func make(grid: inout [[Int]]) -> Bool {
            var count = 0
            while true {
                count += 1
                var array = grid
                if fillEmptyCells(array: &array) {
                    grid = array
                    return true
                }
                if count > 100 {
                    return false
                }
            }
        }
        
        private func fillEmptyCells(array: inout [[Int]]) -> Bool {
            let rows = array.count
            let cols = array[0].count
            var visited = Array(repeating: Array(repeating: false, count: cols), count: rows)
            var queue: [(Int, Int)] = []
            
            for i in 0..<rows {
                for j in 0..<cols {
                    if array[i][j] != -1 {
                        queue.append((i, j))
                        visited[i][j] = true
                    }
                }
            }
            
            let directions = [(1, 0), (-1, 0), (0, 1), (0, -1)].shuffled()
            while !queue.isEmpty {
                let (x, y) = queue.removeFirst()
                let currentValue = array[x][y]
                for (dx, dy) in directions {
                    let newX = x + dx
                    let newY = y + dy
                    if isValid(x: newX, y: newY, rows: rows, cols: cols) && !visited[newX][newY] {
                        let neighborValue = array[newX][newY]
                        if neighborValue == -1 {
                            let newValue = currentValue + getRandomOffset()
                            array[newX][newY] = newValue
                            queue.append((newX, newY))
                            visited[newX][newY] = true
                        } else if abs(neighborValue - currentValue) > 1 {
                            array[newX][newY] = currentValue + (neighborValue > currentValue ? 1 : -1)
                        }
                    }
                }
            }
            
            for row in array {
                for val in row {
                    if val < 0 { return false }
                }
            }
            return true
        }
        
        private func getRandomOffset() -> Int {
            return Int.random(in: -1...1)
        }
        
        private func isValid(x: Int, y: Int, rows: Int, cols: Int) -> Bool {
            return x >= 0 && x < rows && y >= 0 && y < cols
        }
    }
}

// MARK: - Supporting Structures
struct CornerDrawable {
    let backgroundColor: UIColor
    let cornerRadiusPercent: Float
    
    init(color: UIColor, cornerRadiusPercent: Float) {
        self.backgroundColor = color
        self.cornerRadiusPercent = cornerRadiusPercent
    }
}


struct R7 {
    struct id {
        static let view_background = 1
        static let text_name = 2
    }
}


