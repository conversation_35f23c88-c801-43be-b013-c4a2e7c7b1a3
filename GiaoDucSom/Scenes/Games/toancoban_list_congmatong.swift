//
//  toancoban_list_congmatong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/4/25.
//


import UIKit
import SnapKit

class toancoban_list_congmatong: NhanBietGameFragment {
    // MARK: - Properties
    private var imageLeft: UIImageView!
    private var imageRight: UIImageView!
    private var value: Int = 0
    private var leftValue: Int = 0
    private var rightValue: Int = 0
    private var numpad: MathNumpad!
    private var answerText: HeightRatioTextView!
    private var textLeft: HeightRatioTextView!
    private var textRight: HeightRatioTextView!
    private var index: Int = 0
    private var answerLayout: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        //view.backgroundColor = UIColor(hex: "#663535")
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_garden"))
        bgImage.clipsToBounds = true
        bgImage.contentMode = .scaleAspectFill
        addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        bringSubviewToFront(view)
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        answerLayout = UIView()
        answerLayout.clipsToBounds = false
        view.addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.18)
            make.height.equalTo(answerLayout.snp.width) // Ratio 1:1
        }
        addActionOnLayoutSubviews{
            [weak self] in
            guard let self = self else { return }
            self.answerLayout.snapToHorizontalBias(horizontalBias: 0.6)
            self.answerLayout.snapToVerticalBias(verticalBias: 0.7)
        }
        
        let honeyImage = UIImageView(image: Utilities.SVGImage(named: "toan_congmatong"))
        answerLayout.addSubview(honeyImage)
        
        
        answerText = HeightRatioTextView()
        answerText.text = "?"
        answerText.setHeightRatio(0.65)
        answerText.textColor = UIColor(hex: "#FF8212")
        answerText.font = .Freude(size: 20)
        answerText.textAlignment = .center
        answerText.adjustsFontSizeToFitWidth = true
        answerText.minimumScaleFactor = 0.1
        answerLayout.addSubview(answerText)
        answerText.snp.makeConstraints { make in
            make.height.equalTo(answerLayout).multipliedBy(0.8)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        honeyImage.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(1.0)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(1.07)
        }
        
        let handsContainer = UIView()
        handsContainer.clipsToBounds = false
        view.addSubview(handsContainer)
        handsContainer.snp.makeConstraints { make in
            make.top.bottom.equalTo(answerLayout)
            make.left.equalToSuperview()
            make.right.equalTo(answerLayout.snp.left)
        }
        
        let innerHandsContainer = UIView()
        handsContainer.addSubview(innerHandsContainer)
        innerHandsContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.94)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
            make.height.equalTo(innerHandsContainer.snp.width).multipliedBy(1.0/6.1)
        }
        
        imageLeft = UIImageView(image: animation_bee)
        imageLeft.contentMode = .scaleAspectFit
        innerHandsContainer.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.height.equalTo(innerHandsContainer).multipliedBy(3.0)
            make.width.equalTo(imageLeft.snp.height).multipliedBy(4.0 / 6.0) // Ratio 4:6
            make.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.25)
        }
        
        imageRight = UIImageView(image: animation_bee_2)
        imageRight.contentMode = .scaleAspectFit
        innerHandsContainer.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.height.equalTo(innerHandsContainer).multipliedBy(3.0)
            make.width.equalTo(imageRight.snp.height).multipliedBy(4.0 / 6.0) // Ratio 4:6
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview().multipliedBy(0.9)
        }
        
        let textLeftContainer = UIView()
        innerHandsContainer.addSubview(textLeftContainer)
        textLeftContainer.snp.makeConstraints { make in
            make.width.equalTo(textLeftContainer.snp.height).multipliedBy(1.3)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.25)
        }
        
        textLeft = HeightRatioTextView()
        textLeft.text = "1"
        textLeft.setHeightRatio(0.9)
        textLeft.textColor = UIColor.black
        textLeft.font = .Freude(size: 20)
        textLeft.textAlignment = .center
        textLeft.adjustsFontSizeToFitWidth = true
        textLeft.minimumScaleFactor = 0.1
        textLeftContainer.addSubview(textLeft)
        textLeft.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let plusContainer = UIView()
        innerHandsContainer.addSubview(plusContainer)
        plusContainer.snp.makeConstraints { make in
            make.width.equalTo(plusContainer.snp.height)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textLeftContainer.snp.right)
        }
        
        let plusText = HeightRatioTextView()
        plusText.text = "+"
        plusText.setHeightRatio(0.9)
        plusText.textColor = UIColor.black
        plusText.font = .Freude(size: 20)
        plusText.textAlignment = .center
        plusText.adjustsFontSizeToFitWidth = true
        plusText.minimumScaleFactor = 0.1
        plusContainer.addSubview(plusText)
        plusText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textRightContainer = UIView()
        innerHandsContainer.addSubview(textRightContainer)
        textRightContainer.snp.makeConstraints { make in
            make.width.equalTo(textRightContainer.snp.height).multipliedBy(1.3)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(plusContainer.snp.right)
        }
        
        textRight = HeightRatioTextView()
        textRight.text = "2"
        textRight.setHeightRatio(0.9)
        textRight.textColor = UIColor.black
        textRight.font = .Freude(size: 20)
        textRight.textAlignment = .center
        textRight.adjustsFontSizeToFitWidth = true
        textRight.minimumScaleFactor = 0.1
        textRightContainer.addSubview(textRight)
        textRight.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let equalContainer = UIView()
        innerHandsContainer.addSubview(equalContainer)
        equalContainer.snp.makeConstraints { make in
            make.width.equalTo(equalContainer.snp.height)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textRightContainer.snp.right)
        }
        
        let equalText = HeightRatioTextView()
        equalText.text = "="
        equalText.setHeightRatio(0.9)
        equalText.textColor = UIColor.black
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalText.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        equalContainer.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        value = 2 + Int.random(in: 0..<9)
        while true {
            leftValue = Int.random(in: 0..<value)
            rightValue = value - leftValue
            if leftValue >= 1 && leftValue <= 5 && rightValue >= 1 && rightValue <= 5 {
                break
            }
        }
        
        textLeft.text = String(leftValue)
        textRight.text = String(rightValue)
        updateWing()
        
        let delay = playSound(openGameSound(), "toan/toan_cong mat ong")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_cong mat ong")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    let animation_bee = Utilities.SVGImage(named: "animations/animation_bee.svg")
    let animation_bee_2 = Utilities.SVGImage(named: "animations/animation_bee_2.svg")
    private func updateWing() {
        index += 1
        imageLeft.image = index % 2 == 0 ? animation_bee : animation_bee_2
        imageRight.image = index % 2 == 1 ? animation_bee : animation_bee_2
        scheduler.schedule(after: 0.03) { [weak self] in
            self?.updateWing()
        }
    }
    
    override func createGame() {
        super.createGame()
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.answerLayout.transform = CGAffineTransform(translationX: -self.answerLayout.frame.width / 3, y: 0)
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_congmatong: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(hex: "#FF8212")
    }
    
    func onDelClick(value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(hex: "#74B6FF")
    }
    
    func onCheckClick(value: Int) {
        answerText.text = String(value)
        pauseGame()
        let correct = value == self.value
        var delay = playSound(correct ? answerCorrect1EffectSound() : answerWrongEffectSound())
        
        if correct {
            animateCoinIfCorrect(view: answerText)
            delay += playSound(delay: delay, names: [
                getCorrectHumanSound(),
                "topics/Numbers/\(leftValue)",
                "toan/cộng",
                "topics/Numbers/\(rightValue)",
                "toan/bằng",
                "topics/Numbers/\(value)",
                endGameSound()
            ])
            answerText.textColor = UIColor(hex: "#87D657")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [])
            answerText.textColor = UIColor(hex: "#FF7760")
            numpad.reset()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

