//
//  FlashcardViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio


// MARK: - FlashcardViewController
class FlashcardViewController: BaseViewController {
    // MARK: - Properties
    private let timeoutShowHint = 3.0
    private var timeoutTimer: TimeoutTimer?
    private var showPhoto: Bool = false
    private var showText: Bool = false
    private var playIndex: Int = 0
    private var playCount: Int = 0
    private var cardCount: Int = 0
    private var flashcardImage: SVGKFastImageView!
    private var flashcardText: UILabel!
    private var chooseItems: [ItemData] = []
    private var isReading: Bool = false
    private var btnClose: KUButton!
    private var arcView: ArcView!
    private var folderText: UILabel!
    private var playFolder: Bool = false
    private var finishedStatus: FinishedStatus = .wait
    private var timeoutShowHintTimer: TimeoutTimer?
    private var viewAnimation: XamlAnimationView!
    private var items: [ItemData] = []
    var pack: Folder?
    
    // MARK: - View Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        playFolder = pack != nil
        view.backgroundColor = .white
        //hideStatusBar()
        showPhoto = DataManager.shared.currentProfile!.flashcardShowPhoto
        showText = DataManager.shared.currentProfile!.flashcardShowText
        configureLayout(view)
        
        timeoutTimer = TimeoutTimer()
        timeoutTimer?.duration = 2.0
        timeoutTimer?.onActived = { [weak self] in
            self?.btnClose.alpha = 0.3
        }
        
        timeoutShowHintTimer = TimeoutTimer()
        timeoutShowHintTimer?.duration = timeoutShowHint
        timeoutShowHintTimer?.onActived =  { [weak self] in
            guard let self = self else { return }
            self.viewAnimation.isHidden = false
            UIView.animate(withDuration: 0.3) {
                self.viewAnimation.alpha = 1
            }
            self.viewAnimation.startAnimation(name: "sb2")
            self.scheduler.schedule(after: 2.0) { [weak self] in
                self?.timeoutShowHintTimer?.schedule()
            }
        }
        
        loadData(check15min: false)
    }
    
    override func viewDidDisappear(_ animated: Bool) {
        super.viewDidDisappear(animated)
        scheduler.clearAll()
        arcView.stopAnimations()
    }
    
    // MARK: - Configure Layout
    private func configureLayout(_ view: UIView) {
                
        flashcardImage = SVGKFastImageView(svgkImage: nil)
        flashcardImage.stringTag = "flashcard_image"
        view.addSubview(flashcardImage)
        flashcardImage.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.9)
            make.width.equalTo(flashcardImage.snp.height)
        }
        
        flashcardText = AutosizeLabel()
        flashcardText.stringTag = "flashcard_text"
        flashcardText.text = "Xin chào thế gới"
        flashcardText.textColor = UIColor(hex: "#FF7761") // Giả định @color/flashcard_color
        flashcardText.textAlignment = .center
        flashcardText.font = UIFont(name: "UTM-AvoBold", size: 500)
        flashcardText.adjustsFontSizeToFitWidth = true
        flashcardText.minimumScaleFactor = 0.1
        flashcardText.numberOfLines = 1
        flashcardText.isHidden = true
        view.addSubview(flashcardText)
        flashcardText.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.6)
        }
        
        folderText = AutosizeLabel()
        folderText.stringTag = "folder_text"
        folderText.text = "Xin chào thế gới"
        folderText.textColor = UIColor.black // Giả định @color/blacktext
        folderText.textAlignment = .center
        folderText.font = UIFont(name: "SVN-Freude", size: 500)
        folderText.adjustsFontSizeToFitWidth = true
        folderText.minimumScaleFactor = 0.1
        folderText.numberOfLines = 1
        folderText.isHidden = true
        view.addSubview(folderText)
        folderText.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        
        
        let flashcardFinishView = UIView()
        flashcardFinishView.stringTag = "flashcard_finish_view"
        flashcardFinishView.isHidden = true
        view.addSubview(flashcardFinishView)
        flashcardFinishView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let guideCenter = UIView()
        guideCenter.stringTag = "guide_center"
        flashcardFinishView.addSubview(guideCenter)
        guideCenter.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.7)
            make.width.equalTo(guideCenter.snp.height).multipliedBy(0.7)
            make.center.equalToSuperview()
        }
        
        let flashcardCountdown = UIView()
        flashcardCountdown.stringTag = "flashcard_countdown"
        flashcardFinishView.addSubview(flashcardCountdown)
        flashcardCountdown.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.7)
            make.width.equalTo(flashcardCountdown.snp.height)
            make.center.equalToSuperview()
        }
        
        let bgFlashcardsCountdown = UIImageView()
        bgFlashcardsCountdown.stringTag = "bg_flashcards_countdown"
        bgFlashcardsCountdown.image = Utilities.SVGImage(named: DataManager.shared.currentProfile!.flashcardAutoNextCard ? "bg_flashcards_countdown" : "icon_flashcards_end")
        flashcardCountdown.addSubview(bgFlashcardsCountdown)
        bgFlashcardsCountdown.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        arcView = ArcView()
        arcView.stringTag = "arc_view"
        flashcardCountdown.addSubview(arcView)
        arcView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.51)
            make.height.equalTo(arcView.snp.width)
            make.center.equalToSuperview()
        }
        
        let replayContainer = UIView()
        flashcardFinishView.addSubview(replayContainer)
        replayContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.equalTo(guideCenter.snp.left)
        }
        
        let btnReplay = AutosizeLabel()
        btnReplay.stringTag = "btn_replay"
        btnReplay.text = "Tráo lại"
        btnReplay.textColor = UIColor.color(hex: "#FF7761")
        btnReplay.textAlignment = .center
        btnReplay.font = .Freude(size: 20)
        btnReplay.isUserInteractionEnabled = true
        btnReplay.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(replayTapped)))
        //AnimationUtils.setTouchEffect(btnReplay)
        replayContainer.addSubview(btnReplay)
        btnReplay.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
            make.height.equalTo(btnReplay.snp.width).dividedBy(4)
        }
        
        let nextContainer = UIView()
        flashcardFinishView.addSubview(nextContainer)
        nextContainer.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.left.equalTo(guideCenter.snp.right)
        }
        
        let btnNext = AutosizeLabel()
        btnNext.stringTag = "btn_next"
        btnNext.text = "Chủ đề tiếp"
        btnNext.textColor = UIColor.color(hex: "#FF7761")
        btnNext.textAlignment = .center
        btnNext.font = .Freude(size: 20)
        btnNext.isUserInteractionEnabled = true
        btnNext.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(nextTapped)))
        //AnimationUtils.setTouchEffect(btnNext)
        nextContainer.addSubview(btnNext)
        btnNext.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.center.equalToSuperview()
            make.height.equalTo(btnNext.snp.width).dividedBy(4)
        }
        //btnNext.makeViewCenterAndKeep(widthPercent: 0.8, ratio: 4)
        
        let viewTopLeft = UIView()
        viewTopLeft.stringTag = "view_topleft"
        view.addSubview(viewTopLeft)
        viewTopLeft.snp.makeConstraints { make in
            make.width.equalTo(viewTopLeft.snp.height)
            make.height.equalToSuperview().multipliedBy(0.05)
            make.left.top.equalToSuperview()
        }
        
        btnClose = KUButton()
        btnClose.stringTag = "btn_close"
        btnClose.setImage(Utilities.SVGImage(named: "btn_close"), for: .normal)
        btnClose.imageView?.contentMode = .scaleAspectFit
        btnClose.alpha = 0.3
        btnClose.addTarget(self, action: #selector(closeTapped), for: .touchUpInside)
        //AnimationUtils.setTouchEffect(btnClose)
        view.addSubview(btnClose)
        btnClose.snp.makeConstraints { make in
            make.width.equalTo(btnClose.snp.height)
            make.height.equalToSuperview().multipliedBy(0.1)
            make.left.equalTo(viewTopLeft.snp.right)
            make.top.equalTo(viewTopLeft.snp.bottom)
        }
        
        viewAnimation = XamlAnimationView()
        viewAnimation.stringTag = "view_animation"
        viewAnimation.loadFile(named: "animations/xaml/flashcards_hand")
        viewAnimation.isHidden = true
        view.addSubview(viewAnimation)
        viewAnimation.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(viewAnimation.snp.height)
            make.right.bottom.equalToSuperview()
        }
        //viewAnimation.makeViewCenterAndKeep(ratio: 1, heightPercent: 0.8, horizontalBias: 1, verticalBias: 1)
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(contentTapped))
        view.addGestureRecognizer(tapGesture)
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        if btnClose.alpha < 1 {
            btnClose.alpha = 1
            timeoutTimer?.schedule()
            return
        }
        dismiss(animated: true, completion: nil)
        navigationController?.popViewController(animated: true)
    }
    
    @objc private func replayTapped() {
        restart()
    }
    
    @objc private func nextTapped() {
        nextTopic()
    }
    
    @objc private func contentTapped() {
        if !DataManager.shared.currentProfile!.flashcardAutoNextCard && !isReading {
            playNext()
        }
    }
    
    // MARK: - Helper Methods
    
    private func loadData(check15min: Bool) {
        items.removeAll()
        if !playFolder {
            pack = FlashcardsManager.shared.getNextPack(check15minutes: check15min)
        }
        guard let pack = pack else {
            dismiss(animated: true, completion: nil)
            navigationController?.popViewController(animated: true)
            return
        }
        
        for item in pack.items {
            let name = item.name
            if let path = item.path, !path.isEmpty {
                var data = ItemData()
                data.name = name
                data.path = "\(pack.folder)/\(path)"
                data.sound = item.sound == nil || item.sound!.isEmpty ? data.path!.replacingOccurrences(of: ".svg", with: "") : item.sound!.replacingOccurrences(of: ".mp3", with: "")
                items.append(data)
            }
        }
        playItems()
    }
    /*
    private func getKeyName() -> String {
        return items.prefix(5).map { $0.name?.description ?? "" }.joined(separator: "_").md5(prefix: "index_")
    }*/
    
    private func getItemIndex() -> Int {
        return AppSettings.getFlashcardPlayIndex(topicName: pack?.name.en ?? "")
    }
    
    private func ChooseItems() -> [ItemData] {
        var index = getItemIndex()
        index += 1
        var choose = items
        if BuildConfig.DEBUG {
            return choose.shuffled()
        }
        let numberOfCard = DataManager.shared.currentProfile!.flashcardNumberOfCards
        if items.count > numberOfCard {
            let skipCount = min(items.count - numberOfCard, max(min(index, items.count) - (numberOfCard - 1), 0))
            choose = Array(choose.dropFirst(skipCount).prefix(numberOfCard))
        }
        if DataManager.shared.currentProfile!.flashcardShowInRandomOrder {
            choose = choose.shuffled()
        }
        return choose
    }
    
    private func playItems() {
        isReading = true
        var delay: TimeInterval = 1.0
        delay += playSound(delay: delay, names: ["\(DataManager.shared.currentProfile!.flashcardLanguage)/topics/\(pack?.folder ?? "")/\(pack?.folder ?? "")"])
        folderText.isHidden = false
        folderText.text = pack?.name.localName
        flashcardText.textColor = .black
        scheduler.schedule(after: delay) { [weak self] in
            self?.folderText.isHidden = true
            self?.flashcardText.textColor = UIColor(hex: "#FF7761") // Giả định @color/flashcard_color
        }
        delay += 1.0
        scheduler.schedule(after: delay) { [weak self] in
            self?.playNext()
        }
        if let flashcardFinishView = view.viewWithStringTag("flashcard_finish_view") as? UIView {
            flashcardFinishView.isHidden = true
        }        
        flashcardText.isHidden = false
        flashcardText.text = ""
        guard !items.isEmpty else { return }
        finishedStatus = .started
        chooseItems = ChooseItems()
        flashcardText.isHidden = false
        cardCount = chooseItems.count
        playCount = ((showPhoto ? 1 : 0) + (showText ? 1 : 0)) * cardCount
        playIndex = 0
        btnClose.alpha = 0.3
    }
    
    private func playNext() {
        if playIndex >= playCount {
            timeoutShowHintTimer?.cancel()
            finishFlashcard()
            return
        }
        UIView.animate(withDuration: 0.3) {
            self.viewAnimation.alpha = 0
        }
        let playBoth = showPhoto && showText
        let cardIndex = playIndex / (playBoth ? 2 : 1)
        let data = chooseItems[cardIndex]
        let isPhoto = showPhoto && (!showText || playIndex % 2 == 0)
        if isPhoto {
            flashcardText.text = ""
            flashcardText.isHidden = true
            flashcardImage.alpha = 0
            let svg = Utilities.GetSVGKImage(named: "topics/\(data.path ?? "")")
            flashcardImage.image = svg
            flashcardImage.alpha = 1
        
            flashcardImage.isHidden = false
        } else {
            flashcardImage.isHidden = true
            flashcardText.text = data.name?.localName
            flashcardText.isHidden = false
        }
        let sound = "\(DataManager.shared.currentProfile!.flashcardLanguage)/topics/\(data.sound ?? "")"
        let duration = playSound(sound)
        let nextCard = max(duration, 1.0)
        isReading = true
        playIndex += 1
        if !DataManager.shared.currentProfile!.flashcardAutoNextCard {
            timeoutShowHintTimer?.schedule()
        }
        scheduler.schedule(after: nextCard) { [weak self] in
            self?.isReading = false
            if DataManager.shared.currentProfile!.flashcardAutoNextCard {
                self?.playNext()
            }
        }
    }
    
    private func finishFlashcard() {
        finishedStatus = .wait
        if let pack = pack {
            FlashcardsManager.shared.setPlayedTime(pack: pack)
        }
        let endFlashcard = playFolder || FlashcardsManager.shared.getNextPack(check15minutes: true) == nil
        if let btnNext = view.viewWithStringTag("btn_next") as? UILabel {
            btnNext.text = endFlashcard ? "Kết thúc" : "Chủ đề tiếp"
        }
        if let flashcardFinishView = view.viewWithStringTag("flashcard_finish_view") as? UIView {
            flashcardFinishView.isHidden = false
        }
        flashcardText.isHidden = true
        flashcardImage.isHidden = true
        
        if DataManager.shared.currentProfile!.flashcardAutoNextCard {
            arcView.animateArc { [weak self] in
                if self?.finishedStatus == .wait {
                    self?.nextTopic()
                }
            }
        } else {
            arcView.isHidden = true
        }
        btnClose.alpha = 1
    }
    
    private func restart() {
        arcView.stopAnimations()
        scheduler.clearAll()
        finishedStatus = .restarted
        playItems()
    }
    
    private func nextTopic() {
        arcView.stopAnimations()
        scheduler.clearAll()
        let numberOfCard = DataManager.shared.currentProfile!.flashcardNumberOfCards
        var index = getItemIndex()
        index += 1
        let count = items.count <= numberOfCard ? numberOfCard : items.count + numberOfCard - 1
        if BuildConfig.DEBUG {
            index = 100
        }
        if index >= count {
            if let packName = pack?.name.en {
                AppSettings.setFlashcardPlayIndex(topicName: packName, index: -1)
                FlashcardsManager.shared.finishTopic(flashcardPack: pack!)
                let dialog = MessageDialogView()
                dialog.setListener(onConfirm: {
                    [weak self] in
                    guard let self = self else { return }
                    
                }, onClose: { buttonIndex in
                    switch buttonIndex {
                    case .ok: print("OK tapped")
                    case .confirm: print("OK confirm")
                    case .close: print("Close tapped")
                    }
                    self.switchToNextTopic()
                })
                dialog.setTitle("Chú ý")
                    .setMessage("Bé đã học hết một lượt chủ đề \"\((pack?.name.localName) ?? "")\"")
                    .setImageResId("icon_finish")
                    .setButtonOkText("Đóng")
                    .showIn(self.view)
            }
            return
        }
        if let packName = pack?.name.en {
            AppSettings.setFlashcardPlayIndex(topicName: packName, index: index)
        }
        switchToNextTopic()
    }
    
    private func switchToNextTopic() {
        finishedStatus = .next
        let endFlashcard = playFolder || FlashcardsManager.shared.getNextPack(check15minutes: true) == nil
        if endFlashcard {
            dismiss(animated: true, completion: nil)
            navigationController?.popViewController(animated: true)
        } else {
            loadData(check15min: true)
        }
    }
}

// MARK: - Helper Structs and Enums


struct ItemData {
    var name: Name?
    var path: String?
    var sound: String?
}

enum FinishedStatus {
    case started
    case restarted
    case next
    case wait
}

// MARK: - ArcView
class ArcView: UIView {
    private let shapeLayer = CAShapeLayer()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupShapeLayer()
    }
    
    required init?(coder: NSCoder) {
        super.init(coder: coder)
        setupShapeLayer()
    }
    
    private func setupShapeLayer() {
        shapeLayer.fillColor = UIColor(hex: "#FF7761").cgColor
        shapeLayer.strokeColor = nil
        layer.addSublayer(shapeLayer)
    }
    
    override func layoutSubviews() {
        super.layoutSubviews()
        let radius = min(bounds.width, bounds.height) / 2
        let center = CGPoint(x: bounds.width / 2, y: bounds.height / 2)
        let path = UIBezierPath(arcCenter: center,
                              radius: radius,
                              startAngle: -CGFloat.pi / 2,
                              endAngle: -CGFloat.pi / 2 + 360 * CGFloat.pi / 180,
                              clockwise: true)
        shapeLayer.path = path.cgPath
    }
    
    func animateArc(finishAction: @escaping () -> Void) {
        let animation = CABasicAnimation(keyPath: "endAngle")
        animation.toValue = -CGFloat.pi / 2
        animation.duration = 5.0
        animation.timingFunction = .linear
        animation.isRemovedOnCompletion = false
        animation.fillMode = .forwards
        shapeLayer.add(animation, forKey: "arcAnimation")
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
            finishAction()
        }
    }
    
    func stopAnimations() {
        shapeLayer.removeAnimation(forKey: "arcAnimation")
    }
}
