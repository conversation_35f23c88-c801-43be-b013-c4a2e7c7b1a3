//
//  toancoban_list_sosanhluongdovat.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_sosanhluongdovat: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var itemContainer: UIView!
    private var meIndex: Int = 0
    private var items: [Item] = []
    private var folder: String = ""
    private var nhieuHon: Bool = false
    private var count: Int = 0
    private var count1: Int = 0
    private let rightBg = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        rightBg.alpha = 0
        rightBg.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(rightBg)
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(gridLayout.snp.height).multipliedBy(0.5) // Ratio 0.5
        }
        rightBg.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(gridLayout)
        }
        
        itemContainer = UIView()
        //itemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
            make.left.equalToSuperview().inset(view.frame.height * 0.1) // Margin left 10% height
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        nhieuHon = Bool.random()
        let packs = FlashcardsManager.shared.getPacks().shuffled()
        for pack in packs {
            if pack.folder.lowercased().contains("fruit") { continue }
            let filteredItems = pack.items.filter { $0.counting == 1 }.shuffled().prefix(2)
            if filteredItems.count == 2 {
                items = Array(filteredItems)
                folder = pack.folder
                break
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        let svgPaths = [items[0].path, items[1].path].map { "topics/\(folder)/\($0!)" }
        let svgImages = svgPaths.map { Utilities.GetSVGKImage(named: $0) }
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = createItemSvgWithShadow(svg: svgImages[i])
            views.append(view)
            let finalI = i
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            view.alpha = 0
        }
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        count = 10 + Int.random(in: 0..<6) // 10 to 15
        var values: [Int] = []
        count1 = 0
        while true {
            values = (0..<count).map { _ in Int.random(in: 0..<2) }
            count1 = values.filter { $0 == 1 }.count
            if count1 != count - count1 { break }
        }
        meIndex = count1 > count - count1 ? 1 : 0 // 0: left, 1: right
        if !nhieuHon { meIndex = 1 - meIndex }
        
        let points = PointsHelper.getPoints(size: count, container: itemContainer)
        let size = min(itemContainer.frame.height, itemContainer.frame.width) / 4
        for i in 0..<count {
            let scale = 1 - CGFloat.random(in: 0..<0.3)
            let svgView = UIImageView(image: svgImages[values[i]].uiImage)
            svgView.contentMode = .scaleAspectFit
            itemContainer.addSubview(svgView)
            svgView.snp.makeConstraints { make in
                make.width.height.equalTo(size * scale)
                make.center.equalToSuperview()
            }
            svgView.transform = CGAffineTransformMakeTranslation(
                CGFloat(points[i].x) - itemContainer.frame.width / 2,
                CGFloat(points[i].y) - itemContainer.frame.height / 2
            )
        }
        
        itemContainer.moveToCenter(of: self, duration: 0)
        scheduler.schedule(after: 0.1) { [weak self] in
            guard let self = self else { return }
            self.itemContainer.alpha = 1
        }
        gridLayout.alpha = 0
        
        var delay = playSound(openGameSound(), nhieuHon ? "toan/toan_so sanh do_do nhieu" : "toan/toan_so sanh do_do it")
        UIView.animate(withDuration: 0.6, delay: delay) {
            self.itemContainer.transform = .identity // translationX(0)
        }
        UIView.animate(withDuration: 0.5, delay: delay + 0.5) {
            self.gridLayout.alpha = 1
            self.rightBg.alpha = 1
        }
        delay += 1
        delay += self.gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(nhieuHon ? "toan/toan_so sanh do_do nhieu" : "toan/toan_so sanh do_do it")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Item Creation
    private func createItemSvgWithShadow(svg: SVGKImage) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(0.9) // Ratio 0.9
        }
        
        let svgView = UIImageView(image: svg.uiImage)
        svgView.contentMode = .scaleAspectFit
        viewBackground.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(svgView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return container
    }
    
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let svgView = view.subviews.first?.subviews.first else { return }
        pauseGame()
        let index = gridLayout.subviews.firstIndex(of: view) ?? 0
        let correct = index == meIndex
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: svgView)
            delay += playSound(
                delay: delay,
                names: [
                    answerCorrect1EffectSound(),
                    getCorrectHumanSound(),
                    "topics/\(folder)/\(items[index].path!.replacingOccurrences(of: ".svg", with: ""))",
                    nhieuHon ? "toan/nhiều hơn" : "toan/ít hơn",
                    endGameSound()
                ]
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}
