//
//  toancoban_list_ngonnen.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 26/3/25.
//


import UIKit
import SnapKit

class toancoban_list_ngonnen: NhanBietGameFragment {
    // MARK: - Properties
    private let ids: [UIImage] = [
        Utilities.SVGImage(named: "math_1nen_1"),
        Utilities.SVGImage(named: "math_1nen_2"),
        Utilities.SVGImage(named: "math_1nen_3")
    ]
    private var meIndex: Int = 0
    private var imageViews: [UIImageView] = [UIImageView(), UIImageView(), UIImageView()]
    private var imageFires: [UIImageView] = [UIImageView(), UIImageView(), UIImageView()] // Thay SVGAutosizeView bằng UIView
    private var values: [Int]?
    private var itemContainer: UIView?
    private var backgroundImageView: UIImageView?

    // MARK: - Setup Layout from XML
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 60/255, green: 44/255, blue: 54/255, alpha: 1.0) // #3C2C36
        
        // Background ImageView
        backgroundImageView = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_door"))
        backgroundImageView?.contentMode = .scaleAspectFill // Thay centerCrop
        backgroundImageView?.clipsToBounds = false
        addSubview(backgroundImageView!)
        backgroundImageView?.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Intermediate ConstraintLayout (height_percent="0.65")
        let intermediateContainer = UIView()
        addSubview(intermediateContainer)
        intermediateContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.65)
            make.centerY.equalToSuperview()
        }

        // Item Container
        itemContainer = UIView()
        itemContainer?.isUserInteractionEnabled = true
    
        intermediateContainer.addSubview(itemContainer!)
        itemContainer?.makeViewCenterAndKeep(ratio: 2.5)
        
        // ImageViews trong item_container
        for (index, imageView) in imageViews.enumerated() {
            imageView.isUserInteractionEnabled = true
            imageView.contentMode = .scaleAspectFit
            itemContainer?.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.height.equalTo(itemContainer!.snp.height).multipliedBy(0.9) // Height 90%
                make.width.equalTo(imageView.snp.height).multipliedBy(374.7 / 652.1) // Tỷ lệ 374.7:652.1
                make.centerY.equalToSuperview()

                // Horizontal bias
                switch index {
                case 0:
                    make.centerX.equalToSuperview().multipliedBy(0.15 * 2) // Bias 0.15
                case 1:
                    make.centerX.equalToSuperview() // Bias mặc định (0.5)
                case 2:
                    make.centerX.equalToSuperview().multipliedBy(0.85 * 2) // Bias 0.85
                default:
                    break
                }
            }
        }

        // Guideline (line_top)
        let guideline = UIView()
        itemContainer?.addSubview(guideline)
        guideline.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.11) // Guide_percent 0.11
            make.height.equalTo(0)
        }

        // ImageFires (SVGAutosizeView)
        for (index, imageFire) in imageFires.enumerated() {
            imageFire.backgroundColor = UIColor.black.withAlphaComponent(0.0006) // #0f00
            itemContainer?.addSubview(imageFire)
            imageFire.snp.makeConstraints { make in
                make.height.equalTo(itemContainer!.snp.height).multipliedBy(0.6) // Height 60%
                make.width.equalTo(imageFire.snp.height).multipliedBy(323.0 / 792.0) // Tỷ lệ 323:792
                make.bottom.equalTo(guideline)

                // Horizontal bias
                switch index {
                case 0:
                    make.centerX.equalToSuperview().multipliedBy(0.15 * 2) // Bias 0.19
                case 1:
                    make.centerX.equalToSuperview() // Bias mặc định (0.5)
                case 2:
                    make.centerX.equalToSuperview().multipliedBy(0.85 * 2) // Bias 0.81
                default:
                    break
                }
            }
        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true
    }

    // MARK: - GameFragment Methods
    override open func createGame() {
        super.createGame()
    }

    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        setupLayout()
    }

    override open func updateData() {
        super.updateData()
        values = Utils.generatePermutation(3)
        meIndex = Int.random(in: 0..<values!.count)
        
        let delay = playSound(openGameSound(), getLanguage() + "/toan/toan_1_nen\(values![meIndex] + 1)")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }

        for (index, imageView) in imageViews.enumerated() {
            imageView.image = ids[values![index]]
            imageView.isExclusiveTouch = true // Đảm bảo chỉ một gesture nhận sự kiện
            imageView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(imageTapped(_:))))
        }
    }

    // MARK: - Event Handling
    @objc private func imageTapped(_ sender: UITapGestureRecognizer) {
        guard let view = sender.view, let index = imageViews.firstIndex(of: view as! UIImageView) else { return }
        
        if index == meIndex {
            animateCoinIfCorrect(view: view)
            pauseGame()
            
            // Giả lập SVG animation
            let animationDuration: TimeInterval = 1.0 // Giả định thời gian animation
            var delay = 0.0
            var svgs =  Utilities.svgFrames(named: "animations/animation_fire.svg")
            for i in 0..<4 {
                for j in 0..<svgs.count { // Giả định 3 frame SVG
                    delay += 0.1
                    scheduler.schedule(delay: delay) { [weak self] in
                        guard let self = self else { return }
                        self.imageFires[self.meIndex].image = svgs[j]
                    }
                }
            }
            
            let flameDelay = playSound("effect/flame")
            var totalDelay = max(flameDelay, animationDuration)
            totalDelay += playSound(delay: totalDelay, names: finishCorrect1Sounds())
            scheduler.schedule(delay: totalDelay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(chooseWrongSounds())
            pauseGame()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }

    // MARK: - Override Methods
    override open func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound( getLanguage() + "/toan/toan_1_nen\(values![meIndex] + 1)")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}
