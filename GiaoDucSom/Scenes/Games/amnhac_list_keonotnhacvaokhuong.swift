//
//  amnhac_list_keonotnhacvaokhuong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 19/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class amnhac_list_keonotnhacvaokhuong: MusicGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var btnPlay: KUButton!
    private var placeHolder1: UIImageView! = UIImageView()
    private var placeHolder2: UIImageView! = UIImageView()
    private var placeHolder3: UIImageView! = UIImageView()
    private var placeHolder4: UIImageView! = UIImageView()
    private var imageNote1: UIImageView! = UIImageView()
    private var imageNote2: UIImageView! = UIImageView()
    private var imageNote3: UIImageView! = UIImageView()
    private var imageNote4: UIImageView! = UIImageView()
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private let notes: [String] = ["C", "D", "E", "F", "G", "A", "B"]
    private var items: [Int] = []
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 237/255, green: 251/255, blue: 255/255, alpha: 1) // #EDFBFF
        
        let topContainer = UIView()
        topContainer.clipsToBounds = false
        view.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        btnPlay = KUButton()
        btnPlay.setImage(Utilities.SVGImage(named: "music_btn_playback"), for: .normal)
        btnPlay.addTarget(self, action: #selector(playButtonTapped), for: .touchUpInside)
        topContainer.addSubview(btnPlay)
        btnPlay.snp.makeConstraints { make in
            make.width.equalTo(btnPlay.snp.height) // Ratio 1:1
            make.height.equalTo(topContainer).multipliedBy(0.5)
            make.right.equalToSuperview().multipliedBy(0.95)
            make.centerY.equalToSuperview()
        }
        let leftContainer = UIView()
        topContainer.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.right.equalTo(btnPlay.snp.left)
        }
        
        let leftPaddingView = UIView()
        leftContainer.addSubviewWithPercentInset(subview: leftPaddingView, percentInset: 5)
        
        let musicContainer = UIView()
        leftPaddingView.addSubview(musicContainer)
        musicContainer.makeViewCenterAndKeep(ratio: 5.3)
                
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        musicContainer.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(musicContainer).multipliedBy(0.98)
            make.height.equalTo(musicContainer)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg").withRenderingMode(.alwaysTemplate))
        barline.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(musicContainer)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg").withRenderingMode(.alwaysTemplate))
        endline.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(musicContainer)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg").withRenderingMode(.alwaysTemplate))
        clef.contentMode = .scaleAspectFill
        clef.tintColor = UIColor.color(hex: "#74B6FF")
        musicContainer.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(musicContainer)
            make.centerX.equalToSuperview().multipliedBy(0.13) // Bias 0.05
            make.centerY.equalToSuperview()
        }
        
        
        // Khai báo và xử lý placeholders
        let placeholderViews = [
            ("place_holder_1", 0.2),
            ("place_holder_2", 0.45),
            ("place_holder_3", 0.7),
            ("place_holder_4", 0.95)
        ]
        
        placeHolder1 = UIImageView(image: Utilities.SVGImage(named: "music_giaidieu_soannhac"))
        placeHolder2 = UIImageView(image: Utilities.SVGImage(named: "music_giaidieu_soannhac"))
        placeHolder3 = UIImageView(image: Utilities.SVGImage(named: "music_giaidieu_soannhac"))
        placeHolder4 = UIImageView(image: Utilities.SVGImage(named: "music_giaidieu_soannhac"))
        
        let placeholders = [placeHolder1, placeHolder2, placeHolder3, placeHolder4]
        
        for (index, (tag, bias)) in placeholderViews.enumerated() {
            let placeholder = placeholders[index]!
            placeholder.stringTag = tag
            musicContainer.addSubview(placeholder)
            placeholder.snp.makeConstraints { make in
                make.width.equalTo(placeholder.snp.height) // Ratio 1:1
                make.height.equalTo(musicContainer)
                //make.centerX.equalToSuperview().multipliedBy(bias * 2)
                make.centerY.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                placeholder.snapToHorizontalBias(horizontalBias: bias)
            }
        }
        
        // Khai báo và xử lý image notes
        imageNote1 = UIImageView()
        imageNote2 = UIImageView()
        imageNote3 = UIImageView()
        imageNote4 = UIImageView()
        imageNote1.contentMode = .scaleAspectFit
        imageNote2.contentMode = .scaleAspectFit
        imageNote3.contentMode = .scaleAspectFit
        imageNote4.contentMode = .scaleAspectFit
        
        let imageNotes = [imageNote1, imageNote2, imageNote3, imageNote4]
        
        for (index, imageNote) in imageNotes.enumerated() {
            imageNote?.stringTag = "image_note_\(index + 1)"
            imageNote?.tintColor = UIColor.color(hex: "#74B6FF")
            musicContainer.addSubview(imageNote!)
            imageNote?.snp.makeConstraints { make in
                make.edges.equalTo(placeholders[index]!)
            }
        }
        
        let bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        view.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        let bottomBg = UIView()
        bottomBg.backgroundColor =  UIColor.color(hex: "#D6FAFF")
        bottomContainer.addSubview(bottomBg)
        bottomBg.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.bottom.right.equalTo(self)
        }
        
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor.black.withAlphaComponent(0.31) // #4f00
        gridLayout.clipsToBounds = false
        bottomContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.height.equalTo(musicContainer.snp.height)
            make.centerY.centerX.equalToSuperview()
            make.width.equalTo(gridLayout.snp.height).multipliedBy(5.3) // Ratio 5.3:1
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleGridPan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    
    func loadPiano() {
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            for note in notes {
                if let url = Utilities.url(soundPath: "effect/music/piano_2\(note.lowercased())1") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        items = []
        for _ in 0..<4 {
            items.append(Int.random(in: 0..<7))
        }
        
        var views: [UIView] = []
        for i in 0..<items.count {
            let view = createItemGhepCap(note: notes[items[i]])
            view.stringTag = "\(items[i])"
            views.append(view)
        }
        
        gridLayout.backgroundColor = .clear
        gridLayout.columns = items.count
        gridLayout.insetRatio = 0
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.reloadItemViews(views: views.shuffled())
        
        placeHolder1.stringTag = "\(items[0])"
        placeHolder2.stringTag = "\(items[1])"
        placeHolder3.stringTag = "\(items[2])"
        placeHolder4.stringTag = "\(items[3])"
        
        var delay: TimeInterval = 1.0
        delay += playSound("music/keonotnhac")
        for i in 0..<items.count {
            let note = notes[items[i]].lowercased()
            scheduler.schedule(after: delay) { [weak self] in
                self?.playSound("effect/music/piano_2\(note)1")
            }
            delay += 1.0
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            var delay: TimeInterval = playSound("music/keonotnhac")
            for i in 0..<items.count {
                let note = notes[items[i]].lowercased()
                scheduler.schedule(after: delay) { [weak self] in
                    self?.playSound("effect/music/piano_2\(note)1")
                }
                delay += 1.0
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    
    var originX = 0.0 , originY: CGFloat = 0.0
    @objc private func handleGridPan(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.layer.zPosition = CGFloat.greatestFiniteMagnitude
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                originX = currentView.frame.minX
                originY = currentView.frame.minY
            }
            
        case .changed:
            if let currentView = currentView {
                let translation = gesture.translation(in: view)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y + translation.y
                )
                gesture.setTranslation(.zero, in: view)
            }
            
        case .ended:
            if let currentView = currentView {
                let placeHolders = [placeHolder1, placeHolder2, placeHolder3, placeHolder4]
                var minDistance = CGFloat.greatestFiniteMagnitude
                var minView: UIView?
                
                for placeholder in placeHolders {
                    let vector = currentView.distanceFromCenterToCenter(to: placeholder!)
                    let distance = hypot(vector.x, vector.y)
                    if distance < minDistance {
                        minDistance = distance
                        minView = placeholder
                    }
                }
                
                if let minView = minView, minDistance < currentView.frame.height / 2, let destTag = minView.stringTag, let srcTag = currentView.stringTag {
                    let dest = Int(destTag) ?? 0
                    let src = Int(srcTag) ?? 0
                    if dest == src {
                        minView.stringTag = nil
                        let finish = placeHolders.allSatisfy { $0?.stringTag == nil }
                        let imageNotes = [imageNote1, imageNote2, imageNote3, imageNote4]
                        let ids = ["2c1", "2d1", "2e1", "2f1", "2g1", "2a1", "2b1"]
                        let index = placeHolders.firstIndex(of: minView as! UIImageView) ?? 0
                        
                        playSound("effect/word puzzle drop")
                        UIView.animate(withDuration: 0.2) {
                            currentView.alpha = 0
                        }
                        currentView.moveToCenter(of: minView, duration: 0.2) {
                            [weak self] _ in
                            guard let self = self else { return }
                            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                            imageNotes[index]?.image = Utilities.SVGImage(named: "music/note/\(ids[src]).svg").withRenderingMode(.alwaysTemplate)
                            imageNotes[index]?.tintColor = UIColor.color(hex: "#74B6FF")
                            self.scheduler.schedule(after: 0.3) { [weak self] in
                                self?.playSound("effect/music/piano_2\(self?.notes[src].lowercased() ?? "")1")
                            }
                        }
                        
                        if finish {
                            pauseGame()
                            var delay: TimeInterval = 1.0
                            delay += playSound(delay: 0, names: ["effect/answer_end", getCorrectHumanSound()])
                            for i in 0..<items.count {
                                let note = notes[items[i]].lowercased()
                                scheduler.schedule(after: delay) { [weak self] in
                                    imageNotes[i]?.tintColor = nil
                                    self?.playSound("effect/music/man_2\(note)\(note == "b1" ? "_vi" : "")1")
                                }
                                delay += 1.0
                            }
                            animateCoinIfCorrect(view: gridLayout)
                            scheduler.schedule(after: delay + 1.0) { [weak self] in
                                self?.finishGame()
                            }
                        }
                    } else {
                        setGameWrong()
                        playSound("effect/answer_wrong")
                        UIView.animate(withDuration: 0.2) {
                            currentView.transform = .identity
                            currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                        }
                    }
                } else {
                    playSound("effect/answer_wrong")
                    UIView.animate(withDuration: 0.2) {
                        currentView.transform = .identity
                        currentView.frame = CGRectMake(self.originX, self.originY, currentView.frame.width, currentView.frame.height)
                    }
                }
                self.currentView = nil
            }
            
        default:
            break
        }
    }
    
    @objc private func playButtonTapped() {
        pauseGame()
        var delay: TimeInterval = 0
        for i in 0..<items.count {
            let note = notes[items[i]].lowercased()
            scheduler.schedule(after: delay) { [weak self] in
                self?.playSound("effect/music/piano_2\(note)1")
            }
            delay += 1.0
        }
        scheduler.schedule(after: delay) { [weak self] in
            self?.resumeGame()
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for i in (0..<gridLayout.subviews.count).reversed() {
            let child = gridLayout.subviews[i]
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
    
    private func createItemGhepCap(note: String) -> UIView {
        let view = UIView()
        let container = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_card1"))
        view.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 1)
        
        let bgWhite = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_card2"))
        container.addSubview(bgWhite)
        bgWhite.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let innerContainer = UIView()
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height).multipliedBy(1.5) // Ratio 1.5:1
            make.width.equalTo(container).multipliedBy(0.95)
            make.center.equalToSuperview()
        }
        
        let viewImage = UIView()
        viewImage.stringTag = "view_image"
        viewImage.isHidden = true
        innerContainer.addSubview(viewImage)
        viewImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        viewImage.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.width.equalTo(viewImage).multipliedBy(0.94)
            make.height.equalTo(viewImage)
            make.center.equalToSuperview()
        }
        
        let barline = UIImageView(image: Utilities.SVGImage(named: "music/note/barline.svg").withRenderingMode(.alwaysTemplate))
        barline.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(barline)
        barline.snp.makeConstraints { make in
            make.width.equalTo(barline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.left.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let endline = UIImageView(image: Utilities.SVGImage(named: "music/note/endline.svg").withRenderingMode(.alwaysTemplate))
        endline.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(endline)
        endline.snp.makeConstraints { make in
            make.width.equalTo(endline.snp.height).multipliedBy(0.1) // Ratio 0.1:1
            make.height.equalTo(viewImage)
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        let imageNote = UIImageView(image: Utilities.SVGImage(named: "music/note/8b1.svg").withRenderingMode(.alwaysTemplate))
        imageNote.stringTag = "image_note"
        imageNote.tintColor = UIColor.color(hex: "#74B6FF")
        viewImage.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.width.equalTo(imageNote.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(1.3) // Bias 0.65
            make.centerY.equalToSuperview()
        }
        
        let clef = UIImageView(image: Utilities.SVGImage(named: "music/note/music_clef.svg").withRenderingMode(.alwaysTemplate))
        clef.tintColor = UIColor.color(hex: "#74B6FF")
        clef.contentMode = .scaleAspectFill
        viewImage.addSubview(clef)
        clef.snp.makeConstraints { make in
            make.width.equalTo(clef.snp.height).multipliedBy(50.0 / 130.0) // Ratio 50:130
            make.height.equalTo(viewImage)
            make.centerX.equalToSuperview().multipliedBy(0.3) // Bias 0.15
            make.centerY.equalToSuperview()
        }
        
        let viewText = HeightRatioTextView()
        viewText.setHeightRatio(0.7)
        viewText.stringTag = "view_text"
        viewText.text = note
        viewText.font = .Freude(size: 20)
        viewText.textColor = UIColor.color(hex: "#74B6FF")
        viewText.textAlignment = .center
        viewText.backgroundColor = .clear
        container.addSubview(viewText)
        viewText.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.8)
            make.height.equalTo(container).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9) // Bias 0.3
        }
        
        return view
    }
}

struct R19 {
    struct drawable {
        static let music_btn_playback = 1
        static let music_bg = 2
        static let ic_barline = 3
        static let endline = 4
        static let music_giaidieu_soannhac = 5
        static let music_clef = 6
        static let ic_2c1 = 7
        static let ic_2d1 = 8
        static let ic_2e1 = 9
        static let ic_2f1 = 10
        static let ic_2g1 = 11
        static let ic_2a1 = 12
        static let ic_2b1 = 13
        static let nhanbiet_bg_card1 = 14
        static let nhanbiet_bg_card2 = 15
        static let ic_8b1 = 16
        static let music_keonotnhac = 17
        static let music_item_ghepcap = 18
    }
}

extension Int {
    func toDrawableNameKNN() -> String? {
        switch self {
        case R19.drawable.music_btn_playback:
            return "music_btn_playback"
        case R19.drawable.music_bg:
            return "music_bg"
        case R19.drawable.ic_barline:
            return "barline"
        case R19.drawable.endline:
            return "endline"
        case R19.drawable.music_giaidieu_soannhac:
            return "music_giaidieu_soannhac"
        case R19.drawable.music_clef:
            return "music_clef"
        case R19.drawable.ic_2c1:
            return "2c1"
        case R19.drawable.ic_2d1:
            return "2d1"
        case R19.drawable.ic_2e1:
            return "2e1"
        case R19.drawable.ic_2f1:
            return "2f1"
        case R19.drawable.ic_2g1:
            return "2g1"
        case R19.drawable.ic_2a1:
            return "2a1"
        case R19.drawable.ic_2b1:
            return "2b1"
        case R19.drawable.nhanbiet_bg_card1:
            return "nhanbiet_bg_card1"
        case R19.drawable.nhanbiet_bg_card2:
            return "nhanbiet_bg_card2"
        case R19.drawable.ic_8b1:
            return "8b1"
        case R19.drawable.music_keonotnhac:
            return "music_keonotnhac"
        case R19.drawable.music_item_ghepcap:
            return "music_item_ghepcap"
        default:
            return "empty"
        }
    }
}

