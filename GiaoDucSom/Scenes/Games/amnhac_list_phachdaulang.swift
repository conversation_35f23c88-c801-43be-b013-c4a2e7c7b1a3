
//
//  amnhac_list_phachdaulang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 21/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class amnhac_list_phachdaulang: MusicGameFragment {
    // MARK: - Properties
    private var startPoint: Int = 0
    private var svgView: SVGKFastImageView!
    private var imageOrigin: UIImageView!
    private var imageDrag: UIImageView!
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var itemContainer: UIView!
    private var imageNote: UIImageView!
    private var phach: String = ""
    private var meIndex: Int = 0
    private var originX: CGFloat = 0
    private var snapIndex: Int = 0
    private var noteToMusicId: [String: AVAudioPlayer] = [:]
    var loadingSounds = false
    let coinView = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 237/255, green: 251/255, blue: 255/255, alpha: 1) // #EDFBFF
        
        let outerContainer = UIView()
        outerContainer.clipsToBounds = false
        view.addSubviewWithPercentInset(subview: outerContainer, percentInset: 5)
        
        let innerContainer = UIView()
        //innerContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        innerContainer.clipsToBounds = false
        outerContainer.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 2)
        
        let noteContainer = UIView()
        noteContainer.clipsToBounds = false
        innerContainer.addSubview(noteContainer)
        noteContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer).multipliedBy(0.933)
            make.height.equalTo(innerContainer).multipliedBy(0.8)
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        let bgMusic = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg.svg"))
        bgMusic.alpha = 0.15
        bgMusic.tintColor = UIColor.color(hex: "#91A3AF")
        noteContainer.addSubview(bgMusic)
        bgMusic.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let lineLeft = UIView()
        noteContainer.addSubview(lineLeft)
        lineLeft.snp.makeConstraints { make in
            make.width.equalTo(0)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.03)
        }
        
        imageNote = UIImageView()
        imageNote.tintColor = UIColor.color(hex: "#74B6FF")
        noteContainer.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.width.equalTo(imageNote.snp.height).multipliedBy(5.0 / 13.0) // Ratio 5:13
            make.center.equalTo(lineLeft)
            make.top.bottom.equalToSuperview()
        }
        
        let viewLine = UIView()
        viewLine.alpha = 0.15
        viewLine.backgroundColor = UIColor.color(hex: "#91A3AF")
        noteContainer.addSubview(viewLine)
        viewLine.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.14)
            //make.bottom.equalToSuperview().multipliedBy(0.975) // verticalBias=0.975
        }
        addActionOnLayoutSubviews {
            viewLine.snapToVerticalBias(verticalBias: 0.99)
        }
        
        svgView = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "music/note/phach_bg.svg"))
        noteContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.width.equalTo(svgView.snp.height).multipliedBy(400.0/27.2) // Ratio 12:1
            make.bottom.equalToSuperview()
        }
        
        let lineTop = UIView()
        innerContainer.addSubview(lineTop)
        lineTop.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.height.equalTo(0)
            make.bottom.equalToSuperview().multipliedBy(0.05)
        }
        
        let dragContainer = UIView()
        dragContainer.clipsToBounds = false
        innerContainer.addSubview(dragContainer)
        dragContainer.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(lineTop)
            make.height.equalTo(innerContainer)
        }
        
        itemContainer = UIView()
        itemContainer.alpha = 0
        itemContainer.clipsToBounds = false
        dragContainer.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let biases: [(tag: String, bias: CGFloat)] = [
            ("0", 0.0), ("0.5", 0.125), ("1", 0.25), ("1.5", 0.375),
            ("2", 0.5), ("3", 0.75), ("4", 1.0)
        ]
        
        for (tag, bias) in biases {
            let item = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
            item.contentMode = .scaleAspectFit
            item.stringTag = tag
            itemContainer.addSubview(item)
            item.snp.makeConstraints { make in
                make.width.equalTo(item.snp.height) // Ratio 1:1
                make.height.equalTo(itemContainer).multipliedBy(0.15)
                make.bottom.equalToSuperview().multipliedBy(0.95) // verticalBias=0.9
            }
            addActionOnLayoutSubviews {
                item.snapToHorizontalBias(horizontalBias: bias)
            }
        }
        
        imageOrigin = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
        imageOrigin.contentMode = .scaleAspectFit
        imageOrigin.alpha = 0
        dragContainer.addSubview(imageOrigin)
        imageOrigin.snp.makeConstraints { make in
            make.width.equalTo(imageOrigin.snp.height) // Ratio 1:1
            make.height.equalTo(dragContainer).multipliedBy(0.15)
            make.bottom.equalToSuperview().multipliedBy(0.95) // verticalBias=0.9
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.imageOrigin.snapToHorizontalBias(horizontalBias: 0.0)
        }
        
        imageDrag = UIImageView(image: Utilities.SVGImage(named: "toan_slider_blue"))
        imageDrag.contentMode = .scaleAspectFit
        dragContainer.addSubview(imageDrag)
        imageDrag.snp.makeConstraints { make in
            make.width.equalTo(imageDrag.snp.height) // Ratio 1:1
            make.height.equalTo(dragContainer).multipliedBy(0.15)
            make.bottom.equalToSuperview().multipliedBy(0.95) // verticalBias=0.9
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.imageDrag.snapToHorizontalBias(horizontalBias: 0.0)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleDragPan(_:)))
        imageDrag.addGestureRecognizer(panGesture)
        imageDrag.isUserInteractionEnabled = true
        
        loadPiano()
        
        self.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    func loadPiano() {
        loadingSounds = true
        // Initialize SoundPool equivalent with AVAudioPlayer
        DispatchQueue.global(qos: .userInitiated).async { [weak self] in
            guard let self = self else { return }
            let notes = ["not05", "not1", "not15", "not2", "not3", "not4"]
            for note in notes {
                if let url = Utilities.url(soundPath: "vi/music/\(note)") {
                    do {
                        let player = try AVAudioPlayer(contentsOf: url)
                        player.prepareToPlay()
                        noteToMusicId[note] = player
                    } catch {
                        print("Error loading sound for \(note)")
                    }
                }
            }
            loadingSounds = false
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        var delay: TimeInterval = playSound(openGameSound())
        meIndex = Int.random(in: 0..<6)
        phach = ["05", "1", "15", "2", "3", "4"][meIndex]
        delay += playSound(name: "music/phachdaulang", delay: delay)
        delay += playSound(name: "music/phach\(phach)", delay: delay)
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }        
        
        self.svgView.image = Utilities.GetSVGKImage(named: "music/note/phach_bg.svg")
    }
    
    override func createGame() {
        super.createGame()
        snap(index: startPoint)
        originX = imageDrag.frame.minX
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            var delay: TimeInterval = 0.3
            delay += playSound(name: "music/phachdaulang", delay: delay)
            delay += playSound(name: "music/phach\(phach)", delay: delay)
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleDragPan(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        let location = gesture.location(in: view.superview)
        
        switch gesture.state {
        case .began:
            dX = view.frame.minX - location.x
            dY = view.frame.minY - location.y
            imageDrag.image = Utilities.SVGImage(named: "toan_slider_blue")
            imageNote.tintColor = UIColor.color(hex: "#74B6FF")
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            
        case .changed:
            var newX = location.x + dX
            if newX < 0 { newX = 0 }
            view.frame.origin.x = newX
            
            var minDistance = CGFloat.greatestFiniteMagnitude
            var target: UIView?
            var index = 0
            for i in 0..<itemContainer.subviews.count {
                let child = itemContainer.subviews[i]
                let vector = imageDrag.distanceFromCenterToCenter(to: child)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    target = child
                    index = i
                }
            }
            if minDistance < imageDrag.frame.width * 0.5 {
                snap(index: index)
            } else {
                snap(index: -1)
            }
            
        case .ended:
            var minDistance = CGFloat.greatestFiniteMagnitude
            var target: UIView?
            var index = 0
            for i in 0..<itemContainer.subviews.count {
                let child = itemContainer.subviews[i]
                let vector = imageDrag.distanceFromCenterToCenter(to: child)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    target = child
                    index = i
                }
            }
            if minDistance < imageDrag.frame.width * 0.5 {
                playSound("effect/word puzzle drop")
                let rightAnswer = index == meIndex + 1
                let snap = index != 0
                let targetView = target ?? view
                UIView.animate(withDuration: 0.2) { [weak self] in
                    self?.imageDrag.center = targetView.center
                } completion: { [weak self] _ in
                    guard let self = self else { return }
                    self.imageDrag.image = Utilities.SVGImage(named: !snap ? "toan_slider_blue" : rightAnswer ? "toan_slider_green" : "toan_slider_red")
                    self.imageNote.tintColor = UIColor.color(hex: !snap ? "#74B6FF" : rightAnswer ? "#87D657" : "#FF7760")
                    UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                }
                
                let dragDistance = abs(index - startPoint)
                if rightAnswer {
                    pauseGame()
                    var delay: TimeInterval = 0.5
                    delay += playSound(delay: delay, names: [answerCorrect1EffectSound(), getCorrectHumanSound(), "vi/music/lang\(phach)", endGameSound()])
                    scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.coinView.moveToCenter(of: imageDrag, duration: 0)
                        self.animateCoinIfCorrect(view: coinView)
                    }
                    
                    scheduler.schedule(after: delay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    setGameWrong()
                    playSound(delay: 0.5, names: ["effect/answer_wrong", getWrongHumanSound()])
                }
            } else {
                UIView.animate(withDuration: 0.8, delay: 0, options: .curveLinear) { [weak self] in
                    self?.imageDrag.frame.origin.x = self?.originX ?? 0
                }
                imageDrag.image = Utilities.SVGImage(named: "toan_slider_blue")
                imageNote.tintColor = UIColor.color(hex: "#74B6FF")
                playSound("effect/slide2")
                scheduler.schedule(after: 0.8) { [weak self] in
                    UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                }
            }
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func snap(index: Int) {
        var snapIndex = index
        if snapIndex == -1 { snapIndex = 0 }
        
        if svgView.image == nil {
            scheduler.schedule(after: 0.1) { [weak self] in
                self?.snap(index: snapIndex)
            }
            return
        }
        
        if snapIndex != self.snapIndex {
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
            // Placeholder for SVG path manipulation
            let svg = svgView.image!
            for i in 0..<7 {
                // Assuming SVG has 7 paths for phach_bg
                svg.caLayerTree.sublayers![i].setFillColor(color: snapIndex >= i ? UIColor.color(hex: "#74B6FF") : UIColor.color(hex: "#91A2AE"))
                 svg.caLayerTree.sublayers![i].opacity = 1
            }
            svgView.setNeedsDisplay()
            
            self.snapIndex = snapIndex
            let ids = ["", "1s", "2s", "3s", "4s", "6s", "8s"]
            imageNote.image = snapIndex == -1 ? nil : Utilities.SVGImage(named: snapIndex == 0 ? "empty": "music/note/\(ids[snapIndex]).svg").withRenderingMode(.alwaysTemplate)
            UIImpactFeedbackGenerator(style: .medium).impactOccurred()
        }
    }
}
