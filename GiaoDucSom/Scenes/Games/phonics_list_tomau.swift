//
//  phonics_list_tomau.swift
//  KidsUPTiengViet
//
//  Created by <PERSON><PERSON> on 26/01/2023.
//

import Foundation
import SVGKit
import UIKit
import ImageScrollView
import SnapKit

final class phonics_list_tomau : GameFragment{
    var svgPath  = "Coloring/0.svg"
    var image : ImageScrollView?
    var svgimage: SVGKImage?
    var previewImage = UIImageView()
    var pensView : UIControl?
    var colors: [CGColor] = []
    var placeHolderColor = UIColor(hexString: "#FAFAFA").cgColor
    var svgkimage = SVGKImage(contentsOf: Utilities.SVGURL(of: "selected color"))
    var selectedImage = SVGKImage(contentsOf: Utilities.SVGURL(of: "coloring selected"))
    private lazy var colorButton = SVGButton(SVGIcon: "coloring selected")
    
    var colorIndex = 0
    var colorViews : [UIView] = []
    override func configureLayout(_ view: UIView) {
        clipsToBounds = true
        backgroundColor = .color(hex: "#F4FAFF")
        image = ImageScrollView(frame: .zero)
        image?.maxScaleFromMinScale = 6
        addSubview(image!)
        addSubview(previewImage)
        addSubview(colorButton)
        colorButton.isHidden = true
        
        NotificationCenter.default.addObserver(self, selector: #selector(handleColorListChanged), name: .ColorSelectionChanged, object: nil)
        loadPens()
        image!.snp.makeConstraints { make in
            make.left.bottom.right.equalToSuperview()
            make.top.equalTo(penContainer.snp.bottom)
        }
        
        previewImage.snp.makeConstraints { make in
            make.left.top.equalToSuperview()
            make.width.height.equalTo(100)
        }
        colorButton.snp.makeConstraints{ make in
            make.right.top.equalToSuperview().inset(20)
            make.width.height.equalTo(50)
        }
    }
    @objc func handleColorListChanged(){
        loadPens()
    }
    var penImage = SVGKImage(contentsOf: Utilities.SVGURL(of: "pencil"))
    let penContainer = UIView()
    var tapGesture : UITapGestureRecognizer?
    func loadPens(){
        colorIndex = -1
        penContainer.removeAllSubviews()
        addSubview(penContainer)
        penContainer.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.15)
        }
        var scrollView = UIScrollView()
        scrollView.showsHorizontalScrollIndicator = false
        var contentView = UIView()
        penContainer.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalTo(scrollView)
            make.height.equalTo(scrollView)
        }
        
        var leftAnchor = contentView.snp.left
        var colors = ColorManager.shared.getSelectedColors()
        
        var index = 0
        for color in colors {
            let penView = UIView()
            let imageView = UIImageView()
            (penImage!.caLayerTree.sublayers![0] as! CAShapeLayer).fillColor = UIColor.color(hex: color).cgColor
            imageView.image = penImage?.uiImage
            imageView.transform = CGAffineTransformMakeScale(1, -1)
            penView.addSubviewWithInset(subview: imageView, inset: 0)
            contentView.addSubview(penView)
            penView.snp.makeConstraints { make in
                make.height.equalTo(contentView)
                make.bottom.equalToSuperview().multipliedBy(0.8)
                make.width.equalTo(penView.snp.height).multipliedBy(0.5)
                make.left.equalTo(leftAnchor).offset(20)
            }
            leftAnchor = penView.snp.right
            penView.isUserInteractionEnabled = true
            var tapGesture = UITapGestureRecognizer(target: self, action: #selector(penTapped))
            penView.addGestureRecognizer(tapGesture)
            penView.tag = index
            index += 1
        }
        contentView.snp.makeConstraints { make in
            make.right.equalTo(leftAnchor).offset(20)
        }
    }
    var selectedView : UIView?
    @objc func penTapped(_ sender: UITapGestureRecognizer) {
       if sender.state == .ended {
           if let selectedView = selectedView {
               UIView.animate(withDuration: 0.2, animations: {
                   selectedView.transform = CGAffineTransformMakeTranslation(0, 0)
               })
           }
           if let view = sender.view {
               UIView.animate(withDuration: 0.2, animations: {
                   view.transform = CGAffineTransformMakeTranslation(0, view.bounds.height/5)
               })
               selectedView = view
               colorIndex = view.tag
               print("View Tapped" )
           }
       }
   }
    
    var selectedPen: UIView?
    var selectedPenIndex = -1
    
    @objc func itemClick(_ sender : UITapGestureRecognizer){
        if colorIndex == -1 {
            return
        }
        if let imgview = image?.subviews[0] as? UIImageView{
            var p = sender.location(in: imgview)
            let scale = svgimage!.size.height / imgview.bounds.height
            p.x *= scale
            p.y *= scale
            let test = svgimage!.caLayerTree.hitTest(p)
            if let layer = test as? CAShapeLayer {
                if let fillColor = layer.fillColor {
                    if !fillColor.isBlack() {
                        var color = UIColor.color(hex:  ColorManager.shared.getSelectedColors()[colorIndex])
                        layer.fillColor = color.cgColor
                        imgview.image = svgimage?.uiImage
                        save()
                        playSound(name: "effects/true2")
                    }
                }
            }
        }
    }
    var isNeedShowHintButton: (Bool){false}
    override func createGame() {
        super.createGame()
        
        playSound(name: "effects/games/coloring")
        svgPath = game.values![0].value as! String
        svgimage  = SVGKImage(contentsOf: Utilities.SVGURL(of: svgPath))
        previewImage.image = svgimage?.uiImage
        previewImage.isHidden = true
        if let image = image {
            image.setup()
            load()
            image.display(image: svgimage!.uiImage)
            scheduler.schedule(delay: 0.1) {
                [weak self] in
                guard let self = self else { return }
                image.test()
            }
            image.imageContentMode = .aspectFit
            image.isUserInteractionEnabled = true
            image.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(itemClick(_:))))
        }
        resumeGame()
    }
    func load(){
        svgimage?.loadFrom(svgPath: svgPath)
    }
    func save(){
        var dictionary: [Int: UIColor] = [:]
        var index = -1
        for layer in svgimage!.caLayerTree.subviews {
            index += 1
            if let layer = layer as? CAShapeLayer {
                if let fillColor = layer.fillColor {
                    if !fillColor.isBlack() && !fillColor.same(as: placeHolderColor) {
                        dictionary[index] = UIColor(cgColor: fillColor)
                    }
                }
            }
        }
        let data = NSKeyedArchiver.archivedData(withRootObject: dictionary)
        do{
            try data.write(to: getDocumentsDirectory().appendingPathComponent(getFilename(text: svgPath)))
        }catch{
            
        }
    }
    let colorViewContainer = UIView()
    let blackOverlay = UIView()
    
    @objc func replay(){
        let vc = ConfirmDialogViewController()
        vc.setupPopupPresentation()
        vc.imageIcon.SVGBackgroundImage = "icon_notice"
        vc.labelTitle.text = "Reset colors"
        vc.labelMessage.text = "Do you want to reset all colors?\n"
        vc.confirmAction = {
            [weak self] in
            guard let self = self else { return }
            var dictionary: [Int: UIColor] = [:]
            let data = NSKeyedArchiver.archivedData(withRootObject: dictionary)
            do{
                try data.write(to: getDocumentsDirectory().appendingPathComponent(getFilename(text: self.svgPath)))
            }catch{
                
            }
            self.load()
            self.image?.display(image: svgimage!.uiImage)
        }
        self.parentViewController?.present(vc, animated: false, completion: {
            [weak self] in
            guard let self = self else { return }
            vc.presentPopup()
        })
    }
    
    var layerToCorrectColor: [CAShapeLayer: CGColor] = [:]
    
    deinit{
        NotificationCenter.default.removeObserver(self)
    }
}
extension CGColor {
    func isBlack() -> Bool {
        let count = numberOfComponents
        if count > 1 {
            if let components = components {
                for c in 0..<components.count-1 { // skip the alpha component
                    // All components are 0 for black
                    if components[c] != 0.0 {
                        return false
                    }
                }
                return true
            }
        }
        return false
    }
    func isWhite() -> Bool {
        let count = numberOfComponents
        if count > 1 {
            if let components = components {
                for c in 0..<components.count-1 { // skip the alpha component
                    // All components are 0 for black
                    if components[c] != 1.0 {
                        return false
                    }
                }
                return true
            }
        }
        return false
    }
    func same(as another: CGColor) -> Bool {
        let components = components ?? []
        let anotherComponents = another.components ?? []
        return zip(components, anotherComponents).allSatisfy { abs($0 - $1) < 1e-4 }
    }
}
extension ImageScrollView{
    func test(){
        for subview in self.subviews {
            if let gestures = subview.gestureRecognizers {
                for gesture in gestures{
                    if let gesture = gesture as? UITapGestureRecognizer {
                        gesture.isEnabled = false
                    }
                }
            }
        }
    }
}

extension UIColor
{
    var hue: CGFloat
    {
        var hue: CGFloat = 0
        var saturation: CGFloat = 0
        var brightness: CGFloat = 0
        var alpha: CGFloat = 0
        
        self.getHue(&hue,
                    saturation: &saturation,
                    brightness: &brightness,
                    alpha: &alpha)
        
        return hue
    }
}

extension UIView {
    var parentViewController: UIViewController? {
        // Starts from next (As we know self is not a UIViewController).
        var parentResponder: UIResponder? = self.next
        while parentResponder != nil {
            if let viewController = parentResponder as? UIViewController {
                return viewController
            }
            parentResponder = parentResponder?.next
        }
        return nil
    }
}

func getDocumentsDirectory() -> URL {
    let paths = FileManager.default.urls(for: .documentDirectory, in: .userDomainMask)
    return paths[0]
}
func getFilename(text: String)->String{
    return "\(DataManager.shared.currentProfile!.id)_\(text.replacingOccurrences(of: "/", with: "_")).save"
}
