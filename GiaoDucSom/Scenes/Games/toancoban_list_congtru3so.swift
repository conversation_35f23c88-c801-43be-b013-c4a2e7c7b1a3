//
//  toancoban_list_congtru3so.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit

class toancoban_list_congtru3so: NhanBietGameFragment {
    // MARK: - Properties
    private var textA: AutosizeLabel!
    private var textOperator1: AutosizeLabel!
    private var textB: AutosizeLabel!
    private var textOperator2: AutosizeLabel!
    private var textC: AutosizeLabel!
    private var textResult: AutosizeLabel!
    private var numpad: MathNumpad!
    private var a: Int = 0
    private var b: Int = 0
    private var c: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        let skyView = UIView()
        skyView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(skyView)
        
        
        let numpadContainer = UIView()
        view.addSubview(numpadContainer)
        numpadContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.35)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        numpadContainer.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let answerLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        view.addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.13)
            make.height.equalTo(answerLayout.snp.width)
            make.centerY.equalToSuperview()
        }
        answerLayout.waitForLayout {
            answerLayout.snapToHorizontalBias(horizontalBias: 0.6)
        }
        
        textResult = AutosizeLabel()
        textResult.text = "?"
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        answerLayout.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.width.equalTo(answerLayout).multipliedBy(0.75)
            make.height.equalTo(answerLayout).multipliedBy(0.75)
            make.center.equalToSuperview()
        }
        
        let equationContainer = UIView()
        view.addSubview(equationContainer)
        equationContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(answerLayout.snp.left)
        }
        
        let innerEquationContainer = UIView()
        equationContainer.addSubview(innerEquationContainer)
        innerEquationContainer.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.95)
            make.centerY.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalTo(innerEquationContainer.snp.height).multipliedBy(6.0) // Ratio 6:1
        }
        
        let textALayout = UIView()
        innerEquationContainer.addSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(textALayout.snp.width)
            make.left.top.bottom.equalToSuperview()
        }
        
        textA = AutosizeLabel()
        textA.text = "1"
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textA.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let operator1Layout = UIView()
        innerEquationContainer.addSubview(operator1Layout)
        operator1Layout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(operator1Layout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textALayout.snp.right)
        }
        
        textOperator1 = AutosizeLabel()
        textOperator1.text = "+"
        textOperator1.textColor = UIColor(hex: "#87D657")
        textOperator1.font = .Freude(size: 20)
        textOperator1.textAlignment = .center
        textOperator1.adjustsFontSizeToFitWidth = true
        textOperator1.minimumScaleFactor = 0.1
        textOperator1.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        operator1Layout.addSubview(textOperator1)
        textOperator1.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBLayout = UIView()
        innerEquationContainer.addSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(textBLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(operator1Layout.snp.right)
        }
        
        textB = AutosizeLabel()
        textB.text = "2"
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textB.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let operator2Layout = UIView()
        innerEquationContainer.addSubview(operator2Layout)
        operator2Layout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(operator2Layout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textBLayout.snp.right)
        }
        
        textOperator2 = AutosizeLabel()
        textOperator2.text = "-"
        textOperator2.textColor = UIColor(hex: "#74B6FF")
        textOperator2.font = .Freude(size: 20)
        textOperator2.textAlignment = .center
        textOperator2.adjustsFontSizeToFitWidth = true
        textOperator2.minimumScaleFactor = 0.1
        textOperator2.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        operator2Layout.addSubview(textOperator2)
        textOperator2.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textCLayout = UIView()
        innerEquationContainer.addSubview(textCLayout)
        textCLayout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(textCLayout.snp.width)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(operator2Layout.snp.right)
        }
        
        textC = AutosizeLabel()
        textC.text = "3"
        textC.textColor = UIColor(hex: "#74B6FF")
        textC.font = .Freude(size: 20)
        textC.textAlignment = .center
        textC.adjustsFontSizeToFitWidth = true
        textC.minimumScaleFactor = 0.1
        textC.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        textCLayout.addSubview(textC)
        textC.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let equalLayout = UIView()
        innerEquationContainer.addSubview(equalLayout)
        equalLayout.snp.makeConstraints { make in
            make.width.equalTo(innerEquationContainer).multipliedBy(1.0 / 6.0)
            make.height.equalTo(equalLayout.snp.width)
            make.top.bottom.right.equalToSuperview()
            make.left.equalTo(textCLayout.snp.right)
        }
        
        let equalText = AutosizeLabel()
        equalText.text = "="
        equalText.textColor = UIColor(hex: "#74B6FF")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalText.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        equalLayout.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        skyView.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(textResult.snp.centerX)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = Int.random(in: 0..<100)
            b = Int.random(in: -99..<100)
            c = Int.random(in: -99..<100)
            if a + b < 100 && a + b >= 0 && a + b + c < 100 && a + b + c >= 0 {
                break
            }
        }
        
        textA.text = String(a)
        let operator1 = b > 0 ? "+" : b < 0 ? "-" : Bool.random() ? "+" : "-"
        textOperator1.text = operator1
        textOperator1.textColor = UIColor(hex: operator1 == "+" ? "#87D657" : "#FF7760")
        textB.text = String(abs(b))
        let operator2 = c > 0 ? "+" : c < 0 ? "-" : Bool.random() ? "+" : "-"
        textOperator2.text = operator2
        textOperator2.textColor = UIColor(hex: operator2 == "+" ? "#87D657" : "#FF7760")
        textC.text = String(abs(c))
        
        numpad.setListener(self)
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toan_congtru3so")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_congtru3so")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_congtru3so: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        var adjustedValue = value
        if value > 99 {
            adjustedValue = value % 10
            numpad.reset(value: adjustedValue)
        }
        textResult.text = String(adjustedValue)
        textResult.textColor = UIColor(hex: "#74B6FF")
    }
    
    func onDelClick(value: Int) {
        textResult.text = String(value)
        textResult.textColor = UIColor(hex: "#74B6FF")
    }
    
    func onCheckClick(value: Int) {
        textResult.text = String(value)
        let correct = value == a + b + c
        textResult.textColor = UIColor(hex: correct ? "#87D657" : "#FF7760")
        pauseGame()
        if correct {
            animateCoinIfCorrect(view: textResult)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.textResult.text = "?"
                self?.textResult.textColor = UIColor(hex: "#74B6FF")
                self?.resumeGame()
            }
        }
    }
}
