//
//  toancoban_list_demngontay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit

class toancoban_list_demngontay: NhanBietGameFragment {
    // MARK: - Properties
    private var imageLeft: UIImageView!
    private var imageRight: UIImageView!
    private var value: Int = 0
    private var leftValue: Int = 0
    private var rightValue: Int = 0
    private var numpad: MathNumpad!
    private var answerText: UILabel!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 102/255, green: 53/255, blue: 53/255, alpha: 1) // #663535
        
        let groundView = UIView()        
        view.addSubview(groundView)
        groundView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.415)
        }
        
        let colorView = UIView()
        colorView.backgroundColor = .init(hex: "#482828")
        groundView.addSubview(colorView)
        colorView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.top.right.bottom.equalTo(self)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let answerLayout = UIImageView()
        answerLayout.isUserInteractionEnabled = true
        answerLayout.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        view.addSubview(answerLayout)
        answerLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.18)
            make.height.equalTo(answerLayout.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            answerLayout.snapToHorizontalBias(horizontalBias: 0.6)
        }
        
        answerText = AutosizeLabel()
        answerText.text = "0"
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        answerText.font = .Freude(size: 20)
        answerText.textAlignment = .center
        answerText.adjustsFontSizeToFitWidth = true
        answerText.minimumScaleFactor = 0.1
        answerLayout.addSubview(answerText)
        answerText.snp.makeConstraints { make in
            make.height.equalTo(answerLayout).multipliedBy(0.75)
            make.width.equalTo(answerLayout)
            make.center.equalToSuperview()
        }
        
        let handsContainer = UIView()
        view.addSubview(handsContainer)
        handsContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(answerLayout.snp.left)
        }
        
        imageLeft = UIImageView()
        imageLeft.contentMode = .scaleAspectFit
        imageLeft.transform = CGAffineTransform(scaleX: -1, y: 1) // scaleX="-1"
        handsContainer.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.width.equalTo(handsContainer).multipliedBy(0.5)
            make.height.equalTo(imageLeft.snp.width) // Ratio 1:1
            make.left.centerY.equalToSuperview()
        }
        
        imageRight = UIImageView()
        imageRight.contentMode = .scaleAspectFit
        handsContainer.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.width.equalTo(handsContainer).multipliedBy(0.5)
            make.height.equalTo(imageRight.snp.width) // Ratio 1:1
            make.right.centerY.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        value = 1 + Int.random(in: 0..<10)
        while true {
            leftValue = Int.random(in: 0...value)
            rightValue = value - leftValue
            if leftValue <= 5 && rightValue <= 5 {
                break
            }
        }
                
        imageLeft.image = Utilities.SVGImage(named: "math_hand_\(leftValue)")
        imageRight.image = Utilities.SVGImage(named: "math_hand_\(rightValue)")
        
        let delay = playSound(openGameSound(), "toan/toan_dem ngon tay")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem ngon tay")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_demngontay: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    }
    
    func onDelClick(value: Int) {
        answerText.text = String(value)
        answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
    }
    
    func onCheckClick(value: Int) {
        answerText.text = String(value)
        pauseGame()
        let correct = value == self.value
        var delay = playSound(correct ? "effect/answer_correct" : "effect/answer_wrong")
        delay += playSound(delay: delay, names: ["topics/Numbers/\(value)"])
        
        if correct {
            animateCoinIfCorrect(view: answerText)
            delay += 1.0
            delay += playSound(delay: delay, names: [getCorrectHumanSound(), endGameSound()])
            answerText.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [getWrongHumanSound()])
            answerText.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            numpad.reset()
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                self?.numpad.reset(value: 0)
                self?.answerText.text = "0"
                self?.answerText.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}
