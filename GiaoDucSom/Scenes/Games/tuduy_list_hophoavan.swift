//
//  tuduy_list_hophoavan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_hophoavan: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var imageTop: UIImageView!
    private var imageBottom: UIImageView!
    private var txtQuestion: HeightRatioTextView!
    private var viewLeft: UIView!
    private var rotation: Int = 0
    var startTop = Bool.random()
    var endRight = Bool.random()
    var endTop = Bool.random()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        
        startTop = Bool.random()
        endRight = Bool.random()
        endTop = Bool.random()
        
        view.backgroundColor = .white // #FFFFFF
        
        let leftContainer = UIView()
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        let viewLeftContainer = UIView()
        leftContainer.addSubviewWithPercentInset(subview: viewLeftContainer, percentInset: 5)
        
        viewLeft = UIView()
        viewLeft.alpha = 0.01
        viewLeftContainer.addSubview(viewLeft)
        viewLeft.makeViewCenterAndKeep(ratio: 7.0/6.0)
        
        
        
        let topImage = UIImageView(image: Utilities.SVGImage(named: "tuduy_hophoavan"))
        topImage.transform = CGAffineTransform(rotationAngle: -40 * .pi / 180)
        viewLeft.addSubview(topImage)
        topImage.snp.makeConstraints { make in
            make.height.equalTo(viewLeft).multipliedBy(0.1666)
            make.height.equalTo(topImage.snp.width)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.25)
        }
        
        let innerContainer = UIImageView()
        innerContainer.image = Utilities.SVGImage(named: "tuduy_hophoavan_bg")
        viewLeft.addSubview(innerContainer)
        innerContainer.makeViewCenterAndKeep(ratio: 7.0/2.0)
        
        imageTop = UIImageView(image: Utilities.SVGImage(named: "tuduy_hophoavan"))
        innerContainer.addSubview(imageTop)
        imageTop.snp.makeConstraints { make in
            make.height.equalTo(innerContainer).multipliedBy(0.502)
            make.height.equalTo(imageTop.snp.width) // Ratio 1:1
            make.left.equalToSuperview()
            make.top.equalToSuperview()
        }
        
        imageBottom = UIImageView(image: Utilities.SVGImage(named: "tuduy_hophoavan"))
        innerContainer.addSubview(imageBottom)
        imageBottom.snp.makeConstraints { make in
            make.height.equalTo(innerContainer).multipliedBy(0.502)
            make.height.equalTo(imageBottom.snp.width) // Ratio 1:1
            make.left.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        txtQuestion = HeightRatioTextView()
        txtQuestion.text = "?"
        txtQuestion.font = .Freude(size: 20)
        txtQuestion.setHeightRatio(0.5)
        txtQuestion.textColor = UIColor(red: 221/255, green: 120/255, blue: 0/255, alpha: 1) // #DD7800
        txtQuestion.textAlignment = .center
        txtQuestion.backgroundColor = .clear
        innerContainer.addSubview(txtQuestion)
        txtQuestion.snp.makeConstraints { make in
            make.height.equalTo(innerContainer).multipliedBy(0.502)
            make.height.equalTo(txtQuestion.snp.width)
            if endTop {
                make.top.equalToSuperview()
            } else {
                make.bottom.equalToSuperview()
            }
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.txtQuestion.snapToHorizontalBias(horizontalBias: self.endRight ? 1 : 0.83)
        }
                
        gridLayout = MyGridView()
        gridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
       
        if startTop {
            imageTop.transform = CGAffineTransform(rotationAngle: 270 * .pi / 180)
            imageBottom.transform = CGAffineTransform(rotationAngle: 90 * .pi / 180)
        } else {
            imageTop.transform = CGAffineTransform(rotationAngle: 180 * .pi / 180)
            imageBottom.transform = CGAffineTransform(rotationAngle: 0)
        }
        
        rotation = endRight ? (endTop ? Int(imageTop.transform.rotationAngleDegrees()) : Int(imageBottom.transform.rotationAngleDegrees())) : (endTop ? (startTop ? 180 : 270) : (startTop ? 0 : 90))
        
        var views: [UIView] = []
        for i in 0..<4 {
            let view = createItemHopHoaVan(rotation: i * 90)
            view.tag = i
            //view.transform = CGAffineTransform(scaleX: 0, y: 0)
            //view.alpha = 0
            //AnimationUtils.setTouchEffect(view: view)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleItemTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            views.append(view)
        }
        
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_hop hoa van")
        scheduler.schedule(after: delay) {
            [weak self] in
            guard let self = self else { return }
            UIView.animate(withDuration: 0.5) {
                self.viewLeft.transform = .identity
                self.viewLeft.alpha = 1
                self.gridLayout.alpha = 1
            }
            self.gridLayout.showItems(startDelay: 1.0)
            self.scheduler.schedule(after: 1.0) { [weak self] in
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_hop hoa van")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        viewLeft.moveToCenter(of: self)
        viewLeft.alpha = 1
        //viewLeft.frame = containerLayout.frame
        //viewLeft.transform = .identity
        gridLayout.alpha = 0
    }
    
    // MARK: - Touch Handling
    @objc private func handleItemTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let imageView = view.subviews.first?.subviews.first(where: { $0 is UIImageView }) as? UIImageView else { return }
        
        pauseGame()
        let imageRotation = Int(imageView.transform.rotationAngleDegrees())
        if abs(imageRotation - rotation) % 360 == 0 {
            animateCoinIfCorrect(view: view)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        // Nếu cần xử lý kéo thả, thêm logic tại đây
    }
    
    // MARK: - Helper Methods
    private func createItemHopHoaVan(rotation: Int) -> UIView {
        let view = UIView()
        let container = UIImageView()
        container.image = Utilities.SVGImage(named: "option_bg_white_shadow")
        view.addSubview(container)
        container.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(container.snp.height) // Ratio 1:1
        }
        
        let imageView = UIImageView(image: Utilities.SVGImage(named: "tuduy_hophoavan"))
        imageView.transform = CGAffineTransform(rotationAngle: CGFloat(rotation) * .pi / 180)
        container.addSubview(imageView)
        imageView.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.6)
            make.height.equalTo(imageView.snp.width) // Ratio 1:1
            make.center.equalToSuperview()
        }
        
        return view
    }
}

// MARK: - Supporting Structures


extension CGAffineTransform {
    func rotationAngleDegrees() -> CGFloat {
        let radians = atan2(b, a)
        return radians * 180 / .pi
    }
}
