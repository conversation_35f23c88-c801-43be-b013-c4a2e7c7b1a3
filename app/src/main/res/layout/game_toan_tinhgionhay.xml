<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#FFFFFF"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/nhanbiet_bg_drying" />


    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.kidsup.giaoducsom.view.PercentConstraintLayout
            android:id="@+id/item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginBottom="20dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="3.3"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.9">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout17"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout16"
                app:layout_constraintHeight_percent="0.33"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintHorizontal_chainStyle="packed"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintWidth_percent="0.09"
                app:pcl_layout_marginLeftPercentPerHeight="0.02">

                <include layout="@layout/item_9bacthang" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout16"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout18"
                app:layout_constraintHeight_percent="0.40444444444"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout17"
                app:layout_constraintWidth_percent="0.09"
                app:pcl_layout_marginLeftPercentPerHeight="0.02">

                <include layout="@layout/item_9bacthang" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout18"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout19"
                app:layout_constraintHeight_percent="0.47888888888"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout16"
                app:layout_constraintWidth_percent="0.09"
                app:pcl_layout_marginLeftPercentPerHeight="0.02">

                <include layout="@layout/item_9bacthang" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout19"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout20"
                app:layout_constraintHeight_percent="0.55333333333"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout18"
                app:layout_constraintWidth_percent="0.09"
                app:pcl_layout_marginLeftPercentPerHeight="0.02">

                <include layout="@layout/item_9bacthang" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout20"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="0.62777777777"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout19"
                app:layout_constraintWidth_percent="0.09"
                app:pcl_layout_marginLeftPercentPerHeight="0.02">

                <include layout="@layout/item_9bacthang" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/boy_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.28"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.45">

                <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
                    android:id="@+id/xaml_boy_1"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/boy_2_container"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="0.28"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.45">

                <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
                    android:id="@+id/xaml_boy_2"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</androidx.constraintlayout.widget.ConstraintLayout>