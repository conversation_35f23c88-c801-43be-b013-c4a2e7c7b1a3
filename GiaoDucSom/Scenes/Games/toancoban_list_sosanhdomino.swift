//
//  toancoban_list_sosanhdomino.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//

import UIKit
import SnapKit

class toancoban_list_sosanhdomino: NhanBietGameFragment {
    // MARK: - Properties
    private var values: [Int] = []
    private var button1: UIView!
    private var button2: UIView!
    private var button3: UIView!
    private var dominoViewLeft: UIView!
    private var dominoViewRight: UIView!
    private var imageLeft1: UIImageView!
    private var imageLeft2: UIImageView!
    private var imageRight1: UIImageView!
    private var imageRight2: UIImageView!
    private var viewCompare: UIImageView!
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 197/255, green: 255/255, blue: 224/255, alpha: 1) // #C5FFE0
        
        let containerLayout = UIView()
        containerLayout.clipsToBounds = false
        addSubviewWithPercentInset(subview: containerLayout, percentInset: 5)
        containerLayout.makeViewCenterAndKeep(ratio: 1.9)
        
        let topContainer = UIView()
        //topContainer.alpha = 0
        containerLayout.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.height.equalTo(containerLayout).multipliedBy(0.4)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            topContainer.snapToVerticalBias(verticalBias: 0.1)
            topContainer.alpha = 1
        }
        
        let topInnerContainer = UIView()
        topContainer.addSubview(topInnerContainer)
        topInnerContainer.makeViewCenterAndKeep(ratio: 4.0)
        
        dominoViewLeft = createDominoItem()        
        topInnerContainer.addSubview(dominoViewLeft)
        dominoViewLeft.snp.makeConstraints { make in
            make.width.equalTo(dominoViewLeft.snp.height).multipliedBy(1.3) // Ratio 1.3
            make.top.bottom.left.equalToSuperview()
        }
        
        imageLeft1 = dominoViewLeft.viewWithTag(R3.id.image_left) as? UIImageView
        imageLeft2 = dominoViewLeft.viewWithTag(R3.id.image_right) as? UIImageView
        
        viewCompare = UIImageView()
        viewCompare.image = Utilities.SVGImage(named: "math_sosanhqua")
        
        topInnerContainer.addSubview(viewCompare)
        viewCompare.snp.makeConstraints { make in
            make.width.equalTo(viewCompare.snp.height) // Ratio 1:1
            make.top.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        dominoViewRight = createDominoItem()
        
        topInnerContainer.addSubview(dominoViewRight)
        dominoViewRight.snp.makeConstraints { make in
            make.width.equalTo(dominoViewRight.snp.height).multipliedBy(1.3) // Ratio 1.3
            make.top.bottom.right.equalToSuperview()
        }
        
        imageRight1 = dominoViewRight.viewWithTag(R3.id.image_left) as? UIImageView
        imageRight2 = dominoViewRight.viewWithTag(R3.id.image_right) as? UIImageView
        
        let bottomContainer = UIView()
        bottomContainer.alpha = 0
        containerLayout.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.height.equalTo(containerLayout).multipliedBy(0.4)
            make.left.right.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            bottomContainer.snapToVerticalBias(verticalBias: 0.9)
            bottomContainer.alpha = 1
        }
        
        let buttonContainer = UIView()
        bottomContainer.addSubview(buttonContainer)
        buttonContainer.makeViewCenterAndKeep(ratio: 4.0)
        
        button1 = createButton(withText: ">")
        button1.tag = 0
        buttonContainer.addSubview(button1)
        button1.snp.makeConstraints { make in
            make.width.equalTo(button1.snp.height) // Ratio 1:1
            make.left.top.bottom.equalToSuperview()
        }
        
        button2 = createButton(withText: "<")
        button2.tag = 1
        buttonContainer.addSubview(button2)
        button2.snp.makeConstraints { make in
            make.width.equalTo(button2.snp.height) // Ratio 1:1
            make.center.top.bottom.equalToSuperview()
        }
        
        button3 = createButton(withText: "=")
        button3.tag = 2
        buttonContainer.addSubview(button3)
        button3.snp.makeConstraints { make in
            make.width.equalTo(button3.snp.height) // Ratio 1:1
            make.right.top.bottom.equalToSuperview()
        }
        
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
        
        //AnimationUtils.setTouchEffect(views: [button1, button2, button3])
        
        let tap1 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button1.addGestureRecognizer(tap1)
        button1.isUserInteractionEnabled = true
        button1.isExclusiveTouch = true
        
        let tap2 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button2.addGestureRecognizer(tap2)
        button2.isUserInteractionEnabled = true
        button2.isExclusiveTouch = true
        
        let tap3 = UITapGestureRecognizer(target: self, action: #selector(onButtonTapped(_:)))
        button3.addGestureRecognizer(tap3)
        button3.isUserInteractionEnabled = true
        button3.isExclusiveTouch = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            let value = 2 + Int.random(in: 0..<8) // 2 to 9
            values = [value]
            let delta = Int.random(in: 0..<3) * (Int.random(in: 0..<2) * 2 - 1) // -2, 0, 2
            let nextValue = value + delta
            if nextValue >= 2 && nextValue <= 9 {
                values.append(nextValue)
                break
            }
        }
        values.shuffle()
        print("value 0: \(values[0])")
        print("value 1: \(values[1])")
        
        var left1 = 1 + Int.random(in: 0..<values[0] - 1)
        if left1 > 5 { left1 = 5 }
        let left2 = values[0] - left1
        var right1 = 1 + Int.random(in: 0..<values[1] - 1)
        if right1 > 5 { right1 = 5 }
        let right2 = values[1] - right1
        
        imageLeft1.image = Utilities.SVGImage(named: "tuduy_domino_piece\(left1)")
        imageLeft2.image = Utilities.SVGImage(named: "tuduy_domino_piece\(left2)")
        imageRight1.image = Utilities.SVGImage(named: "tuduy_domino_piece\(right1)")
        imageRight2.image = Utilities.SVGImage(named: "tuduy_domino_piece\(right2)")
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_so sanh domino")
        let buttons = [button1!, button2!, button3!]
        for (i, button) in buttons.enumerated() {
            button.transform = CGAffineTransform(scaleX: 0, y: 0)
            button.alpha = 0
            UIView.animate(withDuration: 0.8, delay: delay, usingSpringWithDamping: 0.7, initialSpringVelocity: 0, options: [], animations: {
                button.alpha = 1
                button.transform = .identity
            })
            delay += playSound(name: "effect/bubble\(i + 1)", delay: delay)
            delay += 0.5
        }
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so sanh domino")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onButtonTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        pauseGame()
        let correct: Bool
        switch view {
        case button1:
            correct = values[0] > values[1]
        case button2:
            correct = values[0] < values[1]
        case button3:
            correct = values[0] == values[1]
        default:
            correct = false
        }
        
        if correct {
            let buttons = [button1!, button2!, button3!]
            for button in buttons where button != view {
                UIView.animate(withDuration: 0.3) {
                    button.alpha = 0
                    button.transform = CGAffineTransform(scaleX: 0, y: 0)
                }
            }
            view.moveCenter(to: viewCompare, duration: 0.3)            
            var delay = playSound(answerCorrect1EffectSound(), getCorrectHumanSound(), "topics/Numbers/\(self.values[0])", self.values[0] > self.values[1] ? "toan/lớn hơn" : self.values[0] == self.values[1] ? "toan/bằng" : "toan/nhỏ hơn", "topics/Numbers/\(self.values[1])")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.animateCoinIfCorrect(view: self?.coinView ?? UIView())
            }
            delay += 1.0
            delay += playSound(delay: delay, names: [self.endGameSound()])
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    private func createButton(withText text: String) -> UIView {
        let container = UIImageView()
        container.isUserInteractionEnabled = true
        container.image = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
        
        let label = AutosizeLabel()
        label.text = text
        label.textColor = text == ">" ? UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) : text == "<" ? UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) : UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #87D657, #FF7760, #74B6FF
        label.font = .Freude(size: 20)
        label.textAlignment = .center
        label.adjustsFontSizeToFitWidth = true
        label.minimumScaleFactor = 0.1
        container.addSubview(label)
        label.snp.makeConstraints { make in
            make.height.equalTo(container).multipliedBy(1.15)
            make.left.bottom.right.equalToSuperview()
        }
        
        return container
    }
    
    private func createDominoItem() -> UIView {
        let container = UIView()
        //container.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        
        let dominoPiece = UIImageView()
        dominoPiece.image = Utilities.SVGImage(named: "tuduy_domino_piece")
        container.addSubview(dominoPiece)
        dominoPiece.makeViewCenterAndKeep(ratio: 373.6 / 209.8)
        
        let imageLeft = UIImageView(image: Utilities.SVGImage(named: "tuduy_domino_piece1"))
        imageLeft.contentMode = .scaleAspectFit
        imageLeft.tag = R3.id.image_left
        dominoPiece.addSubview(imageLeft)
        imageLeft.snp.makeConstraints { make in
            make.width.equalTo(dominoPiece).multipliedBy(0.5)
            make.left.top.bottom.equalToSuperview()
        }
        
        let imageRight = UIImageView(image: Utilities.SVGImage(named: "tuduy_domino_piece2"))
        imageRight.contentMode = .scaleAspectFit
        imageRight.tag = R3.id.image_right
        dominoPiece.addSubview(imageRight)
        imageRight.snp.makeConstraints { make in
            make.width.equalTo(dominoPiece).multipliedBy(0.5)
            make.right.top.bottom.equalToSuperview()
        }
        
        return container
    }
}

// MARK: - Supporting Structures
struct R3 {
    struct id {
        static let image_left = 1
        static let image_right = 2
    }
}
