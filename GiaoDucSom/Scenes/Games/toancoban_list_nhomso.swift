//
//  toancoban_list_nhomso.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 16/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_nhomso: NhanBietGameFragment {
    // MARK: - Properties
    private var textA: HeightRatioTextView!
    private var textB: HeightRatioTextView!
    private var textResult: HeightRatioTextView!
    private var textCurrent: HeightRatioTextView?
    private var numpad: MathNumpad!
    private var svgPath: String = ""
    private var a: Int = 0
    private var b: Int = 0
    private var topGridLayout: MyGridView!
    private var bottomGridLayout: MyGridView!
    private var flip: Bool = false
    private var viewGroup: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        let skyView = UIView()
        skyView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(skyView)
        skyView.snp.makeConstraints { make in
            make.left.right.top.equalTo(self)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = false
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let gridContainer = UIView()
        view.addSubview(gridContainer)
        gridContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        let innerGridContainer = UIView()
        gridContainer.addSubview(innerGridContainer)
        innerGridContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        let gridBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        innerGridContainer.addSubview(gridBackground)
        gridBackground.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.9)
            make.width.equalTo(gridBackground.snp.height).multipliedBy(0.95) // Ratio 0.95:1
        }
        gridBackground.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            gridBackground.snapToHorizontalBias(horizontalBias: 0.05) // Bias 0.05
        }
        
        topGridLayout = MyGridView()
        gridBackground.addSubview(topGridLayout)
        topGridLayout.snp.makeConstraints { make in
            make.width.equalTo(gridBackground).multipliedBy(0.9)
            make.height.equalTo(gridBackground).multipliedBy(0.3)
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(0.4) // Bias 0.2
        }
        topGridLayout.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            topGridLayout.snapToVerticalBias(verticalBias: 0.2)
        }
        
        bottomGridLayout = MyGridView()
        gridBackground.addSubview(bottomGridLayout)
        bottomGridLayout.snp.makeConstraints { make in
            make.width.equalTo(gridBackground).multipliedBy(0.9)
            make.height.equalTo(gridBackground).multipliedBy(0.3)
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.6) // Bias 0.8
        }
        bottomGridLayout.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            bottomGridLayout.snapToVerticalBias(verticalBias: 0.8)
        }
        
        viewGroup = UIView()
        innerGridContainer.addSubview(viewGroup)
        viewGroup.snp.makeConstraints { make in
            make.width.equalTo(innerGridContainer).multipliedBy(0.5)
            make.height.equalTo(innerGridContainer)
            make.centerY.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.viewGroup.snapToHorizontalBias(horizontalBias: 0.94)
        }
        
        let separatorImage = UIImageView(image: Utilities.SVGImage(named: "image_nhomso"))
        viewGroup.addSubview(separatorImage)
        separatorImage.snp.makeConstraints { make in
            make.height.equalTo(viewGroup).multipliedBy(0.5)
            make.width.equalTo(viewGroup).multipliedBy(0.22)
            make.center.equalToSuperview()
        }
        
        let textALayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        textALayout.isUserInteractionEnabled = true
        viewGroup.addSubview(textALayout)
        textALayout.snp.makeConstraints { make in
            make.height.equalTo(viewGroup).multipliedBy(0.38)
            make.width.equalTo(textALayout.snp.height) // Ratio 1:1
            make.left.equalToSuperview()
            make.top.equalToSuperview().offset(viewGroup.frame.height * 0.1)
        }
        
        textA = HeightRatioTextView()
        textA.text = "?"
        textA.setHeightRatio(0.7)
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textA.isUserInteractionEnabled = true
        textALayout.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textA.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextA)))
        
        let textBLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        textBLayout.isUserInteractionEnabled = true
        viewGroup.addSubview(textBLayout)
        textBLayout.snp.makeConstraints { make in
            make.height.equalTo(viewGroup).multipliedBy(0.38)
            make.width.equalTo(textBLayout.snp.height) // Ratio 1:1
            make.left.equalToSuperview()
            make.bottom.equalToSuperview().offset(-viewGroup.frame.height * 0.1)
        }
        
        textB = HeightRatioTextView()
        textB.text = "?"
        textB.setHeightRatio(0.7)
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textB.isUserInteractionEnabled = true
        textBLayout.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textB.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextB)))
        
        let resultLayout = UIImageView(image: Utilities.SVGImage(named: "math_result_bg"))
        resultLayout.isUserInteractionEnabled = true
        viewGroup.addSubview(resultLayout)
        resultLayout.snp.makeConstraints { make in
            make.height.equalTo(viewGroup).multipliedBy(0.38)
            make.width.equalTo(resultLayout.snp.height) // Ratio 1:1
            make.right.equalToSuperview()
            make.centerY.equalToSuperview()
        }
        
        textResult = HeightRatioTextView()
        textResult.text = "?"
        textResult.setHeightRatio(0.7)
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        textResult.isUserInteractionEnabled = true
        resultLayout.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textResult.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextResult)))
    }
    
    // MARK: - Selection Logic
    @objc private func selectTextA() { select(view: textA) }
    @objc private func selectTextB() { select(view: textB) }
    @objc private func selectTextResult() { select(view: textResult) }
    
    private func select(view: HeightRatioTextView?) {
        if let current = textCurrent, let parent = current.superview as? UIImageView {
            parent.image = Utilities.SVGImage(named: "math_result_bg")
        }
        if let view = view, let parent = view.superview as? UIImageView {
            numpad.isEnabled = true
            parent.image = Utilities.SVGImage(named: "math_result_bg_focus")
            view.textColor = UIColor(hex: "#74B6FF")
        } else {
            numpad.isEnabled = false
        }
        textCurrent = view
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        while true {
            a = 1 + Int.random(in: 0..<9)
            b = 1 + Int.random(in: 0..<9)
            if a + b <= 9 {
                break
            }
        }
        
        flip = Bool.random()
        if flip {
            viewGroup.transform = CGAffineTransform(scaleX: -1, y: 1)
            textA.transform = CGAffineTransform(scaleX: -1, y: 1)
            textB.transform = CGAffineTransform(scaleX: -1, y: 1)
            textResult.transform = CGAffineTransform(scaleX: -1, y: 1)
        }
        
        let packs = FlashcardsManager.shared.getPacks().shuffled()
        for pack in packs {
            let items = pack.items.filter { $0.counting == 1 }.shuffled().prefix(1)
            if let item = items.first {
                let folder = pack.folder
                svgPath = "topics/\(folder)/\(item.path!)"
                break
            }
        }
        let svg = Utilities.GetSVGKImage(named: svgPath)
        let column = min(5,max(self.a, self.b))
        
        
        var views: [UIView] = []
        for _ in 0..<self.a {
            let view = SVGKFastImageView(svgkImage: svg)
            view?.contentMode = .scaleAspectFit
            if let view = view {
                views.append(view)
            }
        }
        self.topGridLayout.columns = column
        self.topGridLayout.itemRatio = 1.0
        self.topGridLayout.itemSpacingRatio = 0.005
        self.topGridLayout.insetRatio = 0.005
        self.topGridLayout.reloadItemViews(views: views)
        
        views = []
        for _ in 0..<self.b {
            let view = SVGKFastImageView(svgkImage: svg)
            view?.contentMode = .scaleAspectFit
            if let view = view {
                views.append(view)
            }
        }
        self.bottomGridLayout.columns = column
        self.bottomGridLayout.itemRatio = 1.0
        self.bottomGridLayout.itemSpacingRatio = 0.005
        self.bottomGridLayout.insetRatio = 0.005
        self.bottomGridLayout.reloadItemViews(views: views)
    }
    
    override func createGame() {
        super.createGame()
        let delay = playSound(openGameSound(), "toan/toancoban_nhom so")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toancoban_nhom so")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func checkFinish() {
        guard let aText = textA.text, !aText.isEmpty,
              let bText = textB.text, !bText.isEmpty,
              let resultText = textResult.text, !resultText.isEmpty else {
            return
        }
        
        let aOK = aText.lowercased() == String(a).lowercased()
        let bOK = bText.lowercased() == String(b).lowercased()
        let resultOK = resultText.lowercased() == String(a + b).lowercased()
        
        if aOK && bOK && resultOK {
            animateCoinIfCorrect(view: bottomGridLayout)
            textA.textColor = UIColor(hex: "#74B6FF")
            textB.textColor = UIColor(hex: "#74B6FF")
            textResult.textColor = UIColor(hex: "#74B6FF")
            pauseGame()
            let delay = flip ?
                playSound(
                    answerCorrect1EffectSound(),
                    getCorrectHumanSound(),
                    "topics/Numbers/\(a + b)",
                    "toan/tách ra thành",
                    "topics/Numbers/\(a)",
                    "toan/và",
                    "topics/Numbers/\(b)",
                    endGameSound()
                ) :
                playSound(
                    answerCorrect1EffectSound(),
                    getCorrectHumanSound(),
                    "topics/Numbers/\(a)",
                    "toan/và",
                    "topics/Numbers/\(b)",
                    "toan/gộp lại thành",
                    "topics/Numbers/\(a + b)",
                    endGameSound()
                )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            let aWrong = !aOK && aText != "?"
            let bWrong = !bOK && bText != "?"
            let resultWrong = !resultOK && resultText != "?"
            if aWrong || bWrong || resultWrong {
                setGameWrong()
            }
            textA.textColor = UIColor(hex: aOK ? "#74B6FF" : "#FF7760")
            textB.textColor = UIColor(hex: bOK ? "#74B6FF" : "#FF7760")
            textResult.textColor = UIColor(hex: resultOK ? "#74B6FF" : "#FF7760")
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_nhomso: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        let adjustedValue = value >= 10 ? value % 10 : value
        textCurrent?.text = String(adjustedValue)
    }
    
    func onDelClick(value: Int) {
        textCurrent?.text = ""
    }
    
    func onCheckClick(value: Int) {
        select(view: nil)
        checkFinish()
    }
}


// Giả lập StringUtils nếu không có trong context
struct StringUtils {
    static func equalsIgnoreCase(_ str1: String, _ str2: String) -> Bool {
        return str1.lowercased() == str2.lowercased()
    }
}

