//
//  toannangcao_list_toanchuthappv10.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toannangcao_list_toanchuthappv10: NhanBietGameFragment {
    // MARK: - Constants
    let PLUS = 1000
    let MINUS = 2000
    let EQUAL = 3000
    var MAX_VALUE = 9
    var SIZE_COL = 9
    var SIZE_ROW = 9
    
    // MARK: - Properties
    var grid: [[Int]] = []
    private var originalGrid: [[Int]] = []
    private var filledGrid: [[Int]] = []
    private var pieceView: PieceView!
    private let ops = ["+=", "-=", "=+", "=-"]
    private var leftGridLayout: MyGridView!
    private var pieceGrid: [[Int]] = []
    private var pieceCurrentX = -1
    private var pieceCurrentY = -1
    private var currentTextView: HeightRatioTextView?
    private var numpad: MathNumpad!
    private var zPos: CGFloat = 1000.0
    
    // MARK: - Selection Logic
    private func selectPiece(x: Int, y: Int) {
        if pieceCurrentX != -1 && pieceCurrentY != -1 {
            pieceGrid[pieceCurrentX][pieceCurrentY] = 2
        }
        if x >= 0 && x < pieceGrid.count && y >= 0 && y < pieceGrid[0].count {
            pieceCurrentX = x
            pieceCurrentY = y
            pieceGrid[x][y] = 3
        }
        pieceView.setPiece(pieceGrid)
    }
    
    // MARK: - Grid Logic
    func addLine(startRow: Int, startCol: Int, endRow: Int, endCol: Int) {
        let deltaRow = (endRow - startRow) / 4
        let deltaCol = (endCol - startCol) / 4
        let a = getValue(row: startRow, col: startCol)
        let b = getValue(row: startRow + 2 * deltaRow, col: startCol + 2 * deltaCol)
        let c = getValue(row: endRow, col: endCol)
        if a == 0 || b == 0 || c == 0 {
            let d = 0
        }
        if a > 0 && b > 0 && c > 0 {
            if a == b + c {
                grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
            } else if a == b - c {
                grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
            } else if a + b == c {
                grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
            } else if a - b == c {
                grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
            }
        } else if a > 0 && b > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if a + b <= MAX_VALUE {
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[endRow][endCol] = a + b
                        return
                    }
                case "-=":
                    if a > b {
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[endRow][endCol] = a - b
                        return
                    }
                case "=+":
                    if a > b {
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        grid[endRow][endCol] = a - b
                        return
                    }
                case "=-":
                    if a < b {
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        grid[endRow][endCol] = b - a
                        return
                    }
                default:
                    break
                }
            }
        } else if a > 0 && c > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if c > a {
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = c - a
                        return
                    }
                case "-=":
                    if a > c {
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = a - c
                        return
                    }
                case "=+":
                    if a > c {
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = a - c
                        return
                    }
                case "=-":
                    if a + c <= MAX_VALUE {
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = a + c
                        return
                    }
                default:
                    break
                }
            }
        } else if b > 0 && c > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if b < c {
                        grid[startRow][startCol] = c - b
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        return
                    }
                case "-=":
                    if b + c <= MAX_VALUE {
                        grid[startRow][startCol] = b + c
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        return
                    }
                case "=+":
                    if c + b <= MAX_VALUE {
                        grid[startRow][startCol] = b + c
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        return
                    }
                case "=-":
                    if b > c {
                        grid[startRow][startCol] = b - c
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        return
                    }
                default:
                    break
                }
            }
        } else if a > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if a + 1 <= MAX_VALUE {
                        let b = 1 + Int.random(in: 0..<(MAX_VALUE - a))
                        let c = a + b
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[endRow][endCol] = c
                        return
                    }
                case "-=":
                    if a > 1 {
                        let b = 1 + Int.random(in: 0..<(a - 1))
                        let c = a - b
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[endRow][endCol] = c
                        return
                    }
                case "=+":
                    if a > 1 {
                        let b = 1 + Int.random(in: 0..<(a - 1))
                        let c = a - b
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[endRow][endCol] = c
                        return
                    }
                case "=-":
                    if a + 1 <= MAX_VALUE {
                        let b = a + 1 + Int.random(in: 0..<(MAX_VALUE - a))
                        let c = b - a
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[endRow][endCol] = c
                        return
                    }
                default:
                    break
                }
            }
        } else if b > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if b + 1 <= MAX_VALUE {
                        let a = 1 + Int.random(in: 0..<(MAX_VALUE - b))
                        let c = a + b
                        grid[startRow][startCol] = a
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[endRow][endCol] = c
                        return
                    }
                case "-=":
                    if b + 1 <= MAX_VALUE {
                        let a = b + 1 + Int.random(in: 0..<(MAX_VALUE - b))
                        let c = a - b
                        grid[startRow][startCol] = a
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        grid[endRow][endCol] = c
                        if a == 0 || c == 0 {
                            let d = 0
                            let e = 0
                        }
                        return
                    }
                case "=+":
                    if b + 1 <= MAX_VALUE {
                        let c = 1 + Int.random(in: 0..<(MAX_VALUE - b))
                        let a = b + c
                        grid[startRow][startCol] = a
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        grid[endRow][endCol] = c
                        return
                    }
                case "=-":
                    if b > 1 {
                        let c = 1 + Int.random(in: 0..<(b - 1))
                        let a = b - c
                        grid[startRow][startCol] = a
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        grid[endRow][endCol] = c
                        return
                    }
                default:
                    break
                }
            }
        } else if c > 0 {
            let shuffledOps = ops.shuffled()
            for op in shuffledOps {
                switch op {
                case "+=":
                    if c > 1 {
                        let a = 1 + Int.random(in: 0..<(c - 1))
                        let b = c - a
                        grid[startRow][startCol] = a
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[startRow + deltaRow][startCol + deltaCol] = PLUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        return
                    }
                case "-=":
                    if c + 1 <= MAX_VALUE {
                        let a = c + 1 + Int.random(in: 0..<(MAX_VALUE - c))
                        let b = a - c
                        grid[startRow][startCol] = a
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[startRow + deltaRow][startCol + deltaCol] = MINUS
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = EQUAL
                        return
                    }
                case "=+":
                    if c + 1 <= MAX_VALUE {
                        let b = 1 + Int.random(in: 0..<(MAX_VALUE - c))
                        let a = b + c
                        grid[startRow][startCol] = a
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = PLUS
                        return
                    }
                case "=-":
                    if c + 1 <= MAX_VALUE {
                        let b = c + 1 + Int.random(in: 0..<(MAX_VALUE - c))
                        let a = b - c
                        grid[startRow][startCol] = a
                        grid[startRow + 2 * deltaRow][startCol + 2 * deltaCol] = b
                        grid[startRow + deltaRow][startCol + deltaCol] = EQUAL
                        grid[startRow + 3 * deltaRow][startCol + 3 * deltaCol] = MINUS
                        return
                    }
                default:
                    break
                }
            }
        } else {
            let a = 1 + Int.random(in: 0..<MAX_VALUE)
            grid[startRow][startCol] = a
            addLine(startRow: startRow, startCol: startCol, endRow: endRow, endCol: endCol)
        }
    }
    
    private func checkValid(row: Int, col: Int) -> Bool {
        return row >= 0 && row < grid.count && col >= 0 && col < grid[0].count
    }
    
    private func getValue(row: Int, col: Int, outsideValue: Int = -1) -> Int {
        return checkValid(row: row, col: col) ? grid[row][col] : outsideValue
    }
    
    private func initLine() {
        if Bool.random() {
            addLine(startRow: 2, startCol: 4, endRow: 6, endCol: 4)
        } else {
            addLine(startRow: 4, startCol: 2, endRow: 4, endCol: 6)
        }
    }
    
    func createLines() {
        grid = Array(repeating: Array(repeating: 0, count: SIZE_COL), count: SIZE_ROW)
        var value = 2
        initLine()
        var tries = 0
        while value < 10 && tries < 1000 {
            tries += 1
            let row = Int.random(in: 0..<((SIZE_ROW+1) / 2)) * 2
            let col = Int.random(in: 0..<((SIZE_COL+1) / 2)) * 2
            let deltaRow = Int.random(in: 0...1)
            let deltaCol = 1 - deltaRow
            let endRow = row + deltaRow * 4
            let endCol = col + deltaCol * 4
            
            if !checkValid(row: endRow, col: endCol) {
                continue
            }
            if getValue(row: row, col: col) < 1 && getValue(row: endRow, col: endCol) < 1 && getValue(row: row + deltaRow * 2, col: col + deltaCol * 2) < 1 {
                continue
            }
            if getValue(row: row + deltaRow, col: col + deltaCol) > 0 || getValue(row: endRow - deltaRow, col: endCol - deltaCol) > 0 ||
               getValue(row: row - deltaRow, col: col - deltaCol) > 0 || getValue(row: endRow + deltaRow, col: endCol + deltaCol) > 0 {
                continue
            }
            value += 1
            addLine(startRow: row, startCol: col, endRow: endRow, endCol: endCol)
        }
    }
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        let bgView = UIView()
        bgView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(bgView)
        
        
        let numpadContainer = UIView()
        view.addSubview(numpadContainer)
        numpadContainer.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        bgView.snp.makeConstraints { make in
            make.top.bottom.right.equalTo(self)
            make.left.equalTo(numpadContainer)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        numpadContainer.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let leftView = UIView()
        view.addSubview(leftView)
        leftView.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(numpadContainer.snp.left)
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        leftView.layer.zPosition = zPos
        
        pieceView = PieceView(frame: .zero)
        leftView.addSubview(pieceView)
                
        leftGridLayout = MyGridView()
        leftView.addSubview(leftGridLayout)
        leftGridLayout.snp.makeConstraints { make in
            make.edges.equalTo(pieceView)
        }
    }
    
    // MARK: - Game Logic
    private func checkWin() -> Bool {
        var win = true
        for i in 0..<filledGrid.count {
            for j in 0..<filledGrid[i].count {
                if filledGrid[i][j] != originalGrid[i][j] {
                    win = false
                    break
                }
            }
        }
        if win {
            pauseGame()
            animateCoinIfCorrect(view: leftGridLayout)
            let delay = playSound(finishEndSounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
        return win
    }
    
    private func buildGridView() {
        let font = UIFont.Freude(size: 20)
        var views: [UIView] = []
        for i in 0..<grid.count {
            for j in 0..<grid[i].count {
                let textView = HeightRatioTextView()
                let text: String
                switch grid[i][j] {
                case PLUS: text = "+"
                case MINUS: text = "-"
                case EQUAL: text = "="
                case 0..<1: text = ""
                default: text = String(grid[i][j])
                }
                textView.text = text
                textView.setHeightRatio(0.6)
                textView.font = font
                textView.textColor = UIColor(hex: "#91A2AE")
                textView.textAlignment = .center
                if originalGrid[i][j] == 0 {
                    textView.isHidden = true
                }
                views.append(textView)
                textView.isUserInteractionEnabled = true
                let row = i
                let col = j
                textView.addGestureRecognizer(UITapGestureRecognizer{
                    [weak self] _ in
                        guard let self = self else { return }
                        if self.originalGrid[row][col] > 0 && self.grid[row][col] < 1 {
                            if let current = self.currentTextView {
                                let text = current.text ?? ""
                                self.filledGrid[self.pieceCurrentX][self.pieceCurrentY] = Int(text == "|" || text.isEmpty ? "0" : text) ?? 0
                                if self.filledGrid[self.pieceCurrentX][self.pieceCurrentY] == 0 {
                                    current.text = ""
                                } else {
                                    let right = self.filledGrid[self.pieceCurrentX][self.pieceCurrentY] == self.originalGrid[self.pieceCurrentX][self.pieceCurrentY]
                                    current.textColor = UIColor(hex: right ? "#87D657" : "#FF7760")
                                    if !right {
                                        self.setGameWrong()
                                        if current != textView {
                                            playSound(answerWrongEffectSound())
                                        }
                                    } else {
                                        if current != textView {
                                            playSound(answerCorrect1EffectSound())
                                        }
                                    }
                                }
                                if self.checkWin() {
                                    return
                                }
                            }
                            textView.text = "|"
                            self.numpad.reset(value: 0)
                            self.selectPiece(x: row, y: col)
                            self.currentTextView = textView
                            textView.textColor = .white
                        }
                })
            }
        }
        
        leftGridLayout.columns = SIZE_COL
        leftGridLayout.itemRatio = 1.0
        leftGridLayout.itemSpacingRatio = 0.0
        leftGridLayout.insetRatio = 0.0
        leftGridLayout.reloadItemViews(views: views)
        
        pieceGrid = Array(repeating: Array(repeating: 0, count: SIZE_COL), count: SIZE_ROW)
        for i in 0..<SIZE_ROW {
            for j in 0..<SIZE_COL {
                pieceGrid[i][j] = originalGrid[i][j] > 0 && grid[i][j] > 0 ? 1 : originalGrid[i][j] > 0 ? 2 : 0
            }
        }
        pieceView.snp.makeConstraints { make in
            make.width.equalTo(pieceView.snp.height).multipliedBy(CGFloat(SIZE_COL) / CGFloat(SIZE_ROW))
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9).priority(.high)
            make.width.height.lessThanOrEqualToSuperview().multipliedBy(0.9).priority(.high)
        }
        pieceView.setPiece(pieceGrid)
    }
    
    func removeNumbers() {
        var tries = 0
        while tries < 1000 {
            tries += 1
            let row = Int.random(in: 0...(SIZE_ROW / 2)) * 2
            let col = Int.random(in: 0...(SIZE_COL / 2)) * 2
            let deltaRow = Int.random(in: 0...1)
            let deltaCol = 1 - deltaRow
            let endRow = row + deltaRow * 4
            let centerRow = row + deltaRow * 2
            let endCol = col + deltaCol * 4
            let centerCol = col + deltaCol * 2
            
            if checkValid(row: endRow, col: endCol) &&
               grid[row][col] > 0 &&
               grid[row + deltaRow][col + deltaCol] > 0 &&
               grid[centerRow][centerCol] > 0 &&
               grid[row + 3 * deltaRow][col + 3 * deltaCol] > 0 &&
               grid[endRow][endCol] > 0 {
                let pos = [0, 2, 4].randomElement()!
                grid[row + deltaRow * pos][col + deltaCol * pos] = 0
            }
        }
    }
    
    override func updateData() {
        super.updateData()
        createLines()
        originalGrid = grid.map { $0.map{ $0} }
        removeNumbers()
        filledGrid = grid.map { $0.map {$0} }
        buildGridView()
        let delay = playSound(openGameSound(), "toan/toan_chu thap")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_chu thap")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

// MARK: - MathNumpadListener
extension toannangcao_list_toanchuthappv10: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        let range = MAX_VALUE == 9 ? 10 : 100
        var adjustedValue = value
        if value >= range {
            adjustedValue = value % 10
            numpad.reset(value: adjustedValue)
        }
        if let current = currentTextView {
            current.text = String(adjustedValue)
        }
    }
    
    func onDelClick(value: Int) {
        if let current = currentTextView {
            current.text = value == 0 ? "|" : String(value)
        }
    }
    
    func onCheckClick(value: Int) {
        if let current = currentTextView {
            let index = leftGridLayout.subviews.firstIndex(of: current) ?? 0
            let row = index / SIZE_COL
            let col = index % SIZE_COL
            filledGrid[row][col] = value
            if filledGrid[row][col] == originalGrid[row][col] {
                playSound("effect/answer_correct")
                current.textColor = UIColor(hex: "#87D657")
            } else {
                playSound(answerWrongEffectSound())
                current.textColor = UIColor(hex: "#FF7760")
                setGameWrong()
            }
            checkWin()
        }
    }
}

import UIKit
import ObjectiveC

private var TapHandlerKey: UInt8 = 0

extension UITapGestureRecognizer {

    /// Convenience initializer “ăn” closure
    convenience init(handler: @escaping (UITapGestureRecognizer) -> Void) {
        self.init()                              // gọi init mặc định
        addTarget(self, action: #selector(_didFire))
        // gài closure vào runtime
        objc_setAssociatedObject(self,
                                 &TapHandlerKey,
                                 handler,
                                 .OBJC_ASSOCIATION_RETAIN_NONATOMIC)
    }

    /// Lấy closure ra xài
    private var _handler: ((UITapGestureRecognizer) -> Void)? {
        objc_getAssociatedObject(self, &TapHandlerKey) as? (UITapGestureRecognizer) -> Void
    }

    /// Selector trung gian
    @objc private func _didFire() {
        _handler?(self)        // gọi closure
    }
}
