//
//  ngonngu_list_noitughep.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 2/4/25.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class ngonngu_list_noitughep: NhanBietGameFragment {
    private var values1 : [Int] = []
    private var values2 : [Int] = []
    private var answers : [Int] = []
    private var randomOrder1: [Int] = []
    private var randomOrder2: [Int] = []
    private var contentLayout: MyGridView = MyGridView()
    private var answerLayout: MyGridView = MyGridView()
    private var meIndex = 0
    private var step = 0
    private var leftViews: [UIView] = []
    private var rightViews: [UIView] = []
    private var allViews: [UIView] = []
    private var selectedView: UIView?
    private var hand: UIView = UIView()
    private var lineContainer = UIView()
    private var drawingLine = LinesBetweenViews()
    private var viewConnectedToView: [UIView : UIView] = [:]
    private var viewConnectedToLine: [UIView : LinesBetweenViews] = [:]
    var coinView = UIView()
    let wordGroups: [[String]] = [
        ["bể cá", "bó cỏ", "bế bé"],
        ["bi bô", "ba ba", "cá cờ"],
        ["cá cờ", "kì đà", "hồ cá"],
        ["bờ hồ", "gỗ gụ", "ghế đá"],
        ["no nê", "bó mạ", "ca nô"],
        ["nhổ cỏ", "nhà ga", "lá nho", "nghé ọ"],
        ["bẹ ngô", "lá nghệ", "nhà lá", "ngủ khì"],
        ["cá ngừ", "đu đủ", "do dự"],
        ["ngu ngơ", "nụ bí", "củ nghệ"],
        ["cá thu", "lá thư", "cú vọ", "cử tạ"],
        ["tổ cò", "thứ tư", "thợ mỏ"],
        ["thỏ thẻ", "thơ ca", "ra về"],
        ["thủ quỹ", "ý nghĩ", "trí nhớ", "nghề y"],
        ["đồ chơi", "thổi còi", "cái chổi", "gà mái"],
        ["cây cối", "túi lưới", "cối xay", "buổi tối"],
        ["chú cừu", "tí xíu", "trêu đùa"],
        ["cái chiếu", "già yếu", "diều sáo", "chú hươu"],
        ["bàn ghế", "cần cẩu", "ngựa vằn", "cái cân"],
        ["cẩn thận", "may mắn", "gần gũi"],
        ["ân cần", "múa lân", "bơi lặn"],
        ["nhảy sạp", "tập múa", "lắp bắp"],
        ["tươi đẹp", "nằm sấp", "nắp nồi"],
        ["lọ mực", "bông cúc", "máy xúc", "thể dục"]
    ]
    var words: [String] = []
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = .color(hex: "#849BFD")
        addSubview(contentLayout)
        addSubview(answerLayout)
        addSubview(lineContainer)
        addSubview(hand)
        addSubviewWithInset(subview: drawingLine, inset: 0)
        
        drawingLine.isHidden = true
        hand.frame = CGRectMake(0, 0, 1, 1)
        hand.backgroundColor = .clear
        
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
            
        addSubview(coinView)
        coinView.snp.makeConstraints{ make in
            make.width.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        contentLayout.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        
        answerLayout.snp.makeConstraints { make in
            make.bottom.centerX.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.45)
        }
        
        lineContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        coinView.isUserInteractionEnabled = false
    }
    
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        guard let touch = touches.first else { return }
        if gameState != .playing { return }
        canRead = true
        for view in allViews {
            if touch.placeInView(view: view){
                selectedView = view
                hand.frame = CGRectMake(selectedView!.frame.midX, selectedView!.frame.maxY, 1, 1)
                let index = view.tag - 100
                if let label = view.viewWithTag(3) as? UILabel {
                    playSound("ngonngu/noi tu ghep/\(label.text!)")
                }
                drawingLine.views[0] = selectedView!
                if let line = viewConnectedToLine[selectedView!] {
                    line.alpha = 0.3
                }
                return;
            }
        }
        selectedView = nil
        return
    }
    
    var folder: [String] = []
    var items: [Item] = []
    var tones: [String] = []
    
    override func createGame() {
        super.createGame()
        
        words = MyList(wordGroups[Int.random(in: 0..<wordGroups.count)]).randomOrder().prefix(3).map { $0 }
        values1 = Utils.generatePermutation(3)
        values2 = Utils.generatePermutation(3)
                
        var delay = playSound(openGameSound(), "ngonngu/ngonngu_noi tu ghep")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
        
        step += 1
        allViews = []
        leftViews = []
        lineContainer.subviews.forEach{$0.removeFromSuperview()}
        for i in 0..<values1.count {
            let index = values1[i]
            let view = createItemBottom(text: String(words[index].split(separator: " ")[0]))
            view.tag = 100 + index
            view.stringTag = "top"
            leftViews.append(view)
            allViews.append(view)
        }
        contentLayout.itemRatio = 1
        contentLayout.columns = 3
        contentLayout.insetRatio = 0.1
        contentLayout.reloadItemViews(views: leftViews)
        
        rightViews = []
        for i in 0..<values1.count {
            let index = values2[i]
            let view = createItemBottom(text: String(words[index].split(separator: " ")[1]))
            view.tag = 100 + index
            view.stringTag = "bottom"
            rightViews.append(view)
            allViews.append(view)
        }
        answerLayout.itemRatio = 1
        answerLayout.columns = 3
        answerLayout.insetRatio = 0.1
        answerLayout.reloadItemViews(views: rightViews)
        hand.tag = -1
        drawingLine.views = [hand, hand]
    }
    
    func haveSameItem(_ list1: [Int]?, _ list2: [Int]?) -> Bool {
        guard let list1 = list1 else {
            return false
        }
        guard let list2 = list2 else {
            return false
        }
        for item in list1 {
            if list2.contains(item) {
                return true
            }
        }
        return false
    }
    
    func connect(left:UIView, right:UIView){
        if left.superview == right.superview {
            return
        }
        if let line = viewConnectedToLine[left] {
            line.removeFromSuperview()
        }
        if let line = viewConnectedToLine[right] {
            line.removeFromSuperview()
        }
        if let other = viewConnectedToView[left] {
            viewConnectedToView[other] = nil
        }
        if let other = viewConnectedToView[right] {
            viewConnectedToView[other] = nil
        }
        let line = LinesBetweenViews()
        line.views = [left,right]
        line.backgroundColor = .clear
        line.clipsToBounds = false
        lineContainer.addSubviewWithInset(subview: line, inset: 0)
        scheduler.schedule(delay: 0.01) {
            line.createPath()
        }
        let p = hand.convert(CGPoint.zero, to: self)
        viewConnectedToLine[left] = line
        viewConnectedToLine[right] = line
        viewConnectedToView[left] = right
        viewConnectedToView[right] = left
        checkFinish()
    }
    
    func checkFinish(){
        if lineContainer.subviews.count == 3 {
            var valid = true
            for view in lineContainer.subviews {
                if let view = view as? LinesBetweenViews {
                    if view.views[0].tag != view.views[1].tag {
                        valid = false
                        break
                    }
                }
            }
            if valid {
                pauseGame()
                animateCoinIfCorrect(view: coinView)
                var delay = 0.0
                delay += self.playSound(delay: delay, names: finishCorrect1Sounds())
                delay += 1
                self.scheduler.schedule(delay: delay) {
                    [weak self] in
                    guard let self = self else { return }
                    self.finishGame()
                }
            } else {
                playSound("effect/answer_wrong")
                pauseGame()
                scheduler.schedule(delay: 1) {
                    [weak self] in
                    guard let self = self else { return }
                    self.lineContainer.subviews.forEach{$0.removeFromSuperview()}
                    self.viewConnectedToLine = [:]
                    self.viewConnectedToView = [:]
                    self.resumeGame()
                }
                setGameWrong()
            }
        }
    }
    var canRead = true
    @objc func handlePan(_ sender: UIPanGestureRecognizer) {
        if gameState != .playing {
            return
        }
        let state = sender.state
        if state == .began {
            playSound("effect/cungchoi_pick\(random(1, 2))")
            return
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        let p = sender.location(in: self)
        hand.frame = CGRectMake(p.x, p.y, 1, 1)
        drawingLine.views[1] = hand
        drawingLine.createPath()
        drawingLine.isHidden = false
        
        // Ngưỡng khoảng cách để snap (có thể điều chỉnh)
        let snapThreshold: CGFloat = 50.0
        
        // Kiểm tra snap trong quá trình kéo
        var nearestView: UIView? = nil
        var minDistance: CGFloat = (selectedView?.bounds.height)!/2
        
        for view in allViews where view != selectedView {
            let viewCenter = view.convert(CGPoint(x: view.bounds.width / 2, y: view.bounds.height / 2), to: self)
            let distance = hypot(p.x - viewCenter.x, p.y - viewCenter.y)
            if distance < minDistance {
                minDistance = distance
                nearestView = view
            }
        }
        
        if nearestView == nil {
            canRead = true
        }
        
        if state == .changed {
            // Nếu gần một view khác trong lúc kéo, cập nhật drawingLine để "tạm snap"
            if let nearest = nearestView{
                if selectedView?.superview != nearest.superview {
                    if canRead {
                        canRead = false
                        if let label = nearest.viewWithTag(3) as? UILabel {
                            playSound("ngonngu/noi tu ghep/\(label.text!)")
                        }
                    }
                    drawingLine.views[1] = nearest
                    drawingLine.createPath()
                }
            }
        }
        
        if state == .ended || state == .cancelled || state == .failed {
            drawingLine.isHidden = true
            if nearestView != nil {
                playSound("effect/word puzzle drop")
            } else {
                playSound("effect/slide2")
            }
            if let line = viewConnectedToLine[selectedView!] {
                line.alpha = 1
            }
            
            // Snap tự động khi thả tay nếu đủ gần
            if let nearest = nearestView {
                let index = nearest.tag - 100
                //playSound("topics/\(folder[index])/\(items[index].path!.replacingOccurrences(of: ".svg", with: ""))")
                connect(left: selectedView!, right: nearest)
            } else {
                // Nếu không snap, kiểm tra xem người dùng có thả tay trong một view không
                for view in allViews where view != selectedView {
                    if sender.placeInView(view: view) {
                        let index = view.tag - 100
                        //playSound("topics/\(folder[index])/\(items[index].path!.replacingOccurrences(of: ".svg", with: ""))")
                        connect(left: selectedView!, right: view)
                        break
                    }
                }
            }
            
            selectedView = nil // Reset selectedView sau khi thả
        }
    }
    
    class LinesBetweenViews: UIView {
        var views: [UIView] = []
        var shapeLayer: CAShapeLayer { return self.layer as! CAShapeLayer }
        public var pathColor: UIColor = .color(hex: "$FFFFFF"){
            didSet {
                guard let layer = self.layer as? CAShapeLayer else { return }
                layer.strokeColor = pathColor.cgColor
            }
        }
        override class var layerClass : AnyClass {
            return CAShapeLayer.self}
        override var bounds: CGRect {
            didSet {
            }
        }
        override func didMoveToSuperview() {
            shapeLayer.strokeColor = pathColor.cgColor
            shapeLayer.fillColor = UIColor.clear.cgColor
            shapeLayer.lineWidth = Utilities.isIPad ? 20 : 10
            shapeLayer.borderWidth = 1
            shapeLayer.lineCap = .round
            shapeLayer.borderColor = UIColor.color(hex: "#FFFFFF").cgColor
        }
        override func didAddSubview(_ subview: UIView) {
            createPath()
        }
        public func createPath() {
            let path = UIBezierPath()
            var view1 = views[0]
            var view2 = views[1]
            var p1 = views[0].convert(CGPoint.zero, to: self)
            var p2 = views[1].convert(CGPoint.zero, to: self)
            if p1.y > p2.y { //Thay đổi so sánh p1.x > p2.x thành p1.y > p2.y
                view1 = views[1]
                view2 = views[0]
                let p = p1
                p1 = p2
                p2 = p
            }
            p1.x += view1.bounds.width / 2 // Thay đổi từ p1.x += view1.bounds.width thành p1.x += view1.bounds.width / 2
            if view1.stringTag == "top" {
                p1.y += view1.bounds.height * 0.95
            } else {
                p1.y += view1.bounds.height * 0.05
            }
            if view2.stringTag == "top" {
                p2.y += view2.bounds.height * 0.95
            } else {
                p2.y += view2.bounds.height * 0.05
            }
            p2.x += view2.bounds.width / 2 // thay đổi từ p2.y += view2.bounds.height/2 thành p2.x += view2.bounds.width / 2
            path.move(to: p1)
            path.addLine(to: p2)
            shapeLayer.path = path.cgPath
        }
    }
        
       
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("ngonngu/ngonngu_noi tu ghep")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    func createItemBottom(text:String)->UIView{
        let view = UIView()
        let background = SVGImageView(SVGName: "option_bg_white_shadow")
        background.tag = 2
        view.addSubview(background)
        background.makeViewCenterAndKeep(ratio: 40/42.0)
        
        let label = AutosizeLabel()
        label.font = .Freude(size: 20)
        label.textColor = .color(hex: "#849BFD")
        label.text = text
        label.tag = 3
        view.addSubview(label)
        label.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.6)
        }
        return view
    }
}
