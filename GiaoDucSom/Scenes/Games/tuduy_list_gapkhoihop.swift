//
//  tuduy_list_gapkhoihop.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 8/4/25.
//


import UIKit
import SnapKit

class tuduy_list_gapkhoihop: NhanBietGameFragment {
    // MARK: - Properties
    let allNets: [[[Int]]] = [
        [[2, 1], [2, 2], [2, 3], [1, 3], [1, 4], [1, 5]],
        [[3, 1], [3, 2], [2, 2], [2, 3], [1, 3], [1, 4]],
        [[3, 1], [3, 2], [2, 2], [2, 3], [2, 4], [1, 4]],
        [[1, 1], [2, 1], [2, 2], [2, 3], [2, 4], [3, 4]],
        [[1, 1], [2, 1], [3, 1], [2, 2], [2, 3], [2, 4]],
        [[2, 1], [2, 2], [2, 3], [2, 4], [1, 2], [3, 4]],
        [[2, 1], [2, 2], [2, 3], [1, 2], [3, 3], [3, 4]],
        [[2, 1], [2, 2], [2, 3], [1, 3], [3, 3], [3, 4]],
        [[2, 1], [2, 2], [2, 3], [2, 4], [3, 1], [1, 2]],
        [[2, 1], [2, 2], [2, 3], [2, 4], [3, 2], [1, 2]],
        [[2, 1], [2, 2], [2, 3], [2, 4], [3, 2], [1, 3]]
    ]
    
    private var gridLayout: MyGridView!
    private var coinView: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        gridLayout = MyGridView()
        addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false        
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let net1 = createRandomNetAndCheck(needValid: true)
        let net2 = createRandomNetAndCheck(needValid: false)
        let net3 = createRandomNetAndCheck(needValid: false)
        let nets = [net1, net2, net3]
        let indexes = (0..<3).shuffled()
        
        var maxRow = 0
        var maxCol = 0
        var grids: [[[Int]]] = []
        for net in nets {
            let grid = convertNetToGrid(net: net)
            grids.append(grid)
            maxRow = max(maxRow, grid.count)
            maxCol = max(maxCol, grid[0].count)
        }
        
        var views: [UIView] = []
        for i in 0..<indexes.count {
            let index = indexes[i]
            let grid = grids[index]
            let piece = PieceView()
            var expandedPiece = expandPiece(piece: grid, m3: maxRow, n3: maxCol)
            for x in 0..<expandedPiece.count {
                for y in 0..<expandedPiece[0].count {
                    if expandedPiece[x][y] == 1 {
                        expandedPiece[x][y] = 2
                    }
                }
            }
            piece.setPiece(expandedPiece)
            piece.tag = index
            //AnimationUtils.setTouchEffect(view: piece)
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(onPieceTapped(_:)))
            piece.addGestureRecognizer(tapGesture)
            piece.isUserInteractionEnabled = true
            views.append(piece)
            piece.transform = CGAffineTransform(scaleX: 0, y: 0)
            piece.alpha = 0
        }
        
        gridLayout.columns = views.count
        gridLayout.itemRatio = Float(maxCol) / Float(maxRow)
        gridLayout.itemSpacingRatio = 0.1
        gridLayout.insetRatio = 0.1
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_gap khoi hop")
        let gridDelay = delay + gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: gridDelay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_gap khoi hop")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func onPieceTapped(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let index = view.tag as? Int else { return }
        
        pauseGame()
        if index == 0 { // net1 (valid net) luôn ở index 0
            UIView.animate(withDuration: 0) {
                self.coinView.frame = view.frame
            }
            animateCoinIfCorrect(view: coinView)
            let delay = playSound("effect/answer_correct1", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(after: 0.5) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Helper Methods
    func isValidNet(b: [[Int]]) -> Bool {
        guard b.count == 6 else { return false }
        
        let normalizedNets = allNets.map { normalizeCoords(coords: $0) }
        let variants = getAllVariants(b: b)
        
        return variants.contains { variant in
            normalizedNets.contains { areEqual(a: $0, b: variant) }
        }
    }
    
    func normalizeCoords(coords: [[Int]]) -> [[Int]] {
        var minX = Int.max
        var minY = Int.max
        for c in coords {
            minX = min(minX, c[0])
            minY = min(minY, c[1])
        }
        
        var list = coords.map { [$0[0] - minX, $0[1] - minY] }
        list.sort { a, b in
            if a[0] == b[0] { return a[1] < b[1] }
            return a[0] < b[0]
        }
        return list
    }
    
    func areEqual(a: [[Int]], b: [[Int]]) -> Bool {
        guard a.count == b.count else { return false }
        return a.elementsEqual(b) { $0.elementsEqual($1) }
    }
    
    func getAllVariants(b: [[Int]]) -> [[[Int]]] {
        let b0 = b
        let b90 = rotate90(coords: b0)
        let b180 = rotate90(coords: b90)
        let b270 = rotate90(coords: b180)
        
        let b0Flip = flipHorizontal(coords: b0)
        let b90Flip = flipHorizontal(coords: b90)
        let b180Flip = flipHorizontal(coords: b180)
        let b270Flip = flipHorizontal(coords: b270)
        
        return [
            normalizeCoords(coords: b0),
            normalizeCoords(coords: b90),
            normalizeCoords(coords: b180),
            normalizeCoords(coords: b270),
            normalizeCoords(coords: b0Flip),
            normalizeCoords(coords: b90Flip),
            normalizeCoords(coords: b180Flip),
            normalizeCoords(coords: b270Flip)
        ]
    }
    
    func copyOf(arr: [[Int]]) -> [[Int]] {
        arr.map { [$0[0], $0[1]] }
    }
    
    func rotate90(coords: [[Int]]) -> [[Int]] {
        coords.map { [ $0[1], -$0[0]] }
    }
    
    func flipHorizontal(coords: [[Int]]) -> [[Int]] {
        coords.map { [-$0[0], $0[1]] }
    }
    
    func createRandomConnectedNet() -> [[Int]] {
        var used: Set<String> = []
        var cells: [[Int]] = [[0, 0]]
        used.insert("0,0")
        
        let directions = [[1, 0], [-1, 0], [0, 1], [0, -1]]
        
        while cells.count < 6 {
            let base = cells.randomElement()!
            var added = false
            for _ in 0..<20 {
                let dir = directions.randomElement()!
                let nx = base[0] + dir[0]
                let ny = base[1] + dir[1]
                let key = "\(nx),\(ny)"
                if !used.contains(key) {
                    cells.append([nx, ny])
                    used.insert(key)
                    added = true
                    break
                }
            }
            if !added {
                return createRandomConnectedNet()
            }
        }
        
        return Array(cells.prefix(6))
    }
    
    func createRandomNetAndCheck(needValid: Bool) -> [[Int]] {
        for _ in 0..<100 {
            let net = createRandomConnectedNet()
            let isValid = isValidNet(b: net)
            if isValid == needValid {
                if !needValid {
                    if Int.random(in: 0..<10) < 9 && containsPositive2x2Square(matrix: convertNetToGrid(net: net)) {
                        continue
                    }
                }
                return net
            }
        }
        return [] // Không tìm được net hợp lệ
    }
    
    func containsPositive2x2Square(matrix: [[Int]]) -> Bool {
        guard matrix.count >= 2 && matrix[0].count >= 2 else { return false }
        
        let rows = matrix.count
        let cols = matrix[0].count
        for i in 0..<(rows - 1) {
            for j in 0..<(cols - 1) {
                if matrix[i][j] > 0 && matrix[i][j + 1] > 0 &&
                   matrix[i + 1][j] > 0 && matrix[i + 1][j + 1] > 0 {
                    return true
                }
            }
        }
        return false
    }
    
    func convertNetToGrid(net: [[Int]]) -> [[Int]] {
        var minX = Int.max
        var maxX = Int.min
        var minY = Int.max
        var maxY = Int.min
        
        for cell in net {
            minX = min(minX, cell[0])
            maxX = max(maxX, cell[0])
            minY = min(minY, cell[1])
            maxY = max(maxY, cell[1])
        }
        
        let rows = maxY - minY + 1
        let cols = maxX - minX + 1
        var grid = Array(repeating: Array(repeating: 0, count: cols), count: rows)
        
        for cell in net {
            let row = cell[1] - minY
            let col = cell[0] - minX
            grid[row][col] = 1
        }
        
        return grid
    }
    
    private func expandPiece(piece: [[Int]], m3: Int, n3: Int) -> [[Int]] {
        let m = piece.count
        let n = piece[0].count
        guard m3 >= m && n3 >= n else { fatalError("Kích thước mới quá nhỏ so với piece gốc!") }
        
        var expanded = Array(repeating: Array(repeating: 0, count: n3), count: m3)
        let rowOffset = (m3 - m) / 2
        let colOffset = (n3 - n) / 2
        
        for r in 0..<m {
            for c in 0..<n {
                expanded[rowOffset + r][colOffset + c] = piece[r][c]
            }
        }
        return expanded
    }
}

