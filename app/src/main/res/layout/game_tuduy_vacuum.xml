<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#626A7A"
    android:clipChildren="false"
    android:clipToPadding="false">


    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_square"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#22242D"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginLeftPercent="0.2"
        app:pcl_layout_marginRightPercent="0.2">

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/grid_layout_map"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#3A3C47"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.97"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/image_vacuum_zoom"
            android:alpha="0"
            android:layout_width="0dp"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintHeight_percent="0.97"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintBottom_toBottomOf="parent"
            android:src="@drawable/tuduy_vacuum_end2"
            android:layout_height="0dp"/>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_car"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.2">

            <ImageView
                android:id="@+id/image_car"
                android:visibility="invisible"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/tuduy_car" />

            <com.kidsup.giaoducsom.utils.xaml.XamlAnimationView
                android:id="@+id/vacuum_animation_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

    <com.kidsup.giaoducsom.view.SVGAutosizeView
        android:id="@+id/image_pin"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="192:1039"
        app:layout_constraintHeight_percent="0.8"
        app:layout_constraintHorizontal_bias="0.3"
        app:layout_constraintLeft_toRightOf="@id/view_square"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>