//
//  phonics_list_wordpuzzle.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 09/11/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_wordpuzzle: GameFragment {
    private var values : [String] = []
    var selectedValues: [String] = []
    var labelCenter = UILabel()
    var svgView = SVGImageView(frame: .zero)
    var lettersView = UIView()
    var topContainer = UIView()
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#E2EEFE")
        addSubview(labelCenter)
        labelCenter.isHidden = true
        labelCenter.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()//.multipliedBy(1.1)
        }
        svgView.contentMode = .scaleAspectFit
        addSubview(svgView)
        svgView.snp.makeConstraints{ make in
            make.top.equalTo(labelCenter.snp.bottom)
            make.bottom.equalTo(self.safeAreaLayoutGuide)
            make.left.right.equalToSuperview()
        }
        addSubview(lettersView)
        lettersView.snp.makeConstraints{ make in
            make.edges.equalTo(labelCenter)
        }
        addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.bottom.equalTo(lettersView.snp.top)
        }
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
    }
    
    private var selectedView: UIView?
    private var zPosition: CGFloat = 1
    private var tx: Double = 0, ty: Double = 0
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        if gameState != .playing { return }
        let state = sender.state
        if state == .began {
            selectedView = sender.nearestView(views: lettersView.subviews.map{$0.subviews[1]})
            tx = Double(selectedView!.transform.tx)
            ty = Double(selectedView!.transform.ty)
            zPosition += 1
            selectedView?.superview?.layer.zPosition = zPosition
            //self.playSound(name: (selectedView as! UILabel).text! + "1", delay: 0.1)
            UIView.animate(withDuration: 0.3) {
                self.selectedView?.transform = CGAffineTransformMakeScale(1.1, 1.1).concatenating(self.selectedView!.transform)
            }
            playSound("effect/cungchoi_pick\(random(1,2))")
            return;
        }
        if selectedView == nil {
            return
        }
        
        let translation = sender.translation(in: self)
        sender.setTranslation(CGPointZero, in: self)
        var transform = selectedView!.transform
        transform.tx += translation.x
        transform.ty += translation.y
        selectedView?.transform = transform
        if state == .ended || state == .cancelled || state == .failed {
            if let selectedView = selectedView as? UILabel {
                let closedBottomView = selectedView.nearestView(views: lettersView.subviews.map{$0.subviews[0]}) as? UILabel
                if selectedView.text == closedBottomView?.text && closedBottomView?.alpha != 0 {
                    
                    self.playSound(name: "en/english phonics/effects/word puzzle drop")
                    let temp = selectedView.transform
                    selectedView.transform = CGAffineTransformMakeTranslation(selectedView.transform.tx, selectedView.transform.ty)
                    let d = selectedView.distanceFromCenterToCenter(to: closedBottomView!)
                    selectedView.transform = temp
                    UIView.animate(withDuration: 0.3) {
                        selectedView.transform = CGAffineTransformMakeTranslation(selectedView.transform.tx - d.x, selectedView.transform.ty - d.y)
                        closedBottomView?.alpha = 0.0
                        selectedView.textColor = .color(hex: "#73D048")
                    }
                    scheduler.schedule(delay: 0.5, execute: {
                        [weak self] in
                        guard let self = self else { return }
                        if lettersView.subviews.map{$0.subviews[0]}.allSatisfy{$0.alpha == 0} {
                            CoinAnimationUtils.shared.animate(view: lettersView, answer: true)
                            var delay = 0.5
                            delay += self.playSound(name: getCorrectHumanSound(), delay: delay)
                            delay += self.playSound(name: endGameSound(), delay: delay)
                            delay += 1
                            scheduler.schedule(delay: delay, execute: {
                                [weak self] in
                                guard let self = self else { return }
                                self.finishGame()
                            })
                        }
                    })
                } else {
                    self.playSound(name: "effect/slide2")
                    UIView.animate(withDuration: 0.3) {
                        selectedView.transform = CGAffineTransformMakeTranslation(self.tx, self.ty)
                    }
                }
            }
        }
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            for sound in self.parseIntroText()! {
                delay += self.playSound(name: sound.replacingOccurrences(of: "@", with: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer), delay: delay)
            }
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder()
        selectedValues = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: 2)
        loadNextStep()
    }
    var step = 0
    var answer = ""
    func loadNextStep(){
        pauseGame()
        CoinAnimationUtils.shared.removeList()
        if step >= 1 {
            var delay = 0.5            
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = selectedValues[step]
        step += 1
        var delay = playSound(openGameSound())
        for sound in self.parseIntroText()! {
            delay += self.playSound(name: sound.replacingOccurrences(of: "@", with: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer), delay: delay)
        }
        svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(answer)))!] : "english phonics/\(game.level!)/\(answer).svg"
        labelCenter.text = answer
        let fontSize = fontSizeThatFits(string: answer, boundingSize: CGSizeMake(bounds.width * 0.8, bounds.height * 0.2), font: .Freude(size: 15))
        labelCenter.font = .Freude(size: fontSize)
        lettersView.removeAllSubviews()
        var anchorView = lettersView
        for i in 0..<answer.count {
            let text = "\(answer[i])"
            let container = UIView()
            lettersView.addSubview(container)
            container.snp.makeConstraints { make in
                make.left.equalTo(anchorView == lettersView ? anchorView.snp.left :  anchorView.snp.right)
                make.top.bottom.equalTo(lettersView)
            }
            let label1 = UILabel()
            label1.text = text
            label1.font = labelCenter.font
            label1.textColor = .color(hex: "#C0D6DD")
            container.addSubview(label1)
            label1.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            let label2 = UILabel()
            label2.text = text
            label2.font = labelCenter.font
            label2.textColor = .color(hex: "#1497E0")
            container.addSubview(label2)
            label2.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            anchorView = container
        }
        svgView.alpha = 0
        //delay = 3
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.moveImage()
        })
    }
    func moveImage(){
        svgView.transform = CGAffineTransformMakeTranslation(0, svgView.bounds.height * 3)
        UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 1.0, options: .curveEaseOut, animations: {
            self.svgView.transform = CGAffineTransformMakeScale(1.2, 1.2)
            self.svgView.alpha = 1
        }, completion: { _ in
            
        })
        scheduler.schedule(delay: 0.2, execute: {
            [weak self] in
            guard let self = self else { return }
            self.playSound(name: "en/english phonics/effects/word puzzle break")
            self.moveLetters()
        })
    }
    func moveLetters(){
        let indexes = Array(0..<answer.count).randomOrder()
        var i = 0
        for view in self.lettersView.subviews {
            let label2 = view.subviews[1]
            let d = label2.distanceFromCenterToCenter(to: self.topContainer)
            let step = self.topContainer.bounds.width / CGFloat(self.answer.count)
            let tx = -d.x - self.topContainer.bounds.width / 2.0 + step / 2.0 + step * CGFloat(indexes[i])
            let ty = -d.y - CGFloat.random(in: -1..<1) * (topContainer.bounds.height / 2.0 - view.bounds.height / 2.0)
            UIView.animate(withDuration: 0.5) {
                label2.transform = CGAffineTransformMakeTranslation( tx, ty)
            }
            i += 1
        }
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            self.startGame()
        })
    }
    func fontSizeThatFits(string: String, boundingSize: CGSize, font: UIFont) -> CGFloat {
        var minFontSize: CGFloat = 5.0
        var maxFontSize: CGFloat = 1000.0
        var fontSize: CGFloat = 0.0
        
        while minFontSize <= maxFontSize {
            fontSize = (minFontSize + maxFontSize) / 2
            let attributes = [NSAttributedString.Key.font: font.withSize(fontSize)]
            let boundingRect = (string as NSString).boundingRect(with: CGSizeMake(.greatestFiniteMagnitude, .greatestFiniteMagnitude), options: .usesLineFragmentOrigin, attributes: attributes, context: nil)
            
            if boundingRect.size.width < boundingSize.width && boundingRect.size.height < boundingSize.height {
                minFontSize = fontSize + 1
            } else {
                maxFontSize = fontSize - 1
            }
        }
        
        return fontSize
    }
       
    override func getSkills() -> [GameSkill] {
        return [.GameReading]
    }
    override func getScore() -> Float {
        return 1.0 / (1.0 + Float(incorrect))
    }
}

