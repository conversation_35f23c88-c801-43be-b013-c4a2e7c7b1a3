//
//  toancoban_list_9bacthang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 3/4/25.
//


import UIKit
import SnapKit

class toancoban_list_9bacthang: NhanBietGameFragment {
    // MARK: - Properties
    private let topColorHighlight = UIColor(red: 255/255, green: 222/255, blue: 115/255, alpha: 1) // #FFDE73
    private let topColorNormal = UIColor(red: 255/255, green: 194/255, blue: 112/255, alpha: 1) // #FFC270
    private let textColorHighlight = UIColor(red: 255/255, green: 222/255, blue: 115/255, alpha: 1) // #FFDE73
    private let textColorNormal = UIColor(red: 204/255, green: 109/255, blue: 41/255, alpha: 1) // #CC6D29
    private var itemContainer: UIView!
    private var boyContainer: UIView!
    private var currentNumber: Int = -1
    let xamlBoy = XAMLAnimationView()
  
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white
        
        let outerContainer = UIView()
        outerContainer.clipsToBounds = false
        view.addSubview(outerContainer)
        //outerContainer.backgroundColor = .green
        outerContainer.makeViewCenterAndKeep(ratio: 1.8)
                
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        outerContainer.addSubview(itemContainer)
        //itemContainer.backgroundColor = .blue
        itemContainer.snp.makeConstraints { make in
            make.width.equalTo(itemContainer.snp.height).multipliedBy(3.3)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        
        // Tạo 10 bậc thang
        let heights: [CGFloat] = [0.33, 0.40444444444, 0.47888888888, 0.55333333333, 0.62777777777, 0.70222222222, 0.77666666666, 0.85111111111, 0.92555555555, 1.0]
        for i in 0..<10 {
            let stepView = createStepView(number: i)
            itemContainer.addSubview(stepView)
            
            stepView.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.09)
                make.height.equalToSuperview().multipliedBy(heights[i])
                make.bottom.equalToSuperview()
                make.right.equalToSuperview().multipliedBy(Double(i)*0.1+0.1-0.005)
            }
             
            if i > 0 {
                stepView.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(moveToStep(_:))))
            }
        }
        
        boyContainer = UIView()
        //boyContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        outerContainer.addSubview(boyContainer)
        boyContainer.snp.makeConstraints { make in
            make.width.equalTo(outerContainer).multipliedBy(0.22)
            make.width.equalTo(boyContainer.snp.height) // Ratio 1:1
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
                
        boyContainer.addSubview(xamlBoy)
        xamlBoy.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        do {
            let xamlData = try XAMLParser.parseXMLFile(at: Utilities.url(xamlFile: "jumpingboy")!)
            xamlBoy.loadView(from: xamlData)
        } catch {}
    }
    
    // MARK: - Create Step View
    private func createStepView(number: Int) -> UIView {
        let container = UIView()
        
        let imageTop = UIImageView(image: Utilities.SVGImage(named: "math_9bacthang_bg"))
        imageTop.contentMode = .scaleAspectFit
        container.addSubview(imageTop)
        imageTop.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(imageTop.snp.width).multipliedBy(59.7/239.1)
        }
        
        let middleView = UIView()
        middleView.backgroundColor = UIColor(red: 236/255, green: 146/255, blue: 71/255, alpha: 1) // #EC9247
        container.addSubview(middleView)
        middleView.snp.makeConstraints { make in
            make.top.equalTo(imageTop.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        let textContainer = UIView()
        container.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.width.equalTo(textContainer.snp.height).multipliedBy(1.1) // Ratio 1.1:1
        }
        
        let textName = AutosizeLabel()
        textName.text = String(number)
        textName.textColor = number <= currentNumber ? textColorHighlight : textColorNormal
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.1
        //textName.maximumContentSize = CGSize(width: 500, height: 500)
        textContainer.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageTop.tintColor = number <= currentNumber ? topColorHighlight : topColorNormal
        
        container.tag = number
        return container
    }
    
    // MARK: - Touch Handling
    @objc private func moveToStep(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let number = view.tag
        moveTo(number: number)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), "toan/toan_9 bac thang")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_9 bac thang")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        moveTo(number: 0)
        scheduler.schedule(delay: 1000 / 1000) {
            [weak self] in
            guard let self = self else { return }
            self.xamlBoy.startAnimationStoryboard(with: "standing")
        }
    }
    
    private func moveTo(number: Int) {
        guard number == currentNumber + 1 else { return }
        pauseGame()
        currentNumber = number
        let duration = number == 0 ? 0 : 1.0
        let destView = itemContainer.subviews[number]
        let point = boyContainer.distanceFromCenterToCenter(to: destView)
        // Tính toán vị trí đích của boyContainer dựa trên destView
        let currentTranslationX = self.boyContainer.transform.tx // Vị trí hiện tại theo X
        let currentTranslationY = self.boyContainer.transform.ty // Vị trí hiện tại theo Y
        let deltaX = -point.x // Khoảng cách cần di chuyển theo X để căn giữa destView
        let centerYOffset = -(self.boyContainer.frame.height / 2 + destView.frame.height / 2) // Căn giữa theo Y giữa boyContainer và destView
        let fineTuneYOffset = destView.frame.width / 7 // Điều chỉnh nhỏ theo Y dựa trên chiều rộng destView
        let targetTranslationX = currentTranslationX + deltaX
        let targetTranslationY = currentTranslationY - point.y + centerYOffset + fineTuneYOffset

        // Animation di chuyển boyContainer đến vị trí đích
        let animationDuration = duration * 4 / 6
        let animationDelay = duration / 6
        UIView.animate(withDuration: animationDuration, delay: animationDelay, options: .curveLinear) {
            self.boyContainer.transform = CGAffineTransform(translationX: targetTranslationX, y: targetTranslationY)
        }
                
        if number != 0 {
            playSound("effect/hit\(Int.random(in: 1...4))")
            xamlBoy.startAnimationStoryboard(with: "jump")
        }
        
        scheduler.schedule(delay: duration) { [weak self] in
            self?.updateColor()
            let delay = number == 0 ? 0 : self?.playSound("effect/jump_landing", "topics/Numbers/\(number)") ?? 0
            if number >= 1 {
                self?.scheduler.schedule(delay: delay) {
                    [weak self] in
                    guard let self = self else { return }
                    self.resumeGame()
                }
            }
        }
        
        if number == 9 {
            pauseGame()
            var delay: TimeInterval = 1.5
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.xamlBoy.startAnimationStoryboard(with: "win")
            }
            delay += 0.5
            animateCoinIfCorrect(view: xamlBoy)
            delay += playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func updateColor() {
        for i in 0..<itemContainer.subviews.count {
            let child = itemContainer.subviews[i]
            if let textName = child.subviews.last?.subviews.first as? UILabel,
               let imageTop = child.subviews.first as? UIImageView {
                textName.textColor = i <= currentNumber ? textColorHighlight : textColorNormal
                imageTop.tintColor = i <= currentNumber ? topColorHighlight : topColorNormal
            }
        }
    }
}

// MARK: - Supporting Classes

