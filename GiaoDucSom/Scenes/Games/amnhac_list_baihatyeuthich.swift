//
//  amnhac_list_baihatyeuthich.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/7/25.
//
import UIKit
import SnapKit
import AVFoundation // Đ<PERSON> thay thế SoundPool và AudioManager
import SVGKit


class SVGPath {
    func getId() -> String? { return nil }
    func getFillColor() -> UIColor { return .clear }
    func setFillColor(_ color: UIColor) { /* Logic set fill color */ }
    func setOpacity(_ opacity: CGFloat) { /* Logic set opacity */ }
}



// Cần một thư viện animation tương tự ViewAnimator
// <PERSON><PERSON> thể dùng UIView.animate, Core Animation hoặc thư viện như Spring
extension UIView {
    static func animate(withDuration duration: TimeInterval, animations: @escaping () -> Void, completion: ((Bool) -> Void)? = nil) {
        UIView.animate(withDuration: duration, delay: 0, options: .curveLinear, animations: animations, completion: completion)
    }
}


// --- Enum và Structs ---

struct TouchHolder {
    var view: UIView
    var svg: SVG
}

// --- Custom Views (Cần tạo các file riêng cho chúng) ---
class AnimatedImageButton: KUButton {}


class PercentConstraintLayout: UIView {} // Dùng SnapKit thay thế

class amnhac_list_baihatyeuthich: MusicGameFragment {
    
    class SVGManager {
        static let shared = SVGManager()
        func loadSVG(resourceName: String, completion: @escaping (SVG) -> Void) {
            // Logic load file SVG từ bundle
            // Ví dụ:
            DispatchQueue.global().async {
                let svg = SVG() // Tạo đối tượng SVG giả
                DispatchQueue.main.async {
                    completion(svg)
                }
            }
        }
    }
    class SVGAutosizeView: SVGKFastImageView {
        
    }
    class SVG : SVGKImage {}
    enum GameStatus {
        case wait, pressable, waitAndPressable, stopped
    }

    // MARK: - Properties
    let speedRatio: CGFloat = 1.0
    let distanceRatio: CGFloat = 1.1
    var audioPlayers: [String: AVAudioPlayer] = [:] // Thay thế cho SoundPool

    let sampleNotes = ["c1", "d1", "e1", "f1", "g1", "a1", "b1", "c2", "d2", "e2", "f2"]
    let colorNotes: [UIColor] = [
        UIColor(hex: "#F73535"), UIColor(hex: "#FF8600"), UIColor(hex: "#FFD300"),
        UIColor(hex: "#05BC34"), UIColor(hex: "#00CFFF"), UIColor(hex: "#1B7BCC"),
        UIColor(hex: "#BE04BA"), UIColor(hex: "#F73535"), UIColor(hex: "#FF8600"),
        UIColor(hex: "#FFD300"), UIColor(hex: "#05BC34"), UIColor(hex: "#00CFFF"),
        UIColor(hex: "#1B7BCC"), UIColor(hex: "#BE04BA")
    ]
    let colorNotes2: [UIColor] = [
        UIColor(hex: "#32F73535"), UIColor(hex: "#32FF8600"), UIColor(hex: "#32FFD300"),
        UIColor(hex: "#3205BC34"), UIColor(hex: "#3200CFFF"), UIColor(hex: "#321B7BCC"),
        UIColor(hex: "#32BE04BA"), UIColor(hex: "#32F73535"), UIColor(hex: "#32FF8600"),
        UIColor(hex: "#32FFD300"), UIColor(hex: "#3205BC34"), UIColor(hex: "#3200CFFF"),
        UIColor(hex: "#321B7BCC"), UIColor(hex: "#32BE04BA")
    ]
    let colorHighlightBg = UIColor(hex: "#552EAFFF")

    let musicKinds = ["piano", "guitar", "flute"]
    var rightNotes: [String] = []
    var gameStatus: GameStatus = .stopped
    var autoPlay = false

    var pathMap: [Int: CAShapeLayer] = [:]
    var colorMap: [Int: UIColor] = [:]
    var themeColor = true

    // Cần thay thế R.raw... bằng tên file trong project
    let beatIds = [["beat_clapping_01", "beat_clapping_02", "beat_clapping_03"],
                   ["beat_drum_01", "beat_drum_02", "beat_drum_03"],
                   ["beat_tambourine_01", "beat_tambourine_02", "beat_tambourine_03"]]
    let beatLogoNames = ["music_top_btn_beat_clapping", "music_top_btn_beat_drum", "music_top_btn_beat_tambourine"]
    var beatIndex = -1

    var notesMap: [Int: UIView] = [:]
    var slursMap: [Int: UIView] = [:]
    var songName: String?
    var hideCurrentNoteTimer: Timer?
    var lyrics: [String] = [" ", " ", "Cây ", "đèn ", " ", " "]
    var notes: [String] = [" ", " ", "2c1", "4c1", " ", " "]
    var durations: [Int] = [0, 0, 500, 1000, 0, 0]
    var currentIndex = 2 // 'index' là từ khóa trong Swift, đổi tên thành 'currentIndex'

    private var svgView = SVGAutosizeView(frame: .zero)
    private var touchHolders: [TouchHolder] = []
    private var contentLayout = UIView()
    private var timeoutHideWrongNote: Timer?
    private var textPaths: [CAShapeLayer] = []
    private var bgPaths: [CAShapeLayer] = []
    private var mapColors: [String: UIColor] = [:]
    private var song: Lyric?
    private var musicKind: String?
    private var notesContainer = UIView()
    private var block_container = UIView()
    private var notesContainerAnimator: UIViewPropertyAnimator?
    private var blockContainerAnimator: UIViewPropertyAnimator?

    private var oldSize: CGSize = .zero

    // MARK: - UI Components
    private let topView = UIView()
    private let btnClose = AnimatedImageButton()
    private let btnTutorial = AnimatedImageButton()
    private let textNote = HeightRatioTextView()
    private let musicKindButton = AnimatedImageButton()
    private let musicBeatButton = AnimatedImageButton()
    private let btnTheme = AnimatedImageButton()

    private let musicNoteContainer = UIView()
    private let clefImageView = UIImageView()
    private let beatImageView = UIImageView()
    private let highlightBg = UIView()
    private let wrongNoteImageView = UIImageView()

    private let bottomView = UIView()
    private let lyricView = UIView() // Placeholder for item_lyric.xml
    private let btnPlayPauseReset = AnimatedImageButton()
    private let btnList = AnimatedImageButton()
    
    private let rightText = UILabel()
    private let textContainer = UIStackView() // Container cho các label lyric
    
    
    override func configureLayout(_ view: UIView) {
        backgroundColor = .white
        setupUI()
        configureLayout()
        //loadInitialData()
    }
    
    override func updateLayout() {
        super.updateLayout()
        handleOnLayout()
    }
    
    override func updateData() {
        super.updateData()
        loadInitialData()
    }
    
    // Trong file MusicPlayerGameView.swift

    override func didMoveToWindow() {
        super.didMoveToWindow()
        if self.window != nil {
            // CÓ CỬA SỔ -> Tương đương viewWillAppear
            // View đã được gắn lên màn hình, sẵn sàng để "quẩy"
            // -> Bắt đầu chạy timer, animation...
            updateStatus()
            if autoPlay, let notesContainerAnimator = notesContainerAnimator {
                notesContainerAnimator.startAnimation()
                blockContainerAnimator?.startAnimation()
            }
        } else {
            // KHÔNG CÓ CỬA SỔ (nil) -> Tương đương viewWillDisappear
            // View đã bị gỡ khỏi màn hình
            // -> Dọn dẹp tài nguyên, dừng timer, animation để tránh rò rỉ bộ nhớ
            if autoPlay {
                notesContainerAnimator?.stopAnimation(true)
                blockContainerAnimator?.stopAnimation(true)
            }
        }
    }

    // MARK: - Setup
    private func setupUI() {
        // Thêm các view vào view chính
        topView.accessibilityIdentifier = "topView"
        bottomView.accessibilityIdentifier = "bottomView"
        musicNoteContainer.accessibilityIdentifier = "musicNoteContainer"
        lyricView.accessibilityIdentifier = "lyricView"
        textContainer.accessibilityIdentifier = "textContainer"
        rightText.accessibilityIdentifier = "rightText"
        btnPlayPauseReset.accessibilityIdentifier = "btnPlayPauseReset"
        btnList.accessibilityIdentifier = "btnList"
        btnTheme.accessibilityIdentifier = "btnTheme"
        btnClose.accessibilityIdentifier = "btnClose"
        btnTutorial.accessibilityIdentifier = "btnTutorial"
        beatImageView.accessibilityIdentifier = "beatImageView"
        clefImageView.accessibilityIdentifier = "clefImageView"
        highlightBg.accessibilityIdentifier = "highlightBg"
        wrongNoteImageView.accessibilityIdentifier = "wrongNoteImageView"
        notesContainer.accessibilityIdentifier = "notesContainer"
        block_container.accessibilityIdentifier = "blockContainer"
        contentLayout.accessibilityIdentifier = "contentLayout"
        svgView.accessibilityIdentifier = "svgView"
        
        addSubview(topView)
        addSubview(musicNoteContainer)
        addSubview(bottomView)
        addSubview(lyricView)
        addSubview(btnPlayPauseReset)
        addSubview(btnList)

        // Setup Top View
        topView.addSubview(musicKindButton)
        topView.addSubview(musicBeatButton)
        topView.addSubview(textNote)
        
        // Setup Music Note Container
        musicNoteContainer.addSubview(UIImageView(image: Utilities.SVGImage(named: "ic_music_bg2"))) // Giả sử tên ảnh
        musicNoteContainer.addSubview(clefImageView)
        musicNoteContainer.addSubview(beatImageView)
        musicNoteContainer.addSubview(highlightBg)
        musicNoteContainer.addSubview(block_container)
        musicNoteContainer.addSubview(notesContainer)
        musicNoteContainer.addSubview(wrongNoteImageView)

        // Setup Bottom View
        bottomView.addSubview(contentLayout)
        contentLayout.addSubview(svgView)
        
        // Setup Lyric View
        lyricView.addSubview(textContainer)
        lyricView.addSubview(rightText)
        for _ in 0..<5 {
            let label = UILabel()
            label.font = .UTMAvo(size: 12)
            label.textColor = .white
            //... config label
            textContainer.addArrangedSubview(label)
        }

        // --- Config các thuộc tính cho view ---
        
        // Top View
        textNote.font = UIFont(name: "UTM Avo Bold", size: 40)
        textNote.textAlignment = .center
        textNote.text = "A"

        musicKindButton.setImage(Utilities.SVGImage(named: "music_top_btn_tieng_guitar"), for: .normal)
        musicBeatButton.setImage(Utilities.SVGImage(named: "music_top_btn_nhip34"), for: .normal)
        //btnTheme.setImage(Utilities.SVGImage(named: "ic_music_bw"), for: .normal)
        
        // Music Note Container
        clefImageView.image = Utilities.SVGImage(named: "ic_music_clef")
        beatImageView.tintColor = UIColor(hex: "#2EAFFF")
        highlightBg.backgroundColor = UIColor(hex: "#552EAFFF")
        wrongNoteImageView.isHidden = true
        
        // Bottom View
        btnPlayPauseReset.setImage(Utilities.SVGImage(named: "ic_music_play"), for: .normal)
        btnList.setImage(Utilities.SVGImage(named: "ic_music_list"), for: .normal)
        
        // Clip to bounds
        musicNoteContainer.clipsToBounds = true
        block_container.clipsToBounds = true
        notesContainer.clipsToBounds = true
        
        // Add targets
        musicKindButton.addTarget(self, action: #selector(didTapMusicKind), for: .touchUpInside)
        btnPlayPauseReset.addTarget(self, action: #selector(didTapPlayPauseReset), for: .touchUpInside)
        btnTheme.addTarget(self, action: #selector(didTapTheme), for: .touchUpInside)
        musicBeatButton.addTarget(self, action: #selector(didTapBeat), for: .touchUpInside)
    }

    private func configureLayout() {
        // Sử dụng SnapKit để thiết lập constraints
        topView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.2)
        }

        musicNoteContainer.snp.makeConstraints { make in
            make.top.equalTo(topView.snp.bottom)
            make.left.right.equalToSuperview()
            make.bottom.equalTo(bottomView.snp.top)
        }
        
        bottomView.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(self.snp.width).multipliedBy(12.0/42.0)
        }
        
        // ... Thêm các constraints chi tiết cho từng element bên trong
        // Ví dụ cho một vài element:
        
        // Constraints trong topView
        musicBeatButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalToSuperview().inset(10) // Giả định
            make.height.equalToSuperview().multipliedBy(0.5)
            make.width.equalTo(musicBeatButton.snp.height)
        }
        
        musicKindButton.snp.makeConstraints { make in
            make.centerY.equalToSuperview()
            make.right.equalTo(musicBeatButton.snp.left).offset(-10) // Giả định
            make.height.width.equalTo(musicBeatButton)
        }
        
        textNote.snp.makeConstraints { make in
            make.centerX.equalToSuperview().multipliedBy(0.67)
            make.top.bottom.equalToSuperview()
            make.width.equalTo(textNote.snp.height).multipliedBy(0.4)
        }
        
        // Constraints trong musicNoteContainer
        let bgImageView = musicNoteContainer.subviews.first as! UIImageView
        bgImageView.snp.makeConstraints { $0.edges.equalToSuperview() }
        
        clefImageView.snp.makeConstraints { make in
            make.top.left.bottom.equalToSuperview()
            make.width.equalTo(clefImageView.snp.height).multipliedBy(63.0/130.0)
        }
        
        beatImageView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.left.equalTo(clefImageView.snp.right)
            make.width.equalTo(beatImageView.snp.height).multipliedBy(25.0/130.0)
        }
        
        let rightGuidelinePercentage = 0.67
        highlightBg.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview()
            make.width.equalTo(highlightBg.snp.height).multipliedBy(0.3)
            make.centerX.equalToSuperview().multipliedBy(rightGuidelinePercentage)
        }
        
        notesContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(highlightBg)
            // Tỷ lệ height/width của notesContainer là 130/50
            // SnapKit không hỗ trợ trực tiếp, cần tính toán
        }
        
        // ... và các constraints khác
        
        contentLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        svgView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        lyricView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            make.top.equalTo(bottomView)
            make.width.equalTo(lyricView.snp.height).multipliedBy(42.0/4.0) // Tỷ lệ 42:4
        }
        
        textContainer.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        btnPlayPauseReset.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.089)
            make.top.equalTo(bottomView)
            make.width.equalTo(btnPlayPauseReset.snp.height).multipliedBy(37.0/42.0)
        }
    }
    
    private func loadInitialData() {
        if let songName = songName {
            self.song = MusicManager.shared.getLyrics()!.first { $0.name.lowercased() == songName.lowercased() }
        }
        let svg = Utilities.GetSVGKImage(named: "music2")
        self.loadSVG(svg)
        
        loadSoundAsync(kind: musicKinds.first ?? "piano")
        
        if let song = self.song {
            loadSong(song)
        }
    }

    // MARK: - Actions
    @objc private func didTapMusicKind() {
        guard let currentKind = musicKind, var index = musicKinds.firstIndex(of: currentKind) else { return }
        index = (index + 1) % musicKinds.count
        loadSoundAsync(kind: musicKinds[index])
    }

    @objc private func didTapPlayPauseReset() {
        if gameStatus == .stopped {
            btnPlayPauseReset.setImage(Utilities.SVGImage(named: autoPlay ? "ic_music_pause" : "ic_music_play"), for: .normal)
            if let song = song {
                loadSong(song)
            }
        } else {
            autoPlay = !autoPlay
            btnPlayPauseReset.setImage(Utilities.SVGImage(named: autoPlay ? "ic_music_pause" : "ic_music_play"), for: .normal)
            if autoPlay {
                moveLeft(fromX: notesContainer.transform.tx + notesContainer.bounds.width / 4)
            } else {
                notesContainerAnimator?.stopAnimation(true)
                blockContainerAnimator?.stopAnimation(true)
            }
        }
    }

    @objc private func didTapTheme() {
        themeColor = !themeColor
        if themeColor {
            updateColorTheme()
            btnTheme.setImage(Utilities.SVGImage(named: "ic_music_bw"), for: .normal)
        } else {
            updateBlackWhiteTheme()
            btnTheme.setImage(Utilities.SVGImage(named: "ic_music_color"), for: .normal)
        }
    }
    
    @objc private func didTapBeat() {
        beatIndex += 1
        loadBeatButton()
    }

    // MARK: - Game Logic
    
    private func loadSVG(_ svg: SVGKImage) {
        svgView.image = svg
        textPaths = []
        bgPaths = []
        
        for i in 0..<svg.caLayerTree.sublayers!.count {
            guard let path = svg.caLayerTree.sublayers?[i] as? CAShapeLayer, let id = path.name, !id.isEmpty else { continue }
            
            if let noteIndex = sampleNotes.firstIndex(of: id) {
                let view = UIView()
                let bounds = path.shapeContentBounds!
                //view.frame = bounds
                view.backgroundColor = .red
                view.alpha = 0.1
                contentLayout.addSubview(view)
                view.snp.makeConstraints { make in
                    make.width.equalToSuperview().multipliedBy(bounds.width / svg.size.width)
                    make.height.equalToSuperview().multipliedBy(bounds.height / svg.size.height)
                    make.right.equalToSuperview().multipliedBy(bounds.maxX / svg.size.width)
                    make.bottom.equalToSuperview().multipliedBy(bounds.maxY / svg.size.height)
                }
                pathMap[noteIndex] = path
                colorMap[noteIndex] = path.getFillColor()?.uiColor
                
                // Thêm gesture recognizer thay cho onTouchListener
                let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleNoteTap(_:)))
                let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(handleNotePress(_:)))
                longPressGesture.minimumPressDuration = 0.0
                view.addGestureRecognizer(tapGesture)
                view.addGestureRecognizer(longPressGesture)
                view.tag = noteIndex
                
            } else {
                if id.hasSuffix("text") { textPaths.append(path) }
                if id.hasSuffix("bg") { bgPaths.append(path) }
                mapColors[id] = path.getFillColor()?.uiColor
            }
        }
    }
    
    @objc private func handleNoteTap(_ gesture: UITapGestureRecognizer) {
        // Xử lý khi note được tap nhanh
    }
    
    @objc private func handleNotePress(_ gesture: UILongPressGestureRecognizer) {
        guard let view = gesture.view, !autoPlay else { return }
        let note = view.tag
        
        if gesture.state == .began {
            press(note: note)
        } else if gesture.state == .ended || gesture.state == .cancelled {
            release(note: note)
        }
    }
    
    private func loadSong(_ song: Lyric) {
        self.song = song
        parse(song)
        currentIndex = 2
        
        let beatImage = Utilities.SVGImage(named: "music/note/beat_\(song.beat)_\(song.speed).svg")
        beatImageView.image = beatImage
        
        
        // Reset UI
        textContainer.transform = .identity
        rightText.alpha = 0.01
        
        // Cập nhật lyric
        // Cập nhật lyric
        for (i, subview) in textContainer.subviews.enumerated() {
            guard let textView = subview as? UILabel, (currentIndex - 2 + i) < lyrics.count else { continue }
            textView.alpha = 1.0
            let lyric = lyrics[currentIndex - 2 + i]
            textView.text = (lyric == "^ ") ? "" : lyric
        }
        
        updateCurrentNote()
        
        btnPlayPauseReset.setImage(Utilities.SVGImage(named: autoPlay ? "ic_music_pause" : "ic_music_play"), for: .normal)
        notesContainer.subviews.forEach { $0.removeFromSuperview() }
        block_container.subviews.forEach { $0.removeFromSuperview() }
        
        notesContainerAnimator?.stopAnimation(true)
        blockContainerAnimator?.stopAnimation(true)
        
        // Dùng DispatchQueue để chờ layout
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            for i in 2..<(self.durations.count - 1) {
                self.addMusicNoteAtPosition(i)
            }
            self.notesContainer.transform = .identity
            self.block_container.transform = .identity
            
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.moveLeft(fromX: 0)
                self.startGame()
            }
        }
        
        // ... Logic tính toán 'drums'
        
        loadBeatButton()
    }
    
    private func moveLeft(fromX: CGFloat) {
        notesContainer.transform = CGAffineTransform(translationX: fromX, y: 0)
        block_container.transform = CGAffineTransform(translationX: fromX, y: 0)
        
        guard let lastView = notesContainer.subviews.last else { return }
        
        let distance = lastView.frame.origin.x + fromX
        let speed = 1280 * speedRatio / distanceRatio
        let duration = TimeInterval(distance / notesContainer.bounds.height * speed)
        
        notesContainerAnimator = UIViewPropertyAnimator(duration: duration, curve: .linear) { [weak self] in
            self?.notesContainer.transform = CGAffineTransform(translationX: fromX - distance, y: 0)
        }
        
        blockContainerAnimator = UIViewPropertyAnimator(duration: duration, curve: .linear) { [weak self] in
            self?.block_container.transform = CGAffineTransform(translationX: fromX - distance, y: 0)
        }
        
        notesContainerAnimator?.startAnimation()
        blockContainerAnimator?.startAnimation()
    }
    
    private func press(note: Int, textNote: String? = nil) {
        // Logic khi nhấn một nốt nhạc
        
        // Play sound
        let soundKey = textNote ?? "2\(sampleNotes[note])"
        playSound(key: soundKey)
        
        // Update UI
        pathMap[note]?.opacity = (0.2)
        svgView.setNeedsDisplay() // Yêu cầu vẽ lại SVG
        
        // Vibrate
        // UIImpactFeedbackGenerator(style: .light).impactOccurred()
        
        // Game logic
        if gameStatus == .pressable {
            // ...
        }
        
        updateNote(note: note)
        updateCurrentNote()
    }
    
    private func release(note: Int) {
        pathMap[note]?.opacity = (1.0)
        svgView.setNeedsDisplay()
    }
    
    private func updateStatus() {
        // Hàm này sẽ được gọi liên tục để cập nhật trạng thái game
        // Có thể dùng CADisplayLink để thay thế cho Handler.postDelayed
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.01) { [weak self] in
            self?.updateStatus()
        }
        
        let viewIndex = rightNotes.count
        let nextIndex = rightNotes.count + 2
        
        let waitingNote = self.waitingNote()
        if waitingNote != -1 {
            textNote.text = sampleNotes[waitingNote].prefix(1).uppercased()
            textNote.textColor = themeColor ? colorNotes[waitingNote] : .black
            highlightBg.backgroundColor = themeColor ? colorNotes2[waitingNote] : colorHighlightBg
        } else {
            textNote.text = ""
            highlightBg.backgroundColor = colorHighlightBg
        }
        
        guard viewIndex < notesContainer.subviews.count - 1 else {
            if gameStatus != .stopped {
                gameStatus = .stopped
                btnPlayPauseReset.setImage(Utilities.SVGImage(named: "ic_music_replay"), for: .normal)
            }
            return
        }
        
        gameStatus = .wait
        let noteView = notesContainer.subviews[viewIndex]
        // Cần lấy vị trí tuyệt đối của noteView
        let noteViewFrame = noteView.convert(noteView.bounds, to: self)
        
        if noteViewFrame.origin.x < notesContainer.bounds.height * 0.23 && notesContainerAnimator?.isRunning == true {
            gameStatus = .pressable
        }
        
        // ... Các logic cập nhật gameStatus khác
        
        if autoPlay && (gameStatus == .pressable || gameStatus == .waitAndPressable) {
            // Logic auto play
            // ...
        }
    }
    
    private func waitingNote() -> Int {
        let nextIndex = rightNotes.count + 2
        if nextIndex < notes.count - 2 {
            return getMusicIndex(note: notes[nextIndex])
        }
        return -1
    }
    
    private func getMusicIndex(note: String) -> Int {
        for (i, sampleNote) in sampleNotes.enumerated() {
            if note.contains(sampleNote) {
                return i
            }
        }
        return 0
    }
    
    private func getMusicDuration(note: String) -> Int {
        // Lấy ký tự đầu tiên của chuỗi nốt nhạc và chuyển thành số
        guard let firstChar = note.first, let duration = Int(String(firstChar)) else {
            return 0
        }
        return duration
    }
    
    private func parse(_ song: Lyric) {
            // Vì Lyric là struct, nó được truyền theo giá trị.
            // Mọi thay đổi sẽ không ảnh hưởng đến bản gốc, nên ta tạo biến tạm.
            var lyricString = song.lyric
            var notesString = song.notes
            
            rightNotes.removeAll()

            // Dọn dẹp chuỗi, loại bỏ các khoảng trắng thừa
            while lyricString.contains("  ") {
                lyricString = lyricString.replacingOccurrences(of: "  ", with: " ")
            }
            while notesString.contains("  ") {
                notesString = notesString.replacingOccurrences(of: "  ", with: " ")
            }

            // Trim và đảm bảo lyric kết thúc bằng dấu "_"
            lyricString = lyricString.trimmingCharacters(in: .whitespacesAndNewlines)
            if !lyricString.hasSuffix("_") {
                lyricString += "_"
            }
            notesString = notesString.trimmingCharacters(in: .whitespacesAndNewlines)

            // Phân tích chuỗi nốt nhạc
            var notesArray = notesString.components(separatedBy: .whitespaces)
            notesArray.insert("", at: 0)
            notesArray.insert("", at: 0)
            notesArray.append("")
            notesArray.append("")
            self.notes = notesArray

            // Phân tích chuỗi lời bài hát
            var lyricsArray: [String] = [" ", " "]
            var currentWord = ""
            for char in lyricString {
                if char == " " {
                    currentWord.append(char)
                    lyricsArray.append(currentWord)
                    currentWord = ""
                    continue
                }
                if char == "_" {
                    lyricsArray.append(currentWord)
                    currentWord = ""
                    continue
                }
                currentWord.append(char)
            }
            lyricsArray.append(" ")
            lyricsArray.append(" ")
            self.lyrics = lyricsArray

            // Phân tích và gán trường độ cho các nốt nhạc
            self.durations = [Int](repeating: 0, count: self.notes.count)
            guard self.notes.count > 4 else { return } // Đảm bảo mảng đủ lớn
            for i in 2..<(self.notes.count - 2) {
                self.durations[i] = getMusicDuration(note: self.notes[i])
            }
        }
        
    
    private func addMusicNoteAtPosition(_ position: Int) {
        // Logic thêm nốt nhạc vào container
        let note = getMusicIndex(note: notes[position])
        var totalDuration: Int = 0
        for i in 0..<position {
            totalDuration += durations[i]
        }
        
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        if !notes[position].isEmpty {
            let imageName = "music/note/\(notes[position]).svg"
            imageView.image = Utilities.SVGImage(named: imageName)
            imageView.tintColor = themeColor ? nil : .black
        }
                
        let xPosition = notesContainer.bounds.height * CGFloat(totalDuration + Int(3.2 / distanceRatio)) / (3.2 / distanceRatio)
        
        // Cần frame cụ thể, không thể dùng MATCH_PARENT như XML
        // Tạm thời đặt frame, cần tính toán chính xác hơn
        imageView.frame = CGRect(x: xPosition, y: 0, width: 50, height: notesContainer.bounds.height)
        
        notesContainer.addSubview(imageView)
        notesMap[position] = imageView
        
        // ... Logic thêm các separator và slur
    }
    
    // MARK: - Sound
    private func loadSoundAsync(kind: String) {
        DispatchQueue.global(qos: .background).async { [weak self] in
            self?.loadSound(kind: kind)
        }
    }
    
    private func loadSound(kind: String) {
        DispatchQueue.main.async { [weak self] in
            self?.musicKindButton.setImage(Utilities.SVGImage(named: "music_top_btn_tieng_\(kind)"), for: .normal)
        }
        self.musicKind = kind
        audioPlayers.removeAll()
        
        let durations = (kind == "guitar") ? [2] : [1, 2, 3, 4, 6, 8]
        for duration in durations {
            for sampleNote in sampleNotes {
                let noteName = "Sounds/effect/music/\(kind)_\(duration)\(sampleNote)"
                let soundKey = "\(duration)\(sampleNote)"
                if let soundURL = Bundle.main.url(forResource: noteName, withExtension: "mp3") { // hoặc wav, ...
                    do {
                        let player = try AVAudioPlayer(contentsOf: soundURL)
                        player.prepareToPlay()
                        audioPlayers[soundKey] = player
                    } catch {
                        print("Error loading sound: \(noteName)")
                    }
                }
            }
        }
        
        // Load beat sounds
        // ...
    }
    
    private func playSound(key: String) {
        guard let player = audioPlayers[key] else {
            print("Sound not found for key: \(key)")
            return
        }
        if player.isPlaying {
            player.stop()
            player.currentTime = 0
        }
        player.play()
    }

    // MARK: - Helpers
    private func handleOnLayout() {
        let newSize = svgView.bounds.size
        if newSize.width <= 0 || newSize.height <= 0 || newSize == oldSize {
            return
        }
        oldSize = newSize
        
        // Re-calculate positions of touch holders
        // ...
        
        // Update font size
        // ...
    }
    
    private func updateBlackWhiteTheme() {
        // ...
    }
    
    private func updateColorTheme() {
        // ...
    }
    
    private func loadBeatButton() {
        // ...
    }
    
    private func updateCurrentNote() {}
    private func updateNote(note: Int) {}
}

// MARK: - UIColor Extension
extension UIColor {
    convenience init(hex: String, alpha: CGFloat = 1.0) {
        var hexSanitized = hex.trimmingCharacters(in: .whitespacesAndNewlines)
        hexSanitized = hexSanitized.replacingOccurrences(of: "#", with: "")

        var rgb: UInt64 = 0

        Scanner(string: hexSanitized).scanHexInt64(&rgb)

        let red = CGFloat((rgb & 0xFF0000) >> 16) / 255.0
        let green = CGFloat((rgb & 0x00FF00) >> 8) / 255.0
        let blue = CGFloat(rgb & 0x0000FF) / 255.0

        self.init(red: red, green: green, blue: blue, alpha: alpha)
    }
}
