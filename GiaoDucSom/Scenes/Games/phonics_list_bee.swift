//
//  phonics_list_bee.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate
import AnyCodable

class phonics_list_bee: GameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var hive: UIView!
    private var hiveBg: SVGImageView!
    private var correctCount: Int = 0
    private var bias: CGFloat = 0
    private var lastestAnswer: String?
    private var stop: Bool = false
    private var coinView: UIView!
    private var values: [String] = []
    private var step: Int = 0
    private var answer: String = ""
    private var chooseValues: [String] = []
    private let bee1 = Utilities.SVGImage(named: "bee1")
    private let bee2 = Utilities.SVGImage(named: "bee2")
    private lazy var mapAnim = [UIView : Interpolate]()
    private lazy var hiveBgImageView = SVGImageView(SVGName: "hive").then{
        $0.transform = CGAffineTransformMakeScale(-1, 1)
    }
    private lazy var hiveBgContainer = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = .clear
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let background = UIImageView(image: Utilities.SVGImage(named: "math_5canhhoa_bg"))
        background.contentMode = .scaleAspectFill
        view.addSubview(background)
        background.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        hiveBg = SVGImageView(frame: .zero)
        hiveBg.contentMode = .scaleAspectFit
        hiveBg.image = Utilities.SVGImage(named: "hive")
        hiveBg.transform = .init(scaleX: -1, y: 1)
        view.addSubview(hiveBg)
        hiveBg.snp.makeConstraints { make in
            make.right.equalToSuperview()
            make.top.equalToSuperview().offset(50)
            make.height.equalToSuperview().multipliedBy(0.2)
            make.width.equalTo(hiveBg.snp.height).multipliedBy(0.8)
        }
        
        hive = UIView()
        hive.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(hive)
        hive.snp.makeConstraints { make in
            make.edges.equalTo(hiveBg)
            make.height.equalTo(hive.snp.width).multipliedBy(1.0)
            make.width.equalTo(hiveBg.snp.width).multipliedBy(0.3)
            make.centerX.equalTo(hiveBg).multipliedBy(0.84) // horizontal_bias=0.42
            make.centerY.equalTo(hiveBg).multipliedBy(1.56) // vertical_bias=0.78
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let progressBar = UIView() // Giả định include view_progress_bar
        view.addSubview(progressBar)
        progressBar.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.height.equalTo(progressBar.snp.width).multipliedBy(1.0 / 20.0) // Ratio 20:1
            make.top.equalToSuperview().offset(view.frame.height * 0.05) // Vertical bias 0.05
            make.centerX.equalToSuperview()
            make.height.lessThanOrEqualTo(20)
        }
        addSubview(hiveBgContainer)
        hiveBgContainer.addSubviewWithInset(subview: hiveBgImageView, inset: 0)
        hiveBgContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.2)
            make.right.equalToSuperview()
            make.top.equalToSuperview()
            make.width.equalTo(hiveBgContainer.snp.height).multipliedBy(0.8)
        }
        
        coinView = UIView() // Giả định include item_coin_view
        coinView.isUserInteractionEnabled = false
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        values = data?.value?.split(separator: ",").map { String($0) } ?? []
        data?.values = values.map {AnyCodable($0)}
        data?.questions = 6
        data?.answer = 6
    }
    
    override func createGame() {
        super.createGame()
        
        let texts = parseIntroText() ?? []
        var delay: TimeInterval = playSound(openGameSound())
        for text in texts {
            delay += playSound(text, delay: delay)
        }
        
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
        
        scheduler.schedule(after: delay + 1) { [weak self] in
            self?.loadNextStep()
            self?.createItem()
        }
        
        setGameProgress(0)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let texts = parseIntroText() ?? []
            var delay: TimeInterval = 0.5
            for text in texts {
                delay += playSound(text, delay: delay)
            }
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func onHintClick() {
        super.onHintClick()
        playSound("games/select\(data?.gamedatatype ?? "")", answer)
    }
    
    // MARK: - Touch Handling
    @objc private func handleSoundTap(_ gesture: UITapGestureRecognizer) {
        pauseGame(stopMusic: false)
        let texts = parseIntroText() ?? []
        var delay: TimeInterval = 0.5
        for text in texts {
            delay += playSound(text, delay: delay)
        }
        scheduler.schedule(after: delay) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    
    @objc private func itemClick(_ sender: UIControl) {
        guard let label = sender.viewWithTag(1) as? AutosizeLabel,
              let imageView = sender.viewWithTag(3) as? SVGImageView else { return }
        
        let text = label.text ?? ""
        if text.lowercased() == answer.lowercased() {
            sender.isUserInteractionEnabled = false
            playSound(text.lowercased())
            correctCount += 1
            
            // Animate bee moving to hive
            let moveInfo = sender.moveToCenterInfo(of: hive)
            let animValues: [CGPoint] = [
                sender.center,
                CGPoint(x: moveInfo.tx, y: moveInfo.ty)
            ]
            let moveAnim = Interpolate(values: animValues) { [weak sender] point in
                sender?.center = point
            }
            moveAnim.animate(1, duration: 2) { [weak self, weak sender] in
                guard let self = self, let sender = sender else { return }
                sender.isHidden = true
                sender.alpha = 0
                sender.removeFromSuperview()
                
                // Update progress
                let totalQuestions = self.data?.answer ?? 5
                let stepCount = 1
                let totalProgress = Double(self.step - 1) / Double(stepCount)
                let subProgress = Double(self.correctCount) / Double(totalQuestions) / Double(stepCount)
                let progress = totalProgress + subProgress
                self.setGameProgress(progress)
                
                if self.correctCount >= totalQuestions {
                    self.pauseGame(stopMusic: false)
                    self.scheduler.schedule(after: 1) { [weak self] in
                        self?.loadNextStep()
                    }
                }
            }
            
            // Animate bee swing (frame animation)
            let frameAnim = Interpolate(values: [0.0, 1.0]) { [weak imageView] _ in
                let step = Int.random(in: 0...1)
                imageView?.image = step % 2 == 0 ? self.bee1 : self.bee2
            }
            frameAnim.animate(1, duration: 2)
            
            sender.animateCoin(answer: true)
        } else {
            playSound(text.lowercased(), delay: 0.5)
            setGameWrong()
            incorrect += 1
            UIView.animate(withDuration: 0.1) {
                sender.transform = .init(scaleX: 0.6, y: 0.6)
            } completion: { _ in
                UIView.animate(
                    withDuration: 0.8,
                    delay: 0,
                    usingSpringWithDamping: 0.15,
                    initialSpringVelocity: 4,
                    options: [.curveEaseOut, .allowUserInteraction],
                    animations: {
                        sender.transform = .identity
                    }
                )
            }
            sender.animateCoin(answer: false)
        }
    }
    
    // MARK: - Helper Methods
    private func loadNextStep() {
        setProgress(index: step, count: 3)
        if step >= 1 {
            stop = true
            pauseGame(stopMusic: false)
            var delay: TimeInterval = 1
            delay += playSound(getCorrectHumanSound(), delay: delay)
            delay += 0.5
            delay += playSound("effects/end game", delay: delay)
            animateCoinIfCorrect(view: coinView)
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
            return
        }
        
        answer = values[0]
        correctCount = 0
        step += 1
        chooseValues = [values[0]]
        scheduler.schedule(after: 1) { [weak self] in
            self?.resumeGame(startMusic: false)
        }
    }
    
    private func getNextBias() -> CGFloat {
        var count = 0
        while true {
            count += 1
            let nextBias = 0.2 + CGFloat.random(in: 0...0.8)
            if abs(nextBias - bias) > 0.3 || count > 20 {
                bias = nextBias
                return bias
            }
        }
    }
    
    private func getNextAnswer() -> String {
        if values.count <= 2 {
            return values.randomElement()!
        }
        while true {
            let answer = values.randomElement()!
            if answer != lastestAnswer {
                lastestAnswer = answer
                return answer
            }
        }
    }
    
    func createItem(){
        let beeView = BeeView()
        let height = itemContainer.frame.size.height
        let width = itemContainer.frame.size.width
        let ans = getNextAnswer()
        beeView.label.text = ans.replacingOccurrences(of: "1", with: "")
        beeView.text = ans
        addSubview(beeView)
        beeView.snp.makeConstraints { make in
            make.left.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(Double.random(in: 0.3..<1))
            make.width.equalTo(beeView.snp.height).multipliedBy(0.5)
            make.height.equalToSuperview().multipliedBy(0.3)
        }
        beeView.transform = CGAffineTransform(translationX: -1000, y: 0)
        
        let animValues: [Double] = [0,180*3]
        let timeChange = Interpolate(values: animValues,
        apply: { [weak self] (value) in
            guard let self = self else { return }
            let xPos = -0.35 * width + (CGFloat(value)/180/3) * 1.75 * width
            let yPos = sin(CGFloat(value) * .pi/180) * height * 0.1
            beeView.transform = CGAffineTransform(translationX: xPos, y: yPos)
            beeView.bgView.image = Int(value/2) % 2 == 0 ? self.bee1 : self.bee2
        })
        timeChange.animate(180*3, duration: 8)
        mapAnim.add([beeView : timeChange])
        
        beeView.addTarget(self, action: #selector(beeClicked), for: UIControl.Event.touchUpInside)
        scheduler.schedule(delay: 7.5) {
            [weak self] in
            guard let self = self else { return }
            beeView.removeFromSuperview()
            self.mapAnim.removeValue(forKey: beeView) // Clean up
        }
         
        scheduler.schedule(delay: 1.5) {
            [weak self] in
            guard let self = self else { return }
            self.createItem()
        }
    }
    
    class BeeView: UIControl, AnimateActionable{
        private lazy var container = UIControl()
        var bgView = SVGImageView(SVGName: "bee")
        var label = AutosizeLabel()
        var text: String?
        override init(frame: CGRect) {
            super.init(frame: frame)
            setupView()
        }
        
        required init?(coder aDecoder: NSCoder) {
            super.init(coder: aDecoder)
            setupView()
        }
        
        func setText(text: String){
            
        }
        func swing(){
            
        }

        private func startAnimatingPressActions() {
            let animateDownSelector = #selector(animateDown)
            let animateDownEvents: UIControl.Event = [.touchDown, .touchDragEnter]
            addTarget(self, action: animateDownSelector, for: animateDownEvents)
            
            let animateUpSelector = #selector(animateUp)
            let animateUpEvents: UIControl.Event = [
                .touchDragExit,
                .touchCancel,
                .touchUpInside,
                .touchUpOutside
            ]
            addTarget(self, action: animateUpSelector, for: animateUpEvents)
        }
        @objc private func animateDown(sender: UIButton) {
            
            animateDown(item: container)
        }
        
        @objc private func animateUp(sender: UIButton) {
            animateUp(item: container)
        }
        func setupView(){
            startAnimatingPressActions()
            isUserInteractionEnabled = true
            container.isUserInteractionEnabled = false
            bgView.contentMode = .scaleAspectFill
            bgView.clipsToBounds = false
            //bgView.backgroundColor = UIColor.green
            addSubview(container)
            container.addSubview(bgView)
            container.addSubview(label)
            
            container.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
             
            bgView.snp.makeConstraints { make in
                make.width.equalTo(bgView.snp.height).multipliedBy(0.5)
                make.width.lessThanOrEqualToSuperview()
                make.height.lessThanOrEqualToSuperview()
                make.width.equalToSuperview().priority(.high)
                make.height.equalToSuperview().priority(.high)
                make.center.equalToSuperview()
            }
            label.snp.makeConstraints { make in
                make.width.equalTo(label.snp.height).multipliedBy(0.7)
                make.width.lessThanOrEqualToSuperview().multipliedBy(0.5)
                make.height.lessThanOrEqualToSuperview().multipliedBy(0.5)
                make.width.equalToSuperview().multipliedBy(0.5).priority(.high)
                make.height.equalToSuperview().multipliedBy(0.5).priority(.high)
                make.centerX.equalToSuperview()
                make.bottom.equalTo(bgView.snp.bottom).multipliedBy(1)
            }
            label.text = "chào"
            label.textColor = UIColor.black
            label.font = .UTMAvo(size: 15)
            label.textAlignment = .center
            label.adjustsFontSizeToFitWidth = true
            label.minimumScaleFactor = 0.1
            label.font = label.font.withSize(100)
        }
    }
    
    @objc func beeClicked(sender : UIControl){
        sender.isUserInteractionEnabled = false
        let animator = mapAnim[sender]
        if let beeView = sender as? BeeView {
            self.playSound(name: beeView.text!.lowercased(), delay: 0.3)
            if beeView.text?.lowercased() == answer.lowercased() {
                //playCorrectSound()
                animator?.stopAnimation()
                scheduler.schedule(delay: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    //sender.animateCoin(answer: true)
                    let distance = sender.layer.presentation()!.convert(CGPoint(x: 0, y: 0), to: self.hiveBgContainer.layer)
                    UIView.animate(withDuration: 1, delay: 0) {
                        sender.transform = CGAffineTransform(scaleX: 0.4, y: 0.4).concatenating(CGAffineTransform(translationX: sender.transform.tx - distance.x, y: sender.transform.ty - distance.y))
                    }
                    UIView.animate(withDuration: 0.7) {
                        sender.alpha = 0.7
                    } completion: { Bool in
                        UIView.animate(withDuration: 0.3) {
                            sender.alpha = 0
                        }
                    }
                }
                correctCount += 1
                let totalQuestions = 6
                let stepCount = 1
                let totalProgress = Double(self.step - 1) / Double(stepCount)
                let subProgress = Double(self.correctCount) / Double(totalQuestions) / Double(stepCount)
                let progress = totalProgress + subProgress
                //self.setGameProgress(progress: progress)
                if correctCount >= 6 {
                    pauseGame()
                    scheduler.schedule(delay: 1) {
                        [weak self] in
                        guard let self = self else { return }
                        self.loadNextStep()
                    }
                }
            } else {
                incorrect += 1
                //sender.animateCoin(answer: false)
                setGameWrong()
                playSound("en/english phonics/effects/games/bee_fail")
            }
        }
        
    }
    
    override func getSkills() -> [GameSkill] {
        return [.GameReading]
    }
    
    override func getScore() -> Float {
        return 6.0 / (6.0 + Float(incorrect))
    }
    deinit {
        
    }
}
