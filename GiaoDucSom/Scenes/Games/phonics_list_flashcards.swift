//
//  phonics_list_flashcards.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 22/07/2023.
//


import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate

class phonics_list_flashcards: GameFragment {
    private var values : [String] = []
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#FF7761")
    }
    var svgView = SVGImageView(frame: .zero).then{
        $0.contentMode = .scaleAspectFit
    }
    var svgView2 = SVGImageView(frame: .zero).then{
        $0.contentMode = .scaleAspectFit
        $0.transform = CGAffineTransformMakeScale(2, 2)
    }
    var leftContainer = UIView()
    var leftButton = SVGButton(SVGIcon: "bot btn replay")
    var rightContainer = UIView()
    var rightButton = SVGButton(SVGIcon: "bot btn next")
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#F5FAFE")
        addSubview(textName)
        addSubview(svgView)
        addSubview(svgView2)
        textName.snp.makeConstraints{ make in
            make.left.top.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.25)
        }
        svgView.snp.makeConstraints{ make in
            make.left.right.equalToSuperview()
            make.top.equalTo(textName.snp.bottom)
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        svgView2.snp.makeConstraints{ make in
            make.left.right.bottom.equalToSuperview()
            make.top.equalTo(svgView.snp.bottom)
        }
        addSubview(leftContainer)
        addSubview(rightContainer)
        let leftBackground = SVGImageView(SVGName: "bot bg left")
        let rightBackground = SVGImageView(SVGName: "bot bg right")
        leftContainer.addSubview(leftBackground)
        rightContainer.addSubview(rightBackground)
        leftContainer.addSubview(leftButton)
        rightContainer.addSubview(rightButton)
        leftContainer.snp.makeConstraints{ make in
            make.left.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        rightContainer.snp.makeConstraints{ make in
            make.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().inset(30)
            make.width.equalToSuperview().multipliedBy(0.2)
            make.height.equalTo(leftContainer.snp.width).multipliedBy(40.0/32.0)
        }
        leftBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.1)
            make.width.equalTo(leftBackground.snp.height).multipliedBy(628.0/401.0)
        }
        rightBackground.snp.makeConstraints{ make in
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(1.85)
            make.width.equalTo(rightBackground.snp.height).multipliedBy(628.0/401.0)
        }
        leftButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        rightButton.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        leftButton.addTarget(self, action: #selector(replay), for: .touchUpInside)
        rightButton.addTarget(self, action: #selector(loadNext), for: .touchUpInside)
        // Create a UISwipeGestureRecognizer for left swipe
        let leftSwipeGesture = UISwipeGestureRecognizer(target: self, action: #selector(handleLeftSwipe(_:)))
        leftSwipeGesture.direction = .left
        
        // Add the gesture recognizer to the view
        self.addGestureRecognizer(leftSwipeGesture)
    }
    @objc func handleLeftSwipe(_ gestureRecognizer: UISwipeGestureRecognizer) {
        if gestureRecognizer.state == .ended {
            slideNext()
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!
        leftContainer.alpha = 0
        rightContainer.alpha = 0
        hideMenu()
        scheduler.schedule(delay: 0.5, execute: {
            [weak self] in
            guard let self = self else { return }
            self.leftContainer.alpha = 1
            self.rightContainer.alpha = 1
        })
        scheduler.schedule(delay: 1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
        })
    }
    var step = 0
    func loadNextStep(){
        self.pauseGame()
        if step >= values.count {
            pauseGame()
            scheduler.schedule(delay: 1) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            return
        }
        let answer = values[step]
        step += 1
        textName.text = game.sound
        svgView.SVGName = "flashcards/\(game.level!)/\(answer).svg"
        svgView2.SVGName = "flashcards/\(game.level!)/\(answer)2.svg"
        scheduler.schedule(delay: 4, execute: {
            [weak self] in
            guard let self = self else { return }
            showMenu()
        })
        scheduler.schedule(delay: 1.3, execute: {
            [weak self] in
            guard let self = self else { return }
            self.playSound(name: self.game.sound!)
            self.textName.alpha = 0
            self.textName.transform = CGAffineTransformMakeScale(0, 0)
            let easy = BackEaseInterpolater()
            easy.mode = .easeOut
            let animValues: [Double] = [0, 1]
            let timeChange = Interpolate(values: [0,1],
                                         apply: { [weak self] (value) in
                guard let self = self else { return }
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                self.textName.transform = CGAffineTransformMakeScale(finalValue, finalValue)
                self.textName.alpha = finalValue
            })
            timeChange.animate(1, duration: 0.5){
                
            }
            scheduler.schedule(delay: 1.3, execute: {
                [weak self] in
                guard let self = self else { return }
                self.playSound(name: answer)
                self.svgView2.alpha = 0
                self.svgView2.transform = CGAffineTransformMakeScale(0, 0)
                let easy = BackEaseInterpolater()
                easy.mode = .easeOut
                let animValues: [Double] = [0, 1]
                let timeChange = Interpolate(values: [0,1],
                                             apply: { [weak self] (value) in
                    guard let self = self else { return }
                    let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                    self.svgView2.transform = CGAffineTransformMakeScale(finalValue, finalValue)
                    self.svgView2.alpha = finalValue
                })
                timeChange.animate(1, duration: 0.5){
                    
                }
            })
        })
        textName.alpha = 0
        svgView2.alpha = 0
        if step == 1 {
            svgView.alpha = 0
            svgView.transform = CGAffineTransformMakeTranslation(svgView.bounds.width, 0)
            let easy = BackEaseInterpolater()
            easy.mode = .easeOut
            let animValues: [Double] = [svgView.bounds.width, 0]
            let timeChange = Interpolate(values: [0,1],
                                         apply: { [weak self] (value) in
                guard let self = self else { return }
                let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                self.svgView.transform.tx = finalValue
            })
            timeChange.animate(1, duration: 0.5){
                
            }
            UIView.animate(withDuration: 0.5, animations: {
                [weak self] in
                guard let self = self else { return }
                self.svgView.alpha = 1
            })
        }
    }
    func slideNext(){
        hideMenu()
        self.playSound(name: "effects/slide")
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0,-svgView.bounds.width]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            self.svgView.transform.tx = finalValue
        })
        timeChange.animate(1, duration: 0.5){
            
        }
        UIView.animate(withDuration: 0.5, animations: {
            [weak self] in
            guard let self = self else { return }
            self.svgView.alpha = 0.5
        })
        scheduler.schedule(delay: 0.51, execute: {
            [weak self] in
            guard let self = self else { return }
            if step < values.count {
                svgView.transform = CGAffineTransformMakeTranslation(svgView.bounds.width, 0)
                let easy = BackEaseInterpolater()
                easy.mode = .easeOut
                let animValues: [Double] = [svgView.bounds.width, 0]
                let timeChange = Interpolate(values: [0,1],
                                             apply: { [weak self] (value) in
                    guard let self = self else { return }
                    let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
                    self.svgView.transform.tx = finalValue
                })
                timeChange.animate(1, duration: 0.5){
                    
                }
                UIView.animate(withDuration: 0.5, animations: {
                    [weak self] in
                    guard let self = self else { return }
                    self.svgView.alpha = 1
                })
            }
            self.loadNextStep()
        })
    }
    @objc func loadNext(){
        if step < values.count {
            slideNext()
        } else {
            finishGame()
        }
    }
    @objc func replay(){
        step -= 1
        hideMenu()
        loadNextStep()
    }
    func hideMenu(){
        leftContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(0, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -leftContainer.bounds.width * 1.2 - 20.0]
        let animValues2: [Double] = [0, rightContainer.bounds.width * 1.2 + 20.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showMenu(){
        startGame()
        leftContainer.transform = CGAffineTransformMakeTranslation(-leftContainer.bounds.width, 0)
        rightContainer.transform = CGAffineTransformMakeTranslation(rightContainer.bounds.width, 0)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [-leftContainer.bounds.width, 0]
        let animValues2: [Double] = [rightContainer.bounds.width, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            self.leftContainer.transform.tx = finalValue
            self.rightContainer.transform.tx = finalValue2
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameReading, .GameListening, .GameSpeaking]
    }
}
