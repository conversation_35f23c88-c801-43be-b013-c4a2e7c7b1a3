//
//  toancoban_list_doidua.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/3/25.
//


import UIKit
import SnapKit

class toancoban_list_doidua: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView?
    private var imageBats: [UIImageView] = Array(repeating: UIImageView(), count: 4)
    private var imagePlaceHolders: [UIView] = Array(repeating: UIView(), count: 8)
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
        
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), getLanguage() + "/toan/toan_2 dua")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound( getLanguage() + "/toan/toan_2 dua")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
        
    // MARK: - Configure Layout
    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        setupLayout()
    }
        
    private func setupLayout() {
        backgroundColor = UIColor(red: 1, green: 0.96, blue: 0.96, alpha: 1) // #FFF5F5
        //backgroundColor = .green
        // Background layer
        let backgroundLayer = UIView()
        backgroundLayer.backgroundColor = UIColor(red: 1, green: 0.92, blue: 0.89, alpha: 1) // #FFEAE2
        //backgroundLayer.backgroundColor = .blue
        addSubview(backgroundLayer)
        backgroundLayer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.25)
        }
        
        // Main container
        let mainContainer = UIView()
        //mainContainer.backgroundColor = .red.withAlphaComponent(0.3)
        mainContainer.clipsToBounds = false
        addSubview(mainContainer)
        mainContainer.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().priority(.low)
            make.width.height.lessThanOrEqualToSuperview()
            make.width.height.equalToSuperview().priority(.low)
            make.width.equalTo(mainContainer.snp.height).multipliedBy(2.2) // Ratio 2.2
            //make.bottom.equalToSuperview().multipliedBy(0.9) // bias 0.8
        }
        addActionOnLayoutSubviews {
            mainContainer.snapToVerticalBias(verticalBias: 0.8)
        }
        
        // Image bats
        for i in 0..<4 {
            imageBats[i] = UIImageView(image: Utilities.SVGImage(named: "math_2dua_2"))
            mainContainer.addSubview(imageBats[i])
            imageBats[i].snp.makeConstraints { make in
                make.width.equalTo(imageBats[i].snp.height).multipliedBy(353.0 / 258.0) // Ratio 353:258
                make.height.equalTo(mainContainer).multipliedBy(0.25)
                //make.centerX.equalToSuperview()//.multipliedBy(0.2 + 0.266 * CGFloat(i)) // bias 0.1, 0.366, 0.633, 0.9
                //make.centerY.equalToSuperview()//.offset(mainContainer.bounds.height * 0.35) // bias 0.85
            }
            addActionOnLayoutSubviews {
                [weak self] in
                guard let self = self else { return }
                self.imageBats[i].snapToHorizontalBias(horizontalBias: 0.1 + 0.266 * CGFloat(i))
                self.imageBats[i].snapToVerticalBias(verticalBias: 0.85)
            }
        }
        
        // Image place holders
        let biases: [(CGFloat, CGFloat)] = [
            (0.08, 0.84), (0.08, 0.94), (0.36, 0.84), (0.36, 0.94),
            (0.64, 0.84), (0.64, 0.94), (0.92, 0.84), (0.92, 0.94)
        ]
        for i in 0..<8 {
            imagePlaceHolders[i] = UIImageView(image: Utilities.SVGImage(named: "math_2dua_1"))
            imagePlaceHolders[i].contentMode = .scaleAspectFit
            imagePlaceHolders[i].isHidden = true // Tương đương View.INVISIBLE
            mainContainer.addSubview(imagePlaceHolders[i])
            imagePlaceHolders[i].snp.makeConstraints { make in
                make.width.equalTo(imagePlaceHolders[i].snp.height) // Ratio 1:1
                make.height.equalTo(mainContainer).multipliedBy(0.45)
                //make.centerX.equalToSuperview().multipliedBy(2 * biases[i].0) // Horizontal bias
                //make.centerY.equalToSuperview().offset(mainContainer.bounds.height * (biases[i].1 - 0.5)) // Vertical bias
            }
            addActionOnLayoutSubviews {
                [weak self] in
                guard let self = self else { return }
                self.imagePlaceHolders[i].snapToHorizontalBias(horizontalBias: biases[i].0)
                self.imagePlaceHolders[i].snapToVerticalBias(verticalBias: biases[i].1)
            }
        }
        
        // Item container
        itemContainer = UIView()
        //itemContainer?.backgroundColor = .blue
        itemContainer?.clipsToBounds = false
        mainContainer.addSubview(itemContainer!)
        itemContainer?.snp.makeConstraints { make in
            make.width.equalTo(mainContainer).multipliedBy(0.8)
            make.height.equalTo(mainContainer).multipliedBy(0.45)
            make.centerX.equalToSuperview()
            //make.centerY.equalToSuperview().offset(-mainContainer.bounds.height * 0.35) // bias 0.15
        }
        addActionOnLayoutSubviews {
            [weak self] in
            guard let self = self else { return }
            self.itemContainer?.snapToVerticalBias(verticalBias: 0.15)
        }
        
        // Item container children
        let itemViews = (0..<8).map { _ in
            let container = UIView()
            let imageView = UIImageView(image: Utilities.SVGImage(named: "math_2dua_1"))
            imageView.contentMode = .scaleAspectFit
            container.addSubview(imageView)
            imageView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            return container
        }
        
        itemViews.forEach { itemContainer?.addSubview($0) }
        itemViews.enumerated().forEach { (index, item) in
            item.snp.makeConstraints { make in
                make.width.equalTo(item.snp.height).multipliedBy(0.3) // Ratio 0.3
                make.top.bottom.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                item.snapToHorizontalBias(horizontalBias: 0.05+0.9/7.0*Double(index))
            }
        }        
        // Gesture handling
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer?.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Gesture Handling
    var originX = 0.0
    var originY = 0.0
    var zindex = 1.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let itemContainer = itemContainer else { return }
        let location = gesture.location(in: itemContainer)
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                originX = currentView.frame.origin.x
                originY = currentView.frame.origin.y
                let lx = gesture.location(in: self).x
                let ly = gesture.location(in: self).y
                dX = currentView.frame.origin.x - gesture.location(in: self).x
                dY = currentView.frame.origin.y - gesture.location(in: self).y
                currentView.layer.zPosition = zindex
                zindex += 1
                currentView.alpha = 1
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
                Utils.vibrate()
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = gesture.location(in: self).x + dX
                let newY = gesture.location(in: self).y + dY
                currentView.transform = CGAffineTransformMakeTranslation(newX - originX, newY - originY)
                
                if let child = currentView.subviews.first {
                    let minView = findClosestView(currentView: currentView)
                    if minView != nil && child.transform == .identity {
                        UIView.animate(withDuration: 0.2) {
                            child.transform = CGAffineTransform(rotationAngle: -.pi / 2)
                        }
                    } else if minView == nil && child.transform != .identity {
                        UIView.animate(withDuration: 0.2) {
                            child.transform = .identity
                        }
                    }
                }
            }
            
        case .ended:
            if let currentView = currentView {
                Utils.vibrate()
                let minView = findClosestView(currentView: currentView)
                pauseGame(stopMusic: false)
                scheduler.schedule(delay: 0.5) { [weak self] in
                    self?.resumeGame(startMusic: false)
                }
                
                if minView == nil {
                    UIView.animate(withDuration: 0.5) {
                        currentView.transform = .identity
                    }
                    playSound("effect/slide2")
                } else {
                    playSound("effect/word puzzle drop")
                    if let index = imagePlaceHolders.firstIndex(of: minView!) {
                        let point = currentView.distanceFromCenterToCenter(to: minView!)
                        UIView.animate(withDuration: 0.5, animations: {
                            currentView.frame.origin.x -= point.x
                            currentView.frame.origin.y -= point.y
                        }) { [weak self] _ in
                            currentView.isHidden = true
                            minView?.transform = CGAffineTransform(rotationAngle: -.pi / 2)
                            minView?.isHidden = false
                            self?.checkFinish()
                        }
                    }
                }
            }
            currentView = nil
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func checkFinish() {
        let finish = imagePlaceHolders.allSatisfy { !$0.isHidden }
        if finish {
            pauseGame()
            animateCoinIfCorrect(view: itemContainer)
            let delay = playSound(finishEndSounds())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func findClosestView(currentView: UIView) -> UIView? {
        var minDistance = CGFloat.greatestFiniteMagnitude
        var minView: UIView?
        
        for imageBat in imageBats {
            let point = currentView.distanceFromCenterToCenter(to: imageBat)
            let distance = hypot(point.x, point.y)
            if distance < minDistance {
                minDistance = distance
                minView = imageBat
            }
        }
        
        if let minView = minView, minDistance < currentView.frame.height / 3 {
            if let index = imageBats.firstIndex(of: minView as! UIImageView) {
                if imagePlaceHolders[2 * index + 1].isHidden {
                    return imagePlaceHolders[2 * index + 1]
                }
                if imagePlaceHolders[2 * index].isHidden {
                    return imagePlaceHolders[2 * index]
                }
            }
        }
        return nil
    }
    
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        guard let itemContainer = itemContainer else { return nil }
        for child in itemContainer.subviews.reversed() {
            if child.isHidden { continue }
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
}


extension Utils {
    static func vibrate() {
        UIImpactFeedbackGenerator(style: .light).impactOccurred()
    }
}

import SnapKit

import UIKit

import UIKit
import ObjectiveC // Để dùng associated object

// Định nghĩa key cho associated objects
private var horizontalConstraintKey: UInt8 = 0
private var verticalConstraintKey: UInt8 = 1
typealias Action = () -> Void

extension UIView {
    /// Lưu trữ constraint ngang
    private var horizontalConstraint: NSLayoutConstraint? {
        get {
            return objc_getAssociatedObject(self, &horizontalConstraintKey) as? NSLayoutConstraint
        }
        set {
            objc_setAssociatedObject(self, &horizontalConstraintKey, newValue, .OBJC_ASSOCIATION_RETAIN)
        }
    }
    
    /// Lưu trữ constraint dọc
    private var verticalConstraint: NSLayoutConstraint? {
        get {
            return objc_getAssociatedObject(self, &verticalConstraintKey) as? NSLayoutConstraint
        }
        set {
            objc_setAssociatedObject(self, &verticalConstraintKey, newValue, .OBJC_ASSOCIATION_RETAIN)
        }
    }
    
    /// Áp dụng bias ngang và xóa constraint cũ nếu có
    func snapToHorizontalBias(horizontalBias: Double) {
        guard let superview = self.superview else { return }
        
        self.translatesAutoresizingMaskIntoConstraints = false
        
        // Xóa constraint ngang cũ nếu có
        if let oldConstraint = horizontalConstraint {
            NSLayoutConstraint.deactivate([oldConstraint])
        }
        
        let availableWidth = superview.bounds.width - self.frame.width
        let clampedBias = max(0.0, min(1.0, horizontalBias))
        let leftOffset = availableWidth * clampedBias
        
        // Tạo constraint mới
        let newConstraint = self.leftAnchor.constraint(equalTo: superview.leftAnchor, constant: leftOffset)
        NSLayoutConstraint.activate([newConstraint])
        
        // Lưu constraint mới
        horizontalConstraint = newConstraint
    }
    
    /// Áp dụng bias dọc và xóa constraint cũ nếu có
    func snapToVerticalBias(verticalBias: Double) {
        guard let superview = self.superview else { return }
        
        self.translatesAutoresizingMaskIntoConstraints = false
        
        // Xóa constraint dọc cũ nếu có
        if let oldConstraint = verticalConstraint {
            NSLayoutConstraint.deactivate([oldConstraint])
        }
        
        let availableHeight = superview.bounds.height - self.frame.height
        let clampedBias = max(0.0, min(1.0, verticalBias))
        let topOffset = availableHeight * clampedBias
        
        // Tạo constraint mới
        let newConstraint = self.topAnchor.constraint(equalTo: superview.topAnchor, constant: topOffset)
        NSLayoutConstraint.activate([newConstraint])
        
        // Lưu constraint mới
        verticalConstraint = newConstraint
    }
}
