//
//  LanguageGameActivity.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 27/3/25.
//

import UIKit

class LanguageGameActivity: TwoClickExitActivity, OnGameFragmentListener {
    func onGameFinish(_ gameFragment: BaseFragmentView) {
        self.navigationController?.popViewController(animated: true)
    }

    func onUpdateGameProgress(_ progress: Float) {

    }

    var category: String?
    var data: LanguageItem?
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        title = category
        checkTimeoutAndRun(
            {
                [weak self] in
                guard let self = self else { return }
                self.initGame()
            },
            cancelAction: { [weak self] in
                guard let self = self else { return }
                self.dismiss(animated: true, completion: nil)
            }
        )
    }

    // MARK: - Initialization
    private func initGame() {
        do {
            let game = ngonngu_list_fill()
            game.data = data
            game.category = category == "Thơ" ? "tho" : category == "Truyện" ? "truyen" : category == "Đồng dao" ? "dong dao" : "cau do"
            gameFragment = game
            gameFragment?.isUserInteractionEnabled = true

            // Gán listener cho game fragment
            gameFragment?.onGameFragmentListener = self
            
            // Thêm gameFragment vào gameView
            if let gameView = gameView {
                //gameView.transform = CGAffineTransformMakeScale(0.5, 0.5)
                gameView.removeAllSubviews()
                gameView.addSubviewWithPercentInset(subview: gameFragment!, percentInset: 0.0)
            }
        } catch {
            print("Error initializing game: \(error.localizedDescription)")
        }
    }
    deinit {
        scheduler.clearAll()
    }    
}
