//
//  tuduy_list_thaphanoi.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit

class tuduy_list_thaphanoi: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var deltaX: CGFloat = 0
    private var deltaY: CGFloat = 0
    private var discContainer: UIView!
    private var bg1: UIImageView!
    private var bg2: UIImageView!
    private var bg3: UIImageView!
    private var topView1: UIView!
    private var topView2: UIView!
    private var topView3: UIView!
    private var coinView: UIView!
    private var list1: [UIView] = []
    private var list2: [UIView] = []
    private var list3: [UIView] = []
    private var selectView: UIView?
    private var oldColumn: Int = -1
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var currentView: UIView?
    private var discs: [UIImageView] = []
    private var discs2: [UIImageView] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFFFFF
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        bg1 = UIImageView(image: Utilities.SVGImage(named: "tuduy_thaphanoi_bg"))
        //bg1.backgroundColor = UIColor.black.withAlphaComponent(0.01) // #01000000
        itemContainer.addSubview(bg1)
        bg1.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.25)
            make.height.equalTo(bg1.snp.width) // Ratio 1:1
            make.bottom.equalToSuperview().multipliedBy(0.95)
        }
        addActionOnLayoutSubviews {
            self.bg1.snapToHorizontalBias(horizontalBias: 0.05)
        }
        let bg1Tap = UITapGestureRecognizer(target: self, action: #selector(handleBgTap(_:)))
        bg1.addGestureRecognizer(bg1Tap)
        bg1.isUserInteractionEnabled = true
        
        bg2 = UIImageView(image: Utilities.SVGImage(named: "tuduy_thaphanoi_bg"))
        //bg2.backgroundColor = UIColor.black.withAlphaComponent(0.01) // #01000000
        itemContainer.addSubview(bg2)
        bg2.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.25)
            make.height.equalTo(bg2.snp.width) // Ratio 1:1
            make.bottom.equalTo(bg1)
            make.centerX.equalToSuperview()
        }
        let bg2Tap = UITapGestureRecognizer(target: self, action: #selector(handleBgTap(_:)))
        bg2.addGestureRecognizer(bg2Tap)
        bg2.isUserInteractionEnabled = true
        
        bg3 = UIImageView(image: Utilities.SVGImage(named: "tuduy_thaphanoi_bg"))
        //bg3.backgroundColor = UIColor.black.withAlphaComponent(0.01) // #01000000
        itemContainer.addSubview(bg3)
        bg3.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.25)
            make.height.equalTo(bg3.snp.width) // Ratio 1:1
            make.bottom.equalTo(bg2)
        }
        addActionOnLayoutSubviews {
            self.bg3.snapToHorizontalBias(horizontalBias: 0.95)
        }
        let bg3Tap = UITapGestureRecognizer(target: self, action: #selector(handleBgTap(_:)))
        bg3.addGestureRecognizer(bg3Tap)
        bg3.isUserInteractionEnabled = true
        
        discContainer = UIView()
        discContainer.isUserInteractionEnabled = false
        itemContainer.addSubview(discContainer)
        discContainer.snp.makeConstraints { make in
            make.edges.equalTo(bg1)
        }
        
        let discConfigs = [
            (id: "disc_1", image: "tuduy_thaphanoi_green", tag: 3, bias: 0.77),
            (id: "disc_2", image: "tuduy_thaphanoi_blue", tag: 2, bias: 0.77),
            (id: "disc_3", image: "tuduy_thaphanoi_red", tag: 1, bias: 0.77)
        ]
        
        discs = []
        for config in discConfigs {
            let disc = UIImageView(image: Utilities.SVGImage(named: config.image))
            disc.alpha = 0.01
            disc.stringTag = config.id
            disc.tag = config.tag
            discContainer.addSubview(disc)
            disc.snp.makeConstraints { make in
                make.width.equalTo(discContainer).multipliedBy(0.9)
                make.height.equalTo(disc.snp.width).multipliedBy(140.0 / 520.0) // Ratio 520:140
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(config.bias * 2)
            }
            discs.append(disc)
        }

        let disc3Container = UIView()
        disc3Container.isUserInteractionEnabled = false
        itemContainer.addSubview(disc3Container)
        disc3Container.snp.makeConstraints { make in
            make.edges.equalTo(bg3)
        }
        
        let disc3Configs = [
            (id: "disc_4", image: "tuduy_thaphanoi_green2", tag: 3, bias: 0.77),
            (id: "disc_5", image: "tuduy_thaphanoi_blue2", tag: 2, bias: 0.77),
            (id: "disc_6", image: "tuduy_thaphanoi_red2", tag: 1, bias: 0.77)
        ]
        
        discs2 = []
        for config in disc3Configs {
            let disc = UIImageView(image: Utilities.SVGImage(named: config.image))
            disc.alpha = 0.01
            disc.stringTag = config.id
            disc.tag = config.tag
            disc3Container.addSubview(disc)
            disc.snp.makeConstraints { make in
                make.width.equalTo(disc3Container).multipliedBy(0.9)
                make.height.equalTo(disc.snp.width).multipliedBy(140.0 / 520.0) // Ratio 520:140
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().multipliedBy(config.bias * 2)
            }
            discs2.append(disc)
        }
        
        topView1 = UIView()
        //topView1.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        itemContainer.addSubview(topView1)
        topView1.snp.makeConstraints { make in
            make.left.right.equalTo(bg1)
            make.width.equalTo(topView1.snp.height).multipliedBy(4.0) // Ratio 4:1
            make.centerY.equalToSuperview().multipliedBy(0.4) // Bias 0.2
        }
        
        topView2 = UIView()
        //topView2.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        itemContainer.addSubview(topView2)
        topView2.snp.makeConstraints { make in
            make.left.right.equalTo(bg2)
            make.width.equalTo(topView2.snp.height).multipliedBy(4.0) // Ratio 4:1
            make.top.equalTo(topView1)
        }
        
        topView3 = UIView()
        //topView3.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        itemContainer.addSubview(topView3)
        topView3.snp.makeConstraints { make in
            make.left.right.equalTo(bg3)
            make.width.equalTo(topView3.snp.height).multipliedBy(4.0) // Ratio 4:1
            make.top.equalTo(topView1)
        }
        
        coinView = UIView() // Giả lập từ item_coin_view.xml
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.height.equalTo(view).multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        self.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        list1 = discs
        let delay = playSound(openGameSound(), "tuduy/thap hanoi1", "tuduy/thap hanoi2")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/thap hanoi1", "tuduy/thap hanoi2")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        for i in 0..<discs.count {
            UIView.animate(withDuration: 0.001) {
                self.discs[i].transform = CGAffineTransform(translationX: 0, y: -self.discs[i].frame.height * CGFloat(i))
                self.discs2[i].transform = CGAffineTransform(translationX: 0, y: -self.discs2[i].frame.height * CGFloat(i))
                self.discs[i].alpha = 1
                self.discs2[i].alpha = 1
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let location = gesture.location(in: self)
        
        switch gesture.state {
        case .began:
            let containerLocation = self.convert(CGPoint.zero, to: nil)
            let discLocation = discContainer.convert(CGPoint.zero, to: nil)
            deltaX = discLocation.x - containerLocation.x
            deltaY = discLocation.y - containerLocation.y
            
            currentView = findViewUnder(x: Float(location.x), y: Float(location.y))
            if let currentView = currentView {
                dX = currentView.frame.minX - location.x
                dY = currentView.frame.minY - location.y
                currentView.bringSubviewToFront(self)
            }
            
        case .changed:
            if let currentView = currentView {
                var newX = location.x + dX
                var newY = location.y + dY
                
                newX = max(-currentView.frame.width / 2 - deltaX, min(newX, self.frame.width - currentView.frame.width / 2 - deltaX))
                newY = max(-currentView.frame.height / 2 - deltaY, min(newY, self.frame.height - currentView.frame.height / 2 - deltaY))
                
                currentView.frame.origin = CGPoint(x: newX, y: newY)
            }
            
        case .ended:
            currentView = nil
            
        default:
            break
        }
    }
    
    @objc private func handleBgTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view else { return }
        let column: Int
        if view == bg1 {
            column = 0
        } else if view == bg2 {
            column = 1
        } else {
            column = 2
        }
        select(column: column)
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: Float, y: Float) -> UIView? {
        let adjustedX = CGFloat(x) - deltaX
        let adjustedY = CGFloat(y) - deltaY
        for i in (0..<discs.count).reversed() {
            let disc = discs[i]
            if adjustedX >= CGFloat(disc.frame.minX) && adjustedX <= CGFloat(disc.frame.maxX) &&
               adjustedY >= CGFloat(disc.frame.minY) && adjustedY <= CGFloat(disc.frame.maxY) {
                return disc
            }
        }
        return nil
    }
    
    private func select(column: Int) {
        var list = column == 0 ? list1 : column == 1 ? list2 : list3
        let topView = column == 0 ? topView1 : column == 1 ? topView2 : topView3
        
        if selectView == nil {
            playSound("effect/slide1")
            guard !list.isEmpty else { return }
            selectView = list.last!
            selectView?.moveToCenter(of: topView!, duration: 0.2)
            //selectView?.frame = topView!.frame
            oldColumn = column
        } else {
            if column == oldColumn {
                playSound(name: "effect/block drop", delay: 0.1)
                UIView.animate(withDuration: 0.2, delay: 0, options: .curveLinear) {
                    self.selectView?.transform = CGAffineTransform(translationX: self.selectView!.transform.tx, y: -self.selectView!.frame.height * CGFloat(list.count - 1))
                }
                selectView = nil
                return
            }
            
            let bottomIndex = list.isEmpty ? -1 : list.count - 1
            let tag = bottomIndex == -1 ? 100 : list[bottomIndex].tag
            let tagSelect = selectView!.tag
            
            if tagSelect >= tag {
                return
            }
            
            let finalBottomIndex = bottomIndex
            self.selectView?.moveToCenter(of: topView!, duration: 0.2) {
                [weak self] _ in
                guard let self = self else { return }
                self.playSound(name: "effect/block drop", delay: 0.1)
                UIView.animate(withDuration: 0.2, delay: 0, options: .curveLinear) {
                    self.selectView?.transform = CGAffineTransform(translationX: self.selectView!.transform.tx, y: -self.selectView!.frame.height * CGFloat(finalBottomIndex + 1))
                }
                
                if let selectView = self.selectView {
                    if column == 0 {
                        self.list1.append(selectView)
                    }
                    if column == 1 {
                        self.list2.append(selectView)
                    }
                    if column == 2 {
                        self.list3.append(selectView)
                    }
                    if self.oldColumn == 0 {
                        self.list1.removeAll{$0 == selectView}
                    }
                    if self.oldColumn == 1 {
                        self.list2.removeAll{$0 == selectView}
                    }
                    if self.oldColumn == 2 {
                        self.list3.removeAll{$0 == selectView}
                    }
                }
                self.selectView = nil
                
                if self.list3.count == 3 {
                    self.animateCoinIfCorrect(view: self.coinView)
                    self.pauseGame()
                    var delay: TimeInterval = 0.5
                    delay += self.playSound(delay: delay, names: ["effect/answer_end", self.getCorrectHumanSound(), self.endGameSound()])
                    self.scheduler.schedule(delay: delay) { [weak self] in
                        self?.finishGame()
                    }
                }
            }
        }
    }
}

// MARK: - Supporting Structures

/*
struct R {
    struct drawable {
        static let tuduy_thaphanoi_bg = 1
        static let tuduy_thaphanoi_green = 2
        static let tuduy_thaphanoi_blue = 3
        static let tuduy_thaphanoi_red = 4
        static let tuduy_thaphanoi_green2 = 5
        static let tuduy_thaphanoi_blue2 = 6
        static let tuduy_thaphanoi_red2 = 7
    }
}

extension Int {
    func toDrawableName() -> String? {
        switch self {
        case R.drawable.tuduy_thaphanoi_bg:
            return "tuduy_thaphanoi_bg"
        case R.drawable.tuduy_thaphanoi_green:
            return "tuduy_thaphanoi_green"
        case R.drawable.tuduy_thaphanoi_blue:
            return "tuduy_thaphanoi_blue"
        case R.drawable.tuduy_thaphanoi_red:
            return "tuduy_thaphanoi_red"
        case R.drawable.tuduy_thaphanoi_green2:
            return "tuduy_thaphanoi_green2"
        case R.drawable.tuduy_thaphanoi_blue2:
            return "tuduy_thaphanoi_blue2"
        case R.drawable.tuduy_thaphanoi_red2:
            return "tuduy_thaphanoi_red2"
        default:
            return nil
        }
    }
}
*/
