//
//  ngonngu_list_tapvietoly.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AVFAudio

final class ngonngu_list_tapvietoly : NhanBietGameFragment, TapVietView2Delegate {
    var data : String?
    var step = 0
    
    func tapvietDragBegin(_ tv: TapVietView2) {
        
    }
    
    func tapvietDragEnd(_ tv: TapVietView2) {
        move += 1
    }
    
    func tapvietOnCompleted(_ tv: TapVietView2) {
        playSound("true")
        drawCompleted = true
        pauseGame()
        var delay = 0.5
        delay += self.playSound(delay: delay, names: [answer.hasSuffix("2") ? "this is capital" : "this is lowercase", answer.replacingOccurrences(of: "2", with: "1")])
        scheduler.schedule(delay: delay) {
            [weak self] in
            guard let self = self else { return }
            self.loadNextStep()
        }
    }
    func tapvietOnStrokeCompleted(_ tv: TapVietView2, _ strokeIndex: Int) {
        correct += 1
        self.strokeIndex = strokeIndex
        playSound("effects/true2")
    }
    var strokeIndex = 0
    var answer : String = ""
    var count = 0
    var drawCompleted = false
    private var backImage : SVGImageView = SVGImageView(frame: CGRect.zero)
    private var svgBackground : SVG?
    private var topView : UIView!
    private var bottomContainer : UIView!
    private var writeContainer : UIView!
   
    
    func loadViews(){
        self.removeAllSubviews()
        backImage = SVGImageView(frame: CGRect.zero)
        topView = UIView()
        bottomContainer = UIView()
        writeContainer = UIView()
        addSubview(topView)
        var img = Utilities.GetSVGKImage(named: "tap viet/\(answer).svg")
        backgroundColor = .white
        addSubview(bottomContainer)
        bottomContainer.addSubview(backImage)
        let ratio = Float(img.size.width/img.size.height)
        backImage.makeViewCenterAndKeep(ratio: ratio)
       
        bottomContainer.addSubview(writeContainer)
        writeContainer.makeViewCenterAndKeep(ratio: ratio)
        
        topView.snp.makeConstraints { make in
            make.left.top.right.equalToSuperview()
            make.height.equalTo(100)
        }
        bottomContainer.snp.makeConstraints { make in
            make.left.bottom.right.top.equalToSuperview()
        }
        self.svg = SVG(image: img)
    }
    
    override func createGame() {
        super.createGame()
        loadNextStep()
        startGame()        
    }
    var svg : SVG?
    func loadNextStep(){
        resumeGame()
        if step >= 1 {
            var delay = 0.5
            delay += playSound(name: "effects/end game", delay: delay)
            delay += 0.5
            scheduler.schedule(delay: delay) {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            }
            return
        }
        answer = data!
        step += 1
        var delay = 0.5
        delay += self.playSound(delay: delay, names: [answer.hasSuffix("2") ? "write a capital" : "write a lowercase"])
        delay += 0.3
        delay += self.playSound(delay: delay, names: [answer.replacingOccurrences(of: "2", with: "1")])
        loadViews()
        writeContainer.removeAllSubviews()
        let tapviet = TapVietView2()
        writeContainer.addSubviewWithInset(subview: tapviet, inset: 0)
        tapviet.delegate = self
        
        svgBackground = svg!.deepClone()
        tapviet.svg = svg!.clone(pathIndexs: [Int](0..<svg!.layers.count))
        tapviet.hideDotHint = true
        tapviet.hideLineHint = true
        tapviet.snap = true
        tapviet.setup()
        strokeIndex = 0
        scheduler.schedule(delay: 0.1) {
            [weak self] in
            guard let self = self else { return }
            self.drawCompleted = false
        }
    }
    deinit {
        print(#function, self)
        NotificationCenter.default.removeObserver(self)
    }
    var correct = 0
    var move = 0
}


class TapVietView2 : UIControl{
    private var viewBackground = UIImageView()
    private let viewBackgroundColor = UIColor.color(hex: "#5EC2FE").withAlphaComponent(0.7)
    private var viewDone = UIView()
    private var viewHint = UIView()
    private var viewDoing = UIView()
    private var autoDrawingLayer = CAShapeLayer()
    private var autoDrawingIncorrectLayer = CAShapeLayer()
    private var autoPath : CGMutablePath?
    private var autoPoints : [CGPoint] = []
    private var autoPointIndex = 0
    private var drawingPath = UIBezierPath()
    private var shapeLayer = CAShapeLayer()
    private final let strokeWidthRatio = 13.0 / 300.0
    var audioPlayer: AVAudioPlayer?
    var svg: SVG?
    var strokeIndex = 1
    weak var delegate: TapVietView2Delegate?
    var validDrawing = true
    
    var checkLineImage = SVGImageView(frame: CGRect.zero)
    var strokeLength = 0.0
    func setup(){
        self.addSubviewWithInset(subview: viewBackground, inset: 0)
        self.addSubviewWithInset(subview: viewDone, inset: 0)
        self.addSubviewWithInset(subview: viewHint, inset: 0)
        viewHint.addSubviewWithInset(subview: imageHint, inset: 0)
        self.addSubviewWithInset(subview: viewDoing, inset: 0)
        viewBackground.image = svg?.image.uiImage.tinted(withColor: viewBackgroundColor)
        self.addSubviewWithInset(subview: checkLineImage, inset: 0)
        isUserInteractionEnabled = true
        let panGesture = UIPanGestureRecognizer()
        panGesture.maximumNumberOfTouches = 1
        addGestureRecognizer(panGesture)
        panGesture.addTarget(self, action: #selector(handlePan(_:)))
        setupDraw()
        updateHint()
        setupSound()
    }
    func setupSound(){
        // Initialize the AVAudioPlayer with the sound file
        if let soundURL = Utilities.url(soundPath: "effect/writing") {
            do {
                audioPlayer = try AVAudioPlayer(contentsOf: soundURL)
                audioPlayer?.prepareToPlay()
            } catch {
                print("Error loading sound:", error.localizedDescription)
            }
        } else {
            print("Sound file not found.")
        }
    }
    var schedulerHint = Scheduler()
    var hintIndex = 0
    var svgHint1 : UIImage!
    var svgHint2 : UIImage!
    var svgHint3 : UIImage!
    let imageHint = UIImageView()
    func updateHint(){
        schedulerHint.schedule(delay: 0.4, execute: {
            [weak self] in
            guard let self = self else { return }
            self.updateHint()
        })
        if hintDone {
            imageHint.image = hintIndex == 0 ? svgHint1 : hintIndex == 1 ? svgHint2 : svgHint3
            hintIndex = (hintIndex + 1) % 3
            imageHint.isHidden = false
        }
    }
    private var viewLoaded = false
    override func layoutSubviews() {
        if !viewLoaded {
            viewLoaded = true
            nextStroke()
        }
    }
    
    private var touch = false
    override func touchesBegan(_ touches: Set<UITouch>, with event: UIEvent?) {
        processBegin(point: touches.first!.location(in: self))
    }
    override func touchesEnded(_ touches: Set<UITouch>, with event: UIEvent?) {
        processEnd(point: touches.first!.location(in: self))
    }
    func processBegin(point: CGPoint){
        if touch {
            return
        }
        delegate?.tapvietDragBegin(self)
        touch = true
        shapeLayer.strokeColor = UIColor.color(hex: "#A060FC").cgColor
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.lineWidth = lineWidth * self.bounds.height / svg!.size.height * 2
        drawingPath = UIBezierPath()
        drawingPath.move(to: point)
        shapeLayer.path = drawingPath.cgPath
        validDrawing = true
        
        if snap {
            autoPointIndex = 0
            autoDrawingLayer.fillColor = UIColor.clear.cgColor
            autoDrawingLayer.strokeColor = UIColor.color(hex: "#A060FC").cgColor
            autoDrawingLayer.lineWidth = lineWidth * self.bounds.height / svg!.size.height
            autoDrawingIncorrectLayer.strokeColor = UIColor.red.cgColor
            autoDrawingIncorrectLayer.lineWidth = lineWidth * self.bounds.height / svg!.size.height
            autoDrawingIncorrectLayer.fillColor = UIColor.clear.cgColor
            let startPoint = autoPath!.startPoint
            autoPath = CGMutablePath()
            autoPath?.move(to: startPoint!)
            autoDrawingLayer.path = CGMutablePath()
            autoDrawingIncorrectLayer.path = CGMutablePath()
        }
        
        if point.distance(to: startPoint!) < self.bounds.height / 5 {
            
        } else {
            validDrawing = false
            shapeLayer.strokeColor = UIColor.red.cgColor
            startWrongPath = CGMutablePath()
            startWrongPath.move(to: point)
            autoDrawingIncorrectLayer.path = startWrongPath
            QueueSoundPlayer.shared.play(sound: "effect/fail", delay: 0)
        }
    }
    var startWrongPath = CGMutablePath()
    func processMove(point: CGPoint){
        let oldValidDrawing = validDrawing
        if checkValidPoint(point: point) {
            
        } else {
            if validDrawing {
                QueueSoundPlayer.shared.play(sound: "effect/fail", delay: 0)
            }
            validDrawing = false
            shapeLayer.strokeColor = UIColor.red.cgColor
        }
        if snap {
            if validDrawing {
                while autoPointIndex < autoPoints.count - 1
                        && point.distance(to: autoPoints[autoPointIndex+1]) <= point.distance(to: autoPoints[autoPointIndex]) {
                    autoPointIndex += 1
                    autoPath?.addLine(to: autoPoints[autoPointIndex])
                    autoDrawingLayer.path = autoPath
                }
            } else {
                if oldValidDrawing {
                    QueueSoundPlayer.shared.play(sound: "fail", delay: 0)
                    let path = CGMutablePath()
                    if let autoPath = autoPath {
                        let count = autoPath.points.count
                        let maxPointForCurve = 1
                        if count > maxPointForCurve*2 {
                            let p1 = autoPath.points[autoPath.points.count-2*maxPointForCurve]
                            let p2 = autoPath.points[autoPath.points.count-maxPointForCurve]
                            let p5 = point
                            let p3 = CGPoint(x: (p1.x+p5.x)/2, y: (p1.y+p5.y)/2)
                            let p4 = CGPoint(x: p3.x*2-p2.x, y: p3.y*2-p2.y)
                            path.move(to: p1)
                            path.addQuadCurve(to: p3, control: p2)
                            path.addQuadCurve(to: p5, control: p4)
                            self.autoPath = CGMutablePath()
                            if let autoPath = self.autoPath {
                                autoPath.move(to: autoPoints[0])
                                for i in 1..<autoPointIndex-2*maxPointForCurve+1{
                                    autoPath.addLine(to: autoPoints[i])
                                }
                                autoDrawingLayer.path = autoPath
                            }
                        } else {
                            path.move(to: autoPath.currentPoint)
                            path.addLine(to: point)
                        }
                    }
                    autoDrawingIncorrectLayer.path = path
                    startWrongPath = path
                } else {
                    startWrongPath.addLine(to: point)
                    autoDrawingIncorrectLayer.path = startWrongPath
                }
            }
        } else {
            drawingPath.addLine(to: point)
        }
    }
    func processEnd(point:CGPoint){
        if !touch {
            return
        }
        delegate?.tapvietDragEnd(self)
        touch = false
        let ratioLength = drawingPath.length / strokeLength
        if !snap && point.distance(to: endPoint!) > self.bounds.height / 10 {
            // chế độ thường và xa điểm cuối
            validDrawing = false
            shapeLayer.strokeColor = UIColor.clear.cgColor
            QueueSoundPlayer.shared.play(sound: "effects/fail", delay: 0)
            return
        }
        if snap {
            if !validDrawing || (autoPoints.count>20 && autoPath!.points.count < Int(Double(autoPoints.count) * 0.9)) {
                autoDrawingLayer.strokeColor = UIColor.clear.cgColor
                autoDrawingIncorrectLayer.strokeColor = UIColor.clear.cgColor
                if validDrawing {
                    QueueSoundPlayer.shared.play(sound: "effects/fail", delay: 0)
                }
            } else {
                delegate?.tapvietOnStrokeCompleted(self, strokeIndex)
                nextStroke()
            }
        } else {
            if !validDrawing || (ratioLength < 0.5 && strokeLength > self.bounds.height/20) {
                validDrawing = false
                shapeLayer.strokeColor = UIColor.clear.cgColor
                QueueSoundPlayer.shared.play(sound: "effects/fail", delay: 0)
            } else {
                drawingPath = UIBezierPath()
                shapeLayer.path = nil
                delegate?.tapvietOnStrokeCompleted(self, strokeIndex)
                nextStroke()
            }
        }
    }
    func resetWrongLine(){
        
    }
    @objc func handlePan(_ sender: UIPanGestureRecognizer )
    {
        let state = sender.state
        if state == .began {
            processBegin(point: sender.location(in: self))
            audioPlayer?.currentTime = 0
            audioPlayer?.play()
            return
        }
        
        processMove(point: sender.location(in: self))
        
        if state == .ended || state == .cancelled || state == .failed {
            processEnd(point: sender.location(in: self))
            audioPlayer?.stop()
        }
        shapeLayer.path = drawingPath.cgPath
    }
    func checkValidPoint(point:CGPoint)->Bool{
        let color = checkLineImage.pixelColorAtPoint(point: point)
        if let color = color {
            if color.cgColor.same(as: UIColor.clear.cgColor) {
                return false
            }
        }
        return true
    }
    
    var startPoint : CGPoint?
    var endPoint : CGPoint?
    var hintDone = false
    func nextStroke(){
        let strokeCount = svg!.layers.count
        if strokeIndex > 1 {
            // lưu path done
            let imageDone = UIImageView()
            imageDone.image = svg?.clone(pathIndex: strokeIndex - 1).uiImage.tinted(withColor: .init(hex: "#A060FC"))
            viewDone.addSubviewWithInset(subview: imageDone, inset: 0)
        }
        if strokeIndex >= strokeCount {
            delegate?.tapvietOnCompleted(self)
            return
        }
        // vẻ hint mới
        
        svgHint1 = svg?.clone(pathIndex: strokeIndex).uiImage(paths: [0,1,2,5,6,7])
        svgHint2 = svg?.clone(pathIndex: strokeIndex).uiImage(paths: [0,1,3,5,6,7])
        svgHint3 = svg?.clone(pathIndex: strokeIndex).uiImage(paths: [0,1,4,5,6,7])
        hintDone = true
        imageHint.isHidden = true
        
        let layer = (svg!.image.caLayerTree!.sublayers![strokeIndex] as! CALayerWithChildHitTest).sublayers![0] as! CAShapeLayer
        lineWidth = layer.lineWidth
        let svgWidth = svg!.size.width
        let svgHeight = svg!.size.height
        let scale = bounds.height / svg!.size.height
        strokeLength = layer.path!.length * scale
        let checkLineSVG = svg?.clone(pathIndex: strokeIndex)
        let sublayer = (checkLineSVG?.image.caLayerTree.sublayers?[strokeIndex] as! CALayerWithChildHitTest).sublayers?[0] as! CAShapeLayer
        let width = sublayer.lineWidth
        sublayer.lineWidth = width * 3
        checkLineImage.contentMode = .scaleAspectFit
        checkLineImage.image = checkLineSVG?.uiImage
        checkLineImage.alpha = 0.01
        sublayer.lineWidth = width
        
        let pos = layer.position
        startPoint = layer.convert(layer.path!.startPoint!, to: svg!.image.caLayerTree)
        endPoint = layer.convert(layer.path!.currentPoint, to: svg!.image.caLayerTree)
        startPoint!.x *= scale
        startPoint!.y *= scale
        endPoint!.x *= scale
        endPoint!.y *= scale
        
        strokeIndex += 1
        
        if snap {
            autoPointIndex = 0
            if autoDrawingLayer.superview == nil {
                autoDrawingLayer.removeFromSuperlayer()
            }
            self.layer.addSublayer(autoDrawingLayer)
            if autoDrawingIncorrectLayer.superview == nil {
                autoDrawingIncorrectLayer.removeFromSuperlayer()
            }
            self.layer.addSublayer(autoDrawingIncorrectLayer)
            let points = layer.path!.copy(dashingWithPhase: 0, lengths: [0,5]).points
            autoPoints = []
            for i in 0..<points.count/2 {
                autoPoints.append(points[2*i])
            }
            autoPoints = autoPoints.map{layer.convert($0, to: svg!.image.caLayerTree)}.map{CGPointMake($0.x*scale, $0.y*scale)}
            autoPath = CGMutablePath()
            autoPath?.move(to: autoPoints[0])
            autoDrawingLayer.path = autoPath
            autoDrawingLayer.lineWidth = lineWidth * 2
            autoDrawingLayer.strokeColor = UIColor.green.cgColor
            autoDrawingLayer.lineCap = .round
            autoDrawingLayer.lineJoin = .round
            
            autoDrawingIncorrectLayer.lineWidth = lineWidth * 2
            autoDrawingIncorrectLayer.strokeColor = UIColor.red.cgColor
            autoDrawingIncorrectLayer.lineCap = .round
            autoDrawingIncorrectLayer.lineJoin = .round
        }
    }
    var hideLineHint = false
    var hideDotHint = false
    var lineWidth : CGFloat = 0
    func setupDraw(){
        shapeLayer.lineWidth = 20
        shapeLayer.fillColor = UIColor.clear.cgColor
        shapeLayer.strokeColor = UIColor.color(hex: "#8A1FB7").cgColor
        shapeLayer.lineCap = .round
        shapeLayer.lineJoin = .round
        
        layer.addSublayer(shapeLayer)
    }
    var snap = false
}

protocol TapVietView2Delegate: AnyObject {
    func tapvietOnCompleted(_ tv: TapVietView2)
    func tapvietOnStrokeCompleted(_ tv: TapVietView2, _ strokeIndex: Int)
    func tapvietDragBegin(_ tv: TapVietView2)
    func tapvietDragEnd(_ tv: TapVietView2)
}
