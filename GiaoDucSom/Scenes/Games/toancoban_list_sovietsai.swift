//
//  toancoban_list_sovietsai.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sovietsai: NhanBietGameFragment {
    // MARK: - Properties
    private var gridLayout: MyGridView!
    private var rightCount: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    var wrongPositions : [Int] = []
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let numbers = [1, 1, 2, 2, 3, 3, 4, 4, 5, 5, 6, 6, 7, 7, 9, 9].shuffled().take(count: 10)
        wrongPositions = Utils.generatePermutation(random(2, 3, 4), size: 10)
        var views: [UIView] = []
        
        for (i, number) in numbers.enumerated() {
            let view = createNumberItem(value: number, isWrong: wrongPositions.contains(i))
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        gridLayout.columns = 5
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.02
        gridLayout.insetRatio = 0.05
        gridLayout.reloadItemViews(views: views)
        
        let delay = playSound(openGameSound(), "toan/toan_so viet sai")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so viet sai")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        // Không có logic đặc biệt trong Java, để trống như nguyên bản
    }
    
    // MARK: - Helper Methods

    
    private func createNumberItem(value: Int, isWrong: Bool) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        textNumber.transform = CGAffineTransform(scaleX: isWrong ? -1 : 1, y: 1)
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        textNumber.tag = 100
        container.tag = isWrong ? 1 : 0 // 1 cho sai, 0 cho đúng
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.viewWithTag(100) as? UILabel else { return }
        pauseGame()
        
        let isWrong = view.tag == 1
        if isWrong {
            playSound("effect/answer_correct")
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            
            scheduler.schedule(delay: 0.5) { [weak self] in
                guard let self = self, let number = Int(textNumber.text ?? "0") else { return }
                self.playSound("topics/Numbers/\(number)")
                UIView.animate(withDuration: 0.5, animations: {
                    textNumber.transform = CGAffineTransform(scaleX: 1, y: 1)
                }) { _ in
                    self.rightCount += 1
                    if self.rightCount == self.wrongPositions.count {
                        self.animateCoinIfCorrect(view: self.gridLayout)
                        let delay = 0.5 + self.playSound(delay: 0.5, names: self.finishEndSounds())
                        self.scheduler.schedule(delay: delay) { [weak self] in
                            self?.finishGame()
                        }
                    } else {
                        self.resumeGame()
                    }
                }
                self.scheduler.schedule(delay: 0.25) { [weak self] in
                    guard self != nil else { return }
                    textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
                }
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}

