<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#88D35A"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/layout_sound"
        android:layout_width="0dp"
        android:layout_height="55dp"
        android:layout_marginTop="20dp"
        android:background="@drawable/btn_sound_no_bg"
        android:visibility="gone"
        app:layout_constraintDimensionRatio="96:18"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginVertical="13dp"
            android:layout_marginRight="10dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="listen and unscramble"
            android:textColor="#99B6C1"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.78" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_margin="50dp"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toTopOf="@id/bottom_layout"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toBottomOf="@id/layout_sound">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/btn_read"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:src="@drawable/bg_obj_white" />

            <com.kidsup.giaoducsom.view.SVGAutosizeView
                android:id="@+id/svg_view"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/bottom_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#C3E87C"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/grid_bottom_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:alpha="0.01"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9" />

        <com.kidsup.giaoducsom.view.MyGridLayout
            android:id="@+id/grid_bottom_2_layout"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.9" />
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>