//
//  nhanbiet_list_bongbay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 11/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio
import Interpolate

class nhanbiet_list_bongbay: NhanBietGameFragment {
    // MARK: - Properties
    private var itemContainer: UIView!
    private var coinView: UIView!
    private let colorList: [UIColor] = [
        UIColor(hex: "#0593FF"),
        UIColor(hex: "#EE2957"),
        UIColor(hex: "#4BDB0D"),
        UIColor(hex: "#F050D8"),
        UIColor(hex: "#FFB901")
    ]
    private var count: Int = 0
    private var lastestColor: UIColor?
    private var originScale: [UIView: CGFloat] = [:]
    private var balloonIndex: Int = 0
    private var maxBalloonPerRound: Int = 4
    private var rightIndex: Int = 0
    private var itemIndex: Int = 0
    private var svgItemMap: [SVGKImage: Item] = [:]
    private var svg: SVGKImage?
    private var svgs: [SVGKImage] = []
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(hex: "#FFF")
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "nhanbiet_bg_cloud"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        coinView = UIView()
        coinView.clipsToBounds = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalTo(coinView.snp.height) // Ratio 1:1
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        var delay = playSound(delay: 0, names: [openGameSound(),"\(getLanguage())/nhanbiet/nhanbiet_balloon"])
        scheduler.schedule(after: 1, execute: startGame)
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("\(getLanguage())/nhanbiet/nhanbiet_balloon")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        guard let listItems = getListItems(), let folder = getFolder() else { return }
        let paths = listItems.map { "topics/\(folder)/\($0.path!)" }
        
        // Load SVGs asynchronously
        var loadedSVGs: [SVGKImage] = []
        for path in paths {
            let svgImage = Utilities.GetSVGKImage(named: path)
            loadedSVGs.append(svgImage)
        }
        self.svgs = loadedSVGs + loadedSVGs // Duplicate as in Java
        self.svgs.shuffle()
        for (i, item) in listItems.enumerated() {
            if i < self.svgs.count {
                self.svgItemMap[self.svgs[i]] = item
            }
        }
        
        // Load balloon SVG
        let balloonSVG = Utilities.GetSVGKImage(named: "balloon")
        self.svg = balloonSVG
        self.createItems()
    }
    
    // MARK: - Helper Methods
    private func getNextColor() -> UIColor {
        var color: UIColor
        repeat {
            color = colorList.randomElement()!
        } while color == lastestColor
        lastestColor = color
        return color
    }
    
    private func createItems() {
        guard gameState != .finished else { return }
        
        if balloonIndex >= maxBalloonPerRound {
            balloonIndex = 0
        }
        if balloonIndex == 0 {
            maxBalloonPerRound = Int.random(in: 3...8)
            rightIndex = Int.random(in: 0..<maxBalloonPerRound)
        }
        
        let width = itemContainer.frame.width
        let height = itemContainer.frame.height
        
        let apple = UIControl()
        itemContainer.addSubview(apple)
        apple.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.25)
            make.width.equalTo(apple.snp.height).multipliedBy(0.8)
            make.right.equalToSuperview().multipliedBy(Double.random(in: 0.2..<0.9))
        }
        apple.clipsToBounds = false
        let svgAutosizeView = SVGImageView(frame: .zero)
        svgAutosizeView.contentMode = .scaleAspectFit
        let svgClone = svg!
        let color = getNextColor()
        apple.tag = color.hashValue
        // Apply color to SVG paths
        svgClone.caLayerTree.sublayers?.forEach { group in
            group.sublayers?.forEach { path in
                if path.name != nil {
                    path.setFillColor(color: color.cgColor)
                }
            }
            group.isHidden = group != svgClone.caLayerTree.sublayers?.first
        }
        
        svgAutosizeView.image = svgClone.uiImage
        
        let textView = UILabel()
        textView.text = balloonIndex == rightIndex ? "Đúng" : "S"
        textView.textColor = .white
        textView.textAlignment = .center
        textView.font = UIFont(name: "SVN-Freude", size: 800)
        textView.adjustsFontSizeToFitWidth = true
        textView.minimumScaleFactor = 10/800
        textView.numberOfLines = 1
        textView.isHidden = true // Visibility gone in XML
        
        let innerContainer = UIView()
        innerContainer.isUserInteractionEnabled = false
        innerContainer.clipsToBounds = false
        apple.addSubview(innerContainer)
        innerContainer.addSubview(svgAutosizeView)
        innerContainer.addSubview(textView)
                
        innerContainer.makeViewCenterAndKeep(ratio: 0.8)
        
        svgAutosizeView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(2.5)
            make.height.equalToSuperview().multipliedBy(2.5)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(1.3)
        }
        
        textView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.8)
            make.centerX.top.equalToSuperview()
        }
        
        let scale = 1 + Double.random(in: 0..<0.5)
        apple.transform = CGAffineTransformMakeScale(scale, scale).concatenating(CGAffineTransformMakeTranslation(0, height * 1.3))
        let animValues: [Double] = [height*1.3,-height * 0.8]
        let timeChange = Interpolate(values: animValues,
        apply: { [weak self] (value) in
            apple.transform = CGAffineTransformMakeScale(scale, scale).concatenating(CGAffineTransformMakeTranslation(0, value))
        })
        let duration = 6/scale + Double.random(in: 0..<2)
        timeChange.animate(1, duration: duration)
        scheduler.schedule(delay: duration) {
            [weak self] in
            guard let self = self else { return }
            apple.isHidden = true
            apple.alpha = 0
            apple.removeFromSuperview()
        }
        
        apple.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        
        // Schedule next balloon
        scheduler.schedule(after: TimeInterval(0.5 + Double.random(in: 0...0.1))) { [weak self] in
            guard let self = self, self.gameState != .finished else { return }
            self.createItems()
        }            
        
        balloonIndex += 1
    }
    
    private func addItemAt(view: UIView) {
        guard itemIndex < svgs.count else { return }
        let svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        svgView.image = svgs[itemIndex].uiImage
        
        var delay: TimeInterval = 0.3
        if let folder = getFolder(), let item = svgItemMap[svgs[itemIndex]] {
            delay += playSound(delay: delay, names: [
                "effect/answer_correct",
                "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
        }
        itemIndex += 1
        
        itemContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalTo(svgView.snp.height)
            make.height.equalToSuperview().multipliedBy(0.25)
            make.left.top.equalToSuperview()
        }
        //svgView.backgroundColor = .red.withAlphaComponent(0.5)
        svgView.alpha = 0
        scheduler.schedule(delay: 0.01) {
            [weak self] in
            guard let self = self else { return }
            svgView.moveToCenter(of: view, duration: 0) {
                [weak self] _ in
                guard let self = self else { return }
                // Elastic animation
                UIView.animate(withDuration: 0.8, delay: 0, usingSpringWithDamping: 0.5, initialSpringVelocity: 0.5, options: [], animations: {
                    svgView.alpha = 1
                    //svgView.transform = .identity
                }, completion: { _ in
                    // Fade out after delay
                    UIView.animate(withDuration: 0.5, delay: 2.0, options: [], animations: {
                        svgView.alpha = 0
                        //svgView.transform = CGAffineTransform(scaleX: 0, y: 0)
                    }, completion: { _ in
                        svgView.removeFromSuperview()
                    })
                })
            }
        }
    }
    
    private func rightClick(svgAutosizeView: SVGImageView, color: UIColor) {
        let svgClone = svg!
        guard let groups = svgClone.caLayerTree.sublayers,
              groups.count > 1 else { return }
        for k in 1..<groups.count {
            scheduler.schedule(delay: 0.1 * Double(k)) {
                [weak self] in
                guard let self = self else { return }
                svgClone.caLayerTree.sublayers?.forEach { group in
                    group.sublayers?.forEach { path in
                        if path.name != nil {
                            path.setFillColor(color: color.cgColor)
                        }
                    }
                    group.isHidden = group != svgClone.caLayerTree.sublayers?[k]
                }
                svgAutosizeView.image = svgClone.uiImage
            }
        }
        scheduler.schedule(after: TimeInterval(groups.count) * 0.1) {
            [weak svgAutosizeView] in
            guard let svgAutosizeView = svgAutosizeView else { return }
            svgAutosizeView.isHidden = true
        }
    }
    
    // MARK: - Touch Handling
    @objc func itemClick(_ sender : UIControl){
        guard let apple = sender as? UIControl,
              let innerContainer = apple.subviews.first,
              let svgAutosizeView = innerContainer.subviews.first(where: { $0 is SVGImageView }) as? SVGImageView,
              let textView = innerContainer.subviews.first(where: { $0 is UILabel }) as? UILabel else { return }
        
        // Remove gesture to prevent multiple taps
        apple.gestureRecognizers?.removeAll()
        
        playSound("effect/balloonpop\(Int.random(in: 1...3))")
        
        if textView.text == "Đúng" {
            // addItemAt(apple)
            guard let color = colorList.first(where: { $0.hashValue == apple.tag }) else { return }
            rightClick(svgAutosizeView: svgAutosizeView, color: color)
            addItemAt(view: apple)
            count += 1
            if count == svgs.count {
                pauseGame(stopMusic: false)
                var delay: TimeInterval = 1.0
                delay += playSound(delay: delay, names: ["effect/answer_end", getCorrectHumanSound(), endGameSound()])
                animateCoinIfCorrect(view: coinView)
                scheduler.schedule(after: delay + 1.0) { [weak self] in
                    self?.finishGame()
                }
            }
        } else {
            guard let color = colorList.first(where: { $0.hashValue == apple.tag }) else { return }
            rightClick(svgAutosizeView: svgAutosizeView, color: color)
        }
    }
}

