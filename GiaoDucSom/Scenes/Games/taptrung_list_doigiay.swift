//
//  taptrung_list_doigiay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/3/25.
//


import UIKit
import SnapKit

class taptrung_list_doigiay: NhanBietGameFragment {
    // MARK: - Properties
    private var itemsTop: UIView?
    private var itemsBottom: UIView?
    private var countDone = 0
    private var items: MyList<String> = MyList()
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var svgs: [SVG] = []
    
    override func configureLayout(_ view: UIView) {
        configureLayout()
    }
    
    // MARK: - Configure Layout
    private func configureLayout() {
        backgroundColor = UIColor(red: 209/255, green: 190/255, blue: 192/255, alpha: 1) // #D1BEC0
        
        // Background images
        let topBg = UIImageView(image: Utilities.SVGImage(named: "taptrung_doigiay_bg1"))
        topBg.contentMode = .scaleAspectFit
        addSubview(topBg)
        topBg.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(topBg.snp.width).multipliedBy(200/1000.0)
        }
        
        let bottomBg = UIImageView(image: Utilities.SVGImage(named: "taptrung_doigiay_bg2"))
        bottomBg.contentMode = .scaleAspectFit
        addSubview(bottomBg)
        bottomBg.snp.makeConstraints { make in
            make.bottom.left.right.equalToSuperview()
            make.height.equalTo(bottomBg.snp.width).multipliedBy(160/1000.0)
        }
        
        // Items bottom
        itemsBottom = UIView()
        addSubview(itemsBottom!)
        itemsBottom?.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.width.equalTo(itemsBottom!.snp.height).multipliedBy(4.5) // Ratio 4.5
        }
        
        let bottomViews = (0..<4).map { _ in UIImageView() }
        bottomViews.forEach { itemsBottom?.addSubview($0) }
        bottomViews.enumerated().forEach { (index, view) in
            view.backgroundColor = UIColor.black.withAlphaComponent(0.18) // #2F00
            view.alpha = 0
            view.snp.makeConstraints { make in
                make.width.equalTo(view.snp.height) // Ratio 1:1
                make.height.equalTo(itemsBottom!).multipliedBy(0.6)
                //make.centerX.equalToSuperview().multipliedBy(0.16 + 0.28 * CGFloat(index)) // bias 0.08, 0.36, 0.64, 0.92
                make.top.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                view.snapToHorizontalBias(horizontalBias: 0.08 + 0.28 * CGFloat(index))
                view.alpha = 1
            }
        }
        
        // Items top
        itemsTop = UIView()
        addSubview(itemsTop!)
        itemsTop?.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.width.equalTo(itemsTop!.snp.height).multipliedBy(4.5) // Ratio 4.5
        }
        
        let topViews = (0..<4).map { _ in UIImageView() }
        topViews.forEach { itemsTop?.addSubview($0) }
        topViews.enumerated().forEach { (index, view) in
            view.backgroundColor = UIColor.black.withAlphaComponent(0.18) // #2F00
            view.alpha = 0
            view.snp.makeConstraints { make in
                make.width.equalTo(view.snp.height) // Ratio 1:1
                make.height.equalTo(itemsTop!).multipliedBy(0.6)
                //make.centerX.equalToSuperview().multipliedBy(0.18 + 0.28 * CGFloat(index)) // bias 0.09, 0.36, 0.64, 0.92
                make.centerY.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                view.snapToHorizontalBias(horizontalBias: 0.09 + 0.28 * CGFloat(index))
                view.alpha = 1
            }
        }
        
        // Gesture handling
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemsTop?.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Gesture Handling
    var originX = 0.0
    var originY = 0.0
    var zindex = 1.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let itemsTop = itemsTop, let itemsBottom = itemsBottom else { return }
        let location = gesture.location(in: itemsTop)
        
        switch gesture.state {
        case .began:
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                originX = currentView.frame.origin.x
                originY = currentView.frame.origin.y
                dX = currentView.frame.origin.x - gesture.location(in: self).x
                dY = currentView.frame.origin.y - gesture.location(in: self).y
                zindex += 1
                currentView.layer.zPosition = zindex
                playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            }
            
        case .changed:
            if let currentView = currentView {
                let newX = gesture.location(in: self).x + dX
                let newY = gesture.location(in: self).y + dY
                currentView.transform = CGAffineTransformMakeTranslation(newX - originX, newY - originY)
            }
            
        case .ended:
            guard let currentView = currentView else { return }
            var minDistance = CGFloat.greatestFiniteMagnitude
            var closedView: UIView?
            
            for i in 0..<itemsBottom.subviews.count {
                let viewB = itemsBottom.subviews[i]
                let point = currentView.distanceFromCenterToCenter(to: viewB)
                let distance = hypot(point.x, point.y)
                if distance < minDistance {
                    minDistance = distance
                    closedView = viewB
                }
            }
            
            if let closedView = closedView, minDistance < currentView.frame.origin.y {
                guard let tag1 = currentView.tag as? Int, let tag2 = closedView.tag as? Int else { return }
                if tag1 == tag2 {
                    playSound("effect/word puzzle drop")
                    currentView.moveToCenter(of: closedView,duration: 0.2) {
                        [weak self] _ in
                           guard let self = self else { return }
                           if currentView.alpha != 0 {
                               self.countDone += 1
                               if self.countDone == 4 {
                                   self.pauseGame()
                                   self.animateCoinIfCorrect(view: closedView)
                                   let delay = self.playSound("effect/answer_end", self.getCorrectHumanSound(), self.endGameSound())
                                   self.scheduler.schedule(delay: TimeInterval(delay)) { [weak self] in
                                       self?.finishGame()
                                   }
                               }
                           }
                           currentView.alpha = 0
                        (closedView as? UIImageView)?.image = self.svgs[tag1].uiImage
                        
                    }
                } else {
                    setGameWrong()
                    UIView.animate(withDuration: 0.4) {
                        currentView.transform = .identity
                    }
                    playSound("effect/slide2")
                }
            } else {
                UIView.animate(withDuration: 0.4) {
                    currentView.transform = .identity
                }
                playSound("effect/slide2")
            }
            self.currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        guard let itemsTop = itemsTop else { return nil }
        for child in itemsTop.subviews.reversed() {
            let frame = child.frame
            if x >= frame.minX && x <= frame.maxX && y >= frame.minY && y <= frame.maxY {
                return child
            }
        }
        return nil
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        items = MyList(StorageManager.manager.list(path: "shoes/")).randomOrder().take(4)
        let svgPaths = items.map { "shoes/\($0)" }
        svgPaths.forEach { path in svgs.append(SVG(image: Utilities.GetSVGKImage(named: path))) }
        let topViews = VisualTree.getChildren(self.itemsTop, ofType: UIImageView.self)
        let bottomViews = VisualTree.getChildren(self.itemsBottom, ofType: UIImageView.self)
        let bottomIndexes = Utils.generatePermutation(4)
        
        for i in 0..<4 {
            let indexes = Utils.generatePermutation(2)
            topViews[i].image = self.svgs[i].clone(pathIndex: indexes[0]).uiImage
            bottomViews[bottomIndexes[i]].image = self.svgs[i].clone(pathIndex: indexes[1]).uiImage
            topViews[i].backgroundColor = .clear
            bottomViews[bottomIndexes[i]].backgroundColor = .clear
            topViews[i].tag = i
            bottomViews[bottomIndexes[i]].tag = i
        }
        let delay = playSound(openGameSound(), getLanguage() + "/taptrung/doi giay")
        scheduler.schedule(delay: TimeInterval(delay)) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(getLanguage() + "/taptrung/doi giay")
            scheduler.schedule(delay: TimeInterval(delay)) { [weak self] in
                self?.resumeGame()
            }
        }
    }
}


struct VisualTree {    
    static func getChildren<T>(_ view: UIView?, ofType: T.Type) -> [T] {
        return view?.subviews.compactMap { $0 as? T } ?? []
    }
}
