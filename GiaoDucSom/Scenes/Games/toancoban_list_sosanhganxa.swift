//
//  toancoban_list_sosanhganxa.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 5/4/25.
//


import UIKit
import SnapKit

class toancoban_list_sosanhganxa: NhanBietGameFragment {
    // MARK: - Properties
    private let drawableIds = ["math_choganxa_dog1", "math_choganxa_dog2", "math_choganxa_dog3", "math_choganxa_dog4", "math_choganxa_dog5", "math_choganxa_dog6"]
    private var isNear: Bool = false
    private var meIndex: Int = 0
    private var imageLine: UIImageView!
    var coinView: UIView!
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 155/255, green: 86/255, blue: 53/255, alpha: 1) // #9B5635
        
        let groundView = UIView()
        groundView.backgroundColor = UIColor(red: 136/255, green: 75/255, blue: 41/255, alpha: 1) // #884B29
        addSubviewWithPercentInset(subview: groundView, percentInset: 0)
        groundView.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.5)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        let itemContainer = UIView()
        addSubviewWithPercentInset(subview: itemContainer, percentInset: 5)
        itemContainer.makeViewCenterAndKeep(ratio: 2.0)
        
        let dogs = [0,1].map { id -> UIImageView in
            let dog = UIImageView()
            dog.contentMode = .scaleAspectFit
            dog.tag = id
            return dog
        }
        
        itemContainer.addSubview(dogs[0])
        dogs[0].snp.makeConstraints { make in
            make.width.equalTo(dogs[0].snp.height).multipliedBy(465.0 / 302.0) // Ratio 465:302
            make.height.equalTo(itemContainer).multipliedBy(0.25)
        }
        addActionOnLayoutSubviews {
            dogs[0].snapToHorizontalBias(horizontalBias: 0.05)
            dogs[0].snapToVerticalBias(verticalBias: 0.52)
        }
        
        itemContainer.addSubview(dogs[1])
        dogs[1].snp.makeConstraints { make in
            make.width.equalTo(dogs[1].snp.height).multipliedBy(465.0 / 302.0) // Ratio 465:302
            make.height.equalTo(itemContainer).multipliedBy(0.25)
        }
        dogs[1].transform = CGAffineTransformMakeScale(-1, 1)
        addActionOnLayoutSubviews {
            dogs[1].snapToHorizontalBias(horizontalBias: 0.95)
            dogs[1].snapToVerticalBias(verticalBias: 0.52)
        }
        
        imageLine = UIImageView(image: Utilities.SVGImage(named: "math_choganxa_bg"))
        imageLine.contentMode = .scaleAspectFit
        itemContainer.addSubview(imageLine)
        imageLine.snp.makeConstraints { make in
            make.width.equalTo(itemContainer).multipliedBy(0.76)
            make.height.equalTo(imageLine.snp.width).multipliedBy(238.0 / 2116.0) // Ratio 2116:238
            make.centerX.equalToSuperview()
        }
        addActionOnLayoutSubviews {
            self.imageLine.snapToVerticalBias(verticalBias: 0.78)
        }
        
        let tap5 = UITapGestureRecognizer(target: self, action: #selector(onDogTapped(_:)))
        dogs[0].addGestureRecognizer(tap5)
        dogs[0].isUserInteractionEnabled = true
        
        let tap6 = UITapGestureRecognizer(target: self, action: #selector(onDogTapped(_:)))
        dogs[1].addGestureRecognizer(tap6)
        dogs[1].isUserInteractionEnabled = true
        
        let indexes = Utils.generatePermutation(2).shuffled()
        dogs[0].image = Utilities.SVGImage(named: drawableIds[indexes[0]])
        dogs[1].image = Utilities.SVGImage(named: drawableIds[indexes[1]])
        meIndex = random.nextInt(bound: 2)
        isNear = Bool.random()
        
        if meIndex == 1 {
            if isNear {
                imageLine.transform = CGAffineTransform(scaleX: -1, y: 1)
            }
        } else {
            if !isNear {
                imageLine.transform = CGAffineTransform(scaleX: -1, y: 1)
            }
        }
        coinView = UIView()
        addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        let delay = playSound(openGameSound(), isNear ? "toan/toan_cho gan xa" : "toan/toan_cho gan xa2")
        scheduler.schedule(delay: delay) { [weak self] in
            DispatchQueue.main.async {
                self?.startGame()
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(isNear ? "toan/toan_cho gan xa" : "toan/toan_cho gan xa2")
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.resumeGame()
                }
            }
        }
    }
    
    override func createGame() {
        super.createGame()
    }
    
    // MARK: - Touch Handling
    @objc private func onDogTapped(_ gesture: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view as? UIImageView else { return }
        pauseGame()
        let tag = view.tag
        if tag == meIndex {
            animateCoinIfCorrect(view: coinView)
            let delay = playSound(finishCorrect1Sounds())
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.finishGame()
                }
            }
        } else {
            setGameWrong()
            let delay = playSound("effect/answer_wrong", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                DispatchQueue.main.async {
                    self?.resumeGame()
                }
            }
        }
    }
}
