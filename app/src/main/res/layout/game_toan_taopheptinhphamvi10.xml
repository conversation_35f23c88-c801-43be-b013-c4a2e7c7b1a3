<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#E9FDFF"
    android:clipChildren="false"
    android:clipToPadding="false">


    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/bottom_item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="5.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercent="0.1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout65"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout64"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_a"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout64"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout66"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout65"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_operator"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout66"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/math_result_bg"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout67"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout64"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_b"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout67"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.6"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout68"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout66"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="="
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout68"
                android:layout_width="0dp"
                android:layout_height="0dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.6"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout67"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_result"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>


        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <com.kidsup.giaoducsom.view.PercentConstraintLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="#D6FAFF"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintHeight_percent="0.5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/top_item_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clipChildren="false"
            android:clipToPadding="false"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="5.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:pcl_layout_marginPercent="0.1">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout77"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout76"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_1"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="1"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout76"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout78"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout77"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_2"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="2"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout78"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout79"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout76"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_3"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="3"
                    android:textColor="#74B6FF"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout79"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toStartOf="@+id/constraintLayout80"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout78"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_4"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="+"
                    android:textColor="#87D657"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/constraintLayout80"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="@drawable/option_bg_white_shadow"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="0.95"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_percent="1"
                app:layout_constraintHorizontal_bias="0.5"
                app:layout_constraintStart_toEndOf="@+id/constraintLayout79"
                app:layout_constraintTop_toTopOf="parent">

                <com.kidsup.giaoducsom.view.HeightRatioTextView
                    android:id="@+id/text_5"
                    android:layout_width="0dp"
                    android:layout_height="0dp"
                    android:fontFamily="@font/svn_freude"
                    android:gravity="center"
                    android:text="-"
                    android:textColor="#FF7760"
                    app:heightRatio="0.7"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintLeft_toLeftOf="parent"
                    app:layout_constraintRight_toRightOf="parent"
                    app:layout_constraintTop_toTopOf="parent" />
            </androidx.constraintlayout.widget.ConstraintLayout>
        </androidx.constraintlayout.widget.ConstraintLayout>
    </com.kidsup.giaoducsom.view.PercentConstraintLayout>

    <include layout="@layout/item_coin_view" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>