<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/background_item"
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginVertical="15dp"
        android:background="@drawable/btn_text_blue"
        android:orientation="vertical"
        app:layout_constraintDimensionRatio="1148:176"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <TextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:autoSizeTextType="uniform"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:padding="5dp"
            android:src="@drawable/cat"
            android:text="+"
            android:textColor="#fff"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintHorizontal_bias="0.025"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0" />

        <TextView
            android:id="@+id/text_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_gravity="center"
            android:fontFamily="@font/svn_freude"
            android:gravity="left|center_vertical"
            android:paddingVertical="0dp"
            android:text="Thêm trẻ"
            android:textColor="#fff"
            android:textSize="16dp"
            app:autoSizeTextType="uniform"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.4"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.8" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>
