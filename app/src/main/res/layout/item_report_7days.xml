<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="5"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginBottomPercentPerWidth="0.01"
        app:pcl_layout_marginTopPercentPerWidth="0.01">

        <com.caverock.androidsvg.SVGImageView
            android:id="@+id/image_subject"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/btn_subject1"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="@id/text_subject"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/text_subject"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="Môn học"
            android:textColor="#969ABC"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintHorizontal_bias="0"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="1"
            app:layout_constraintWidth_percent="0.3" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.8"
            android:scaleY="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.4"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.3">

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:text="Tổng thời gian"
                android:textColor="#1AA7D3"
                app:heightRatio="0.8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.3"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_total_time"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="right"
                android:text="00:00"
                android:textColor="#1AA7D3"
                app:autoSizeTextType="uniform"
                app:heightRatio="0.8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.7"
                app:layout_constraintWidth_percent="0.7" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/report_icon_time"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.4"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.7" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:scaleX="0.8"
            android:scaleY="0.8"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHorizontal_bias="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintWidth_percent="0.34">

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:text="Điểm trung bình"
                android:textColor="#58CC02"
                app:heightRatio="0.8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.3"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0" />

            <com.kidsup.giaoducsom.view.HeightRatioTextView
                android:id="@+id/text_score"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="right"
                android:text="95%"
                android:textColor="#58CC02"
                app:autoSizeTextType="uniform"
                app:heightRatio="0.8"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.5"
                app:layout_constraintHorizontal_bias="1"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.7"
                app:layout_constraintWidth_percent="0.7" />

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:src="@drawable/report_icon_score"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1"
                app:layout_constraintHeight_percent="0.4"
                app:layout_constraintHorizontal_bias="0"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.7" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:gravity="center"
            android:text=">"
            android:textColor="#969ABC"
            app:fontFamily="@font/svn_freude"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="0.5"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.35" />
    </androidx.constraintlayout.widget.ConstraintLayout>

    <View
        android:id="@+id/bottom_line"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:background="#ACF5F9"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintWidth_percent="1" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>