//
//  ChanDongVatGameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 31/3/25.
//


//
//  ChanDongVatGameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/03/2024.
//

import UIKit
import SnapKit

class toancoban_list_loaivat4chan: NhanBietGameFragment {

    // MARK: - Properties
    private var items: MyList<Item> = MyList<Item>()
    private var meIndex: Int = 0
    private var gridLayout: MyGridView!
    private var folders: [String] = []
    private var indexes: [Int] = []
    private var legs: Int = 0
    private var pack: Folder?

    

    // MARK: - Setup Layout
    private func setupLayout() {
        // Background
        backgroundColor = UIColor(red: 235/255, green: 251/255, blue: 252/255, alpha: 1.0) // #EBFBFC

        // Grid Layout
        gridLayout = MyGridView()
        addSubview(gridLayout)

        gridLayout.columns = 4
        gridLayout.itemRatio = 0.8
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
    
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        // Include nhanbiet_top_menu
        buildTopPopupView(self)
        topMenuContainer?.isHidden = true
    }

    // MARK: - GameFragment Methods

    override open func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        // Setup layout programmatically
        setupLayout()
    }

    override open func updateData() {
        super.updateData()

        legs = random(-1, 2, 4)

        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            if pack.folder.contains("Animals") || pack.folder.contains("Insects") {
                for item in pack.items {
                    if item.legs != nil {
                        items.addAndReturn(item)
                        folders.append(pack.folder)
                    }
                }
            }
        }

        while true {
            indexes = Utils.generatePermutation(items.count).shuffled().take(count: 4)
            var count = 0

            for i in 0..<indexes.count {
                if items[indexes[i]].legs != nil && items[indexes[i]].legs == legs {
                    count += 1
                    meIndex = i
                }
            }

            if count == 1 {
                break
            }
        }
        var views: [UIView] = []

        for i in 0..<indexes.count {
            let item = items[indexes[i]]
            let view = createChimBayItemView()

            let svgView = view.viewWithTag(100) as? UIImageView // Assuming tag is 100
            let textName = view.viewWithTag(101) as? UILabel // Assuming tag is 101

            textName?.text = item.name.vi
            svgView?.image = Utilities.SVGImage(named: "topics/\(folders[indexes[i]])/\(item.path!)")

            // Adjust vertical bias using SnapKit, since it's easier than manipulating layoutParams
            let verticalBias: CGFloat = i % 2 == 0 ? 0.85 : 0.15

            scheduler.schedule(delay: 1, execute: {
                UIView.animate(withDuration: 0.2) {
                    view.transform = CGAffineTransformMakeTranslation(0, i%2==0 ? -view.bounds.height/4 : view.bounds.height/4)
                }
            })
            addActionOnLayoutSubviews{
                UIView.animate(withDuration: 0.2) {
                    view.transform = CGAffineTransformMakeTranslation(0, i%2==0 ? -view.bounds.height/4 : view.bounds.height/4)
                }
            }

            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(itemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            tapGesture.view?.tag = i // Store index in tag

            views.append(view)
        }

        gridLayout.reloadItemViews(views: views)

        let delay = playSound(openGameSound(), legs == -1 ? "tunhien/chan 0" : "tunhien/chan \(legs)")

        scheduler.schedule(delay: delay) {
            self.startGame()
        }
    }
    let nhanbiet_bg_option_white = Utilities.SVGImage(named: "nhanbiet_bg_option_white")
    func createChimBayItemView() -> UIView {
        let view = UIView()    
        let itemContainer = UIView()
        let backgroundView = UIImageView()
        backgroundView.image = nhanbiet_bg_option_white
        let svgView = UIImageView()
        let textName = AutosizeLabel()

        // Set tags
        itemContainer.tag = 102
        svgView.tag = 100
        textName.tag = 101

        // Configure appearance
        view.backgroundColor = UIColor.clear // Transparent background
        itemContainer.backgroundColor = UIColor.clear // Transparent background
        svgView.contentMode = .scaleAspectFit
        textName.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1.0) // #74B6FF
        textName.textAlignment = .center
        textName.font = .Freude(size: 20)

        // Add subviews
        view.addSubview(itemContainer)
        itemContainer.addSubview(backgroundView)
        itemContainer.addSubview(svgView)
        itemContainer.addSubview(textName)
        
        // Set transparent background
        
        itemContainer.makeViewCenterAndKeep(ratio: 0.8)

        svgView.snp.makeConstraints { make in
            make.top.leading.trailing.equalToSuperview()
            make.height.equalTo(svgView.snp.width)
        }
        backgroundView.snp.makeConstraints { make in
            make.edges.equalTo(svgView)
        }

        textName.snp.makeConstraints { make in
            make.leading.trailing.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.23)
        }
        // Ensure the item_container is not clipped
        itemContainer.clipsToBounds = false
        // Ensure the layout is not clipped
        view.clipsToBounds = false
        return view
    }
    override func createGame() {
        super.createGame()
    }

    // MARK: - Event Handling

    @objc func itemTapped(_ sender: UITapGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = sender.view, let index = view.tag as? Int else { return }

        let item = items[indexes[index]]
        let folder = folders[indexes[index]]
        let itemSound = "topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"

        pauseGame()

        if index == meIndex {
            let svgView = view.viewWithTag(100) as? UIImageView
            animateCoinIfCorrect(view: svgView!) // Pass the SVGAutosizeView

            let delay = playSound(answerCorrect1EffectSound(), itemSound, getCorrectHumanSound(), endGameSound())

            scheduler.schedule(delay: delay) {
                self.finishGame()
            }
        } else {
            setGameWrong()

            var soundFiles = [answerWrongEffectSound(), getWrongHumanSound()]
            if item.legs != nil && item.legs! > 0 {
                soundFiles.append(contentsOf: [itemSound, "vi/ngonngu/noi tu ghep/có", "topics/Numbers/\(item.legs!)", "vi/ngonngu/chân"])
            }

            let delay = playSound(soundFiles)

            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }


    override func replayIntroSound() {
        super.replayIntroSound()

        if gameState == .playing {
            pauseGame()
            let delay = playSound([legs == -1 ? "tunhien/chan 0" : "tunhien/chan \(legs)"])
            scheduler.schedule(delay: delay) {
                self.resumeGame()
            }
        }
    }
}
