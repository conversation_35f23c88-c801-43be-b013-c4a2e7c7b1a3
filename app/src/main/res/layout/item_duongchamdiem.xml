<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.kidsup.giaoducsom.view.HeightRatioTextView
        android:id="@+id/text_name"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/option_bg_white_shadow"
        android:fontFamily="@font/svn_freude"
        android:gravity="center"
        android:text="A"
        android:textColor="#2DCEC9"
        app:heightRatio="0.5"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="34:37"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>