//
//  nhanbiet_list_ghephinhngang.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 13/5/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class nhanbiet_list_ghephinhngang: NhanBietGameFragment {
    // MARK: - Properties
    private var svgList: [SVGKImage] = []
    private var svgBottomList: [SVGKImage] = []
    private var svgTopList: [SVGKImage] = []
    private var itemContainer: UIView!
    private var imageViews: [SVGImageView] = []
    private var svgItemMap: [SVGKImage: Item] = [:]
    private var coinView: UIView!
    private var currentView: UIView?
    private var dX: CGFloat = 0
    private var dY: CGFloat = 0
    private var indexOfRow: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white
        view.snp.remakeConstraints { make in
            make.edges.equalToSuperview()
        }
        /*
        let bgImage = SVGKFastImageView(svgkImage: Utilities.GetSVGKImage(named: "nhanbiet_bg_scroll3"))!
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
         */
        let paddingView = UIView()
        view.addSubview(paddingView)
        paddingView.snp.makeConstraints { make in
            make.left.right.equalTo(self)
            make.top.bottom.equalToSuperview().inset(20)
        }
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        paddingView.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1.7)
        
        let itemTop = UIView()
        itemTop.clipsToBounds = true
        itemContainer.addSubview(itemTop)
        itemTop.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(2.9)
            make.height.equalToSuperview().multipliedBy(0.333)
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        let topInner = UIView()
        topInner.clipsToBounds = false
        itemTop.addSubview(topInner)
        topInner.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(3)
            make.centerX.top.equalToSuperview()
        }
        
        let guidelineLeft = UIView()
        topInner.addSubview(guidelineLeft)
        guidelineLeft.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.395)
        }
        
        let guidelineRight = UIView()
        topInner.addSubview(guidelineRight)
        guidelineRight.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.605)
        }
        
        let containerLeft = SVGImageView(frame: .zero)
        containerLeft.stringTag = "container_left"
        topInner.addSubview(containerLeft)
        containerLeft.snp.makeConstraints { make in
            make.width.equalTo(containerLeft.snp.height) // Ratio 1:1
            make.right.equalTo(guidelineLeft.snp.left)
            make.top.bottom.equalToSuperview()
        }
        
        let containerCenter = SVGImageView(frame: .zero)
        containerCenter.stringTag = "container_center"
        topInner.addSubview(containerCenter)
        containerCenter.snp.makeConstraints { make in
            make.width.equalTo(containerCenter.snp.height)
            make.centerX.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        let containerRight = SVGImageView(frame: .zero)
        containerRight.stringTag = "container_right"
        topInner.addSubview(containerRight)
        containerRight.snp.makeConstraints { make in
            make.width.equalTo(containerRight.snp.height)
            make.left.equalTo(guidelineRight.snp.right)
            make.top.bottom.equalToSuperview()
        }
        
        let itemCenter = UIView()
        itemCenter.clipsToBounds = true
        itemContainer.addSubview(itemCenter)
        itemCenter.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(2.9)
            make.height.equalToSuperview().multipliedBy(0.333)
            make.center.equalToSuperview()
        }
        
        let centerInner = UIView()
        centerInner.clipsToBounds = false
        itemCenter.addSubview(centerInner)
        centerInner.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(3)
            make.center.equalToSuperview()
        }
        
        let guidelineLeft2 = UIView()
        centerInner.addSubview(guidelineLeft2)
        guidelineLeft2.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.395)
        }
        
        let guidelineRight2 = UIView()
        centerInner.addSubview(guidelineRight2)
        guidelineRight2.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.605)
        }
        
        let containerLeft2 = SVGImageView(frame: .zero)
        containerLeft2.stringTag = "container_left2"
        centerInner.addSubview(containerLeft2)
        containerLeft2.snp.makeConstraints { make in
            make.width.equalTo(containerLeft2.snp.height)
            make.right.equalTo(guidelineLeft2.snp.left)
            make.top.bottom.equalToSuperview()
        }
        
        let containerCenter2 = SVGImageView(frame: .zero)
        containerCenter2.stringTag = "container_center2"
        centerInner.addSubview(containerCenter2)
        containerCenter2.snp.makeConstraints { make in
            make.width.equalTo(containerCenter2.snp.height)
            make.centerX.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        let containerRight2 = SVGImageView(frame: .zero)
        containerRight2.stringTag = "container_right2"
        centerInner.addSubview(containerRight2)
        containerRight2.snp.makeConstraints { make in
            make.width.equalTo(containerRight2.snp.height)
            make.left.equalTo(guidelineRight2.snp.right)
            make.top.bottom.equalToSuperview()
        }
        
        let itemBottom = UIView()
        itemBottom.clipsToBounds = true
        itemContainer.addSubview(itemBottom)
        itemBottom.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(2.9)
            make.height.equalToSuperview().multipliedBy(0.333)
            make.bottom.equalToSuperview()
            make.centerX.equalToSuperview()
        }
        
        let bottomInner = UIView()
        bottomInner.clipsToBounds = false
        itemBottom.addSubview(bottomInner)
        bottomInner.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(3)
            make.centerX.bottom.equalToSuperview()
        }
        
        let guidelineLeft3 = UIView()
        bottomInner.addSubview(guidelineLeft3)
        guidelineLeft3.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.395)
        }
        
        let guidelineRight3 = UIView()
        bottomInner.addSubview(guidelineRight3)
        guidelineRight3.snp.makeConstraints { make in
            make.width.equalTo(1)
            make.height.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.605)
        }
        
        let containerLeft3 = SVGImageView(frame: .zero)
        containerLeft3.stringTag = "container_left3"
        bottomInner.addSubview(containerLeft3)
        containerLeft3.snp.makeConstraints { make in
            make.width.equalTo(containerLeft3.snp.height)
            make.right.equalTo(guidelineLeft3.snp.left)
            make.top.bottom.equalToSuperview()
        }
        
        let containerCenter3 = SVGImageView(frame: .zero)
        containerCenter3.stringTag = "container_center3"
        bottomInner.addSubview(containerCenter3)
        containerCenter3.snp.makeConstraints { make in
            make.width.equalTo(containerCenter3.snp.height)
            make.centerX.equalToSuperview()
            make.top.bottom.equalToSuperview()
        }
        
        let containerRight3 = SVGImageView(frame: .zero)
        containerRight3.stringTag = "container_right3"
        bottomInner.addSubview(containerRight3)
        containerRight3.snp.makeConstraints { make in
            make.width.equalTo(containerRight3.snp.height)
            make.left.equalTo(guidelineRight3.snp.right)
            make.top.bottom.equalToSuperview()
        }
        
        imageViews = [
            containerLeft, containerCenter, containerRight,
            containerLeft2, containerCenter2, containerRight2,
            containerLeft3, containerCenter3, containerRight3
        ]
        
        coinView = UIView()
        coinView.isUserInteractionEnabled = false
        view.addSubview(coinView)
        coinView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.3)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.center.equalToSuperview()
        }
        
        // Add pan gesture for drag
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        itemContainer.addGestureRecognizer(panGesture)
        /* */
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        guard let listItems = getListItems(), let folder = getFolder(), let item = getItem(),
              listItems.allSatisfy({ $0.path != nil && !$0.path!.isEmpty }) else { return }
        
        svgItemMap = [:]
        svgList = []
        for listItem in listItems {
            let svgImage = Utilities.GetSVGKImage(named: "topics/\(folder)/\(listItem.path!)")
            svgList.append(svgImage)
            svgItemMap[svgImage] = listItem
        }
        
        svgBottomList = svgList.shuffled()
        svgTopList = svgList.shuffled()
        
        for (i, svg) in svgTopList.enumerated() {
            imageViews[i].image = svg.uiImage
            imageViews[3 + i].image = svgList[i].uiImage
            imageViews[6 + i].image = svgBottomList[i].uiImage
        }
        
        let delay = playSound(delay: 0, names: [
            openGameSound(),
            "\(getLanguage())/nhanbiet/nhanbiet_scroll3",
            "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            guard let folder = getFolder(), let item = getItem() else { return }
            let delay = playSound(delay: 0, names: [
                "\(getLanguage())/nhanbiet/nhanbiet_scroll3",
                "\(getLanguage())/topics/\(folder)/\(item.path!.replacingOccurrences(of: ".svg", with: ""))"
            ])
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Touch Handling
    var oX = 0.0
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        guard let view = gesture.view else { return }
        
        switch gesture.state {
        case .began:
            let location = gesture.location(in: itemContainer)
            currentView = findViewUnder(x: location.x, y: location.y)
            if let currentView = currentView {
                dX = currentView.frame.minX - gesture.location(in: itemContainer).x
                dY = currentView.frame.minY - gesture.location(in: itemContainer).y
                indexOfRow = itemContainer.subviews.firstIndex(of: currentView) ?? 0
                oX = currentView.frame.minX
            }
            
        case .changed:
            if let currentView = currentView {
               
                let translation = gesture.translation(in: itemContainer)
                currentView.center = CGPoint(
                    x: currentView.center.x + translation.x,
                    y: currentView.center.y
                )
                gesture.setTranslation(.zero, in: itemContainer)
                
                let deltaRatio = (currentView.frame.minX - oX) / currentView.frame.height
                if deltaRatio > 1.7 {
                    imageViews[3 * indexOfRow + 2].transform = CGAffineTransform(translationX: -3 * currentView.frame.height * 3.05, y: 0)
                } else if deltaRatio < -1.7 {
                    imageViews[3 * indexOfRow].transform = CGAffineTransform(translationX: 3 * currentView.frame.height * 3.05, y: 0)
                } else {
                    imageViews[3 * indexOfRow].transform = .identity
                    imageViews[3 * indexOfRow + 2].transform = .identity
                }
            }
            
        case .ended:
            if let currentView = currentView {
                let deltaRatio = (currentView.frame.minX - oX) / currentView.frame.height
                let delta = deltaRatio < -1.5 ? -1 : deltaRatio > 1.5 ? 1 : 0
                let tx = CGFloat(delta) * currentView.frame.height * 3.05
                
                playSound("effect/slide2")
                UIView.animate(withDuration: 0.2, animations: {
                    currentView.frame.origin.x = self.oX + tx
                }, completion: { _ in
                    if delta != 0 {
                        let svgLists = [self.svgTopList, self.svgList, self.svgBottomList]
                        var svgList = svgLists[self.indexOfRow]
                        if delta == -1 {
                            let svg = svgList.removeFirst()
                            svgList.append(svg)
                        } else {
                            let svg = svgList.removeLast()
                            svgList.insert(svg, at: 0)
                        }
                        if self.indexOfRow == 0 {
                            self.svgTopList = svgList
                        } else if self.indexOfRow == 1 {
                            self.svgList = svgList
                        } else {
                            self.svgBottomList = svgList
                        }
                        for i in 0..<self.svgTopList.count {
                            self.imageViews[3 * self.indexOfRow + i].image = svgList[i].uiImage
                        }
                        currentView.frame.origin.x = self.oX
                        if self.svgTopList[1] == self.svgList[1] && self.svgList[1] == self.svgBottomList[1] && self.svgItemMap[self.svgList[1]] == self.getItem() {
                            self.pauseGame(stopMusic: false)
                            self.animateCoinIfCorrect(view: self.coinView)
                            let delay = self.playSound(delay: 0, names: ["effect/answer_end", self.getCorrectHumanSound(), self.endGameSound()])
                            self.scheduler.schedule(after: delay) { [weak self] in
                                self?.finishGame()
                            }
                        }
                        self.itemContainer.alpha = 1
                        self.imageViews.forEach { $0.transform = .identity }
                    }
                })
            }
            currentView = nil
            
        default:
            break
        }
    }
    
    // MARK: - Helper Methods
    private func findViewUnder(x: CGFloat, y: CGFloat) -> UIView? {
        for child in itemContainer.subviews.reversed() {
            if x >= child.frame.minX && x <= child.frame.maxX &&
               y >= child.frame.minY && y <= child.frame.maxY {
                return child
            }
        }
        return nil
    }
}
