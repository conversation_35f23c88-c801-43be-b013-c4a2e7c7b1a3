//
//  mythuat_list_mauoto.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 29/5/25.
//


import UIKit
import SnapKit
import SVGKit
import Interpolate

class mythuat_list_mauoto: NhanBietGameFragment {
    // MARK: - Properties
    private let colorNames = ["Red", "Yellow", "Orange", "Blue", "Green", "Purple", "White", "Black", "Gray", "Pink"]
    private let colors: [UIColor] = [
        UIColor(red: 1, green: 0, blue: 0, alpha: 1), // #FF0000
        UIColor(red: 1, green: 1, blue: 0, alpha: 1), // #FFFF00
        UIColor(red: 1, green: 165/255, blue: 0, alpha: 1), // #FFA500
        UIColor(red: 0, green: 0, blue: 1, alpha: 1), // #0000FF
        UIColor(red: 0, green: 128/255, blue: 0, alpha: 1), // #008000
        UIColor(red: 128/255, green: 0, blue: 128/255, alpha: 1), // #800080
        UIColor.white, // #FFFFFF
        UIColor.black, // #000000
        UIColor(red: 128/255, green: 128/255, blue: 128/255, alpha: 1), // #808080
        UIColor(red: 1, green: 192/255, blue: 203/255, alpha: 1) // #FFC0CB
    ]
    private var topContainer: UIView!
    private var bottomContainer: UIView!
    private var svg: SVGKImage?
    private var svg2: SVGKImage?
    private var chooseIndexes: [Int] = []
    private var meIndex: Int = 0
    private var carIndex: Int = 0
    private var rightCount: Int = 0
    private let carScheduler = Scheduler()
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFF
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "mythuat_mauoto_bg"))
        bgImage.contentMode = .scaleAspectFill
        view.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        topContainer = UIView()
        topContainer.clipsToBounds = false
        topContainer.backgroundColor = .clear // Transparent
        view.addSubview(topContainer)
        topContainer.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.26)
            make.left.right.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.3) // Bias 0.65
        }
        topContainer.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            topContainer.snapToVerticalBias(verticalBias: 0.65)
        }
        
        
        bottomContainer = UIView()
        bottomContainer.clipsToBounds = false
        bottomContainer.backgroundColor = .clear // Transparent
        view.addSubview(bottomContainer)
        bottomContainer.snp.makeConstraints { make in
            make.height.equalTo(view).multipliedBy(0.26)
            make.left.right.equalToSuperview()
            //make.centerY.equalToSuperview().multipliedBy(1.9) // Bias 0.95
        }
        bottomContainer.waitForLayout {
            [weak self] in
            guard let self = self else { return }
            bottomContainer.snapToVerticalBias(verticalBias: 0.95)
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        chooseIndexes = []
        meIndex = Int.random(in: 0..<colorNames.count)
        for _ in 0..<100 { // Giữ logic lặp 100 lần như Java
            let integers = Utils.generatePermutation(random(6, 7, 8, 9), size: colorNames.count)
            if integers.contains(meIndex) {
                chooseIndexes.append(contentsOf: integers)
            }
        }
        playSound("mythuat/mythuat_mau o to", "topics/Colors/\(colorNames[meIndex].lowercased())")
    }
    
    override func createGame() {
        super.createGame()
        let svgImage = Utilities.GetSVGKImage(named: "images/mythuat_mauoto_car.svg")
        self.svg = svgImage
        self.svg2 = svgImage
        makeCars()
        startGame()
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("mythuat/mythuat_mau o to", "topics/Colors/\(colorNames[meIndex].lowercased())")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    func cleanup() {
        carScheduler.clearAll()
    }
    
    // MARK: - Helper Methods
    private func makeCars() {
        guard let svg = svg, let svg2 = svg2 else { return }
        let duration = ((topContainer.frame.width / topContainer.frame.height) + 2) * 1000
        let cars = 4
        
        // Xe trái
        carIndex += 1
        if let path = svg.caLayerTree.sublayers?.first as? CAShapeLayer {
            path.fillColor = colors[chooseIndexes[carIndex % chooseIndexes.count]].cgColor
        }
        let carLeft = UIImageView()
        carLeft.image = svg.uiImage
        carLeft.isUserInteractionEnabled = true
        carLeft.accessibilityIdentifier = "\(chooseIndexes[carIndex % chooseIndexes.count])" // Lưu index để kiểm tra
        bottomContainer.addSubview(carLeft)
        carLeft.snp.makeConstraints { make in
            make.width.equalTo(carLeft.snp.height).multipliedBy(400.0 / 200.0) // Ratio 400:200
            make.top.bottom.equalToSuperview()
            make.left.equalToSuperview()
        }
        setClickEvent(view: carLeft)
       
        
        let animValues: [Double] = [ -3.0 * self.bottomContainer.frame.height, self.bottomContainer.frame.width ]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * value
            carLeft.transform = CGAffineTransform(translationX: finalValue, y: 0)
        })
        timeChange.animate(1, duration: duration / 1000){
            carLeft.removeFromSuperview()
        }
        
        // Xe phải
        carIndex += 1
        if let path = svg2.caLayerTree.sublayers?.first as? CAShapeLayer {
            path.fillColor = colors[chooseIndexes[carIndex % chooseIndexes.count]].cgColor
        }
        let carRight = UIImageView()
        carRight.image = svg2.uiImage
        carRight.isUserInteractionEnabled = true
        carRight.accessibilityIdentifier = "\(chooseIndexes[carIndex % chooseIndexes.count])" // Lưu index để kiểm tra
        topContainer.addSubview(carRight)
        carRight.transform = CGAffineTransform(scaleX: -1, y: 1)
        carRight.snp.makeConstraints { make in
            make.width.equalTo(carRight.snp.height).multipliedBy(400.0 / 200.0) // Ratio 400:200
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
        setClickEvent(view: carRight)
        
        let animValues2 = [ 2.0 * self.topContainer.frame.height, -self.topContainer.frame.width - self.topContainer.frame.height ]
        let timeChange2 = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues2[0] + (animValues2[1] - animValues2[0]) * value
            carRight.transform = .identity.translatedBy(x: finalValue, y: 0).scaledBy(x: -1, y: 1)
        })
        timeChange2.animate(1, duration: duration / 1000){
            carRight.removeFromSuperview()
        }
        
        carScheduler.schedule(delay: duration / 1000.0 / Double(cars)) { [weak self] in
            self?.makeCars()
        }
    }
    
    private func setClickEvent(view: UIView) {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        view.addGestureRecognizer(tapGesture)
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let indexStr = view.accessibilityIdentifier,
              let index = Int(indexStr) else { return }
        
        view.gestureRecognizers?.removeAll() // Tương đương setOnClickListener(null)
        
        if index == meIndex {
            rightCount += 1
            // if DEBUG { rightCount = 5 }
            if rightCount >= 5 {
                pauseGame(stopMusic: false)
                animateCoinIfCorrect(view: view)
                let delay = playSound("effect/answer_end", "topics/Colors/\(colorNames[index].lowercased())", getCorrectHumanSound(), endGameSound())
                scheduler.schedule(delay: delay) { [weak self] in
                    self?.finishGame()
                }
            } else {
                playSound("effect/answer_correct", "topics/Colors/\(colorNames[index].lowercased())")
            }
        } else {
            setGameWrong()
            pauseGame(stopMusic: false)
            let delay = playSound("topics/Colors/\(colorNames[index].lowercased())", getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
}


