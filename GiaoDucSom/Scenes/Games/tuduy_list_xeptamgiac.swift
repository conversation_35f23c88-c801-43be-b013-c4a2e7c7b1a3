//
//  tuduy_list_xeptamgiac.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 15/4/25.
//


import UIKit
import SnapKit
import SVGKit

class tuduy_list_xeptamgiac: NhanBietGameFragment {
    // MARK: - Properties
    private var svgViewTop: UIImageView!
    private var svgViewBottom: UIImageView!
    private var svgViewRight: SVGKFastImageView!
    private var topDots: [Int] = []
    private var bottomDots: [Int] = []
    private var answerDots: [Int] = []
    private var currentDots: [Int] = []
    private var viewRight: UIView!
    private var coinView: UIView!
    let rightSvg = Utilities.GetSVGKImage(named: "tuduy_xepchongtamgiac")
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = .white // #FFFFFF
        
        let container = UIView()
        addSubviewWithPercentInset(subview: container, percentInset: 0)
        
        svgViewTop = UIImageView()
        svgViewTop.contentMode = .scaleAspectFit
        svgViewTop.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        container.addSubview(svgViewTop)
        svgViewTop.snp.makeConstraints { make in
            make.top.left.equalToSuperview()
            make.height.width.equalTo(container).multipliedBy(0.5)
        }
        
        svgViewBottom = UIImageView()
        svgViewBottom.contentMode = .scaleAspectFit
        svgViewBottom.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        container.addSubview(svgViewBottom)
        svgViewBottom.snp.makeConstraints { make in
            make.bottom.left.equalToSuperview()
            make.height.width.equalTo(container).multipliedBy(0.5)
        }
        
        viewRight = UIView()
        viewRight.alpha = 0
        viewRight.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        container.addSubview(viewRight)
        viewRight.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(container).multipliedBy(0.5)
        }
        
        let rightInnerContainer = UIView()
        viewRight.addSubview(rightInnerContainer)
        rightInnerContainer.snp.makeConstraints { make in
            make.height.equalTo(viewRight).multipliedBy(0.5)
            make.left.right.centerY.equalToSuperview()
        }
        
        let right2 = UIView()
        rightInnerContainer.addSubviewWithPercentInset(subview: right2, percentInset: 10)
        
        svgViewRight = SVGKFastImageView(svgkImage: rightSvg)
        svgViewRight.contentMode = .scaleToFill
        //svgViewRight.backgroundColor = .red.withAlphaComponent(0.2)
        svgViewRight.contentMode = .scaleAspectFit
        right2.addSubview(svgViewRight)
        svgViewRight.makeViewCenterAndKeep(ratio: 400.0 / 204.7)
        
        coinView = UIView()
        container.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
        svgViewRight.addGestureRecognizer(tapGesture)
        svgViewRight.isUserInteractionEnabled = true
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        topDots = Utils.generatePermutation(random(3, 4, 5), size: 9)
        bottomDots = Utils.generatePermutation(random(3, 4, 5), size: 9)
        
        // svg = Utilities.GetSVGKImage(named: "tuduy_xepchongtamgiac")
        let topSvg = Utilities.GetSVGKImage(named: "tuduy_xepchongtamgiac")
        for i in 0..<9 {
            let color = topDots.contains(i) ? UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) : UIColor(red: 214/255, green: 234/255, blue: 255/255, alpha: 1) // #74B6FF, #D6EAFF
            (topSvg.caLayerTree.sublayers?[i] as? CAShapeLayer)?.fillColor = color.cgColor
        }
        svgViewTop.image = topSvg.uiImage
        
        let bottomSvg = Utilities.GetSVGKImage(named: "tuduy_xepchongtamgiac")
        for i in 0..<9 {
            let color = bottomDots.contains(i) ? UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) : UIColor(red: 214/255, green: 234/255, blue: 255/255, alpha: 1) // #74B6FF, #D6EAFF
            (bottomSvg.caLayerTree.sublayers?[i] as? CAShapeLayer)?.fillColor = color.cgColor
        }
        svgViewBottom.image = bottomSvg.uiImage
        
        
        answerDots = []
        currentDots = []
        for i in 0..<9 {
            if topDots.contains(i) || bottomDots.contains(i) {
                answerDots.append(i)
            }
            (rightSvg.caLayerTree.sublayers?[i] as? CAShapeLayer)?.fillColor = UIColor(red: 214/255, green: 234/255, blue: 255/255, alpha: 1).cgColor // #D6EAFF
        }
        svgViewRight.image = rightSvg
    }
    
    override func createGame() {
        super.createGame()
        var tranTop = svgViewTop.transform
        tranTop.tx += frame.width / 4
        svgViewTop.transform = tranTop
        
        var tranBottom = svgViewBottom.transform
        tranBottom.tx += frame.width / 4
        svgViewBottom.transform = tranBottom
        
        let delay = playSound(openGameSound(), "tuduy/tuduy_xep tam giac")
        scheduler.schedule(delay: delay) { [weak self] in
            guard let self = self else { return }
            self.startGame()
            UIView.animate(withDuration: 0.6) {
                [weak self] in
                guard let self = self else { return }
                var tranTop = svgViewTop.transform
                tranTop.tx -= frame.width / 4
                svgViewTop.transform = tranTop
                
                var tranBottom = svgViewBottom.transform
                tranBottom.tx -= frame.width / 4
                svgViewBottom.transform = tranBottom
                
                viewRight.alpha = 1
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/tuduy_xep tam giac")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard gesture.numberOfTouches == 1, let view = gesture.view as? SVGKFastImageView else { return }
        let location = gesture.location(in: view)
        let svg = svgViewRight.image!
        let size = svg.size
        let scale = svg.size.width / svgViewRight.frame.width
        let isHeight = size.height / size.width > view.frame.height / view.frame.width
        var unitX = location.x / view.frame.width
        var unitY = location.y / view.frame.height
        
        if isHeight {
            let W = size.width / size.height * view.frame.height
            let deltaX = (view.frame.width - W) / 2
            unitX = (location.x - deltaX) / W
        } else {
            let H = size.height / size.width * view.frame.width
            let deltaY = (view.frame.height - H) / 2
            unitY = (location.y - deltaY) / H
        }
        let rightPath = svg.caLayerTree.hitTest(CGPoint(x: unitX * size.width, y: unitY * size.height)) as? CAShapeLayer
        for i in (0..<svg.caLayerTree.sublayers!.count - 2).reversed() {
            if rightPath == svg.caLayerTree.sublayers?[i] {
                let normalColor = UIColor(red: 214/255, green: 234/255, blue: 255/255, alpha: 1) // #D6EAFF
                let highlightColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
                let selected = rightPath?.fillColor == highlightColor.cgColor
                rightPath?.fillColor = selected ? normalColor.cgColor : highlightColor.cgColor
                view.setNeedsDisplay()
                
                if selected {
                    currentDots.removeAll { $0 == i }
                } else {
                    currentDots.append(i)
                }
                
                let set1 = Set(currentDots)
                let set2 = Set(answerDots)
                let haveSameItems = set1 == set2
                if haveSameItems {
                    animateCoinIfCorrect(view: coinView)
                    let delay = playSound("effect/answer_end", getCorrectHumanSound(), endGameSound())
                    pauseGame()
                    scheduler.schedule(delay: delay) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    if topDots.contains(i) || bottomDots.contains(i) {
                        if !selected {
                            playSound("effect/answer_correct")
                        }
                    } else {
                        if selected {
                            playSound("effect/answer_correct")
                        }
                    }
                }
                break
            }
        }
        
        
    }
}

