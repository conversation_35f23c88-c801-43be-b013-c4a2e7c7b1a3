//
//  GameFragment.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import AVFAudio

class GameFragment: NhanBietGameFragment, ViewTapListener {
    // MARK: - Properties
    var gameId: String?
    var data: Animation?
    var game: Animation { return data! }
    var incorrect: Int = 0
    var lesson: Lesson?
    var lessonIndex: Int = 0
    var tapdoc: Bool = false
    
    // MARK: - Initialization
    static func createGameFragment(by data: Animation, tapdoc: Bool) -> GameFragment {
        let gameId = data.game
        var gameFragment: GameFragment
        
        switch gameId!.rawValue {
        case "balloon":
            gameFragment = tapdoc ? phonics_list_balloon() : phonics_list_balloon()
        case "basketball":
            gameFragment = tapdoc ? phonics_list_basketball() : phonics_list_basketball()
        case "bee":
            gameFragment = tapdoc ? phonics_list_bee() : phonics_list_bee()
        case "beginning sound":
            gameFragment = tapdoc ? phonics_list_beginningsound() : phonics_list_beginningsound()
        case "can you hear":
            gameFragment = tapdoc ? phonics_list_canyouhear() : phonics_list_canyouhear()
        case "chant":
            gameFragment = tapdoc ? phonics_list_chant() : phonics_list_chant()
        case "chant beat1":
            gameFragment = tapdoc ? phonics_list_chantbeat1() : phonics_list_chantbeat1()
        case "crab":
            gameFragment = tapdoc ? phonics_list_crab() : phonics_list_crab()
        case "drag the letter":
            gameFragment = tapdoc ? phonics_list_dragtheletter() : phonics_list_dragtheletter()
        case "flashcards":
            gameFragment = tapdoc ? phonics_list_flashcards() : phonics_list_flashcards()
        case "flashcards2":
            gameFragment = tapdoc ? phonics_list_flashcards2() : phonics_list_flashcards2()
        case "letter and sound":
            gameFragment = tapdoc ? phonics_list_letterandsound() : phonics_list_letterandsound()
        case "letter intro":
            gameFragment = tapdoc ? phonics_list_letterintro() : phonics_list_letterintro()
        case "listen and check":
            gameFragment = tapdoc ? phonics_list_listenandcheck() : phonics_list_listenandcheck()
        case "listen for1":
            gameFragment = tapdoc ? phonics_list_listenfor1() : phonics_list_listenfor1()
        case "listen for2":
            gameFragment = tapdoc ? phonics_list_listenfor2() : phonics_list_listenfor2()
        case "listening":
            gameFragment = tapdoc ? phonics_list_listening() : phonics_list_listening()
        case "memory":
            gameFragment = tapdoc ? phonics_list_memory() : phonics_list_memory()
        case "merged sounds":
            gameFragment = tapdoc ? phonics_list_mergedsound() : phonics_list_mergedsound()
        case "missing letter":
            gameFragment = tapdoc ? phonics_list_missingletter() : phonics_list_missingletter()
        case "photo":
            gameFragment = tapdoc ? phonics_list_photo() : phonics_list_photo()
        case "read":
            gameFragment = tapdoc ? phonics_list_read() : phonics_list_read()
        case "read and check":
            gameFragment = tapdoc ? phonics_list_readandcheck() : phonics_list_readandcheck()
        case "read the word":
            gameFragment = tapdoc ? phonics_list_readtheword() : phonics_list_readtheword()
        case "reading":
            gameFragment = tapdoc ? phonics_list_reading() : phonics_list_reading()
        case "reading2":
            gameFragment = tapdoc ? phonics_list_reading2() : phonics_list_reading2()
        case "rhyme":
            gameFragment = tapdoc ? phonics_list_rhyme() : phonics_list_rhyme()
        case "same beginning sound":
            gameFragment = tapdoc ? phonics_list_samebeginningsound() : phonics_list_samebeginningsound()
        case "same rhyme":
            gameFragment = tapdoc ? phonics_list_samerhyme() : phonics_list_samerhyme()
        case "sound":
            gameFragment = tapdoc ? phonics_list_sound() : phonics_list_sound()
        case "sound2":
            gameFragment = tapdoc ? phonics_list_sound2() : phonics_list_sound2()
        case "spell the word":
            gameFragment = tapdoc ? phonics_list_spelltheword() : phonics_list_spelltheword()
        case "stickers":
            gameFragment = tapdoc ? phonics_list_stickers() : phonics_list_stickers()
        case "tap viet":
            gameFragment = tapdoc ? phonics_list_tapviet() : phonics_list_tapviet()
        case "tomau":
            gameFragment = tapdoc ? phonics_list_tomau() : phonics_list_tomau()
        case "unscramble":
            gameFragment = tapdoc ? phonics_list_unscramble() : phonics_list_unscramble()
        case "what letter is this":
            gameFragment = tapdoc ? phonics_list_whatletteristhis() : phonics_list_whatletteristhis()
        case "what sound is this":
            gameFragment = tapdoc ? phonics_list_whatsoundisthis() : phonics_list_whatsoundisthis()
        case "word puzzle":
            gameFragment = tapdoc ? phonics_list_wordpuzzle() : phonics_list_wordpuzzle()
        case "writing":
            gameFragment = tapdoc ? phonics_list_writing() : phonics_list_writing()
        case "writing word":
            gameFragment = tapdoc ? phonics_list_tapviet() : phonics_list_tapviet()
        case "intro":
            gameFragment = tapdoc ? phonics_list_intro() : phonics_list_intro()
        default:
            gameFragment = DoingFragment()
            // (gameFragment as? DoingFragment)?.setGameName(gameId)
        }
        
        gameFragment.setGameId(gameId!.rawValue)
        gameFragment.setData(data)
        return gameFragment
    }
    
    // MARK: - Methods
    func isVocab() -> Bool {
        return String(describing: type(of: self)).contains("vocab_list_")
    }
    
    func isTapdoc() -> Bool {
        return tapdoc
    }
    
    @discardableResult
    func setTapdoc(_ tapdoc: Bool) -> GameFragment {
        self.tapdoc = tapdoc
        return self
    }
    
    func enableStartGameSound() -> Bool {
        return true
    }
    /*
    func isSupportUpperCase() -> Bool {
        if let data = data, data.questions == 1, data.values!.count > 2, data.values[0].value.map{String($0)}.lowercased() == data.values[1].lowercased() {
            return true
        }
        return false
    }*/
    
    func getGameId() -> String? {
        return gameId
    }
    
    func setGameId(_ gameId: String) {
        self.gameId = gameId
    }
    
    func getData() -> Animation? {
        return data
    }
    
    func setData(_ data: Animation) {
        self.data = data
    }
    
    func onTap(_ view: UIView) {
        // Empty implementation for subclasses to override
    }
    
    func setProgress(index: Int, count: Int) {
        let progress = Float(index) / Float(count)
        let clampedProgress = max(0, min(1, progress))
        onGameFragmentListener?.onUpdateGameProgress(clampedProgress)
    }
    
    func parseIntroText() -> [String]? {
        guard let text = data?.text else { return nil }
        return text.split(separator: "#").map { String($0).lowercased() }
    }
    
    func parseIntro2Text() -> [String]? {
        guard let text2 = data?.text2 else { return nil }
        return text2.split(separator: "#").map { String($0).lowercased() }
    }
    
    func setGameProgress(_ progress: Double) {
        /*
        guard let view = view,
              let progressView = view.viewWithStringTag("progress_bar"),
              let imageProgress = progressView.viewWithStringTag("image_progress") as? UIView else { return }
        
        let layoutParams = imageProgress.constraints.first { $0.identifier == "weight" } // Giả định constraint cho weight
        let left = progress
        let right = max(0.001, 1 - progress)
        let weight = left / right
        
        UIView.animate(withDuration: 1) {
            if let constraint = layoutParams {
                constraint.constant = CGFloat(weight)
                imageProgress.superview?.layoutIfNeeded()
            }
        }*/
    }
    
    func onHintClick() {
        // Empty implementation for subclasses to override
    }
    
    func getScore() -> Float {
        return 1
    }
    
    func getSkills() -> [GameSkill] {
        return []
    }
    func playSound(_ name: String, delay: TimeInterval) -> TimeInterval {
        return playSound(name: name, delay: delay)
    }
    override func playSound(name: String, delay: TimeInterval) -> TimeInterval {
        if name.hasPrefix("en/") || name.hasPrefix("vi/") || name.hasPrefix("effect/") {
            return super.playSound(name: name, delay: delay)
        }
        if tapdoc {
            if name.contains("/topics/") || name.contains("vi/ngonngu/tieng viet/") || name.hasPrefix("effects/") {
                return super.playSound(name: name, delay: delay)
            }
            let path = "vi/ngonngu/tieng viet/\(name)"
            let path2 = "en/english phonics/\(name)"
            if soundDuration(path) > 0 {
                return super.playSound(name: path, delay: delay)
            } else if soundDuration(path2) > 0 {
                return super.playSound(name: path2, delay: delay)
            } else if soundDuration(name) > 0 {
                return super.playSound(name: name, delay: delay)
            }
        }
        let finalName = name.contains("en/english phonics/") ? name : "en/english phonics/\(name)"
        return super.playSound(name: finalName, delay: delay)
    }
    
    override func playSound(_ names: String...) -> TimeInterval {
        return playSound(delay: 0, names: names)
    }
    func playSound(delay: TimeInterval, names: String...) -> TimeInterval {
        return playSound(delay: delay, names: names)
    }
    override func playSound(delay: TimeInterval, names: [String]) -> TimeInterval {
        var d = delay
        for name in names where !name.isEmpty {
            d += playSound(name: name, delay: d)
        }
        return d - delay
    }
    
    override func createGame() {
        super.createGame()
        if enableStartGameSound() {
            //playSound(name: "effects/start game", delay: 0.1)
        }
    }
    
    func showHintButton() -> Bool {
        return true
    }
    
    func soundDuration(_ path: String) -> TimeInterval {
        // Giả định hàm này trả về thời lượng âm thanh
        // Cần triển khai thực tế dựa trên logic của bạn
        return 1.0 // Placeholder
    }
}

// MARK: - Protocols and Enums
protocol GameFragmentListener: AnyObject {
    func onUpdateGameProgress(_ progress: Float)
}

protocol ViewTapListener: AnyObject {
    func onTap(_ view: UIView)
}
