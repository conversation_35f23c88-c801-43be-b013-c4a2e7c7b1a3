<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/item_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/math_xepdiem_bg"
        android:clipChildren="false"
        android:clipToPadding="false"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="574.8:1067.4"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/item_left_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.49"
            app:layout_constraintHorizontal_bias="0.025"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.03" />

        <ImageView
            android:id="@+id/item_right_1"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.49"
            app:layout_constraintHorizontal_bias="0.99"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.03" />

        <ImageView
            android:id="@+id/item_left_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.49"
            app:layout_constraintHorizontal_bias="0.025"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.93" />

        <ImageView
            android:id="@+id/item_right_2"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/math_xepdiem_que"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="63.5:516.9"
            app:layout_constraintHeight_percent="0.49"
            app:layout_constraintHorizontal_bias="0.99"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.93" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_top"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="516.9:63.5"
            app:layout_constraintHorizontal_bias="0.99"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.01"
            app:layout_constraintWidth_percent="0.94">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="63.5:516.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.12" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_center"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="516.9:63.5"
            app:layout_constraintHorizontal_bias="0.99"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.505"
            app:layout_constraintWidth_percent="0.94">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="63.5:516.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.12" />
        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/item_bottom"
            android:layout_width="0dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="516.9:63.5"
            app:layout_constraintHorizontal_bias="0.99"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.995"
            app:layout_constraintWidth_percent="0.94">

            <ImageView
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:rotation="90"
                android:src="@drawable/math_xepdiem_que"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="63.5:516.9"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintWidth_percent="0.12" />
        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.constraintlayout.widget.ConstraintLayout>


</androidx.constraintlayout.widget.ConstraintLayout>