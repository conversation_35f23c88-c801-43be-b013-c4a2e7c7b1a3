//
//  ngonngu_list_chonchu.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 28/4/25.
//


import UIKit
import SnapKit
import SVGKit
import AVFAudio

class ngonngu_list_chonchu: NhanBietGameFragment {
    // MARK: - Properties
    private let characterGroups: [[String]] = [
        ["a", "ă", "â"], ["b", "d", "đ"], ["o", "ô", "ơ"],
        ["d", "đ"], ["g", "gh"], ["k", "kh"], ["c", "k"],
        ["n", "m"], ["u", "ư"], ["n", "nh"], ["qu", "gi"],
        ["ph", "nh"], ["t", "th"], ["r", "s"], ["ch", "tr"],
        ["r", "d", "gi"], ["l", "n"], ["s", "x"], ["ng", "ngh"],
        ["e", "ê"]
    ]
    private var group: [String] = []
    private var meIndex: Int = 0
    private var item: Item?
    private var folder: String = ""
    private var svgView: SVGImageView!
    private var gridLayout: MyGridView!
    private var textName: AutosizeLabel!
    private var letter: String = ""
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor.white
        view.snp.remakeConstraints { make in
            make.edges.equalTo(self)
        }
        
        let itemContainer = UIView()
        itemContainer.clipsToBounds = false
        view.addSubview(itemContainer)
        itemContainer.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.6)
        }
        
        svgView = SVGImageView(frame: .zero)
        svgView.contentMode = .scaleAspectFit
        svgView.accessibilityIdentifier = "svg_view"
        itemContainer.addSubview(svgView)
        svgView.snp.makeConstraints { make in
            make.width.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.7)
            make.top.equalToSuperview()
        }
        
        let textContainer = UIView()
        textContainer.backgroundColor = UIColor.color(hex: "#E9FDFF")
        itemContainer.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.3)
            make.bottom.left.right.equalToSuperview()
        }
        
        textName = AutosizeLabel()
        textName.textAlignment = .center
        textName.textColor = UIColor.color(hex: "#74B6FF")
        textName.font = .Freude(size: 20)
        textContainer.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.9)
            make.height.equalToSuperview().multipliedBy(0.9)
            make.center.equalToSuperview()
        }
        
        gridLayout = MyGridView()
        gridLayout.columns = 2
        gridLayout.itemRatio = 1
        gridLayout.itemSpacingRatio = 0.05
        gridLayout.insetRatio = 0.05
        gridLayout.backgroundColor = UIColor.color(hex: "#D6FAFF")
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.4)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview()
        }
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        /*
        var input = "hà nội"
        let nfdString = input.decomposedStringWithCanonicalMapping
        print("NFD string: \(nfdString)")
        print("NFD Unicode scalars: \(nfdString.unicodeScalars.map { String(format: "U+%04X", $0.value) })")
        
        // Ví dụ sử dụng
        input = "hà nội"
        var result = replaceBaseCharacter(in: input, from: "a", to: "…")
        result = replaceBaseCharacter(in: "á", from: "a", to: "…")
        result = replaceBaseCharacter(in: "à", from: "a", to: "…")
        result = replaceBaseCharacter(in: "ả", from: "a", to: "…")
        result = replaceBaseCharacter(in: "ã", from: "a", to: "…")
        result = replaceBaseCharacter(in: "ạ", from: "a", to: "…")
        print(result) // Kết quả: "hè nội"
        // result    String    "…́"    result    String    "…̀"    result    String    "…̉" result    String    "…̃"    result    String    "…̣"
        */
        var items: [Item] = []
        var folders: [String] = []
        let packs = FlashcardsManager.shared.getPacks()
        for pack in packs {
            for item in pack.items {
                if let letterVi = item.letterVi, !letterVi.isEmpty {
                    var found = false
                    for group in characterGroups {
                        if haveSameItem(group: group, with: letterVi) {
                            found = true
                            break
                        }
                    }
                    if found {
                        items.append(item)
                        folders.append(pack.folder)
                    } else {
                        print("Not found: \(item.name.vi) letter: \(letterVi)")
                    }
                }
            }
        }
        while(true){
            meIndex = Int.random(in: 0..<items.count)
            item = items[meIndex]
            #if DEBUG
            if item!.name.vi! == "bút mực" {
                break
            }
            //continue
            #endif
            break
        }
        folder = folders[meIndex]
        svgView.image = Utilities.GetSVGKImage(named: "topics/\(folder)/\(item!.path!)").uiImage
        
        let shuffledGroups = characterGroups.shuffled()
        for g in shuffledGroups {
            if haveSameItem(group: g, with: item!.letterVi!) {
                #if DEBUG
                if !g.contains("ư") {
                    //continue
                }
                #endif
                group = g
                break
            }
        }
        
        let letters = group.shuffled()
        for l in letters {
            if item!.letterVi!.contains(l) {
                letter = l
                meIndex = group.firstIndex(of: l)!
                break
            }
        }
        
        let text = item!.name.vi!
        let nosign = vietnamese.removeSign(text)
        let index = nosign.firstIndex(of: letter.first!)?.utf16Offset(in: nosign) ?? 0
        
        if text.contains(letter) || letter.count > 1 {
            let startIndex = text.index(text.startIndex, offsetBy: index)
            let endIndex = text.index(startIndex, offsetBy: letter.count)
            let displayText = text[..<startIndex] + "…" + text[endIndex...]
            let attributedText = NSMutableAttributedString(string: String(displayText))
            let nsDisplayText = NSString(string: String(displayText))
            let highlightRange = nsDisplayText.range(of: "…")
            attributedText.addAttribute(.foregroundColor, value: UIColor.color(hex: "#B7E6EA"), range: highlightRange)
            textName.attributedText = attributedText
        } else {
            let text = item!.name.vi!
            let nosign = vietnamese.removeSign(text)
            let index = nosign.firstIndex(of: letter.first!)?.utf16Offset(in: nosign) ?? 0
            let startIndex = text.index(text.startIndex, offsetBy: index)
            let endIndex = text.index(startIndex, offsetBy: 1)
            let spanText = getTextWithHiddenChar(text: String(text[startIndex..<endIndex]), baseCharToHide: letter.first!)
            
            let attributedText = NSMutableAttributedString(string: String(text[..<startIndex]))
            attributedText.append(spanText)
            attributedText.append(NSAttributedString(string: String(text[endIndex...])))
            let highlightRange = NSRange(location: index, length: 1)
            attributedText.addAttribute(.foregroundColor, value: UIColor.color(hex: "#B7E6EA"), range: highlightRange)
            textName.attributedText = attributedText
        }
        
        buildGrid()
        
        let delay = playSound(delay: 0, names: [
            openGameSound(),
            "ngonngu/ngonngu_chon chu"
        ])
        scheduler.schedule(after: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame(stopMusic: false)
            let delay = playSound("ngonngu/ngonngu_chon chu")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
            }
        }
    }
    
    // MARK: - Helper Methods
    private func haveSameItem(group: [String], with list: [String]) -> Bool {
        return !Set(group).isDisjoint(with: list)
    }
       
    private func buildGrid() {
        var views: [UIView] = []
        for i in 0..<group.count {
            let view = UIView()
            view.clipsToBounds = false
            view.isUserInteractionEnabled = true
            
            let viewBackground = UIImageView()
            viewBackground.image = Utilities.SVGImage(named: "option_bg_white_shadow")
            view.addSubview(viewBackground)
            viewBackground.snp.makeConstraints { make in
                make.edges.equalToSuperview()
                make.height.equalTo(viewBackground.snp.width).multipliedBy(373.0 / 346.0) // Ratio 346:373
            }
            
            let textNumber = HeightRatioTextView()
            textNumber.setHeightRatio(0.8)
            textNumber.textAlignment = .center
            textNumber.textColor = UIColor.color(hex: "#74B6FF")
            textNumber.font = .Freude(size: 24)
            textNumber.text = group[i]
            viewBackground.addSubview(textNumber)
            textNumber.snp.makeConstraints { make in
                make.width.equalToSuperview().multipliedBy(0.8)
                make.height.equalToSuperview().multipliedBy(0.7)
                make.centerX.equalToSuperview()
                make.centerY.equalToSuperview().offset(-viewBackground.frame.height * 0.05) // Vertical bias 0.45
            }
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.stringTag = "\(i)"
            
            views.append(view)
        }
        
        gridLayout.reloadItemViews(views: views)
    }
    
    // MARK: - Touch Handling
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view,
              let index = Int(view.stringTag ?? "0"),
              index < group.count else { return }
        
        pauseGame(stopMusic: false)
        let correct = index == meIndex
        var delay: TimeInterval = 0
        
        if correct {
            delay += playSound(delay: delay, names: [
                "\(getLanguage())/topics/\(folder)/\(item!.path!.replacingOccurrences(of: ".svg", with: ""))",
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                endGameSound()
            ])
            animateCoinIfCorrect(view: svgView)
            textName.text = item!.name.vi
            textName.textColor = UIColor.color(hex: "#87D657")
            scheduler.schedule(after: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            delay += playSound(delay: delay, names: [
                answerWrongEffectSound(),
                getWrongHumanSound()
            ])
            setGameWrong()
            textName.textColor = UIColor.color(hex: "#FF7760")
            scheduler.schedule(after: delay) { [weak self] in
                self?.resumeGame(startMusic: false)
                self?.textName.textColor = UIColor.color(hex: "#74B6FF")
            }
        }
    }
}

func getTextWithHiddenChar(text: String, baseCharToHide: Character) -> NSAttributedString {
    let sign = vietnamese.sign(text)
    //// result    String    "…́"    result    String    "…̀"    result    String    "…̉" result    String    "…̃"    result    String    "…̣"
    
    
    let letter = "…"
    let displayText = sign == "á" ? "…́" : sign == "à" ? "…̀" : sign == "ả" ? "…̉" : sign == "ã" ? "…̃" : sign == "ạ" ? "…̣" : "…"
    
    let attributedText = NSMutableAttributedString(string: displayText)
    let nsDisplayText = NSString(string: displayText)
    let highlightRange = nsDisplayText.range(of: letter)
    attributedText.addAttribute(.foregroundColor, value: UIColor.color(hex: "#30A0D8"), range: highlightRange)
    
    return attributedText
}
/*
import Foundation
import UIKit

class LanguageUtils {
    /// Chuyển đổi chuỗi từ dạng dựng sẵn (precomposed Unicode) sang dạng tổ hợp (decomposed Unicode, NFD).
    /// - Parameter input: Chuỗi đầu vào dạng dựng sẵn.
    /// - Returns: Chuỗi dạng tổ hợp (NFD).
    static func convertToDecomposedUnicode(input: String?) -> String? {
        guard let input = input else { return nil }
        return input.decomposedStringWithCanonicalMapping
    }
    
    /// Sắp xếp lại các ký tự tổ hợp: ký tự cơ sở đầu tiên, dấu phụ tiếp theo, dấu thanh sau cùng.
    /// - Parameter input: Chuỗi Unicode tổ hợp.
    /// - Returns: Chuỗi được sắp xếp lại.
    static func reorderUnicodeMarks(input: String?) -> String? {
        guard let input = input, !input.isEmpty else { return input }
        
        let normalized = input.decomposedStringWithCanonicalMapping
        var baseChar = ""
        var combiningMarks = ""
        var toneMarks = ""
        
        for char in normalized {
            if !isCombiningMark(char: char) {
                baseChar.append(char)
            } else if isToneMark(char: char) {
                toneMarks.append(char)
            } else {
                combiningMarks.append(char)
            }
        }
        
        return baseChar + combiningMarks + toneMarks
    }
    
    /// Kiểm tra nếu ký tự là dấu tổ hợp (thuộc khối COMBINING_DIACRITICAL_MARKS).
    /// - Parameter char: Ký tự cần kiểm tra.
    /// - Returns: `true` nếu là dấu tổ hợp.
    private static func isCombiningMark(char: Character) -> Bool {
        guard let scalar = UnicodeScalar(String(char)) else { return false }
        let codePoint = scalar.value
        return codePoint >= 0x0300 && codePoint <= 0x036F // Phạm vi COMBINING_DIACRITICAL_MARKS
    }
    
    /// Kiểm tra nếu ký tự là dấu thanh (sắc, huyền, hỏi, ngã, nặng).
    /// - Parameter char: Ký tự cần kiểm tra.
    /// - Returns: `true` nếu là dấu thanh.
    private static func isToneMark(char: Character) -> Bool {
        let unicode = UnicodeScalar(String(char))?.value
        return unicode == 0x0301 || // Sắc (´)
               unicode == 0x0300 || // Huyền (`)
               unicode == 0x0309 || // Hỏi (̉)
               unicode == 0x0303 || // Ngã (̃)
               unicode == 0x0323    // Nặng (̣)
    }
    
    /// Tạo văn bản với ký tự ẩn, thay thế ký tự cơ sở bằng "…" và tô màu.
    /// - Parameters:
    ///   - text: Văn bản đầu vào.
    ///   - baseCharToHide: Ký tự cơ sở cần ẩn.
    /// - Returns: `NSAttributedString` với ký tự ẩn và tô màu.
    static func getTextWithHiddenChar(text: String, baseCharToHide: Character) -> NSAttributedString {
        let sign = vietnamese.sign(text)
        //// result    String    "…́"    result    String    "…̀"    result    String    "…̉" result    String    "…̃"    result    String    "…̣"
        
        
        let letter = "…"
        let displayText = sign == "á" ? "…́" : sign == "à" ? "…̀" : sign == "ả" ? "…̉" : sign == "ã" ? "…̃" : sign == "ạ" ? "…̣" : "…"
        
        let attributedText = NSMutableAttributedString(string: displayText)
        let nsDisplayText = NSString(string: displayText)
        let highlightRange = nsDisplayText.range(of: letter)
        attributedText.addAttribute(.foregroundColor, value: UIColor.color(hex: "#30A0D8"), range: highlightRange)
        
        return attributedText
    }
}

import Foundation

// Bảng ánh xạ các ký tự tiếng Việt về ký tự chính
let vietnameseAMap: [Character: Character] = [
    "a": "a", "à": "a", "á": "a", "ả": "a", "ã": "a", "ạ": "a",
    "ă": "a", "ằ": "a", "ắ": "a", "ẳ": "a", "ẵ": "a", "ặ": "a",
    "â": "a", "ầ": "a", "ấ": "a", "ẩ": "a", "ẫ": "a", "ậ": "a"
]

import Foundation

import Foundation

// Hàm thay thế ký tự chính trong chuỗi Unicode tổ hợp
func replaceBaseCharacter(in input: String, from oldBase: Character, to newBase: Character) -> String {
    // Phân tách chuỗi thành dạng NFD
    let nfdString = input.decomposedStringWithCanonicalMapping
    print("NFD string: \(nfdString)")
    print("NFD Unicode scalars: \(nfdString.unicodeScalars.map { String(format: "U+%04X", $0.value) })")
    
    // Lấy danh sách Unicode scalars
    let scalars = nfdString.unicodeScalars
    var resultScalars: [UnicodeScalar] = []
    
    // Duyệt qua các scalar
    var i = scalars.startIndex
    while i < scalars.endIndex {
        let currentScalar = scalars[i]
        print("Current scalar: \(String(format: "U+%04X", currentScalar.value))")
        
        // Kiểm tra nếu scalar hiện tại là ký tự chính cần thay
        if currentScalar == oldBase.unicodeScalars.first {
            print("Replacing \(oldBase) (U+\(String(format: "%04X", currentScalar.value))) with \(newBase)")
            // Thêm ký tự mới
            resultScalars.append(newBase.unicodeScalars.first!)
            
            // Kiểm tra các scalar tiếp theo để sao chép dấu (nếu có)
            var nextIndex = scalars.index(after: i)
            while nextIndex < scalars.endIndex {
                let nextScalar = scalars[nextIndex]
                print("Checking next scalar: \(String(format: "U+%04X", nextScalar.value))")
                
                // Kiểm tra nếu scalar tiếp theo là combining mark (U+0300 đến U+036F)
                let isCombiningMark = (0x0300...0x036F).contains(nextScalar.value)
                print("  - Scalar \(String(format: "U+%04X", nextScalar.value)): isCombiningMark=\(isCombiningMark)")
                
                if isCombiningMark {
                    print("Found combining mark: \(String(format: "U+%04X", nextScalar.value))")
                    resultScalars.append(nextScalar)
                    nextIndex = scalars.index(after: nextIndex)
                } else {
                    print("Not a combining mark, stopping")
                    break
                }
            }
            i = nextIndex
        } else {
            // Nếu không phải ký tự cần thay, giữ nguyên
            resultScalars.append(currentScalar)
            i = scalars.index(after: i)
        }
    }
    
    // Tạo chuỗi từ các scalar và chuẩn hóa về NFC
    let result = String(String.UnicodeScalarView(resultScalars))
    let finalResult = result.precomposedStringWithCanonicalMapping
    print("Final result: \(finalResult)")
    return finalResult
}


*/
