//
//  IdentifyMainViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 25/3/25.
//


import UIKit
import SVGKit // <PERSON><PERSON> hiển thị hình ảnh SVG
import SnapKit

class EnglishVocabMainViewController: BaseSubjectViewController {
    // MARK: - Properties
    private var folders: [Folder] = []
    private var stickers: [String] = []
    private var selectedIndex: Int = -1
    private var column: Int = 0
    override var backgroundMusicFile: String? {
        "bg_list10"
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle("Vocabualary")
        setupView()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        if selectedIndex != -1 {
            stickers = GalleryManager.shared.getStickers()
                .filter { $0.vocab == nil || !$0.vocab! }
                .map { "\($0.folder)/\($0.sticker)" }
            collectionView.reloadItems(at: [IndexPath(item: selectedIndex, section: 0)])
        }
    }
    
    override var prefersStatusBarHidden: Bool {
        return true // Ẩn status bar, tương tự hideStatusBar() trong Java
    }
    
    // MARK: - Setup
    private func setupView() {
        // Lấy danh sách folders
        FlashcardsManager.shared.reload(languageCode: "en")
        folders = FlashcardsManager.shared.getPacks()
            .filter { $0.recognize != nil && $0.recognize! }
        
        // Lấy danh sách stickers
        stickers = GalleryManager.shared.getStickers()
            .filter { $0.vocab == nil || !$0.vocab! }
            .map { "\($0.folder)/\($0.sticker)" }
        
        
        // Thiết lập CollectionView
        let layout = UICollectionViewFlowLayout()
        layout.minimumInteritemSpacing = 10
        layout.minimumLineSpacing = 10
        layout.sectionInset = UIEdgeInsets(top: 10, left: 10, bottom: 10, right: 10)
                
        collectionView.translatesAutoresizingMaskIntoConstraints = false
        collectionView.backgroundColor = .clear
        collectionView.dataSource = self
        collectionView.delegate = self
                
        // Đăng ký cell
        collectionView.register(TopicVocabCell.self, forCellWithReuseIdentifier: "TopicVocabCell")
    }
    
    // MARK: - Actions
    @objc private func closeTapped() {
        dismiss(animated: true, completion: nil)
    }
    
    // MARK: - Helper Methods
    private func getSpanCount() -> Int {
        let width = view.bounds.width
        let oneDp = 1.0 // Trong iOS, 1 point tương đương 1 dp
        var spanCount = Int(width / oneDp / 360)
        if spanCount < 4 {
            spanCount = 4
        }
        column = spanCount
        return spanCount
    }
}

// MARK: - UICollectionViewDataSource
extension EnglishVocabMainViewController: UICollectionViewDataSource, UICollectionViewDelegateFlowLayout {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return folders.count
    }
    
    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TopicVocabCell", for: indexPath) as! TopicVocabCell
        let folder = folders[indexPath.item]
        cell.configure(with: folder, stickers: stickers)
        return cell
    }
    
    func collectionView(_ collectionView: UICollectionView, layout collectionViewLayout: UICollectionViewLayout, sizeForItemAt indexPath: IndexPath) -> CGSize {
        let spanCount = getSpanCount()
        let totalSpacing = CGFloat(spanCount - 1) * 10 + 20 // 10 là minimumInteritemSpacing, 20 là padding trái + phải từ sectionInset
        var width = (collectionView.bounds.width - totalSpacing) / CGFloat(spanCount)
        width = (collectionView.frame.width - 20) / 3 // 3 items per row
        return CGSize(width: width, height: width * 0.76)
    }
}

// MARK: - UICollectionViewDelegate
extension EnglishVocabMainViewController {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let folder = folders[indexPath.item]
        let freetoplay = folder.freetoplay ?? false
        let inTrialTime = installedTimeInDays() < 2
        
        runPremiumFeatures({
            self.selectedIndex = indexPath.item
            /*
            let identifyVC = IdentifyViewController()
            identifyVC.pack = folder
            self.present(identifyVC, animated: true, completion: nil)
             */
        }, freetoplay && inTrialTime)
    }
}

// MARK: - TopicVocabCell
class TopicVocabCell: UICollectionViewCell {
    private let backgroundImageView: SVGImageView
    private let thumbImageView: SVGImageView
    private let nameLabel: HeightRatioTextView
    private let progressLabel: HeightRatioTextView
    
    override init(frame: CGRect) {
        backgroundImageView = SVGImageView(frame: .zero)
        thumbImageView = SVGImageView(frame: .zero)
        nameLabel = HeightRatioTextView()
        progressLabel = HeightRatioTextView()
        
        super.init(frame: frame)
        setupViews()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupViews() {
        // Background image
        backgroundImageView.SVGName = "nhanbiet_bg_list_topic"
        contentView.addSubview(backgroundImageView)
        backgroundImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        // Thumb image
        thumbImageView.contentMode = .scaleAspectFit
        contentView.addSubview(thumbImageView)
        thumbImageView.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.5)
            make.height.equalToSuperview().multipliedBy(0.6)
            make.right.equalToSuperview().multipliedBy(0.9)
            make.bottom.equalToSuperview().multipliedBy(0.9)
        }
        
        // Name label
        nameLabel.textAlignment = .left
        nameLabel.numberOfLines = 2
        nameLabel.textColor = .white
        nameLabel.font = .Freude(size: 20)
        nameLabel.setHeightRatio(0.4)
        contentView.addSubview(nameLabel)
        nameLabel.snp.makeConstraints { make in
            make.width.equalToSuperview().multipliedBy(0.6)
            make.height.equalToSuperview().multipliedBy(0.3)
            make.right.equalToSuperview().multipliedBy(0.7)
            make.bottom.equalToSuperview().multipliedBy(0.5)
        }
        
        // Progress label
        progressLabel.textAlignment = .center
        progressLabel.font = .Freude(size: 15)
        progressLabel.textColor = .red
        progressLabel.setHeightRatio(0.5)
        contentView.addSubview(progressLabel)
        progressLabel.snp.makeConstraints { make in
            make.height.equalToSuperview().multipliedBy(0.2)
            make.width.equalToSuperview().multipliedBy(0.25)
            make.right.equalToSuperview().multipliedBy(0.95)
            make.bottom.equalToSuperview().multipliedBy(0.22)
        }
    }
    
    func configure(with folder: Folder, stickers: [String]) {
        // Tên chủ đề
        nameLabel.text = folder.name.en
        
        // Hình ảnh
        if let thumb = folder.thumb {
            let imagePath = "topics/\(folder.folder)/\(thumb)"
            thumbImageView.SVGName = imagePath
        } else {
            thumbImageView.image = nil
        }
        
        // Tiến độ
        var unlocked = 0
        for item in folder.items {
            if let itemName = item.name.en, stickers.contains("\(folder.folder)/\(itemName)") {
                unlocked += 1
            }
        }
        progressLabel.text = "\(unlocked)/\(folder.items.count)"
        
        // Kiểm tra khả năng chơi
        let freetoplay = folder.freetoplay ?? false
        let inTrialTime = (ActivityTracker.getCurrentViewController() as! BaseViewController).installedTimeInDays() < 2
        let playable = (freetoplay && inTrialTime) || PaymentHelper.isFullVersion()
        contentView.alpha = playable ? 1.0 : 0.5
    }
    
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        return false
    }
    
    private func getGameByThumb(_ thumbnail: String) -> String {
        return thumbnail
    }
}
