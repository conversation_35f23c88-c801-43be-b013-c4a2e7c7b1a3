<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_background"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginRight="20dp"
        android:layout_marginBottom="20dp"
        android:background="@drawable/btn_flashcards_folder"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="460:370"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/tv_folder_name"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="Bảng chữ cái tiếp theo"
            android:textColor="#E5630A"
            app:heightRatio="0.25"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.65"
            app:layout_constraintWidth_percent="0.8" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/tv_plus"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="+"
            android:textColor="#E5630A"
            android:visibility="visible"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintHeight_percent="0.8"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.6"
            app:layout_constraintWidth_percent="0.8" />

        <ImageView
            android:id="@+id/iv_check"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:src="@drawable/btn_flashcards_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.2"
            app:layout_constraintHorizontal_bias="0.93"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0.08" />

        <com.kidsup.giaoducsom.view.HeightRatioTextView
            android:id="@+id/tv_check_number"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/btn_flashcards_circle_orange"
            android:fontFamily="@font/svn_freude"
            android:gravity="center"
            android:text="12"
            android:textColor="#fff"
            android:visibility="invisible"
            app:heightRatio="0.6"
            app:layout_constraintBottom_toBottomOf="@id/iv_check"
            app:layout_constraintLeft_toLeftOf="@id/iv_check"
            app:layout_constraintRight_toRightOf="@id/iv_check"
            app:layout_constraintTop_toTopOf="@id/iv_check" />
    </androidx.constraintlayout.widget.ConstraintLayout>
</androidx.constraintlayout.widget.ConstraintLayout>