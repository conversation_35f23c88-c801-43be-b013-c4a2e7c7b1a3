//
//  toancoban_list_demchan.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 4/4/25.
//


import UIKit
import SnapKit

class toancoban_list_demchan: NhanBietGameFragment {
    // MARK: - Properties
    private var backgroundImage: UIImageView!
    private var itemDogContainer: UIView!
    private var itemChickenContainer: UIView!
    private var count: Int = 0
    private var gridLayout: MyGridView!
    private var isChicken: Bool = false
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        view.backgroundColor = UIColor(red: 233/255, green: 253/255, blue: 255/255, alpha: 1) // #E9FDFF
        
        backgroundImage = UIImageView()
        backgroundImage.contentMode = .scaleAspectFill
        view.addSubview(backgroundImage)
        backgroundImage.snp.makeConstraints { make in
            make.edges.equalTo(self)
        }
        
        gridLayout = MyGridView()
        //ridLayout.backgroundColor = UIColor(red: 214/255, green: 250/255, blue: 255/255, alpha: 1) // #D6FAFF
        view.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.4)
        }
        
        itemDogContainer = UIView()
        itemDogContainer.isHidden = true
        view.addSubview(itemDogContainer)
        itemDogContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        let dogIds = ["image_1", "image_2", "image_3", "image_4"]
        let dogImages = ["math_choganxa_dog1", "math_choganxa_dog2", "math_choganxa_dog3", "math_choganxa_dog4"]
        let dogBiases = [0.1, 0.3, 0.6, 0.9]
        let dogVerticalBiases = [0.8, 0.9, 0.8, 0.9]
        for i in 0..<4 {
            let dog = UIImageView(image: Utilities.SVGImage(named: dogImages[i]))
            dog.contentMode = .scaleAspectFit
            dog.isHidden = true
            dog.stringTag = dogIds[i]
            itemDogContainer.addSubview(dog)
            
            dog.snp.makeConstraints { make in
                make.height.equalTo(itemDogContainer).multipliedBy(0.25)
                make.width.equalTo(dog.snp.height).multipliedBy(465.0 / 302.0) // Ratio 465:302
                //make.centerX.equalToSuperview().multipliedBy(0.2 + CGFloat(dogBiases[i]) * 1.8) // Bias from 0.1 to 0.9
                //make.centerY.equalToSuperview().multipliedBy(0.2 + CGFloat(dogVerticalBiases[i]) * 1.6) // Bias from 0.8 to 0.9
            }
            dog.waitForLayout {
                dog.snapToVerticalBias(verticalBias: dogVerticalBiases[i])
                dog.snapToHorizontalBias(horizontalBias: dogBiases[i])
            }
        }
        
        itemChickenContainer = UIView()
        itemChickenContainer.isHidden = true
        view.addSubview(itemChickenContainer)
        itemChickenContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
        
        let chickenIds = ["chick_1", "chick_2", "chick_3", "chick_4", "chick_5", "image_hen"]
        let chickenImages = ["math_demchan_chick", "math_demchan_chick", "math_demchan_chick", "math_demchan_chick", "math_demchan_chick", "math_demchan_hen"]
        let chickenBiases = [0.0, 0.15, 0.32, 0.5, 0.65, 1.0]
        let chickenVerticalBiases = [0.9, 0.78, 0.92, 0.8, 0.9, 0.9]
        let chickenRatios = [1.2, 465.0 / 302.0, 1.2, 465.0 / 302.0, 465.0 / 302.0, 1.0]
        let chickenHeights = [0.15, 0.15, 0.15, 0.15, 0.15, 0.4]
        for i in 0..<6 {
            let chicken = UIImageView(image: Utilities.SVGImage(named: chickenImages[i]))
            chicken.contentMode = .scaleAspectFit
            chicken.isHidden = true
            chicken.stringTag = chickenIds[i]
            itemChickenContainer.addSubview(chicken)
            
            chicken.snp.makeConstraints { make in
                make.height.equalTo(itemChickenContainer).multipliedBy(chickenHeights[i])
                make.width.equalTo(chicken.snp.height).multipliedBy(chickenRatios[i])
                //make.centerX.equalToSuperview().multipliedBy(0.2 + CGFloat(chickenBiases[i]) * 1.8) // Bias from 0.0 to 1.0
                //make.centerY.equalToSuperview().multipliedBy(0.2 + CGFloat(chickenVerticalBiases[i]) * 1.4) // Bias from 0.78 to 0.92
            }
            chicken.waitForLayout {
                chicken.snapToVerticalBias(verticalBias: chickenVerticalBiases[i])
                chicken.snapToHorizontalBias(horizontalBias: chickenBiases[i])
            }
        }
        gridLayout.layer.zPosition = 10
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        isChicken = Bool.random()
        backgroundImage.image = Utilities.SVGImage(named: isChicken ? "math_5canhhoa_bg" : "nhanbiet_bg_garden")
        itemDogContainer.isHidden = isChicken
        itemChickenContainer.isHidden = !isChicken
        
        if isChicken {
            let chickenCount = random(3, 4, 5)
            count = chickenCount * 2 + 2 // 2 legs per chicken + 2 for hen
            let show = Utils.generatePermutation(5).shuffled().prefix(chickenCount)
            let ids = ["chick_1", "chick_2", "chick_3", "chick_4", "chick_5"]
            for i in 0..<5 {
                if let view = itemChickenContainer.viewWithStringTag(ids[i]) as? UIImageView {
                    view.isHidden = !show.contains(i)
                }
            }
            if let hen = itemChickenContainer.viewWithStringTag("image_hen") as? UIImageView {
                hen.isHidden = false
            }
        } else {
            let dogCount = random(2, 3, 4)
            count = dogCount * 4 // 4 legs per dog
            let show = Utils.generatePermutation(4).shuffled().prefix(dogCount)
            let ids = ["image_1", "image_2", "image_3", "image_4"]
            for i in 0..<4 {
                if let view = itemDogContainer.viewWithStringTag(ids[i]) as? UIImageView {
                    view.isHidden = !show.contains(i)
                }
            }
        }
        
        buildGrid(grid: gridLayout, count: count)
    }
    
    override func createGame() {
        super.createGame()
        var delay = playSound(openGameSound(), "toan/toan_dem chan")
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.5, delay: delay) {
            self.gridLayout.alpha = 1
        }
        delay += 0.5
        delay += gridLayout.showItems(startDelay: delay)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem chan")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Build Grid
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let view = createNumberItem(value: value)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 346.0 / 373.0
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views)
    }
    
    private func createNumberItem(value: Int) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.makeViewCenterAndKeep(ratio: 346.0 / 373.0)
        let textNumber = AutosizeLabel()
        textNumber.text = String(value)
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let correct = value == count
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            delay += playSound("topics/Numbers/\(value)")
            delay += playSound(delay: delay, names: finishCorrect1Sounds())
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound("topics/Numbers/\(value)")
            delay += playSound(delay: delay, names: [answerWrongEffectSound(), getWrongHumanSound()])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}


