//
//  toancoban_list_demhop.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/6/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_demhop: BaseBoxGameFragment {
    // MARK: - Properties
    private var count: Int = 0
    private var gridLayout: MyGridView!
    private var blockSampleView: BlockView!
    private var itemContainer: UIView!
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        super.configureLayout(view)
        
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        itemContainer = UIView()
        view.addSubview(itemContainer)
        
        let rightBg = UIView()
        rightBg.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(rightBg)
        
        gridLayout = MyGridView()
        view.addSubview(gridLayout)
        rightBg.snp.makeConstraints { make in
            make.left.equalTo(gridLayout)
            make.top.right.bottom.equalTo(self)
        }
        
        blockSampleView = BlockView(frame: .zero)
        itemContainer.addSubview(blockSampleView)
        
        itemContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.right.equalTo(gridLayout.snp.left)
        }
       
        blockSampleView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }        
        
        gridLayout.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.4)
        }
    }
    
    // MARK: - Game Logic
    override func createGame() {
        super.createGame()
        
        var d: [[Int]] = []
        while true {
            d = createArray(rows: 3, cols: 3)
            let sum = sumArray(d)
            if sum > 5 && sum < 15 && checkValidation(d) {
                break
            }
        }
        
        let fullData = BlockData(currentData: d, originData: d)
        blockSampleView.setData(fullData)
        count = fullData.originData.reduce(0) { $0 + $1.reduce(0, +) }
        buildGrid(grid: gridLayout, count: count)
        
        var delay = playSound(openGameSound(), "toan/toan_dem hop")
        itemContainer.transform = .identity
        gridLayout.alpha = 0
        
        UIView.animate(withDuration: 0.6, delay: (delay), animations: {
            self.itemContainer.transform = .identity
        })
        delay += 0.5
        
        UIView.animate(withDuration: 0.5, delay: (delay), animations: {
            self.gridLayout.alpha = 1
        })
        delay += 0.5
        
        delay += gridLayout.showItems(startDelay: (delay))
        scheduler.schedule(delay: TimeInterval(delay)) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_dem hop")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    
    private func buildGrid(grid: MyGridView, count: Int) {
        var views: [UIView] = []
        let start = max(1, count - Int.random(in: 0..<4))
        for i in 0..<4 {
            let value = start + i
            let math = "\(value)"
            let view = createNumberItem(value: value, math: math)
            view.alpha = 0
            views.append(view)
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(handleTap(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
            //AnimationUtils.setTouchEffect(view: view)
        }
        
        grid.columns = 2
        grid.itemRatio = 1
        grid.itemSpacingRatio = 0.05
        grid.insetRatio = 0.05
        grid.reloadItemViews(views: views.shuffled())
    }
    
    private func createNumberItem(value: Int, math: String) -> UIView {
        let container = UIView()
        
        let viewBackground = UIImageView(image: Utilities.SVGImage(named: "option_bg_white_shadow"))
        container.addSubview(viewBackground)
        viewBackground.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalTo(viewBackground.snp.height).multipliedBy(346.0 / 373.0) // Ratio 346:373
        }
        
        let textNumber = AutosizeLabel()
        textNumber.text = math
        textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
        textNumber.font = .Freude(size: 20)
        textNumber.textAlignment = .center
        textNumber.adjustsFontSizeToFitWidth = true
        textNumber.minimumScaleFactor = 0.1
        viewBackground.addSubview(textNumber)
        textNumber.snp.makeConstraints { make in
            make.width.equalTo(viewBackground).multipliedBy(0.8)
            make.height.equalTo(viewBackground).multipliedBy(0.7)
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().multipliedBy(0.95) // Bias 0.45 -> centerY offset
        }
        
        container.tag = value
        return container
    }
    
    @objc private func handleTap(_ gesture: UITapGestureRecognizer) {
        guard let view = gesture.view, let textNumber = view.subviews.first?.subviews.first as? UILabel else { return }
        pauseGame()
        let value = view.tag
        let math = textNumber.text ?? ""
        let correct = value == self.count
        var delay: TimeInterval = 0
        
        if correct {
            animateCoinIfCorrect(view: textNumber)
            let values = math.replacingOccurrences(of: " ", with: "").split { "+-".contains($0) }.map { String($0) }
            let operatorSymbol = math.contains("+") ? "+" : "-"
            delay += playSound(delay: delay, names: [
                "topics/Numbers/\(value)",
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                endGameSound()]
            )
            textNumber.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            delay += playSound(delay: delay, names: [
                "topics/Numbers/\(value)",
                answerWrongEffectSound(),
                getWrongHumanSound()
            ])
            textNumber.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
                textNumber.textColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1) // #74B6FF
            }
        }
    }
}

