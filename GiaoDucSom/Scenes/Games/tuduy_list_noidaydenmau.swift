//
//  tuduy_list_noidaydenmau.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 12/4/25.
//

import UIKit
import SnapKit
import SVGKit

class tuduy_list_noidaydenmau: NhanBietGameFragment {
    // MARK: - Properties
    static let ROWS = 5
    static let COLS = 6
    static let NUM_PATHS = 4
    private var gridLayout: MyGridView!
    private var lightsView: MyGridView!
    private var pathsView: UIView!
    private var colorIndexs: [Int] = []
    private let colors = [
        UIColor(red: 4/255, green: 252/255, blue: 8/255, alpha: 1), // #04FC08
        UIColor(red: 255/255, green: 212/255, blue: 0/255, alpha: 1), // #FFD400
        UIColor(red: 255/255, green: 69/255, blue: 69/255, alpha: 1), // #FF4545
        UIColor(red: 205/255, green: 85/255, blue: 255/255, alpha: 1), // #CD55FF
        UIColor(red: 0/255, green: 189/255, blue: 255/255, alpha: 1) // #00BDFF
    ]
    private var pathViews: [PathView] = []
    private var lastRow: Int = -1
    private var lastColumn: Int = -1
    private var valid: Bool = false
    private var currentPathIndex: Int = -1
    private var latestCorrect: Int = 0
    private var pointToColor: [Int: Int] = [:]
    private let lightOffIds = [
        R11.drawable.tuduy_noidaydien_den_1_off,
        R11.drawable.tuduy_noidaydien_den_2_off,
        R11.drawable.tuduy_noidaydien_den_3_off,
        R11.drawable.tuduy_noidaydien_den_4_off,
        R11.drawable.tuduy_noidaydien_den_5_off
    ]
    private let lightIds = [
        R11.drawable.tuduy_noidaydien_den_1,
        R11.drawable.tuduy_noidaydien_den_2,
        R11.drawable.tuduy_noidaydien_den_3,
        R11.drawable.tuduy_noidaydien_den_4,
        R11.drawable.tuduy_noidaydien_den_5
    ]
    private var cells: [UIView] = []
    private let generator = FlowFree3Generator()
    private var grid = Array(repeating: Array(repeating: 0, count: 6), count: 5)
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 39/255, green: 57/255, blue: 75/255, alpha: 1) // #27394B
        
        let paddingContainer = UIView()
        addSubviewWithPercentInset(subview: paddingContainer, percentInset: 5)
        
        let contentLayout = UIView()
        contentLayout.clipsToBounds = false
        paddingContainer.addSubview(contentLayout)
        contentLayout.makeViewCenterAndKeep(ratio: 11.0 / 5.0)
        
        lightsView = MyGridView()
        contentLayout.addSubview(lightsView)
        lightsView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
        }
        
        let rightView = UIView()
        rightView.backgroundColor = UIColor(red: 43/255, green: 121/255, blue: 185/255, alpha: 1) // #2B79B9
        contentLayout.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            //make.width.equalTo(contentLayout).multipliedBy(0.5) // Giả lập wrap_content
        }
        
        lightsView.snp.makeConstraints { make in
            make.right.equalTo(rightView.snp.left)
        }
        
        gridLayout = MyGridView()
        rightView.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        pathsView = UIView()
        pathsView.isUserInteractionEnabled  = false
        //pathsView.backgroundColor = .red.withAlphaComponent(0.3)
        rightView.addSubview(pathsView)
        pathsView.snp.makeConstraints { make in
            make.width.equalTo(pathsView.snp.height).multipliedBy(6.0 / 5.0) // Ratio 6:5
            make.edges.equalToSuperview()
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handlePan(_:)))
        gridLayout.addGestureRecognizer(panGesture)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        colorIndexs = [0, 1, 2, 3, 4].shuffled().prefix(4).map { $0 }
        
        var views: [UIView] = []
        for i in colorIndexs {
            let view = UIImageView(image: Utilities.SVGImage(named: lightOffIds[i].toDrawableName()))
            view.contentMode = .scaleAspectFit
            views.append(view)
        }
        lightsView.columns = 2
        lightsView.itemRatio = 1
        lightsView.itemSpacingRatio = 0.01
        lightsView.insetRatio = 0.01
        lightsView.reloadItemViews(views: views)
        
        generator.generatePuzzle(difficulty: Int.random(in: 1...4))
        pathViews = []
        for i in 0..<colorIndexs.count {
            let pathView = PathView()
            pathView.color = colors[colorIndexs[i]]
            pathView.points = []
            pathsView.addSubview(pathView)
            pathView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
            pathViews.append(pathView)
        }
        
        cells = []
        for r in 0..<5 {
            for c in 0..<6 {
                let view = UIImageView()
                view.backgroundColor = UIColor(red: 39/255, green: 57/255, blue: 75/255, alpha: 1) // #27394B
                let p = DMPoint(row: r, column: c)
                for i in 0..<generator.paths.count {
                    let path = generator.paths[i]
                    if path.start == p || path.end == p {
                        view.image = Utilities.SVGImage(named: "circle_noi_day_dien").withRenderingMode(.alwaysTemplate)
                        view.tintColor = colors[colorIndexs[i]]
                    }
                }
                cells.append(view)
            }
        }
        gridLayout.itemRatio = 1
        gridLayout.columns = 6
        gridLayout.itemSpacingRatio = 0.01
        gridLayout.insetRatio = 0.01
        gridLayout.reloadItemViews(views: cells)
        
        let delay = playSound(openGameSound(), "tuduy/noi day dien")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("tuduy/noi day dien")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handlePan(_ gesture: UIPanGestureRecognizer) {
        if gameState != .playing { return }
        let point = gesture.location(in: gridLayout)
        let x = Int(floor(point.x * 6 / gridLayout.frame.width))
        let y = Int(floor(point.y * 5 / gridLayout.frame.height))
        let clampedX = max(0, min(5, x))
        let clampedY = max(0, min(4, y))
        
        switch gesture.state {
        case .began:
            Utils.vibrate()
            lastRow = -1
            lastColumn = -1
            startAt(row: clampedY, column: clampedX)
        case .changed:
            moveTo(row: clampedY, column: clampedX)
        case .ended, .cancelled:
            endAt(row: clampedY, column: clampedX)
        default:
            break
        }
    }
    
    // MARK: - Path Logic
    private func startAt(row: Int, column: Int) {
        currentPathIndex = -1
        if lastRow == row && lastColumn == column { return }
        lastRow = row
        lastColumn = column
        valid = false
        
        for i in 0..<generator.paths.count {
            let path = generator.paths[i]
            if path.start == DMPoint(row: row, column: column) || path.end == DMPoint(row: row, column: column) {
                pathViews[i].points = [DMPoint(row: row, column: column)]
                valid = true
                currentPathIndex = i
                pathViews[i].setNeedsDisplay()
                return
            }
        }
        
        for (i, pathView) in pathViews.enumerated() {
            if pathView.points.contains(DMPoint(row: row, column: column)) {
                pathView.points.append(DMPoint(row: row, column: column))
                valid = true
                currentPathIndex = i
                pathView.setNeedsDisplay()
                return
            }
        }
    }
    
    private func moveTo(row: Int, column: Int) {
        if lastRow == row && lastColumn == column { return }
        lastRow = row
        lastColumn = column
        let point = DMPoint(row: row, column: column)
        
        for pathView in pathViews {
            if let index = pathView.points.firstIndex(of: point) {
                //pathView.points = Array(pathView.points.prefix(index))
                pathView.addPoint(point)
                pathView.points.removeLast()
            }
        }
        
        if currentPathIndex >= 0 {
            for i in 0..<generator.paths.count where i != currentPathIndex {
                let path = generator.paths[i]
                if path.start == point || path.end == point {
                    return
                }
            }
            pathViews[currentPathIndex].addPoint(point)
        }
        
        playSound("effect/slide3")
        checkLight()
    }
    
    private func endAt(row: Int, column: Int) {
        if checkLight() {
            pauseGame()
            animateCoinIfCorrect(view: lightsView)
            let delay = playSound("effect_end", getCorrectHumanSound(), endGameSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        }
    }
    
    private func checkLight() -> Bool {
        var correct = 0
        for i in 0..<pathViews.count {
            let pathView = pathViews[i]
            let path = generator.paths[i]
            if pathView.points.contains(path.end) && pathView.points.contains(path.start) {
                (lightsView.subviews[i] as? UIImageView)?.image = Utilities.SVGImage(named: lightIds[colorIndexs[i]].toDrawableName())
                correct += 1
            } else {
                (lightsView.subviews[i] as? UIImageView)?.image = Utilities.SVGImage(named: lightOffIds[colorIndexs[i]].toDrawableName())
            }
        }
        if correct != latestCorrect {
            if correct > latestCorrect {
                playSound("effect/light_bulb")
            }
            latestCorrect = correct
        }
        return correct == 4
    }
    
    // MARK: - PathView
    class PathView: UIView {
        var color: UIColor = .black
        var points: [DMPoint] = []
        //private let paint: CGContextAttributes = .init()
        private var stepX: CGFloat = 0
        private var stepY: CGFloat = 0
        
        override init(frame: CGRect) {
            super.init(frame: frame)
            initPaint()
        }
        
        required init?(coder: NSCoder) {
            super.init(coder: coder)
            initPaint()
        }
        
        private func initPaint() {
            backgroundColor = .clear
        }
        
        func addPoint(_ p: DMPoint) {
            if let index = points.firstIndex(of: p) {
                points = Array(points.prefix(index))
            }
            if !points.isEmpty {
                let last = points.last!
                if abs(last.row - p.row) + abs(last.column - p.column) != 1 { return }
            }
            points.append(p)
            setNeedsDisplay()
        }
        
        override func draw(_ rect: CGRect) {
            guard let context = UIGraphicsGetCurrentContext(), !points.isEmpty else { return }
            
            context.setStrokeColor(color.withAlphaComponent(0.59).cgColor)
            context.setLineJoin(.round)
            context.setLineCap(.round)
            context.setLineWidth(stepX / 4)
            
            let path = CGMutablePath()
            path.move(to: CGPoint(x: CGFloat(points[0].column) * stepX + stepX / 2, y: CGFloat(points[0].row) * stepY + stepY / 2))
            
            for i in 1..<points.count {
                path.addLine(to: CGPoint(x: CGFloat(points[i].column) * stepX + stepX / 2, y: CGFloat(points[i].row) * stepY + stepY / 2))
            }
            
            context.addPath(path)
            context.strokePath()
        }
        
        override func layoutSubviews() {
            super.layoutSubviews()
            stepX = frame.width / 6
            stepY = frame.height / 5
        }
    }
  
    struct Path {
        var start: DMPoint
        var end: DMPoint
        var pathPoints: [DMPoint]
        
        func containPoint(_ p: DMPoint) -> Bool {
            return pathPoints.contains { $0 == p }
        }
    }

    class FlowFree3Generator {
        var grid = Array(repeating: Array(repeating: 0, count: 6), count: 5)
        var paths: [Path] = []
        
        func generatePuzzle(difficulty: Int) {
            var puzzleComplete: Bool
            repeat {
                resetGrid()
                paths = []
                puzzleComplete = true
                
                var tries = 0
                for i in 1...tuduy_list_noidaydenmau.NUM_PATHS {
                    let start = getRandomEmptyPoint()
                    guard let end = getEndPointBasedOnDifficulty(start: start, difficulty: difficulty) else { break }
                    let path = findPathWithDifficulty(start: start, end: end, difficulty: difficulty)
                    tries += 1
                    if tries > 100 { break }
                    if let path = path {
                        paths.append(Path(start: start, end: end, pathPoints: path))
                        markPathOnGrid(path: path, pathId: i)
                    } else {
                        continue
                    }
                }
                
                if tries > 100 || paths.count < 4 {
                    puzzleComplete = false
                    continue
                }
                
                extendPaths()
                puzzleComplete = isGridFilled()
            } while !puzzleComplete
        }
        
        private func resetGrid() {
            grid = Array(repeating: Array(repeating: 0, count: 6), count: 5)
        }
        
        private func isGridFilled() -> Bool {
            return grid.allSatisfy { $0.allSatisfy { $0 != 0 } }
        }
        
        private func getRandomEmptyPoint() -> DMPoint {
            var point: DMPoint
            repeat {
                let x = Int.random(in: 0..<5)
                let y = Int.random(in: 0..<6)
                point = DMPoint(row: x, column: y)
            } while grid[point.row][point.column] != 0
            return point
        }
        
        private func getEndPointBasedOnDifficulty(start: DMPoint, difficulty: Int) -> DMPoint? {
            let maxDistance = min(difficulty, 2)
            var tries = 0
            repeat {
                tries += 1
                if tries > 100 { return nil }
                let end = getRandomEmptyPoint()
                if calculateManhattanDistance(p1: start, p2: end) >= maxDistance {
                    return end
                }
            } while true
        }
        
        private func calculateManhattanDistance(p1: DMPoint, p2: DMPoint) -> Int {
            return abs(p1.row - p2.row) + abs(p1.column - p2.column)
        }
        
        private func findPathWithDifficulty(start: DMPoint, end: DMPoint, difficulty: Int) -> [DMPoint]? {
            var queue: [[DMPoint]] = [[start]]
            var visited = Array(repeating: Array(repeating: false, count: COLS), count: ROWS)
            visited[start.row][start.column] = true
            
            while !queue.isEmpty {
                let currentPath = queue.removeFirst()
                let current = currentPath.last!
                
                if current.row == end.row && current.column == end.column {
                    if countTurnsInPath(path: currentPath) <= difficulty {
                        return currentPath
                    }
                }
                
                for neighbor in getNeighbors(p: current) {
                    if isValid(p: neighbor, visited: visited) {
                        visited[neighbor.row][neighbor.column] = true
                        var newPath = currentPath
                        newPath.append(neighbor)
                        queue.append(newPath)
                    }
                }
            }
            return nil
        }
        
        private func countTurnsInPath(path: [DMPoint]) -> Int {
            var turns = 0
            for i in 1..<(path.count - 1) {
                let prev = path[i - 1]
                let curr = path[i]
                let next = path[i + 1]
                if (prev.row == curr.row && curr.row != next.row) || (prev.column == curr.column && curr.column != next.column) {
                    turns += 1
                }
            }
            return turns
        }
        
        private func getNeighbors(p: DMPoint) -> [DMPoint] {
            var neighbors: [DMPoint] = []
            let dx = [-1, 1, 0, 0]
            let dy = [0, 0, -1, 1]
            
            for i in 0..<4 {
                let nx = p.row + dx[i]
                let ny = p.column + dy[i]
                if nx >= 0 && nx < 5 && ny >= 0 && ny < 6 {
                    neighbors.append(DMPoint(row: nx, column: ny))
                }
            }
            return neighbors
        }
        
        private func isValid(p: DMPoint, visited: [[Bool]]) -> Bool {
            return p.row >= 0 && p.row < 5 && p.column >= 0 && p.column < 6 && grid[p.row][p.column] == 0 && !visited[p.row][p.column]
        }
        
        private func markPathOnGrid(path: [DMPoint], pathId: Int) {
            for p in path {
                grid[p.row][p.column] = pathId
            }
        }
        
        private func extendPaths() {
            for path in paths {
                var path = path
                var extended: Bool
                repeat {
                    extended = extendFromStart(path: &path) || extendFromEnd(path: &path)
                } while extended
            }
        }
        
        private func extendFromStart(path: inout Path) -> Bool {
            let start = path.start
            for neighbor in getNeighbors(p: start) {
                if canExtendTo(p: neighbor, pathId: grid[start.row][start.column], currentEnd: start) {
                    grid[neighbor.row][neighbor.column] = grid[start.row][start.column]
                    path.pathPoints.insert(DMPoint(row: neighbor.row, column: neighbor.column), at: 0)
                    path.start = DMPoint(row: neighbor.row, column: neighbor.column)
                    return true
                }
            }
            return false
        }
        
        private func extendFromEnd(path: inout Path) -> Bool {
            let end = path.end
            for neighbor in getNeighbors(p: end) {
                if canExtendTo(p: neighbor, pathId: grid[end.row][end.column], currentEnd: end) {
                    grid[neighbor.row][neighbor.column] = grid[end.row][end.column]
                    path.pathPoints.append(DMPoint(row: neighbor.row, column: neighbor.column))
                    path.end = DMPoint(row: neighbor.row, column: neighbor.column)
                    return true
                }
            }
            return false
        }
        
        private func canExtendTo(p: DMPoint, pathId: Int, currentEnd: DMPoint) -> Bool {
            if grid[p.row][p.column] != 0 { return false }
            for neighbor in getNeighbors(p: p) {
                if neighbor != currentEnd && grid[neighbor.row][neighbor.column] == pathId {
                    return false
                }
            }
            return true
        }
    }

}

// MARK: - Supporting Structures

struct DMPoint: Equatable {
    let row: Int
    let column: Int
    
    static func ==(lhs: DMPoint, rhs: DMPoint) -> Bool {
        return lhs.row == rhs.row && lhs.column == rhs.column
    }
}

struct R11 {
    struct drawable {
        static let tuduy_noidaydien_den_1 = 1
        static let tuduy_noidaydien_den_2 = 2
        static let tuduy_noidaydien_den_3 = 3
        static let tuduy_noidaydien_den_4 = 4
        static let tuduy_noidaydien_den_5 = 5
        static let tuduy_noidaydien_den_1_off = 6
        static let tuduy_noidaydien_den_2_off = 7
        static let tuduy_noidaydien_den_3_off = 8
        static let tuduy_noidaydien_den_4_off = 9
        static let tuduy_noidaydien_den_5_off = 10
        static let circle_noi_day_dien = 11
    }
}

extension Int {
    func toDrawableName() -> String {
        switch self {
        case R11.drawable.tuduy_noidaydien_den_1:
            return "tuduy_noidaydien_den_1"
        case R11.drawable.tuduy_noidaydien_den_2:
            return "tuduy_noidaydien_den_2"
        case R11.drawable.tuduy_noidaydien_den_3:
            return "tuduy_noidaydien_den_3"
        case R11.drawable.tuduy_noidaydien_den_4:
            return "tuduy_noidaydien_den_4"
        case R11.drawable.tuduy_noidaydien_den_5:
            return "tuduy_noidaydien_den_5"
        case R11.drawable.tuduy_noidaydien_den_1_off:
            return "tuduy_noidaydien_den_1_off"
        case R11.drawable.tuduy_noidaydien_den_2_off:
            return "tuduy_noidaydien_den_2_off"
        case R11.drawable.tuduy_noidaydien_den_3_off:
            return "tuduy_noidaydien_den_3_off"
        case R11.drawable.tuduy_noidaydien_den_4_off:
            return "tuduy_noidaydien_den_4_off"
        case R11.drawable.tuduy_noidaydien_den_5_off:
            return "tuduy_noidaydien_den_5_off"
        case R11.drawable.circle_noi_day_dien:
            return "circle_noi_day_dien"
        default:
            return "empty"
        }
    }
}
