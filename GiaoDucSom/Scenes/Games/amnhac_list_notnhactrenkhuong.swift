//
//  amnhac_list_notnhactrenkhuong.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 18/4/25.
//


import UIKit
import SnapKit
import SVGKit

class amnhac_list_notnhactrenkhuong: MusicGameFragment {
    // MARK: - Properties
    private var viewA: UIView!
    private var viewB: UIView!
    private var viewC: UIView!
    private var viewD: UIView!
    private var viewE: UIView!
    private var viewF: UIView!
    private var viewG: UIView!
    private var imageDrag: UIImageView!
    private var textNote: HeightRatioTextView!
    private var imageNote: UIImageView!
    private let notes: [String] = ["C", "D", "E", "F", "G", "A", "B"]
    private var meIndex: Int = 0
    private var coinView = UIView()
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        self.backgroundColor = UIColor(red: 237/255, green: 251/255, blue: 255/255, alpha: 1) // #EDFBFF
        
        let paddingView = UIView()
        view.addSubview(paddingView)
        paddingView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.9)
        }
        
        let container = UIView()
        container.clipsToBounds = false
        paddingView.addSubview(container)
        container.makeViewCenterAndKeep(ratio: 2.3)
        
        let bgImage = UIImageView(image: Utilities.SVGImage(named: "music/note/music_bg_notnhac2.svg"))
        bgImage.contentMode = .scaleToFill
        container.addSubview(bgImage)
        bgImage.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.66)
            make.top.left.bottom.equalTo(container)
        }
        
        let innerContainer = UIView()
        innerContainer.alpha = 0.001
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.width.equalTo(innerContainer.snp.height).multipliedBy(0.5) // Ratio 0.5:1
            make.centerY.centerX.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(1.0)
        }
        
        let verticalBiases: [(id: String, bias: CGFloat)] = [
            ("c", 0.874),
            ("d", 0.81),
            ("e", 0.745),
            ("f", 0.681),
            ("g", 0.617),
            ("a", 0.555),
            ("b", 0.493)
        ]
        
        for (id, bias) in verticalBiases {
            let v = UIView()
            v.stringTag = "view_\(id)"
            v.backgroundColor = .red // #f00
            innerContainer.addSubview(v)
            v.snp.makeConstraints { make in
                make.width.equalTo(v.snp.height) // Ratio 1:1
                make.height.equalTo(innerContainer).multipliedBy(0.05)
                make.centerX.equalToSuperview()
            }
            addActionOnLayoutSubviews {
                v.snapToVerticalBias(verticalBias: bias)
            }
            if id == "a" { viewA = v }
            if id == "b" { viewB = v }
            if id == "c" { viewC = v }
            if id == "d" { viewD = v }
            if id == "e" { viewE = v }
            if id == "f" { viewF = v }
            if id == "g" { viewG = v }
        }
        
        imageNote = UIImageView()
        imageNote.stringTag = "image_note"
        imageNote.contentMode = .scaleAspectFit
        imageNote.isHidden = true
        imageNote.tintColor = UIColor.color(hex: "#74B6FF")
        container.addSubview(imageNote)
        imageNote.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        imageDrag = UIImageView(image: Utilities.SVGImage(named: "music/note/music_btn_drag.svg"))
        imageDrag.stringTag = "image_drag"
        imageDrag.isUserInteractionEnabled = true
        container.addSubview(imageDrag)
        imageDrag.snp.makeConstraints { make in
            make.width.equalTo(imageDrag.snp.height) // Ratio 1:1
            make.height.equalTo(container).multipliedBy(0.25)
            make.center.equalToSuperview()
        }
        
        textNote = HeightRatioTextView()
        textNote.setHeightRatio(0.6)
        textNote.stringTag = "text_note"
        textNote.text = "B"
        textNote.font = .UTMAvoBold(size: 20)
        textNote.textColor = UIColor.color(hex: "#74B6FF")
        textNote.textAlignment = .center
        textNote.backgroundColor = .clear
        container.addSubview(textNote)
        textNote.snp.makeConstraints { make in
            make.width.equalTo(container).multipliedBy(0.35)
            make.height.equalTo(container).multipliedBy(0.8)
            make.right.equalToSuperview()
            make.bottom.equalToSuperview().multipliedBy(0.9) // Bias 0.25
        }
        
        let panGesture = UIPanGestureRecognizer(target: self, action: #selector(handleDrag(_:)))
        imageDrag.addGestureRecognizer(panGesture)
        
        view.addSubviewWithPercentInset(subview: coinView, percentInset: 0.33)
        coinView.isUserInteractionEnabled = false
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        
        meIndex = Int.random(in: 0..<notes.count)
        let delay = playSound(
            delay: 0, names: [openGameSound(),
            "music/notnhactrenkhuong_1",
            "topics/Music Notes/\(notes[meIndex].lowercased())",
            "music/notnhactrenkhuong_2"]
        )
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func createGame() {
        super.createGame()
        
        while true {
            let initIndex = Int.random(in: 0..<notes.count)
            if initIndex != meIndex {
                textNote.text = notes[initIndex]
                let views = [viewC, viewD, viewE, viewF, viewG, viewA, viewB]
                scheduler.schedule(after: 0.1) {
                    [weak self] in
                    guard let self = self else { return }
                    imageDrag.moveToCenter(of: views[initIndex]!, duration: 0)
                }
                break
            }
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound(delay: 0,
                names: [openGameSound(),
                "music/notnhactrenkhuong_1",
                "topics/Music Notes/\(notes[meIndex].lowercased())",
                "music/notnhactrenkhuong_2"]
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    // MARK: - Touch Handling
    @objc private func handleDrag(_ gesture: UIPanGestureRecognizer) {
        guard let view = gesture.view else { return }
        
        let translation = gesture.translation(in: view.superview)
        let location = gesture.location(in: view.superview)
        
        switch gesture.state {
        case .began:
            view.transform = .identity
            imageDrag.image = Utilities.SVGImage(named: "music/note/music_btn_drag_2.svg")
            playSound("effect/cungchoi_pick\(Int.random(in: 1...2))")
            
        case .changed:
            var newY = location.y
            let minY = 1.8 * imageDrag.frame.height
            let maxY = 3.6 * imageDrag.frame.height
            newY = max(minY, min(maxY, newY))
            view.frame.origin.y = newY - view.frame.height / 2
            
            let views = [viewA, viewB, viewC, viewD, viewE, viewF, viewG]
            var minDistance = CGFloat.greatestFiniteMagnitude
            var minView: UIView?
            
            for v in views {
                guard let v = v else { continue }
                let vector = v.distanceFromCenterToCenter(to: view)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    minView = v
                }
            }
            
            if let minView = minView, let newValue = minView.stringTag?.replacingOccurrences(of: "view_", with: "").uppercased() {
                let oldValue = textNote.text ?? ""
                if oldValue != newValue {
                    UIImpactFeedbackGenerator(style: .medium).impactOccurred()
                    textNote.text = newValue
                }
            }
            
        case .ended:
            imageDrag.image = Utilities.SVGImage(named: "music/note/music_btn_drag.svg")
            let views = [viewC, viewD, viewE, viewF, viewG, viewA, viewB]
            var minDistance = CGFloat.greatestFiniteMagnitude
            var minView: UIView?
            
            for v in views {
                guard let v = v else { continue }
                let vector = v.distanceFromCenterToCenter(to: view)
                let distance = hypot(vector.x, vector.y)
                if distance < minDistance {
                    minDistance = distance
                    minView = v
                }
            }
            
            if let minView = minView, let note = minView.stringTag?.replacingOccurrences(of: "view_", with: "").uppercased() {
                textNote.text = note
                imageDrag.moveToCenter(of: minView, duration: 0.2)
                
                let correct = note == notes[meIndex]
                playSound("effect/word puzzle drop")
                var delay: TimeInterval = 0.3
                
                if correct {
                    pauseGame()
                    delay += playSound(
                        delay: delay, names: ["effect/answer_end",
                                              getCorrectHumanSound(),
                                              endGameSound()]
                    )
                    scheduler.schedule(after: 1.0) { [weak self] in
                        self?.imageDrag.isHidden = true
                        self?.imageNote.isHidden = false
                        self?.imageNote.image = Utilities.SVGImage(named: "music/note/music_\(self?.notes[self?.meIndex ?? 0].lowercased() ?? "").svg").withRenderingMode(.alwaysTemplate)
                        self?.imageNote.tintColor = .color(hex: "#74B6FF")
                    }
                    animateCoinIfCorrect(view: self.coinView)
                    scheduler.schedule(after: delay + 1.0) { [weak self] in
                        self?.finishGame()
                    }
                } else {
                    pauseGame()
                    setGameWrong()
                    delay += playSound(
                        delay: delay, names: ["effect/answer_wrong",
                                              "answer_wrong\(Int.random(in: 1...2))"]
                    )
                    scheduler.schedule(after: delay) { [weak self] in
                        self?.resumeGame()
                    }
                }
            }
            
        default:
            break
        }
    }
}
