//
//  LanguageListViewController.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/6/25.
//


import UIKit
import SnapKit
import SVGKit

class LanguageListViewController: SubjectMainViewController {
    var category: String?
    override func viewDidLoad() {
        super.viewDidLoad()
        setTitle(category ?? "Thơ truyện")
        titleColor = UIColor(hex: "#222222")
        backgroundColor = UIColor(hex: "#B5B9C5")
    }
    override var backgroundMusicFile: String? {
        "bg_list8"
    }
    override func setupData() {
        guard let stickerSizeUrl = Bundle.main.url(forResource: "language_data", withExtension: "json"),
              let stickerSizeData = try? Data(contentsOf: stickerSizeUrl),
              var topics = try? JSONDecoder().decode([LanguageTopic].self, from: stickerSizeData) else {
            print("Error loading sticker_size.json")
            return
        }
        var topic = topics[0]
        for t in topics {
            if t.category == category {
                topic = t
                break
            }
        }
        let languageSection = SectionData(header: "Danh sách", items: topic.item.map({ d in
            TruyenThoTiemThucItem(data: d, category: category!)
        }))
        
        // Add all sections
        sections = [
            languageSection,
        ]
    }
       
    // Hàm giả lập để kiểm tra game miễn phí
    private func checkFreeGameByThumbnail(_ thumbnail: String) -> Bool {
        // Trong thực tế, bạn cần implement logic tương tự GameLevelHelper.checkFreeGameByThumbnail
        return false
    }
    
    // Hàm giả lập để ánh xạ data sang game
    private func getGameByThumb(_ thumbnail: String) -> String {
        // Trong thực tế, bạn cần implement logic tương tự MusicGameNameHelper.getGameByThumb
        return thumbnail
    }
}
