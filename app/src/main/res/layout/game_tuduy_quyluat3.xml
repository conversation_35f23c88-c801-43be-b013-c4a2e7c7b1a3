<?xml version="1.0" encoding="utf-8"?>
<com.kidsup.giaoducsom.view.PercentConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/container_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:clipChildren="false"
    android:clipToPadding="false">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        android:src="@drawable/tuduy_quyluat_bg" />

    <androidx.constraintlayout.widget.Guideline
        android:id="@+id/center_line"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:orientation="vertical"
        app:layout_constraintGuide_percent="0.5" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:background="@drawable/tuduy_quyluat3_bg"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toRightOf="@id/center_line"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.05" />

    <com.kidsup.giaoducsom.view.MyGridLayout
        android:id="@+id/left_grid_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toLeftOf="@id/center_line"
        app:layout_constraintTop_toTopOf="parent"
        app:pcl_layout_marginPercentPerHeight="0.05" />

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/coin_view"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1"
        app:layout_constraintHeight_percent="0.3"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <include
        layout="@layout/nhanbiet_top_menu"
        android:visibility="invisible" />
</com.kidsup.giaoducsom.view.PercentConstraintLayout>