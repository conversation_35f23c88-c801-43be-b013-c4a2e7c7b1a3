//
//  toancoban_list_pheptinhtraicay.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 6/4/25.
//


import UIKit
import SnapKit
import SVGKit

class toancoban_list_pheptinhtraicay: NhanBietGameFragment {
    // MARK: - Properties
    private var leftItemContainer: UIView!
    private var rightItemContainer: UIView!
    private var textA: HeightRatioTextView!
    private var textB: HeightRatioTextView!
    private var textResult: HeightRatioTextView!
    private var textCurrent: UILabel?
    private var numpad: MathNumpad!
    private var number1: Int = 0
    private var number2: Int = 0
    
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(hex: "#E9FDFF")
        
        let skyView = UIView()
        skyView.backgroundColor = UIColor(hex: "#D6FAFF")
        view.addSubview(skyView)
        skyView.snp.makeConstraints { make in
            make.left.right.top.equalTo(self)
            make.height.equalTo(self).multipliedBy(0.5)
        }
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = false
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let gridContainer = UIView()
        view.addSubview(gridContainer)
        gridContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.67)
        }
        
        let gridLayout = UIView()
        gridContainer.addSubview(gridLayout)
        gridLayout.snp.makeConstraints { make in
            make.left.right.top.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let innerGridLayout = UIView()
        gridLayout.addSubview(innerGridLayout)
        innerGridLayout.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
            make.width.equalTo(innerGridLayout.snp.height).multipliedBy(2.2) // Ratio 2.2:1
        }
        
        leftItemContainer = UIView()
        //leftItemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        innerGridLayout.addSubview(leftItemContainer)
        leftItemContainer.snp.makeConstraints { make in
            make.width.equalTo(innerGridLayout).multipliedBy(0.5)
            make.height.equalTo(leftItemContainer.snp.width) // Ratio 1:1
            make.left.top.bottom.equalToSuperview()
        }
        
        rightItemContainer = UIView()
        //rightItemContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        innerGridLayout.addSubview(rightItemContainer)
        rightItemContainer.snp.makeConstraints { make in
            make.width.equalTo(innerGridLayout).multipliedBy(0.5)
            make.height.equalTo(rightItemContainer.snp.width) // Ratio 1:1
            make.right.top.bottom.equalToSuperview()
        }
        
        let textContainer = UIView()
        gridContainer.addSubview(textContainer)
        textContainer.snp.makeConstraints { make in
            make.left.right.bottom.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.5)
        }
        
        let innerTextContainer = UIView()
        textContainer.addSubview(innerTextContainer)
        innerTextContainer.makeViewCenterAndKeep(ratio: 4.4)
                
        let textAContainer = UIImageView()
        textAContainer.isUserInteractionEnabled = true
        textAContainer.image = Utilities.SVGImage(named: "math_result_bg")
        innerTextContainer.addSubview(textAContainer)
        textAContainer.snp.makeConstraints { make in
            make.width.equalTo(textAContainer.snp.height)
            make.top.bottom.equalToSuperview()
        }
        
        textA = HeightRatioTextView()
        textA.text = "?"
        textA.setHeightRatio(0.7)
        textA.textColor = UIColor(hex: "#74B6FF")
        textA.font = .Freude(size: 20)
        textA.textAlignment = .center
        textA.adjustsFontSizeToFitWidth = true
        textA.minimumScaleFactor = 0.1
        textA.isUserInteractionEnabled = true
        textAContainer.addSubview(textA)
        textA.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textA.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextA)))
        
        let plusContainer = UIView()
        innerTextContainer.addSubview(plusContainer)
        plusContainer.snp.makeConstraints { make in
            make.width.equalTo(plusContainer.snp.height).multipliedBy(0.6)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textAContainer.snp.right)
        }
        
        let plusText = HeightRatioTextView()
        plusText.text = "+"
        plusText.setHeightRatio(0.7)
        plusText.textColor = UIColor(hex: "#74B6FF")
        plusText.font = .Freude(size: 20)
        plusText.textAlignment = .center
        plusText.adjustsFontSizeToFitWidth = true
        plusText.minimumScaleFactor = 0.1
        plusContainer.addSubview(plusText)
        plusText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textBContainer = UIImageView()
        textBContainer.isUserInteractionEnabled = true
        textBContainer.image = Utilities.SVGImage(named: "math_result_bg")
        innerTextContainer.addSubview(textBContainer)
        textBContainer.snp.makeConstraints { make in
            make.width.equalTo(textBContainer.snp.height)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(plusContainer.snp.right)
        }
        
        textB = HeightRatioTextView()
        textB.text = "?"
        textB.setHeightRatio(0.7)
        textB.textColor = UIColor(hex: "#74B6FF")
        textB.font = .Freude(size: 20)
        textB.textAlignment = .center
        textB.adjustsFontSizeToFitWidth = true
        textB.minimumScaleFactor = 0.1
        textB.isUserInteractionEnabled = true
        textBContainer.addSubview(textB)
        textB.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textB.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextB)))
        
        let equalContainer = UIView()
        innerTextContainer.addSubview(equalContainer)
        equalContainer.snp.makeConstraints { make in
            make.width.equalTo(equalContainer.snp.height).multipliedBy(0.6)
            make.top.bottom.equalToSuperview()
            make.left.equalTo(textBContainer.snp.right)
        }
        
        let equalText = HeightRatioTextView()
        equalText.text = "="
        equalText.setHeightRatio(0.7)
        equalText.textColor = UIColor(hex: "#74B6FF")
        equalText.font = .Freude(size: 20)
        equalText.textAlignment = .center
        equalText.adjustsFontSizeToFitWidth = true
        equalText.minimumScaleFactor = 0.1
        equalContainer.addSubview(equalText)
        equalText.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textResultContainer = UIImageView()
        textResultContainer.isUserInteractionEnabled = true
        textResultContainer.image = Utilities.SVGImage(named: "math_result_bg")
        innerTextContainer.addSubview(textResultContainer)
        textResultContainer.snp.makeConstraints { make in
            make.width.equalTo(textResultContainer.snp.height)
            make.top.bottom.equalToSuperview()
            make.right.equalToSuperview().multipliedBy(0.98)
            make.left.equalTo(equalContainer.snp.right)
        }
        
        textResult = HeightRatioTextView()
        textResult.text = "?"
        textResult.setHeightRatio(0.7)
        textResult.textColor = UIColor(hex: "#74B6FF")
        textResult.font = .Freude(size: 20)
        textResult.textAlignment = .center
        textResult.adjustsFontSizeToFitWidth = true
        textResult.minimumScaleFactor = 0.1
        textResult.isUserInteractionEnabled = true
        textResultContainer.addSubview(textResult)
        textResult.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        textResult.addGestureRecognizer(UITapGestureRecognizer(target: self, action: #selector(selectTextResult)))
    }
    
    // MARK: - Selection Logic
    @objc private func selectTextA() { select(view: textA) }
    @objc private func selectTextB() { select(view: textB) }
    @objc private func selectTextResult() { select(view: textResult) }
    
    private func select(view: UILabel?) {
        if let current = textCurrent, let parent = current.superview as? UIImageView{
            parent.image = Utilities.SVGImage(named: "math_result_bg")
        }
        if let view = view, let parent = view.superview as? UIImageView{
            numpad.isEnabled = true
            parent.image = Utilities.SVGImage(named: "math_result_bg_focus")
            view.textColor = UIColor(hex: "#74B6FF")
        } else {
            numpad.isEnabled = false
        }
        textCurrent = view
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        // Không có logic trong Java, để trống như nguyên bản
    }
    
    override func createGame() {
        super.createGame()
        while true {
            let files = StorageManager.manager.list(path: "topics/Fruits/numbers")
                .filter { $0.hasSuffix(".svg") }
                .shuffled()
                .prefix(2)
            guard files.count == 2 else { continue }
            number1 = Int(files[0].split(separator: "_")[0]) ?? 0
            number2 = Int(files[1].split(separator: "_")[0]) ?? 0
            if number1 + number2 < 10 {
                let svgPaths = [
                    "topics/Fruits/numbers/\(files[0])",
                    "topics/Fruits/numbers/\(files[1])",
                    "topics/Fruits/\(String(files[0].dropFirst(2)))",
                    "topics/Fruits/\(String(files[1].dropFirst(2)))"
                ]
                let svgList = svgPaths.map{Utilities.GetSVGKImage(named: $0)}
                self.loadNumber(container: self.leftItemContainer, svgNumber: svgList[0], svgItem: svgList[2])
                self.loadNumber(container: self.rightItemContainer, svgNumber: svgList[1], svgItem: svgList[3])
                break
            }
        }
        let delay = playSound(openGameSound(), "toan/toancoban_phep tinh qua")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toancoban_phep tinh qua")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    private func loadNumber(container: UIView, svgNumber: SVGKImage, svgItem: SVGKImage) {
        for i in 0..<svgNumber.caLayerTree.sublayers!.count {
            let bounds = svgNumber.caLayerTree.sublayers![i].shapeContentBounds!
            let width = svgNumber.size.width
            let height = svgNumber.size.height
            let item = UIImageView(image: svgItem.uiImage)
            item.contentMode = .scaleAspectFit
            container.addSubview(item)
            item.snp.makeConstraints { make in
                make.width.equalTo(container).multipliedBy(bounds.width / width)
                make.height.equalTo(container).multipliedBy(bounds.height / height)
                make.right.equalToSuperview().multipliedBy(bounds.maxX / width)
                make.bottom.equalToSuperview().multipliedBy(bounds.maxY / height)
            }
        }
    }
    
    private func checkFinish() {
        let a = Int(textA.text ?? "") ?? 0
        let b = Int(textB.text ?? "") ?? 0
        let result = Int(textResult.text ?? "") ?? 0
        
        if a == 0 || b == 0 || result == 0 {
            return
        }
        
        if number1 + number2 == result && ((a == number1 && b == number2) || (a == number2 && b == number1)) {
            animateCoinIfCorrect(view: textResult)
            textA.textColor = UIColor(hex: "#74B6FF")
            textB.textColor = UIColor(hex: "#74B6FF")
            textResult.textColor = UIColor(hex: "#74B6FF")
            pauseGame()
            let delay = playSound(
                answerCorrect1EffectSound(),
                getCorrectHumanSound(),
                "topics/Numbers/\(a)",
                "toan/cộng",
                "topics/Numbers/\(b)",
                "toan/bằng",
                "topics/Numbers/\(result)",
                endGameSound()
            )
            scheduler.schedule(delay: delay) { [weak self] in
                self?.finishGame()
            }
        } else {
            setGameWrong()
            pauseGame()
            let delay = playSound(answerWrongEffectSound(), getWrongHumanSound())
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
            textA.textColor = UIColor(hex: a == self.number1 ? "#74B6FF" : "#FF7760")
            textB.textColor = UIColor(hex: b == self.number2 ? "#74B6FF" : "#FF7760")
            textResult.textColor = UIColor(hex: result == self.number1 + self.number2 ? "#74B6FF" : "#FF7760")
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_pheptinhtraicay: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        let adjustedValue = value >= 10 ? value % 10 : value
        textCurrent?.text = String(adjustedValue)
    }
    
    func onDelClick(value: Int) {
        textCurrent?.text = ""
    }
    
    func onCheckClick(value: Int) {
        select(view: nil)
        checkFinish()
    }
}


