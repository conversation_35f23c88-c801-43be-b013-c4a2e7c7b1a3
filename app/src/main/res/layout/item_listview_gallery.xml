<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/rootview"
    android:layout_width="wrap_content"
    android:layout_height="match_parent"
    android:background="#f00">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/view_ratio"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintDimensionRatio="1.1"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <View
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/nhanbiet_bg_option_white"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHorizontal_bias="0.5"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <com.kidsup.giaoducsom.view.BlackSVGImageView
            android:id="@+id/imageView"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="#0000"
            android:scaleType="centerInside"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.9"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/view_sticker_number"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@drawable/gallery_bg_sticker_number"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintDimensionRatio="1"
            app:layout_constraintHeight_percent="0.3"
            app:layout_constraintHorizontal_bias="1"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintRight_toRightOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintVertical_bias="0">

            <TextView
                android:id="@+id/text_sticker_number"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:fontFamily="@font/svn_freude"
                android:gravity="center"
                android:text="2"
                android:textColor="#fff"
                app:autoSizeTextType="uniform"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintHeight_percent="0.7"
                app:layout_constraintLeft_toLeftOf="parent"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
