//
//  phonics_list_canyouhear.swift
//  KidsUPEnglish
//
//  Created by <PERSON><PERSON> on 23/07/2023.
//

import Foundation
import UIKit
import SVGKit
import SnapKit
import Interpolate
import AnyCodable

class phonics_list_canyouhear: GameFragment {
    private var values : [String] = []
    var step = 0
    var answer = ""
    var sound = ""
    var bottomView = SVGImageView(SVGName: "btn bg white").then {
        $0.isUserInteractionEnabled = true
        //$0.alpha = 0
    }
    var textName = AutosizeLabel().then{
        $0.textColor = .color(hex: "#FF7761")
    }
    var textBottom = AutosizeLabel().then{
        $0.textColor = .color(hex: "#99B6C1")
    }
    var svgViewContainer = UIView()
    var svgView = SVGImageView(frame: CGRectZero).then{
        $0.contentMode = .scaleAspectFit
    }
    var buttonTrue = SVGButton(SVGIcon: "btn true")
    var buttonFail = SVGButton(SVGIcon: "btn fail")
    
    var scores: [Double] = []
    
    let textViewBackground = SVGImageView(SVGName: "bg obj white").then{
        $0.contentMode = .scaleAspectFit
    }
    override func configureLayout(_ view: UIView) {
        backgroundColor = .color(hex: "#7CD2FF")
    
        let topContainerView = UIView()
        topContainerView.stringTag = "top_container"
        topContainerView.backgroundColor = .init(hex: "#849BFD")
        addSubview(topContainerView)
        topContainerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.6)
        }
                
        let bottomContainerView = UIView()
        bottomContainerView.stringTag = "bottom_container"
        bottomContainerView.backgroundColor = .init(hex: "#7CD2FF")
        addSubview(bottomContainerView)
        bottomContainerView.snp.makeConstraints { make in
            make.top.equalTo(topContainerView.snp.bottom)
            make.left.right.bottom.equalToSuperview()
        }
        
        let topInnerView = UIView()
        topInnerView.stringTag = "top_inner"
        topContainerView.addSubview(topInnerView)
        topInnerView.makeViewCenterAndKeep(ratio: 2)
        
        bottomView.isUserInteractionEnabled = true
        
        topInnerView.addSubview(svgViewContainer)
        let svgViewBackground = SVGImageView(SVGName: "bg obj white").then{
            $0.contentMode = .scaleAspectFit
        }
        svgViewContainer.addSubview(svgViewBackground)
        svgViewContainer.addSubview(svgView)
        
        textViewBackground.addSubviewWithPercentInset(subview: textName, percentInset: 0.2)
        bottomContainerView.addSubview(bottomView)
        bottomView.addSubview(textBottom)
        bottomView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.8)
            make.height.equalTo(bottomView.snp.width).multipliedBy(151.0/400.0)
        }
        textBottom.snp.makeConstraints{ make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview()
            make.width.equalToSuperview().multipliedBy(0.8)
            make.height.equalToSuperview().multipliedBy(0.7)
        }
        
        svgViewContainer.snp.makeConstraints{ make in
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(svgViewContainer.snp.height)
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().multipliedBy(1.5)
        }
        topInnerView.addSubview(textViewBackground)
        textViewBackground.snp.makeConstraints{ make in
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(svgViewContainer.snp.height)
            make.centerY.equalToSuperview()
            make.centerX.equalToSuperview().multipliedBy(0.5)
        }
        svgViewBackground.snp.makeConstraints{ make in
            make.edges.equalToSuperview()
        }
        svgView.snp.makeConstraints{ make in
            make.center.equalToSuperview()
            make.width.height.equalToSuperview().multipliedBy(0.8)
        }
        //svgViewContainer.alpha = 0
        bottomView.addSubview(buttonFail)
        bottomView.addSubview(buttonTrue)
        buttonFail.snp.makeConstraints{ make in
            make.centerX.equalToSuperview().multipliedBy(0.35)
            make.centerY.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(buttonFail.snp.height)
        }
        buttonTrue.snp.makeConstraints{ make in
            make.centerX.equalToSuperview().multipliedBy(1.65)
            make.centerY.equalToSuperview()
            make.height.equalToSuperview().multipliedBy(0.8)
            make.width.equalTo(buttonFail.snp.height)
        }
        buttonFail.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        buttonTrue.addTarget(self, action: #selector(itemClick(_:)), for: .touchUpInside)
        
        svgViewContainer.alpha = 0
        textViewBackground.alpha = 0
        
        buttonFail.isExclusiveTouch = true
        buttonTrue.isExclusiveTouch = true
    }
    override func replayIntroSound() {
        if gameState == .playing {
            pauseGame()
            var delay = 0.0
            delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{$0.replacingOccurrences(of: "@1", with: sound).replacingOccurrences(of: "@2", with: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer)})
            scheduler.schedule(delay: delay){
                [weak self] in
                guard let self = self else { return }
                self.resumeGame()
            }
        }
    }
    override func createGame() {
        super.createGame()
        values = (game.values?.compactMap { $0.value as? String })!.randomOrder().take(count: game.questions!)
        startGame()
        loadNextStep()
    }
    func loadNextStep(){
        pauseGame()
        CoinAnimationUtils.shared.removeList()
        if step >= 1 {
            var delay = 0.0
            delay += self.playSound(name: endGameSound(), delay: delay)
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.finishGame()
            })
            return
        }
        answer = values[step]
        step += 1
        svgView.SVGName = tapdoc || isVocab() ? game.paths![(game.values!.firstIndex(of: AnyCodable(answer)))!] : "english phonics/\(game.level!)/\(answer).svg"
        
        textBottom.text = ""
        let values1 = (game.values1?.compactMap { $0.value as? String })!
        let nosign = vietnamese.removeSign(answer)
        while true {
            let values1 = values1.randomOrder().take(count: 2)
            var foundSound = false
            for s in values1 {
                if nosign.contains(s) {
                    sound = values1[0]
                    foundSound = true
                    break
                }
            }
            if foundSound {
                break
            }
        }
        textName.text = sound
        scheduler.schedule(delay: 0.1, execute: {
            [weak self] in
            guard let self = self else { return }
            self.showImage()
        })
        
        var delay = playSound(openGameSound())
        for name in self.parseIntroText()! {
            if name.contains("@1"){
                UIView.animate(withDuration: 0.2, delay: delay, options: .curveLinear) {
                    self.textViewBackground.alpha = 1
                }
            }
            if name.contains("@2"){
                UIView.animate(withDuration: 0.2, delay: delay, options: .curveLinear) {
                    self.svgViewContainer.alpha = 1
                }
            }
            let index = (game.values!.firstIndex(of: AnyCodable(answer)))!
            delay += self.playSound(name: name.replacingOccurrences(of: "@1", with: sound).replacingOccurrences(of: "@2", with: tapdoc || isVocab() ? game.sounds![index] : answer), delay: delay)            
        }
        
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
            self.showBottom()
        })
        buttonFail.alpha = 1
        buttonTrue.alpha = 1
    }
   
    @objc func playIntro(){
        pauseGame()
        var delay = 0.0
        delay += self.playSound(delay: delay, names: self.parseIntroText()!.map{$0.replacingOccurrences(of: "@1", with: sound).replacingOccurrences(of: "@2", with: tapdoc || isVocab() ? game.sounds![(game.values!.firstIndex(of: AnyCodable(answer)))!] : answer)})
        scheduler.schedule(delay: delay, execute: {
            [weak self] in
            guard let self = self else { return }
            self.resumeGame()
        })
    }
    func showBottom(){
        let delta = bottomView.bounds.height * 1.5
        //bottomView.transform = CGAffineTransformMakeTranslation(0, delta)
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [delta, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            //self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue)
            //self.bottomView.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func hideAll(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeIn
        let animValues: [Double] = [0, -svgViewContainer.bounds.width * 1.2]
        let animValues2: [Double] = [0, bottomView.bounds.height * 1.2 + 40.0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            //self.svgViewContainer.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            //self.textViewBackground.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            let finalValue2 = animValues2[0] + (animValues2[1] - animValues2[0]) * easy.apply(value)
            //self.bottomView.transform = CGAffineTransformMakeTranslation(0, finalValue2)
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showImage(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [svgViewContainer.bounds.width * 1.2, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            //self.svgViewContainer.transform = CGAffineTransformMakeTranslation(finalValue, 0)
            //self.svgViewContainer.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    func showText(){
        let easy = BackEaseInterpolater()
        easy.mode = .easeOut
        let animValues: [Double] = [svgViewContainer.bounds.width * 1.2, 0]
        let timeChange = Interpolate(values: [0,1],
                                     apply: { [weak self] (value) in
            guard let self = self else { return }
            let finalValue = animValues[0] + (animValues[1] - animValues[0]) * easy.apply(value)
            //self.textViewBackground.transform = CGAffineTransformMakeTranslation(-finalValue, 0)
            //self.textName.alpha = value
        })
        timeChange.animate(1, duration: 0.5){
            
        }
    }
    @objc func itemClick(_ sender : UIControl){
        if gameState != .playing { return }
        if sender == buttonFail || sender == buttonTrue {
            pauseGame()
            textName.text = sound
            showText()
            var delay = 1.0
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                UIView.animate(withDuration: 0.4, animations: {
                    [weak self] in
                    guard let self = self else { return }
                    //self.bottomView.transform = CGAffineTransformMakeScale(0.5, 0.5)
                    //self.bottomView.alpha = 0
                }, completion: { [weak self] _ in
                    guard let self = self else { return }
                    let attributedString = NSMutableAttributedString(string: answer)
                    if let index = self.answer.findPosition(of: self.sound) {
                        let colorRange = NSRange(location: index, length: sound.count)
                        attributedString.addAttribute(.foregroundColor, value: UIColor.color(hex: "#FF7761"), range: colorRange)
                    }
                    self.textBottom.attributedText = attributedString
                    self.buttonFail.alpha = 0
                    self.buttonTrue.alpha = 0
                    UIView.animate(withDuration: 0.4, animations: {
                        [weak self] in
                        guard let self = self else { return }
                        //self.bottomView.transform = .identity
                        //self.bottomView.alpha = 1
                    }, completion: { _ in
                        
                    })
                })
            })
            delay += 1
            let pos = vietnamese.removeSign(answer).findPosition(of: sound)
            if (sender == buttonTrue && pos != nil) || (sender == buttonFail && pos == nil) {
                //sender.animateCoin(answer: true)
                animateCoinIfCorrect(view: sender)
                scores.append(1)
                delay += 0.3 + playSound(answerCorrect1EffectSound(), getCorrectHumanSound())
            } else {
                //sender.animateCoin(answer: false)
                setGameWrong()
                incorrect += 1
                scores.append(0)
                delay += 0.3 + playSound(answerWrongEffectSound(), getWrongHumanSound())
            }
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.hideAll()
            })
            delay += 0.5
            scheduler.schedule(delay: delay, execute: {
                [weak self] in
                guard let self = self else { return }
                self.loadNextStep()
            })
        }
    }
    override func getSkills()->[GameSkill]{
        return [.GameListening]
    }
    override func getScore()->Float{
        var total = 0.0
        for score in scores {
            total += score
        }
        return Float(total) / Float(scores.count)
    }
}
extension String{
    func findPosition(of a: String) -> Int? {
        if let range = self.range(of: a) {
            let position = self.distance(from: self.startIndex, to: range.lowerBound)
            return position
        } else {
            return nil // Return nil if the string 'a' is not found in 'b'
        }
    }
    func findLastPosition(of a: String) -> Int? {
        if let range = self.range(of: a, options: .backwards) {
            let position = self.distance(from: self.startIndex, to: range.lowerBound)
            return position
        } else {
            return nil // Return nil if the string 'a' is not found in 'b'
        }
    }
}
