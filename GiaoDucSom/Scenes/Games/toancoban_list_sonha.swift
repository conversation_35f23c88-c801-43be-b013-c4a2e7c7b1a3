//
//  toancoban_list_sonha.swift
//  GiaoDucSom
//
//  Created by <PERSON><PERSON> on 7/4/25.
//


import UIKit
import SnapKit
import SVGKit
import UIKit
import SnapKit
import SVGKit

class toancoban_list_sonha: NhanBietGameFragment {
    // MARK: - Properties
    private var numpad: MathNumpad!
    private var itemContainer: UIView!
    private var svgView: UIImageView! // Gi<PERSON> lập SVGAutosizeView
    private var numpadValue: Int = 0
    private var selectedNumbers: [Int] = []
    private var selectedPath: CALayer?
    private var selectedNumber: Int = 0
    private var answerText: UILabel?
    let svg = Utilities.GetSVGKImage(named: "math_sonhachanle")
    // MARK: - Configure Layout
    override func configureLayout(_ view: UIView) {
        backgroundColor = UIColor(red: 238/255, green: 227/255, blue: 208/255, alpha: 1) // #EEE3D0
        
        let rightView = UIView()
        view.addSubview(rightView)
        rightView.snp.makeConstraints { make in
            make.top.bottom.right.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.33)
        }
        
        numpad = MathNumpad()
        numpad.isEnabled = true
        rightView.addSubviewWithPercentInset(subview: numpad, percentInset: 5)
        numpad.setListener(self)
        
        let leftBg = UIView()
        leftBg.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        view.addSubview(leftBg)
        let leftContainer = UIView()
        //leftContainer.backgroundColor = UIColor.black.withAlphaComponent(0.06) // #0f00
        leftContainer.clipsToBounds = false
        view.addSubview(leftContainer)
        leftContainer.snp.makeConstraints { make in
            make.top.bottom.left.equalToSuperview()
            make.width.equalTo(view).multipliedBy(0.67)
        }
        leftBg.snp.makeConstraints { make in
            make.top.left.bottom.equalTo(self)
            make.right.equalTo(leftContainer)
        }
        
        svgView = UIImageView()
        svgView.contentMode = .scaleAspectFit
        leftContainer.addSubview(svgView)
        svgView.makeViewCenterAndKeep(ratio: 1510.4 / 1155.3)
        
        itemContainer = UIView()
        itemContainer.clipsToBounds = false
        leftContainer.addSubview(itemContainer)
        itemContainer.makeViewCenterAndKeep(ratio: 1510.4 / 1155.3)
    }
    
    // MARK: - Game Logic
    override func updateData() {
        super.updateData()
        selectedNumbers = []
        selectedNumbers.append(contentsOf: [1, 3, 5, 7, 9, 11].shuffled().prefix(2))
        selectedNumbers.append(contentsOf: [2, 4, 6, 8, 10, 12].shuffled().prefix(2))
        let delay = playSound(openGameSound(), "toan/toan_so nha chan le")
        scheduler.schedule(delay: delay) { [weak self] in
            self?.startGame()
        }
    }
    
    override func replayIntroSound() {
        super.replayIntroSound()
        if gameState == .playing {
            pauseGame()
            let delay = playSound("toan/toan_so nha chan le")
            scheduler.schedule(delay: delay) { [weak self] in
                self?.resumeGame()
            }
        }
    }
    
    override func createGame() {
        super.createGame()
        self.svgView.image = svg.uiImage
        let size = CGSize(width: self.itemContainer.frame.width, height: self.itemContainer.frame.height)
        let sizeSvg = svg.size
        let scale = CGFloat(size.width) / CGFloat(svg.size.width)
        for id in 1...12 {
            let layer = svg.layer(withIdentifier: "\(id)")
            guard let path = layer as? CAShapeLayer else { continue }
            let number = Int(id)
            let bounds = path.shapeContentBounds!
            let view = self.createItemSonhachanle(number: number)
            view.tag = 100 + number
            self.itemContainer.addSubview(view)
            view.snp.makeConstraints { make in
                make.width.equalTo(bounds.width * scale)
                make.height.equalTo(bounds.height * scale)
                make.left.equalToSuperview().offset(bounds.minX * scale)
                make.top.equalToSuperview().offset(bounds.minY * scale)
            }
            
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(self.onItemTapped(_:)))
            view.addGestureRecognizer(tapGesture)
            view.isUserInteractionEnabled = true
        }
    }
    
    // MARK: - Helper Methods
    private func createItemSonhachanle(number: Int) -> UIView {
        let container = UIView()
        
        let innerContainer = UIView()
        container.addSubview(innerContainer)
        innerContainer.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        let textName = AutosizeLabel()
        textName.tag = number
        textName.text = selectedNumbers.contains(number) ? String(number) : ""
        textName.textColor = .black
        textName.font = .Freude(size: 20)
        textName.textAlignment = .center
        textName.adjustsFontSizeToFitWidth = true
        textName.minimumScaleFactor = 0.1
        innerContainer.addSubview(textName)
        textName.snp.makeConstraints { make in
            make.height.equalTo(innerContainer).multipliedBy(0.7)
            make.center.equalToSuperview()
            make.left.right.equalToSuperview()
        }
        
        return container
    }
    
    @objc private func onItemTapped(_ gesture: UITapGestureRecognizer) {
        let svg = Utilities.GetSVGKImage(named: "images/math_sonhachanle.svg")
        guard let view = gesture.view,
              let number = view.tag as? Int - 100,
              !selectedNumbers.contains(number),
              let path = svg.caLayerTree.sublayers?.first(where: { ($0.name) == String(number) }) as? CAShapeLayer else { return }
        
        if let selectedPath = selectedPath as? CAShapeLayer {
            selectedPath.fillColor = UIColor.white.cgColor
            checkValue()
        }
        
        path.fillColor = UIColor(red: 116/255, green: 182/255, blue: 255/255, alpha: 1).cgColor // #74B6FF
        svgView.image = svg.uiImage
        self.selectedPath = path
        self.selectedNumber = number
        self.answerText = view.viewWithTag(number) as? UILabel
        numpad.reset()
    }
    
    private func checkValue() {
        guard let selectedPath = selectedPath else { return }
        if numpadValue == 0 {
            answerText?.textColor = .black
            answerText?.text = ""
            self.selectedPath = nil
            return
        }
        
        answerText?.text = String(numpadValue)
        let correct = numpadValue == selectedNumber
        playSound(correct ? "effect/answer_correct" : "effect/answer_wrong", "topics/Numbers/\(numpadValue)")
        if correct {
            answerText?.textColor = UIColor(red: 135/255, green: 214/255, blue: 87/255, alpha: 1) // #87D657
        } else {
            setGameWrong()
            answerText?.textColor = UIColor(red: 255/255, green: 119/255, blue: 96/255, alpha: 1) // #FF7760
        }
        self.selectedPath = nil
        numpadValue = 0
        checkFinish()
    }
    
    private func checkFinish() {
        for i in 0..<itemContainer.subviews.count {
            let view = itemContainer.subviews[i]
            if let textName = view.viewWithTag(view.tag - 100) as? UILabel,
               textName.text != String(view.tag - 100) {
                if !textName.text!.isEmpty {
                    setGameWrong()
                }
                return
            }
        }
        pauseGame()
        let delay = 1.0 + playSound(delay: 1.0, names: finishCorrect1Sounds())
        animateCoinIfCorrect(view: itemContainer)
        scheduler.schedule(delay: delay) { [weak self] in
            self?.finishGame()
        }
    }
}

// MARK: - MathNumpadListener
extension toancoban_list_sonha: MathNumpadListener {
    func onNumberClick(number: Int, value: Int) {
        guard selectedPath != nil else { return }
        answerText?.text = String(value)
        answerText?.textColor = .white
        numpadValue = value
    }
    
    func onDelClick(value: Int) {
        guard selectedPath != nil else { return }
        answerText?.text = String(value)
        answerText?.textColor = .white
    }
    
    func onCheckClick(value: Int) {
        if let selectedPath = selectedPath as? CAShapeLayer {
            numpadValue = value
            selectedPath.fillColor = UIColor.white.cgColor
            checkValue()
            self.selectedPath = nil
            numpad.reset()
        }
    }
}


